import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import BoardIcon from '../icons/BoardIcon';

interface IconBackgroundProps {
  children?: React.ReactNode;
  iconSize?: number;
  iconOpacity?: number;
  spacing?: number;
  overlayColor?: string;
  overlayOpacity?: number;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const IconBackground: React.FC<IconBackgroundProps> = ({
  children,
  iconSize = 80,
  iconOpacity = 0.05,
  spacing = 120,
  overlayColor = '#000000',
  overlayOpacity = 0.3,
}) => {
  // Calculate how many icons we need to fill the screen
  const iconsPerRow = Math.ceil(screenWidth / spacing) + 1;
  const iconsPerColumn = Math.ceil(screenHeight / spacing) + 1;

  // Generate icon positions
  const generateIcons = () => {
    const icons = [];
    
    for (let row = 0; row < iconsPerColumn; row++) {
      for (let col = 0; col < iconsPerRow; col++) {
        // Offset every other row for a more natural pattern
        const offsetX = row % 2 === 0 ? 0 : spacing / 2;
        const x = col * spacing + offsetX - spacing / 2;
        const y = row * spacing - spacing / 2;
        
        icons.push(
          <View
            key={`icon-${row}-${col}`}
            style={[
              styles.iconContainer,
              {
                left: x,
                top: y,
                width: iconSize,
                height: iconSize,
              },
            ]}
          >
            <BoardIcon
              width={iconSize}
              height={iconSize}
              opacity={iconOpacity}
            />
          </View>
        );
      }
    }
    
    return icons;
  };

  return (
    <View style={styles.container}>
      {/* Background Icons Pattern */}
      <View style={styles.backgroundPattern}>
        {generateIcons()}
      </View>
      
      {/* Overlay */}
      <View 
        style={[
          styles.overlay, 
          { 
            backgroundColor: overlayColor,
            opacity: overlayOpacity,
          }
        ]} 
      />
      
      {/* Content */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  iconContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
    position: 'relative',
    zIndex: 1,
  },
});

export default IconBackground;
