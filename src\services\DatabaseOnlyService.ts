import { supabase } from '../config/supabase';

export interface ProductionDataItem {
  date: string;
  actual_ob: number;
  plan_ob: number;
  actual_ore: number;
  plan_ore: number;
  actual_fuel: number;
  plan_fuel: number;
  stripping_ratio: number;
  fuel_ratio: number;
  total_material: number;
  location_id?: string;
  shift_type?: string;
  operational_scenario?: string;
  year?: number;
  month?: number;
  monthly?: string;
  actual_rain?: number;
  plan_rain?: number;
}

export class DatabaseOnlyService {
  private static instance: DatabaseOnlyService;

  private constructor() {}

  static getInstance(): DatabaseOnlyService {
    if (!DatabaseOnlyService.instance) {
      DatabaseOnlyService.instance = new DatabaseOnlyService();
    }
    return DatabaseOnlyService.instance;
  }

  // Test database connection
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 Testing database connection...');
      
      const { data, error } = await supabase
        .from('daily_production_metrics')
        .select('count', { count: 'exact', head: true });

      if (error) {
        console.error('❌ Database connection test failed:', error);
        return false;
      }

      console.log(`✅ Database connection successful. Found ${data || 0} records in daily_production_metrics`);
      return true;
    } catch (error) {
      console.error('❌ Database connection test error:', error);
      return false;
    }
  }

  async getProductionData(startDate?: string, endDate?: string): Promise<ProductionDataItem[]> {
    try {
      console.log('📡 Fetching production data from database...');
      console.log(`📅 Date range: ${startDate || 'no start'} to ${endDate || 'no end'}`);

      // Test connection first
      const connectionOk = await this.testConnection();
      if (!connectionOk) {
        throw new Error('Database connection failed');
      }

      // Use daily_production_metrics as primary table
      let query = supabase
        .from('daily_production_metrics')
        .select(`
          date, plan_ob, actual_ob, plan_ore, actual_ore,
          plan_fuel, actual_fuel, location_id, operational_scenario,
          stripping_ratio, fuel_ratio, total_material, year, month,
          actual_rain, plan_rain
        `)
        .order('date', { ascending: true });

      // Apply date filters if provided
      if (startDate) {
        query = query.gte('date', startDate);
      }
      if (endDate) {
        query = query.lte('date', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Daily production metrics query error:', error);
        throw new Error(`Database query failed: ${error.message}`);
      }

      if (!data || data.length === 0) {
        console.log('⚠️ No production data found in daily_production_metrics');
        return [];
      }

      console.log(`✅ Fetched ${data.length} production records from daily_production_metrics`);

      // Transform daily_production_metrics data to app format
      return this.transformDailyProductionMetricsData(data);
    } catch (error) {
      console.error('❌ Failed to fetch production data:', error);
      return [];
    }
  }

  // Transform daily_production_metrics data to app format
  private transformDailyProductionMetricsData(data: any[]): ProductionDataItem[] {
    return data.map((item: any) => ({
      date: item.date,
      actual_ob: item.actual_ob || 0,
      plan_ob: item.plan_ob || 0,
      actual_ore: item.actual_ore || 0,
      plan_ore: item.plan_ore || 0,
      actual_fuel: item.actual_fuel || 0,
      plan_fuel: item.plan_fuel || 0,
      stripping_ratio: item.stripping_ratio || 0,
      fuel_ratio: item.fuel_ratio || 0,
      total_material: item.total_material || 0,
      location_id: item.location_id,
      shift_type: item.operational_scenario || 'day',
      operational_scenario: item.operational_scenario || 'normal',
      year: item.year || new Date(item.date).getFullYear(),
      month: item.month || new Date(item.date).getMonth() + 1,
      monthly: new Date(item.date).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
      actual_rain: item.actual_rain || 0,
      plan_rain: item.plan_rain || 0
    }));
  }

  // Get production data by date range
  async getProductionDataByDateRange(startDate: string, endDate: string): Promise<ProductionDataItem[]> {
    return this.getProductionData(startDate, endDate);
  }

  // Get production data for specific date
  async getProductionDataForDate(date: string): Promise<ProductionDataItem[]> {
    return this.getProductionData(date, date);
  }

  // Get recent production data (last N days)
  async getRecentProductionData(days: number = 30): Promise<ProductionDataItem[]> {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    return this.getProductionData(startDate, endDate);
  }

  // Get production summary statistics
  async getProductionSummary(startDate?: string, endDate?: string): Promise<{
    totalRecords: number;
    totalOB: number;
    totalOre: number;
    totalFuel: number;
    avgStrippingRatio: number;
    avgFuelRatio: number;
  }> {
    const data = await this.getProductionData(startDate, endDate);
    
    if (data.length === 0) {
      return {
        totalRecords: 0,
        totalOB: 0,
        totalOre: 0,
        totalFuel: 0,
        avgStrippingRatio: 0,
        avgFuelRatio: 0
      };
    }

    const totalOB = data.reduce((sum, item) => sum + item.actual_ob, 0);
    const totalOre = data.reduce((sum, item) => sum + item.actual_ore, 0);
    const totalFuel = data.reduce((sum, item) => sum + item.actual_fuel, 0);
    
    const validStrippingRatios = data.filter(item => item.stripping_ratio > 0);
    const avgStrippingRatio = validStrippingRatios.length > 0 
      ? validStrippingRatios.reduce((sum, item) => sum + item.stripping_ratio, 0) / validStrippingRatios.length
      : 0;

    const validFuelRatios = data.filter(item => item.fuel_ratio > 0);
    const avgFuelRatio = validFuelRatios.length > 0
      ? validFuelRatios.reduce((sum, item) => sum + item.fuel_ratio, 0) / validFuelRatios.length
      : 0;

    return {
      totalRecords: data.length,
      totalOB,
      totalOre,
      totalFuel,
      avgStrippingRatio,
      avgFuelRatio
    };
  }

  // Get production data grouped by month
  async getProductionDataByMonth(year?: number): Promise<{ [month: string]: ProductionDataItem[] }> {
    const currentYear = year || new Date().getFullYear();
    const startDate = `${currentYear}-01-01`;
    const endDate = `${currentYear}-12-31`;
    
    const data = await this.getProductionData(startDate, endDate);
    
    return data.reduce((acc, item) => {
      const month = item.monthly || new Date(item.date).toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
      if (!acc[month]) {
        acc[month] = [];
      }
      acc[month].push(item);
      return acc;
    }, {} as { [month: string]: ProductionDataItem[] });
  }

  // Validate data integrity
  async validateDataIntegrity(): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    try {
      // Check for recent data
      const recentData = await this.getRecentProductionData(7);
      if (recentData.length === 0) {
        issues.push('No production data found for the last 7 days');
        recommendations.push('Ensure daily production data is being recorded');
      }

      // Check for data consistency
      const inconsistentData = recentData.filter(item => 
        item.actual_ob < 0 || item.actual_ore < 0 || item.actual_fuel < 0
      );
      
      if (inconsistentData.length > 0) {
        issues.push(`Found ${inconsistentData.length} records with negative values`);
        recommendations.push('Review and correct negative production values');
      }

      // Check for missing ratios
      const missingRatios = recentData.filter(item => 
        item.stripping_ratio === 0 && item.actual_ore > 0
      );
      
      if (missingRatios.length > 0) {
        issues.push(`Found ${missingRatios.length} records with missing stripping ratios`);
        recommendations.push('Ensure stripping ratios are calculated for all records');
      }

      return {
        isValid: issues.length === 0,
        issues,
        recommendations
      };
    } catch (error) {
      return {
        isValid: false,
        issues: ['Failed to validate data integrity'],
        recommendations: ['Check database connection and table structure']
      };
    }
  }
}

export default DatabaseOnlyService;
