import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  Image,
  ImageBackground,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';
import { useAuth } from '../contexts/AuthContext';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import ProfileImageService from '../services/ProfileImageService';
import ThemedCard from '../components/ThemedCard';
import ThemedText from '../components/ThemedText';

interface ProfileScreenWithAuthProps {
  navigation: any;
}

const ProfileScreenWithAuth: React.FC<ProfileScreenWithAuthProps> = ({ navigation }) => {
  const { user, profile, signOut, loading, updateProfile, isAdmin } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();
  const [signingOut, setSigningOut] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const profileImageService = ProfileImageService.getInstance();

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              setSigningOut(true);
              await signOut();
            } catch (error: any) {
              Alert.alert('Error', 'Failed to sign out');
            } finally {
              setSigningOut(false);
            }
          },
        },
      ]
    );
  };

  const handleUpdateProfileImage = async () => {
    if (!user?.id) {
      Alert.alert('Error', 'User not found');
      return;
    }

    try {
      setUploadingImage(true);

      const result = await profileImageService.updateProfileImage(
        user.id,
        profile?.full_name || undefined,
        profile?.avatar_url || undefined
      );

      if (result.success && result.publicUrl) {
        // Update local profile state
        await updateProfile({ avatar_url: result.publicUrl });

        Alert.alert(
          'Success',
          'Profile picture updated successfully!',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Error',
          result.error || 'Failed to update profile picture.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error updating profile image:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred while updating your profile picture',
        [{ text: 'OK' }]
      );
    } finally {
      setUploadingImage(false);
    }
  };

  const getProfileImageSource = () => {
    if (profile?.avatar_url) {
      return { uri: profile.avatar_url };
    }

    return {
      uri: profileImageService.getDefaultAvatarUrl(
        profile?.full_name || undefined,
        profile?.jabatan || undefined
      )
    };
  };

  const getDepartmentIcon = (departemen: string) => {
    switch (departemen?.toLowerCase()) {
      case 'produksi':
      case 'production':
        return 'construct';
      case 'keselamatan':
      case 'safety':
        return 'shield-checkmark';
      case 'maintenance':
        return 'build';
      case 'administration':
      case 'administrasi':
        return 'settings';
      case 'supervision':
        return 'people';
      default:
        return 'business';
    }
  };

  // Create theme-aware styles
  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: colors.textPrimary,
    },
    // Professional card styles
    infoCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.lg,
      marginBottom: Layout.spacing.md,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    settingsSection: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.lg,
      marginBottom: Layout.spacing.lg,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    sectionTitle: {
      fontSize: Layout.fontSize.lg,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.md,
      paddingHorizontal: Layout.spacing.lg,
      paddingTop: Layout.spacing.lg,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Layout.spacing.lg,
      paddingVertical: Layout.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    settingText: {
      flex: 1,
      fontSize: Layout.fontSize.md,
      color: colors.textPrimary,
      marginLeft: Layout.spacing.md,
    },
    // Header styles
    headerContainer: {
      height: 280,
      position: 'relative',
    },
    headerBackground: {
      flex: 1,
      justifyContent: 'flex-end',
    },
    headerOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.4)',
    },
    editButton: {
      position: 'absolute',
      top: StatusBar.currentHeight ? StatusBar.currentHeight + 20 : 50,
      right: Layout.spacing.lg,
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    profileInfoContainer: {
      position: 'absolute',
      bottom: 20,
      left: Layout.spacing.lg,
      right: 140,
      justifyContent: 'flex-end',
    },
    profilePhotoContainer: {
      position: 'absolute',
      bottom: -60,
      right: Layout.spacing.lg,
      zIndex: 10,
    },
    avatarContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      marginBottom: Layout.spacing.md,
      position: 'relative',
      ...Layout.shadow,
    },
    avatarImage: {
      width: 120,
      height: 120,
      borderRadius: 60,
      borderWidth: 4,
      borderColor: colors.primary,
    },
    cameraOverlay: {
      position: 'absolute',
      bottom: 8,
      right: 8,
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 3,
      borderColor: colors.textInverse,
    },
    onlineIndicator: {
      position: 'absolute',
      top: 8,
      right: 8,
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: colors.success,
      borderWidth: 3,
      borderColor: colors.textInverse,
    },
    // Content styles
    content: {
      flex: 1,
      paddingHorizontal: Layout.spacing.md,
      paddingTop: Layout.spacing.sm,
      paddingBottom: Layout.spacing.md,
    },
    profileSpacing: {
      height: 40,
    },
    infoSection: {
      marginBottom: Layout.spacing.lg,
      marginTop: -Layout.spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Layout.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    infoContent: {
      flex: 1,
      marginLeft: Layout.spacing.sm,
    },
    infoLabel: {
      fontSize: Layout.fontSize.sm,
      color: colors.textLight,
    },
    infoValue: {
      fontSize: Layout.fontSize.md,
      color: colors.textPrimary,
      fontWeight: '500',
      marginTop: 2,
    },
    // Overlay text styles
    overlayName: {
      fontSize: Layout.fontSize.lg,
      fontWeight: '700',
      color: colors.textInverse,
      marginBottom: Layout.spacing.xs,
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    overlayDepartment: {
      fontSize: Layout.fontSize.md,
      fontWeight: '600',
      color: colors.success,
      marginBottom: Layout.spacing.xs,
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    overlayEmail: {
      fontSize: Layout.fontSize.sm,
      color: colors.textInverse,
      opacity: 0.9,
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
  });

  if (loading) {
    return (
      <View style={dynamicStyles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={dynamicStyles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <View style={dynamicStyles.container}>
      <StatusBar
        barStyle={isDarkMode ? "light-content" : "dark-content"}
        translucent={true}
        backgroundColor="transparent"
      />

      {/* Custom Header with Background Image and Overlay */}
      <View style={dynamicStyles.headerContainer}>
        {/* Background Header Image */}
        <ImageBackground
          source={{
            uri: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'
          }}
          style={dynamicStyles.headerBackground}
          resizeMode="cover"
        >
          {/* Dark Overlay for better text readability */}
          <View style={dynamicStyles.headerOverlay}>
            {/* Edit Button */}
            <TouchableOpacity
              style={dynamicStyles.editButton}
              onPress={() => navigation.navigate('HeaderImageSettings')}
            >
              <Ionicons name="settings-outline" size={20} color={colors.textInverse} />
            </TouchableOpacity>
          </View>

          {/* Profile Info positioned next to profile photo */}
          <View style={dynamicStyles.profileInfoContainer}>
            <Text style={dynamicStyles.overlayName} numberOfLines={1}>
              {profile?.full_name || 'User Name'}
            </Text>
            <Text style={dynamicStyles.overlayDepartment} numberOfLines={1}>
              {profile?.departemen || 'Department'}
            </Text>
            <Text style={dynamicStyles.overlayEmail} numberOfLines={1}>
              {user?.email || '<EMAIL>'}
            </Text>
          </View>

          {/* Profile Photo positioned on top */}
          <View style={dynamicStyles.profilePhotoContainer}>
            <TouchableOpacity
              style={dynamicStyles.avatarContainer}
              onPress={handleUpdateProfileImage}
              disabled={uploadingImage}
            >
              <Image
                source={getProfileImageSource()}
                style={dynamicStyles.avatarImage}
              />

              {/* Camera overlay icon */}
              <View style={dynamicStyles.cameraOverlay}>
                {uploadingImage ? (
                  <ActivityIndicator size="small" color={colors.textInverse} />
                ) : (
                  <Ionicons
                    name="camera"
                    size={16}
                    color={colors.textInverse}
                  />
                )}
              </View>

              {/* Online status indicator */}
              <View style={dynamicStyles.onlineIndicator} />
            </TouchableOpacity>
          </View>
        </ImageBackground>
      </View>

      <ScrollView style={dynamicStyles.content}>
        {/* Spacing for overlapping profile photo */}
        <View style={dynamicStyles.profileSpacing} />

        {/* Profile Information */}
        <View style={dynamicStyles.infoSection}>
          <ThemedText variant="primary" size="lg" weight="semibold" style={{ marginBottom: Layout.spacing.md }}>
            Profile Information
          </ThemedText>

          <ThemedCard variant="elevated">
            <View style={dynamicStyles.infoRow}>
              <Ionicons name="id-card-outline" size={20} color={colors.textLight} />
              <View style={dynamicStyles.infoContent}>
                <Text style={dynamicStyles.infoLabel}>Employee ID</Text>
                <Text style={dynamicStyles.infoValue}>{profile?.employee_id || 'Not set'}</Text>
              </View>
            </View>

            <View style={dynamicStyles.infoRow}>
              <Ionicons name="card-outline" size={20} color={colors.textLight} />
              <View style={dynamicStyles.infoContent}>
                <Text style={dynamicStyles.infoLabel}>NIK</Text>
                <Text style={dynamicStyles.infoValue}>{profile?.nik || 'Not set'}</Text>
              </View>
            </View>

            <View style={dynamicStyles.infoRow}>
              <Ionicons name="business-outline" size={20} color={colors.textLight} />
              <View style={dynamicStyles.infoContent}>
                <Text style={dynamicStyles.infoLabel}>Departemen</Text>
                <Text style={dynamicStyles.infoValue}>{profile?.departemen || 'Not set'}</Text>
              </View>
            </View>

            <View style={dynamicStyles.infoRow}>
              <Ionicons name="briefcase-outline" size={20} color={colors.textLight} />
              <View style={dynamicStyles.infoContent}>
                <Text style={dynamicStyles.infoLabel}>Jabatan</Text>
                <Text style={dynamicStyles.infoValue}>{profile?.jabatan || 'Not set'}</Text>
              </View>
            </View>

            <View style={dynamicStyles.infoRow}>
              <Ionicons name="call-outline" size={20} color={colors.textLight} />
              <View style={dynamicStyles.infoContent}>
                <Text style={dynamicStyles.infoLabel}>Phone</Text>
                <Text style={dynamicStyles.infoValue}>{profile?.phone || 'Not set'}</Text>
              </View>
            </View>

            <View style={dynamicStyles.infoRow}>
              <Ionicons name="location-outline" size={20} color={colors.textLight} />
              <View style={dynamicStyles.infoContent}>
                <Text style={dynamicStyles.infoLabel}>Location</Text>
                <Text style={dynamicStyles.infoValue}>
                  {profile?.location_id ? 'Assigned' : 'Not assigned'}
                </Text>
              </View>
            </View>

            <View style={dynamicStyles.infoRow}>
              <Ionicons name="calendar-outline" size={20} color={colors.textLight} />
              <View style={dynamicStyles.infoContent}>
                <Text style={dynamicStyles.infoLabel}>Hire Date</Text>
                <Text style={dynamicStyles.infoValue}>
                  {profile?.hire_date
                    ? new Date(profile.hire_date).toLocaleDateString()
                    : 'Not set'
                  }
                </Text>
              </View>
            </View>
          </ThemedCard>
        </View>

        {/* Account Settings */}
        <View style={dynamicStyles.settingsSection}>
          <Text style={dynamicStyles.sectionTitle}>Account Settings</Text>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('EditProfile')}
          >
            <Ionicons name="person-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>Edit Profile</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('NotificationSettings')}
          >
            <Ionicons name="notifications-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>Notifications</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('SecuritySettings')}
          >
            <Ionicons name="shield-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>Privacy & Security</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('HeaderImageSettings')}
          >
            <Ionicons name="image-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>Header Image</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('ThemeSettings')}
          >
            <Ionicons name="color-palette-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>Theme Settings</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('PrivacySettings')}
          >
            <Ionicons name="eye-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>Privacy Settings</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('HelpSupport')}
          >
            <Ionicons name="help-circle-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>Help & Support</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={dynamicStyles.settingItem}
            onPress={() => navigation.navigate('About')}
          >
            <Ionicons name="information-circle-outline" size={20} color={colors.textLight} />
            <Text style={dynamicStyles.settingText}>About</Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
          </TouchableOpacity>
        </View>

        {/* Admin Settings - Only show for admin users */}
        {isAdmin && (
          <View style={dynamicStyles.settingsSection}>
            <Text style={dynamicStyles.sectionTitle}>Admin Settings</Text>

            <TouchableOpacity
              style={dynamicStyles.settingItem}
              onPress={() => navigation.navigate('DashboardHeaderManagement')}
            >
              <Ionicons name="images-outline" size={20} color={colors.textLight} />
              <Text style={dynamicStyles.settingText}>Dashboard Headers</Text>
              <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
            </TouchableOpacity>
          </View>
        )}

        {/* App Information */}
        <View style={styles.infoSection}>
          <ThemedText variant="primary" size="lg" weight="semibold" style={{ marginBottom: Layout.spacing.md }}>
            App Information
          </ThemedText>

          <ThemedCard variant="outlined" padding="large">
            <ThemedText variant="primary" size="lg" weight="bold" style={{ marginBottom: Layout.spacing.xs, textAlign: 'center' }}>
              Mining Operations App
            </ThemedText>
            <ThemedText variant="light" size="sm" style={{ marginBottom: Layout.spacing.sm, textAlign: 'center' }}>
              Version 1.0.0
            </ThemedText>
            <ThemedText variant="light" size="sm" style={{ textAlign: 'center', lineHeight: 20 }}>
              Comprehensive mining operations management system with real-time monitoring,
              safety reporting, and production tracking.
            </ThemedText>
          </ThemedCard>
        </View>

        {/* Sign Out Button */}
        <TouchableOpacity
          style={[
            {
              backgroundColor: colors.error,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              padding: Layout.spacing.md,
              borderRadius: Layout.borderRadius.lg,
              marginBottom: Layout.spacing.lg,
              shadowColor: colors.cardShadow,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: isDarkMode ? 0.3 : 0.1,
              shadowRadius: 4,
              elevation: 3,
            },
            signingOut && { opacity: 0.6 }
          ]}
          onPress={handleSignOut}
          disabled={signingOut}
        >
          {signingOut ? (
            <ActivityIndicator color={colors.textInverse} />
          ) : (
            <>
              <Ionicons name="log-out-outline" size={20} color={colors.textInverse} />
              <ThemedText variant="inverse" weight="bold" style={{ marginLeft: Layout.spacing.sm }}>
                Sign Out
              </ThemedText>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

// All styles are now theme-aware in dynamicStyles - no static styles needed
const styles = StyleSheet.create({
  // Empty - all styles moved to dynamicStyles for theme support
  userName: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.xs,
  },
  userRole: {
    fontSize: Layout.fontSize.md,
    color: Colors.primary,
    fontWeight: '600',
    marginBottom: Layout.spacing.xs,
  },
  userEmail: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  infoSection: {
    marginBottom: Layout.spacing.lg,
    marginTop: -Layout.spacing.md, // Negative margin to bring closer to header
  },
  sectionTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.md,
  },
  infoCard: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.md,
    ...Layout.shadowLight,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  infoContent: {
    flex: 1,
    marginLeft: Layout.spacing.sm,
  },
  infoLabel: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  infoValue: {
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    fontWeight: '500',
    marginTop: 2,
  },
  settingsSection: {
    marginBottom: Layout.spacing.lg,
  },
  settingItem: {
    backgroundColor: Colors.surface,
    flexDirection: 'row',
    alignItems: 'center',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.sm,
    ...Layout.shadowLight,
  },
  settingText: {
    flex: 1,
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    marginLeft: Layout.spacing.sm,
  },
  appInfoSection: {
    marginBottom: Layout.spacing.lg,
  },
  appInfoCard: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.md,
    alignItems: 'center',
    ...Layout.shadowLight,
  },
  appInfoText: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.xs,
  },
  appVersionText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    marginBottom: Layout.spacing.sm,
  },
  appDescriptionText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    textAlign: 'center',
    lineHeight: 20,
  },
  signOutButton: {
    backgroundColor: Colors.secondary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.lg,
    ...Layout.shadow,
  },
  signOutButtonDisabled: {
    opacity: 0.6,
  },
  signOutText: {
    color: Colors.textInverse,
    fontSize: Layout.fontSize.md,
    fontWeight: 'bold',
    marginLeft: Layout.spacing.sm,
  },

  // Header Background Styles
  headerContainer: {
    height: 280,
    position: 'relative',
  },
  headerBackground: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  profileInfoContainer: {
    position: 'absolute',
    bottom: 20,
    left: Layout.spacing.lg,
    right: 140, // Leave space for profile photo (120px + margin)
    justifyContent: 'flex-end',
  },
  editButton: {
    position: 'absolute',
    top: StatusBar.currentHeight ? StatusBar.currentHeight + 20 : 50,
    right: Layout.spacing.lg,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profilePhotoContainer: {
    position: 'absolute',
    bottom: -60, // Half of avatar height to overlap
    right: Layout.spacing.lg,
    zIndex: 10,
  },

  // Profile Spacing and Summary
  profileSpacing: {
    height: 40, // Reduced space to bring info section closer to header
  },
  profileSummaryCard: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.lg,
    marginHorizontal: Layout.spacing.lg,
    marginBottom: Layout.spacing.lg,
    alignItems: 'center',
    ...Layout.shadow,
  },
  overlayName: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '700',
    color: Colors.textInverse,
    marginBottom: Layout.spacing.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  overlayDepartment: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.accentLight,
    marginBottom: Layout.spacing.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  overlayEmail: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textInverse,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },


});

export default ProfileScreenWithAuth;
