# 🗄️ Database Design & Schema

## 📋 Table of Contents
- [Database Overview](#database-overview)
- [Schema Design](#schema-design)
- [Table Relationships](#table-relationships)
- [Data Models](#data-models)
- [Role-Based Access Control System](#role-based-access-control-system)
- [Indexing Strategy](#indexing-strategy)
- [Performance Optimization](#performance-optimization)

## 🎯 Database Overview

### Database Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Database Layer                           │
├─────────────────────────────────────────────────────────────┤
│  Production Database (Supabase PostgreSQL)                 │
│  ├── Core Tables (Users, Locations, Equipment)             │
│  ├── Production Data (Daily Metrics, Reports)              │
│  ├── Analytics Tables (Aggregated Data)                    │
│  └── Audit Tables (Change Tracking)                        │
├─────────────────────────────────────────────────────────────┤
│  Local Database (SQLite)                                   │
│  ├── Cached Production Data                                │
│  ├── Offline Changes Queue                                 │
│  ├── User Preferences                                      │
│  └── Sync Metadata                                         │
└─────────────────────────────────────────────────────────────┘
```

### Design Principles
- **Normalization**: 3NF compliance for data integrity
- **Performance**: Optimized for read-heavy workloads
- **Scalability**: Designed for horizontal scaling
- **Audit Trail**: Complete change tracking
- **Security**: Row-level security (RLS) implementation

## 📊 Schema Design

### Core Entity Relationship Diagram
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Users    │    │  Locations  │    │  Equipment  │
│             │    │             │    │             │
│ • id (PK)   │    │ • id (PK)   │    │ • id (PK)   │
│ • email     │    │ • name      │    │ • name      │
│ • role      │    │ • type      │    │ • type      │
│ • profile   │    │ • coords    │    │ • status    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
                           ▼
                ┌─────────────────────┐
                │ Daily Production    │
                │     Metrics         │
                │                     │
                │ • id (PK)          │
                │ • date             │
                │ • location_id (FK) │
                │ • created_by (FK)  │
                │ • actual_ob        │
                │ • actual_ore       │
                │ • plan_ob          │
                │ • plan_ore         │
                │ • actual_fuel      │
                │ • plan_fuel        │
                │ • weather_data     │
                └─────────────────────┘
```

### Table Definitions

#### 1. Users Table
```sql
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    encrypted_password VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role user_role DEFAULT 'operator',
    phone VARCHAR(20),
    employee_id VARCHAR(50) UNIQUE,
    department VARCHAR(100),
    supervisor_id UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User roles enum
CREATE TYPE user_role AS ENUM (
    'admin',
    'supervisor', 
    'operator',
    'viewer'
);
```

#### 2. Locations Table
```sql
CREATE TABLE locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    type location_type NOT NULL,
    description TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    elevation DECIMAL(8, 2),
    timezone VARCHAR(50) DEFAULT 'UTC',
    is_active BOOLEAN DEFAULT true,
    parent_location_id UUID REFERENCES locations(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Location types enum
CREATE TYPE location_type AS ENUM (
    'mine_site',
    'pit',
    'dump',
    'processing_plant',
    'office',
    'warehouse'
);
```

#### 3. Equipment Table
```sql
CREATE TABLE equipment (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    equipment_number VARCHAR(100) UNIQUE NOT NULL,
    type equipment_type NOT NULL,
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    year_manufactured INTEGER,
    capacity DECIMAL(12, 2),
    capacity_unit VARCHAR(20),
    status equipment_status DEFAULT 'operational',
    location_id UUID REFERENCES locations(id),
    assigned_operator_id UUID REFERENCES users(id),
    last_maintenance_date DATE,
    next_maintenance_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Equipment types and status enums
CREATE TYPE equipment_type AS ENUM (
    'excavator',
    'dump_truck',
    'bulldozer',
    'loader',
    'drill',
    'crusher',
    'conveyor'
);

CREATE TYPE equipment_status AS ENUM (
    'operational',
    'maintenance',
    'repair',
    'retired'
);
```

#### 4. Daily Production Metrics Table
```sql
CREATE TABLE daily_production_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    location_id UUID REFERENCES locations(id) NOT NULL,
    created_by UUID REFERENCES users(id) NOT NULL,
    
    -- Production data
    actual_ob DECIMAL(12, 2) DEFAULT 0,
    plan_ob DECIMAL(12, 2) DEFAULT 0,
    actual_ore DECIMAL(12, 2) DEFAULT 0,
    plan_ore DECIMAL(12, 2) DEFAULT 0,
    
    -- Fuel consumption
    actual_fuel DECIMAL(10, 2) DEFAULT 0,
    plan_fuel DECIMAL(10, 2) DEFAULT 0,
    
    -- Weather impact
    actual_rain DECIMAL(5, 2) DEFAULT 0,
    plan_rain DECIMAL(5, 2) DEFAULT 0,
    actual_slippery DECIMAL(5, 2) DEFAULT 0,
    plan_slippery DECIMAL(5, 2) DEFAULT 0,
    
    -- Metadata
    monthly VARCHAR(50),
    week INTEGER,
    notes TEXT,
    weather_conditions VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(date, location_id)
);
```

#### 5. Production Reports Table
```sql
CREATE TABLE production_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    report_number VARCHAR(50) UNIQUE NOT NULL,
    created_by UUID REFERENCES users(id) NOT NULL,
    report_date DATE NOT NULL,
    shift shift_type NOT NULL,
    location_id UUID REFERENCES locations(id) NOT NULL,
    
    -- Production metrics (JSONB for flexibility)
    production_metrics JSONB NOT NULL,
    
    -- Equipment and crew
    equipment_used UUID[],
    crew_size INTEGER,
    
    -- Performance metrics
    total_tonnage DECIMAL(12, 2),
    operating_hours DECIMAL(8, 2),
    downtime_hours DECIMAL(8, 2),
    fuel_consumed DECIMAL(10, 2),
    
    -- Environmental
    weather_conditions VARCHAR(255),
    notes TEXT,
    
    -- Approval workflow
    approved_by UUID REFERENCES users(id),
    approved_date TIMESTAMP WITH TIME ZONE,
    status report_status DEFAULT 'draft',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enums for production reports
CREATE TYPE shift_type AS ENUM ('day', 'night', 'swing');
CREATE TYPE report_status AS ENUM ('draft', 'submitted', 'approved', 'rejected');
```

#### 6. Equipment Maintenance Table
```sql
CREATE TABLE equipment_maintenance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    equipment_id UUID REFERENCES equipment(id) NOT NULL,
    maintenance_type maintenance_type NOT NULL,
    scheduled_date DATE NOT NULL,
    completed_date DATE,
    performed_by UUID REFERENCES users(id),
    
    -- Maintenance details
    description TEXT,
    parts_used JSONB,
    labor_hours DECIMAL(6, 2),
    cost DECIMAL(10, 2),
    
    -- Status tracking
    status maintenance_status DEFAULT 'scheduled',
    priority maintenance_priority DEFAULT 'medium',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Maintenance enums
CREATE TYPE maintenance_type AS ENUM (
    'preventive',
    'corrective',
    'emergency',
    'inspection'
);

CREATE TYPE maintenance_status AS ENUM (
    'scheduled',
    'in_progress',
    'completed',
    'cancelled'
);

CREATE TYPE maintenance_priority AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);
```

#### 7. Safety Incidents Table
```sql
CREATE TABLE safety_incidents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    incident_number VARCHAR(50) UNIQUE NOT NULL,
    reported_by UUID REFERENCES users(id) NOT NULL,
    incident_date TIMESTAMP WITH TIME ZONE NOT NULL,
    location_id UUID REFERENCES locations(id) NOT NULL,
    
    -- Incident details
    type incident_type NOT NULL,
    severity incident_severity NOT NULL,
    description TEXT NOT NULL,
    immediate_action TEXT,
    
    -- People involved
    people_involved JSONB,
    injuries_count INTEGER DEFAULT 0,
    
    -- Investigation
    investigated_by UUID REFERENCES users(id),
    investigation_notes TEXT,
    root_cause TEXT,
    corrective_actions TEXT,
    
    -- Status tracking
    status incident_status DEFAULT 'reported',
    closed_date TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Safety incident enums
CREATE TYPE incident_type AS ENUM (
    'near_miss',
    'first_aid',
    'medical_treatment',
    'lost_time',
    'equipment_damage',
    'environmental'
);

CREATE TYPE incident_severity AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);

CREATE TYPE incident_status AS ENUM (
    'reported',
    'investigating',
    'resolved',
    'closed'
);
```

## 🔗 Table Relationships

### Primary Relationships
```sql
-- User hierarchy (supervisor relationships)
ALTER TABLE users 
ADD CONSTRAINT fk_users_supervisor 
FOREIGN KEY (supervisor_id) REFERENCES users(id);

-- Location hierarchy (parent-child locations)
ALTER TABLE locations 
ADD CONSTRAINT fk_locations_parent 
FOREIGN KEY (parent_location_id) REFERENCES locations(id);

-- Equipment assignment
ALTER TABLE equipment 
ADD CONSTRAINT fk_equipment_location 
FOREIGN KEY (location_id) REFERENCES locations(id);

ALTER TABLE equipment 
ADD CONSTRAINT fk_equipment_operator 
FOREIGN KEY (assigned_operator_id) REFERENCES users(id);

-- Production metrics relationships
ALTER TABLE daily_production_metrics 
ADD CONSTRAINT fk_metrics_location 
FOREIGN KEY (location_id) REFERENCES locations(id);

ALTER TABLE daily_production_metrics 
ADD CONSTRAINT fk_metrics_creator 
FOREIGN KEY (created_by) REFERENCES users(id);
```

### Indexes for Performance
```sql
-- Primary indexes for frequent queries
CREATE INDEX idx_daily_metrics_date ON daily_production_metrics(date);
CREATE INDEX idx_daily_metrics_location ON daily_production_metrics(location_id);
CREATE INDEX idx_daily_metrics_date_location ON daily_production_metrics(date, location_id);

-- User and authentication indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_employee_id ON users(employee_id);
CREATE INDEX idx_users_role ON users(role);

-- Equipment indexes
CREATE INDEX idx_equipment_location ON equipment(location_id);
CREATE INDEX idx_equipment_status ON equipment(status);
CREATE INDEX idx_equipment_type ON equipment(type);

-- Production reports indexes
CREATE INDEX idx_reports_date ON production_reports(report_date);
CREATE INDEX idx_reports_location ON production_reports(location_id);
CREATE INDEX idx_reports_status ON production_reports(status);

-- Maintenance indexes
CREATE INDEX idx_maintenance_equipment ON equipment_maintenance(equipment_id);
CREATE INDEX idx_maintenance_date ON equipment_maintenance(scheduled_date);
CREATE INDEX idx_maintenance_status ON equipment_maintenance(status);
```

## 👥 Role-Based Access Control System

### Overview
Sistem role-based access control (RBAC) yang komprehensif untuk mengatur akses pengguna berdasarkan level hierarki dan permission granular dalam operasi tambang.

### Role Hierarchy (Level 1-10)
```sql
-- Tabel roles untuk mendefinisikan level pengguna
CREATE TABLE roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  level INTEGER NOT NULL DEFAULT 1,
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Update tabel users untuk menambahkan role_id
ALTER TABLE users ADD COLUMN role_id UUID REFERENCES roles(id);
```

### Role Definitions
| Level | Role Name | Display Name | Description |
|-------|-----------|--------------|-------------|
| 10 | `super_admin` | Super Administrator | Akses penuh ke semua fitur sistem |
| 9 | `admin` | Administrator | Administrator dengan akses luas |
| 8 | `mine_manager` | Mine Manager | Manajer tambang dengan akses operasional penuh |
| 7 | `production_supervisor` | Production Supervisor | Supervisor produksi tambang |
| 6 | `equipment_manager` | Equipment Manager | Manajer peralatan tambang |
| 5 | `safety_officer` | Safety Officer | Petugas keselamatan tambang |
| 4 | `shift_supervisor` | Shift Supervisor | Supervisor shift operasional |
| 3 | `operator` | Equipment Operator | Operator peralatan tambang |
| 2 | `technician` | Maintenance Technician | Teknisi perawatan peralatan |
| 1 | `employee` | General Employee | Karyawan umum dengan akses terbatas |

### Permission System
```json
{
  "users": ["create", "read", "update", "delete"],
  "production": ["create", "read", "update", "delete"],
  "equipment": ["create", "read", "update", "delete"],
  "safety": ["create", "read", "update", "delete"],
  "reports": ["create", "read", "update", "delete"],
  "analytics": ["read", "export"],
  "sap": ["read", "sync"],
  "attendance": ["read", "update"],
  "maintenance": ["create", "read", "update", "delete"],
  "incidents": ["create", "read", "update"],
  "profile": ["read", "update"]
}
```

### Database Functions
```sql
-- Function untuk mendapatkan role default
CREATE OR REPLACE FUNCTION get_default_role_id() RETURNS UUID AS $$
BEGIN
  RETURN (SELECT id FROM roles WHERE name = 'employee' LIMIT 1);
END;
$$ LANGUAGE plpgsql;

-- Function untuk check permission user
CREATE OR REPLACE FUNCTION check_user_permission(user_id UUID, resource TEXT, action TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_permissions JSONB;
  resource_permissions JSONB;
BEGIN
  SELECT r.permissions INTO user_permissions
  FROM users u
  JOIN roles r ON u.role_id = r.id
  WHERE u.id = user_id;

  IF user_permissions IS NULL THEN
    RETURN FALSE;
  END IF;

  IF user_permissions->>'all' = 'true' THEN
    RETURN TRUE;
  END IF;

  resource_permissions := user_permissions->resource;
  RETURN resource_permissions ? action;
END;
$$ LANGUAGE plpgsql;

-- Function untuk mendapatkan level role user
CREATE OR REPLACE FUNCTION get_user_role_level(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  user_level INTEGER;
BEGIN
  SELECT r.level INTO user_level
  FROM users u
  JOIN roles r ON u.role_id = r.id
  WHERE u.id = user_id;

  RETURN COALESCE(user_level, 1);
END;
$$ LANGUAGE plpgsql;
```

### Views for Easy Querying
```sql
-- View untuk menggabungkan user dan role data
CREATE OR REPLACE VIEW user_roles AS
SELECT
  u.id,
  u.email,
  u.full_name,
  u.departemen,
  u.jabatan,
  u.role_id,
  r.name as role_name,
  r.display_name as role_display_name,
  r.level as role_level,
  r.permissions as role_permissions
FROM users u
LEFT JOIN roles r ON u.role_id = r.id;
```

### Demo Users for Testing
```sql
-- Tabel demo_users untuk testing (terpisah dari auth.users)
CREATE TABLE demo_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  full_name VARCHAR(255),
  phone VARCHAR(20),
  employee_id VARCHAR(50) UNIQUE,
  nik VARCHAR(20) UNIQUE,
  departemen VARCHAR(100),
  jabatan VARCHAR(100),
  hire_date DATE,
  is_active BOOLEAN DEFAULT true,
  location_id UUID REFERENCES locations(id),
  role_id UUID REFERENCES roles(id),
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);
```

### Security Implementation
- **Hierarchical Access**: Level tinggi dapat mengakses data level rendah
- **Permission-Based**: Granular control per resource dan action
- **Database-Level Validation**: Functions untuk validasi di database
- **Type Safety**: Full TypeScript integration
- **Audit Trail**: Tracking perubahan role dan permission

## 🔒 Row Level Security (RLS)

### Security Policies
```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_production_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE production_reports ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data and subordinates
CREATE POLICY users_policy ON users
    FOR ALL USING (
        auth.uid() = id OR 
        auth.uid() IN (
            SELECT supervisor_id FROM users WHERE id = auth.uid()
        )
    );

-- Location access based on user role and assignment
CREATE POLICY location_access_policy ON locations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND (role IN ('admin', 'supervisor') OR 
                 id IN (SELECT assigned_operator_id FROM equipment WHERE location_id = locations.id))
        )
    );

-- Production metrics access
CREATE POLICY metrics_access_policy ON daily_production_metrics
    FOR ALL USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'supervisor')
        )
    );
```

## 📈 Performance Optimization

### Query Optimization Strategies
1. **Partitioning**: Date-based partitioning for large tables
2. **Materialized Views**: Pre-computed aggregations
3. **Connection Pooling**: Efficient database connections
4. **Query Caching**: Redis for frequently accessed data

### Monitoring & Maintenance
- **Query Performance**: Regular EXPLAIN ANALYZE reviews
- **Index Usage**: Monitor and optimize index effectiveness
- **Table Statistics**: Keep statistics up to date
- **Vacuum & Analyze**: Regular maintenance tasks

---

**Next**: [API Design](api-design.md) | [Security](security.md) | [Overview](overview.md)
