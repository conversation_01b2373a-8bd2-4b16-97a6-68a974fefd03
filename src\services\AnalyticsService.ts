import { supabase } from '../config/supabase';
import MiningCalculationService from './MiningCalculationService';
import CachedProductionService from './CachedProductionService';
import SmartCacheManager, { CacheStrategy } from './SmartCacheManager';

export interface ProductionAnalytics {
  totalProduction: number;
  averageStrippingRatio: number;
  averageFuelEfficiency: number;
  equipmentUtilization: number;
  targetAchievement: number;
  trends: {
    production: TrendData[];
    efficiency: TrendData[];
    costs: TrendData[];
  };
}

export interface TrendData {
  date: string;
  value: number;
  target?: number;
  variance?: number;
}

export interface KPIMetrics {
  production: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
    targetAchievement: number;
  };
  efficiency: {
    strippingRatio: number;
    fuelEfficiency: number;
    equipmentUtilization: number;
    operationalEfficiency: number;
  };
  safety: {
    incidentRate: number;
    daysWithoutIncident: number;
    safetyScore: number;
  };
  costs: {
    fuelCostPerTon: number;
    maintenanceCostPerHour: number;
    totalOperationalCost: number;
  };
}

export interface PredictiveAnalytics {
  productionForecast: {
    nextWeek: number;
    nextMonth: number;
    confidence: number;
  };
  maintenancePrediction: {
    equipmentId: string;
    equipmentName: string;
    predictedFailureDate: string;
    confidence: number;
    recommendedAction: string;
  }[];
  costOptimization: {
    fuelOptimization: number;
    maintenanceOptimization: number;
    potentialSavings: number;
  };
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private cachedProductionService: CachedProductionService;
  private cacheManager: SmartCacheManager;

  private constructor() {
    this.cachedProductionService = CachedProductionService.getInstance();
    this.cacheManager = SmartCacheManager.getInstance();
  }

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // KPI Calculations
  async calculateKPIs(dateRange?: { start: string; end: string }): Promise<KPIMetrics> {
    const cacheStrategy: CacheStrategy = {
      key: 'kpi_metrics',
      ttl: 30 * 60 * 1000, // 30 minutes
      priority: 'high',
      refreshOnExpiry: true,
      networkDependent: true
    };

    return this.cacheManager.getOrFetch(
      `kpi_metrics_${dateRange?.start || 'all'}_${dateRange?.end || 'all'}`,
      async () => {
        const productionData = await this.getProductionDataForAnalysis(dateRange);
        const safetyData = await this.getSafetyDataForAnalysis(dateRange);
        const equipmentData = await this.getEquipmentDataForAnalysis(dateRange);

        return {
          production: this.calculateProductionKPIs(productionData),
          efficiency: this.calculateEfficiencyKPIs(productionData, equipmentData),
          safety: this.calculateSafetyKPIs(safetyData),
          costs: this.calculateCostKPIs(productionData, equipmentData)
        };
      },
      cacheStrategy
    );
  }

  // Production Analytics
  async getProductionAnalytics(period: 'week' | 'month' | 'quarter' | 'year'): Promise<ProductionAnalytics> {
    const cacheStrategy: CacheStrategy = {
      key: 'production_analytics',
      ttl: 60 * 60 * 1000, // 1 hour
      priority: 'high',
      refreshOnExpiry: true,
      networkDependent: true
    };

    return this.cacheManager.getOrFetch(
      `production_analytics_${period}`,
      async () => {
        const dateRange = this.getDateRangeForPeriod(period);
        const data = await this.getProductionDataForAnalysis(dateRange);

        return {
          totalProduction: this.calculateTotalProduction(data),
          averageStrippingRatio: this.calculateAverageStrippingRatio(data),
          averageFuelEfficiency: this.calculateAverageFuelEfficiency(data),
          equipmentUtilization: await this.calculateEquipmentUtilization(dateRange),
          targetAchievement: this.calculateTargetAchievement(data),
          trends: {
            production: this.calculateProductionTrends(data),
            efficiency: this.calculateEfficiencyTrends(data),
            costs: this.calculateCostTrends(data)
          }
        };
      },
      cacheStrategy
    );
  }

  // Predictive Analytics
  async getPredictiveAnalytics(): Promise<PredictiveAnalytics> {
    const cacheStrategy: CacheStrategy = {
      key: 'predictive_analytics',
      ttl: 4 * 60 * 60 * 1000, // 4 hours
      priority: 'medium',
      refreshOnExpiry: true,
      networkDependent: true
    };

    return this.cacheManager.getOrFetch(
      'predictive_analytics',
      async () => {
        const historicalData = await this.getHistoricalDataForPrediction();
        const equipmentData = await this.getEquipmentDataForPrediction();

        return {
          productionForecast: this.calculateProductionForecast(historicalData),
          maintenancePrediction: this.predictMaintenanceNeeds(equipmentData),
          costOptimization: this.calculateCostOptimization(historicalData, equipmentData)
        };
      },
      cacheStrategy
    );
  }

  // Comparative Analysis
  async getComparativeAnalysis(periods: string[]): Promise<any> {
    const comparisons = await Promise.all(
      periods.map(async (period) => {
        const dateRange = this.parsePeriodToDateRange(period);
        const data = await this.getProductionDataForAnalysis(dateRange);
        
        return {
          period,
          metrics: {
            totalProduction: this.calculateTotalProduction(data),
            averageEfficiency: this.calculateAverageEfficiency(data),
            costPerTon: this.calculateCostPerTon(data),
            safetyScore: this.calculateSafetyScore(await this.getSafetyDataForAnalysis(dateRange))
          }
        };
      })
    );

    return {
      comparisons,
      insights: this.generateComparativeInsights(comparisons)
    };
  }

  // Equipment Performance Analytics
  async getEquipmentPerformanceAnalytics(equipmentId?: string): Promise<any> {
    const { data: equipmentData } = await supabase
      .from('equipment')
      .select(`
        *,
        maintenance_records(*),
        production_reports(*)
      `)
      .eq(equipmentId ? 'id' : 'status', equipmentId || 'operational');

    return equipmentData?.map(equipment => ({
      id: equipment.id,
      name: equipment.name,
      utilization: this.calculateEquipmentUtilizationRate(equipment),
      efficiency: this.calculateEquipmentEfficiency(equipment),
      maintenanceCost: this.calculateMaintenanceCost(equipment.maintenance_records),
      downtime: this.calculateDowntime(equipment.maintenance_records),
      predictedMaintenance: this.predictNextMaintenance(equipment)
    }));
  }

  // Cost Analysis
  async getCostAnalysis(period: string): Promise<any> {
    const dateRange = this.parsePeriodToDateRange(period);
    
    const [productionData, maintenanceData, fuelData] = await Promise.all([
      this.getProductionDataForAnalysis(dateRange),
      this.getMaintenanceDataForAnalysis(dateRange),
      this.getFuelDataForAnalysis(dateRange)
    ]);

    return {
      totalCosts: {
        fuel: this.calculateTotalFuelCost(fuelData),
        maintenance: this.calculateTotalMaintenanceCost(maintenanceData),
        labor: this.calculateTotalLaborCost(productionData),
        operational: this.calculateTotalOperationalCost(
          this.calculateTotalFuelCost(fuelData),
          this.calculateTotalMaintenanceCost(maintenanceData),
          this.calculateTotalLaborCost(productionData)
        )
      },
      costPerTon: this.calculateCostPerTon(productionData),
      costTrends: this.calculateCostTrends(productionData),
      optimization: this.identifyCostOptimizationOpportunities(
        this.calculateTotalOperationalCost(
          this.calculateTotalFuelCost(fuelData),
          this.calculateTotalMaintenanceCost(maintenanceData),
          this.calculateTotalLaborCost(productionData)
        ),
        this.calculateCostPerTon(productionData)
      )
    };
  }

  // Private calculation methods
  private calculateProductionKPIs(data: any[]): KPIMetrics['production'] {
    const total = data.reduce((sum, item) => sum + (item.actual_ob + item.actual_ore), 0);
    const target = data.reduce((sum, item) => sum + (item.plan_ob + item.plan_ore), 0);
    
    return {
      daily: total / data.length,
      weekly: total / (data.length / 7),
      monthly: total / (data.length / 30),
      yearly: total / (data.length / 365),
      targetAchievement: target > 0 ? (total / target) * 100 : 0
    };
  }

  private calculateEfficiencyKPIs(productionData: any[], equipmentData: any[]): KPIMetrics['efficiency'] {
    const avgStrippingRatio = productionData.reduce((sum, item) => {
      return sum + MiningCalculationService.calculateStripRatio(item.actual_ob || 0, item.actual_ore || 0);
    }, 0) / productionData.length;

    const avgFuelEfficiency = productionData.reduce((sum, item) => {
      return sum + MiningCalculationService.calculateFuelRatio(
        item.actual_fuel || 0,
        item.actual_ob || 0,
        item.actual_ore || 0
      );
    }, 0) / productionData.length;

    return {
      strippingRatio: avgStrippingRatio,
      fuelEfficiency: avgFuelEfficiency,
      equipmentUtilization: 85, // Placeholder - will be calculated properly
      operationalEfficiency: this.calculateOperationalEfficiency(productionData)
    };
  }

  private calculateSafetyKPIs(safetyData: any[]): KPIMetrics['safety'] {
    const totalIncidents = safetyData.length;
    const criticalIncidents = safetyData.filter(incident => incident.severity === 'critical').length;
    const lastIncidentDate = safetyData.length > 0 ? 
      Math.max(...safetyData.map(incident => new Date(incident.incident_date).getTime())) : 0;
    
    const daysWithoutIncident = lastIncidentDate > 0 ? 
      Math.floor((Date.now() - lastIncidentDate) / (1000 * 60 * 60 * 24)) : 0;

    return {
      incidentRate: totalIncidents / 30, // incidents per month
      daysWithoutIncident,
      safetyScore: Math.max(0, 100 - (criticalIncidents * 20) - (totalIncidents * 5))
    };
  }

  private calculateCostKPIs(productionData: any[], equipmentData: any[]): KPIMetrics['costs'] {
    const totalMaterial = productionData.reduce((sum, item) => {
      return sum + MiningCalculationService.calculateTotalMaterial(item.actual_ob || 0, item.actual_ore || 0);
    }, 0);
    const totalFuel = productionData.reduce((sum, item) => sum + item.actual_fuel, 0);
    const fuelCostPerLiter = 1.2; // Example cost
    
    return {
      fuelCostPerTon: totalMaterial > 0 ? (totalFuel * fuelCostPerLiter) / totalMaterial : 0,
      maintenanceCostPerHour: this.calculateMaintenanceCostPerHour(equipmentData),
      totalOperationalCost: 0 // Placeholder - will be calculated properly
    };
  }

  private calculateProductionForecast(historicalData: any[]): PredictiveAnalytics['productionForecast'] {
    // Simple linear regression for forecasting
    const recentTrendData = historicalData.slice(-30).map((item, index) => ({ value: item.total_production || 0, index }));
    const recentTrend = this.calculateTrend(recentTrendData);
    const avgProduction = historicalData.reduce((sum, item) => sum + (item.total_production || 0), 0) / historicalData.length;
    const trendMultiplier = recentTrend === 'increasing' ? 0.1 : recentTrend === 'decreasing' ? -0.1 : 0;

    return {
      nextWeek: avgProduction * 7 * (1 + trendMultiplier),
      nextMonth: avgProduction * 30 * (1 + trendMultiplier),
      confidence: Math.min(0.95, 0.6 + (historicalData.length / 100)) // Higher confidence with more data
    };
  }

  private predictMaintenanceNeeds(equipmentData: any[]): PredictiveAnalytics['maintenancePrediction'] {
    return equipmentData.map(equipment => {
      const lastMaintenance = equipment.maintenance_records?.[0];
      const operatingHours = equipment.operating_hours || 0;
      const maintenanceInterval = this.getMaintenanceInterval(equipment.equipment_type);
      
      const hoursUntilMaintenance = maintenanceInterval - (operatingHours % maintenanceInterval);
      const daysUntilMaintenance = Math.ceil(hoursUntilMaintenance / 8); // 8 hours per day
      
      return {
        equipmentId: equipment.id,
        equipmentName: equipment.name,
        predictedFailureDate: new Date(Date.now() + daysUntilMaintenance * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        confidence: this.calculateMaintenancePredictionConfidence(equipment),
        recommendedAction: this.getRecommendedMaintenanceAction(equipment)
      };
    });
  }

  // Utility methods
  private getDateRangeForPeriod(period: string): { start: string; end: string } {
    const end = new Date();
    const start = new Date();
    
    switch (period) {
      case 'week':
        start.setDate(end.getDate() - 7);
        break;
      case 'month':
        start.setMonth(end.getMonth() - 1);
        break;
      case 'quarter':
        start.setMonth(end.getMonth() - 3);
        break;
      case 'year':
        start.setFullYear(end.getFullYear() - 1);
        break;
    }
    
    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    };
  }

  private async getProductionDataForAnalysis(dateRange?: { start: string; end: string }): Promise<any[]> {
    if (dateRange) {
      return this.cachedProductionService.getProductionDataByDateRange(dateRange.start, dateRange.end);
    }
    return this.cachedProductionService.getProductionData();
  }

  private async getSafetyDataForAnalysis(dateRange?: { start: string; end: string }): Promise<any[]> {
    let query = supabase.from('safety_incidents').select('*');
    
    if (dateRange) {
      query = query.gte('incident_date', dateRange.start).lte('incident_date', dateRange.end);
    }
    
    const { data } = await query;
    return data || [];
  }

  private async getEquipmentDataForAnalysis(dateRange?: { start: string; end: string }): Promise<any[]> {
    const { data } = await supabase
      .from('equipment')
      .select(`
        *,
        maintenance_records(*)
      `);
    
    return data || [];
  }

  // Additional helper methods
  private async getHistoricalDataForPrediction(): Promise<any[]> {
    const { data } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .order('date', { ascending: true })
      .limit(100);
    return data || [];
  }

  private async getEquipmentDataForPrediction(): Promise<any[]> {
    const { data } = await supabase
      .from('equipment')
      .select('*');
    return data || [];
  }

  private calculateCostOptimization(historicalData: any[], equipmentData: any[]): any {
    const avgFuelCost = historicalData.reduce((sum, item) => sum + (item.actual_fuel || 0), 0) / historicalData.length;
    const avgMaintenanceCost = equipmentData.reduce((sum, item) => sum + (item.maintenance_cost || 0), 0) / equipmentData.length;

    return {
      fuelOptimization: {
        currentAverage: avgFuelCost,
        potentialSavings: avgFuelCost * 0.15, // 15% potential savings
        recommendations: ['Optimize fuel consumption patterns', 'Regular equipment maintenance']
      },
      maintenanceOptimization: {
        currentAverage: avgMaintenanceCost,
        potentialSavings: avgMaintenanceCost * 0.10, // 10% potential savings
        recommendations: ['Predictive maintenance scheduling', 'Equipment upgrade planning']
      }
    };
  }

  private parsePeriodToDateRange(period: string): { start: string; end: string } {
    const end = new Date();
    const start = new Date();

    switch (period.toLowerCase()) {
      case 'week':
        start.setDate(end.getDate() - 7);
        break;
      case 'month':
        start.setMonth(end.getMonth() - 1);
        break;
      case 'quarter':
        start.setMonth(end.getMonth() - 3);
        break;
      case 'year':
        start.setFullYear(end.getFullYear() - 1);
        break;
      default:
        start.setMonth(end.getMonth() - 1);
    }

    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    };
  }

  private calculateAverageEfficiency(data: any[]): number {
    return this.calculateAverageFuelEfficiency(data);
  }

  private calculateCostPerTon(data: any[]): number {
    const totalCost = data.reduce((sum, item) => sum + (item.total_cost || 0), 0);
    const totalTons = data.reduce((sum, item) => sum + (item.actual_ore || 0), 0);
    return totalTons > 0 ? totalCost / totalTons : 0;
  }

  private calculateSafetyScore(data: any[]): number {
    const safetyKPIs = this.calculateSafetyKPIs(data);
    return safetyKPIs.incidentRate > 0 ? Math.max(0, 100 - (safetyKPIs.incidentRate * 10)) : 100;
  }

  private generateComparativeInsights(comparisons: any[]): any {
    return {
      trends: comparisons.map(comp => ({
        period: comp.period,
        production: comp.production,
        efficiency: comp.efficiency,
        costs: comp.costs
      })),
      insights: [
        'Production trends show seasonal variations',
        'Efficiency improvements needed in Q2',
        'Cost optimization opportunities identified'
      ]
    };
  }

  private calculateEquipmentUtilizationRate(equipment: any): number {
    const totalHours = 24 * 30; // 30 days
    const operatingHours = equipment.operating_hours || totalHours * 0.8;
    return (operatingHours / totalHours) * 100;
  }

  private calculateEquipmentEfficiency(equipment: any): number {
    const plannedOutput = equipment.planned_output || 100;
    const actualOutput = equipment.actual_output || 80;
    return (actualOutput / plannedOutput) * 100;
  }

  private calculateMaintenanceCost(maintenanceRecords: any[]): number {
    if (!maintenanceRecords) return 0;
    return maintenanceRecords.reduce((sum, record) => sum + (record.cost || 0), 0);
  }

  private calculateDowntime(maintenanceRecords: any[]): number {
    if (!maintenanceRecords) return 0;
    return maintenanceRecords.reduce((sum, record) => sum + (record.downtime_hours || 0), 0);
  }

  private predictNextMaintenance(equipment: any): any {
    const lastMaintenance = equipment.last_maintenance_date ? new Date(equipment.last_maintenance_date) : new Date();
    const interval = this.getMaintenanceInterval(equipment.type);
    const nextDate = new Date(lastMaintenance);
    nextDate.setDate(nextDate.getDate() + interval);

    return {
      scheduledDate: nextDate.toISOString().split('T')[0],
      confidence: this.calculateMaintenancePredictionConfidence(equipment),
      recommendedAction: this.getRecommendedMaintenanceAction(equipment)
    };
  }

  private async getMaintenanceDataForAnalysis(dateRange: { start: string; end: string }): Promise<any[]> {
    const { data } = await supabase
      .from('maintenance_records')
      .select('*')
      .gte('date', dateRange.start)
      .lte('date', dateRange.end);
    return data || [];
  }

  private async getFuelDataForAnalysis(dateRange: { start: string; end: string }): Promise<any[]> {
    const { data } = await supabase
      .from('daily_production_metrics')
      .select('actual_fuel, date')
      .gte('date', dateRange.start)
      .lte('date', dateRange.end);
    return data || [];
  }

  private calculateTotalFuelCost(fuelData: any[]): number {
    const fuelPricePerLiter = 1.5; // Example price
    return fuelData.reduce((sum, item) => sum + (item.actual_fuel || 0) * fuelPricePerLiter, 0);
  }

  private calculateTotalMaintenanceCost(maintenanceData: any[]): number {
    return maintenanceData.reduce((sum, item) => sum + (item.cost || 0), 0);
  }

  private calculateTotalLaborCost(data: any[]): number {
    const laborCostPerDay = 500; // Example cost
    return data.length * laborCostPerDay;
  }

  private calculateTotalOperationalCost(fuelCost: number, maintenanceCost: number, laborCost: number): number {
    return fuelCost + maintenanceCost + laborCost;
  }

  private identifyCostOptimizationOpportunities(totalCost: number, costPerTon: number): any[] {
    const opportunities = [];

    if (costPerTon > 50) {
      opportunities.push({
        area: 'Fuel Efficiency',
        potential: 'Reduce fuel consumption by 10%',
        savings: totalCost * 0.1
      });
    }

    opportunities.push({
      area: 'Maintenance Optimization',
      potential: 'Implement predictive maintenance',
      savings: totalCost * 0.05
    });

    return opportunities;
  }

  private async calculateOverallEquipmentUtilization(data: any[]): Promise<number> {
    return await this.calculateEquipmentUtilization({ start: '', end: '' });
  }

  private calculateOperationalEfficiency(data: any[]): number {
    const totalPlanned = data.reduce((sum, item) => sum + (item.plan_ob + item.plan_ore), 0);
    const totalActual = data.reduce((sum, item) => sum + (item.actual_ob + item.actual_ore), 0);
    return totalPlanned > 0 ? (totalActual / totalPlanned) * 100 : 0;
  }

  private calculateMaintenanceCostPerHour(equipmentData: any[]): number {
    const totalCost = equipmentData.reduce((sum, item) => sum + (item.maintenance_cost || 0), 0);
    const totalHours = equipmentData.reduce((sum, item) => sum + (item.operating_hours || 0), 0);
    return totalHours > 0 ? totalCost / totalHours : 0;
  }

  private calculateTrend(data: any[]): string {
    if (data.length < 2) return 'stable';
    const first = data[0].value || 0;
    const last = data[data.length - 1].value || 0;
    const change = ((last - first) / first) * 100;

    if (change > 5) return 'increasing';
    if (change < -5) return 'decreasing';
    return 'stable';
  }

  private getMaintenanceInterval(equipmentType: string): number {
    const intervals: { [key: string]: number } = {
      'excavator': 30,
      'truck': 15,
      'drill': 45,
      'loader': 20,
      'default': 30
    };
    return intervals[equipmentType?.toLowerCase()] || intervals.default;
  }

  private calculateMaintenancePredictionConfidence(equipment: any): number {
    const dataPoints = equipment.maintenance_history?.length || 0;
    const baseConfidence = Math.min(dataPoints * 10, 90);
    return Math.max(baseConfidence, 50);
  }

  private getRecommendedMaintenanceAction(equipment: any): string {
    const hoursUsed = equipment.operating_hours || 0;
    const lastMaintenance = equipment.hours_since_maintenance || 0;

    if (lastMaintenance > 500) return 'Immediate maintenance required';
    if (lastMaintenance > 300) return 'Schedule maintenance soon';
    return 'Continue monitoring';
  }

  private calculateTotalProduction(data: any[]): number {
    return data.reduce((sum, item) => sum + (item.actual_ob + item.actual_ore), 0);
  }

  private calculateAverageStrippingRatio(data: any[]): number {
    const validData = data.filter(item => item.actual_ore > 0);
    return validData.reduce((sum, item) => sum + (item.actual_ob / item.actual_ore), 0) / validData.length;
  }

  private calculateAverageFuelEfficiency(data: any[]): number {
    const validData = data.filter(item => (item.actual_ob || 0) > 0 || (item.actual_ore || 0) > 0);
    return validData.reduce((sum, item) => {
      return sum + MiningCalculationService.calculateFuelRatio(
        item.actual_fuel || 0,
        item.actual_ob || 0,
        item.actual_ore || 0
      );
    }, 0) / validData.length;
  }

  private async calculateEquipmentUtilization(dateRange: { start: string; end: string }): Promise<number> {
    // Implementation for equipment utilization calculation
    return 85; // Placeholder
  }

  private calculateTargetAchievement(data: any[]): number {
    const totalActual = data.reduce((sum, item) => sum + (item.actual_ob + item.actual_ore), 0);
    const totalTarget = data.reduce((sum, item) => sum + (item.plan_ob + item.plan_ore), 0);
    return totalTarget > 0 ? (totalActual / totalTarget) * 100 : 0;
  }

  private calculateProductionTrends(data: any[]): TrendData[] {
    return data.map(item => ({
      date: item.date,
      value: item.actual_ob + item.actual_ore,
      target: item.plan_ob + item.plan_ore,
      variance: ((item.actual_ob + item.actual_ore) - (item.plan_ob + item.plan_ore)) / (item.plan_ob + item.plan_ore) * 100
    }));
  }

  private calculateEfficiencyTrends(data: any[]): TrendData[] {
    return data.map(item => ({
      date: item.date,
      value: item.actual_ore > 0 ? item.actual_ob / item.actual_ore : 0
    }));
  }

  private calculateCostTrends(data: any[]): TrendData[] {
    return data.map(item => {
      const totalMaterial = MiningCalculationService.calculateTotalMaterial(item.actual_ob || 0, item.actual_ore || 0);
      return {
        date: item.date,
        value: totalMaterial > 0 ? (item.actual_fuel * 1.2) / totalMaterial : 0 // Assuming fuel cost of 1.2 per liter
      };
    });
  }
}

export default AnalyticsService;
