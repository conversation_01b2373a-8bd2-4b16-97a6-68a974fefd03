import { ApiResponse, RequestConfig, UploadConfig, DownloadConfig } from '../types';

/**
 * Abstract Network Adapter Interface
 * Platform-specific implementations should extend this class
 */
export abstract class NetworkAdapter {
  protected baseURL: string = '';
  protected defaultHeaders: Record<string, string> = {};
  protected timeout: number = 30000;

  /**
   * Set base URL for all requests
   */
  setBaseURL(url: string): void {
    this.baseURL = url.replace(/\/$/, ''); // Remove trailing slash
  }

  /**
   * Set default headers for all requests
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = { ...headers };
  }

  /**
   * Set default timeout for requests
   */
  setTimeout(timeoutMs: number): void {
    this.timeout = timeoutMs;
  }

  /**
   * Make HTTP request
   */
  abstract request<T>(config: RequestConfig): Promise<ApiResponse<T>>;

  /**
   * Upload file
   */
  abstract upload(file: File | Blob, config: UploadConfig): Promise<ApiResponse<any>>;

  /**
   * Download file
   */
  abstract download(url: string, config: DownloadConfig): Promise<Blob>;

  /**
   * GET request helper
   */
  async get<T>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url,
      headers
    });
  }

  /**
   * POST request helper
   */
  async post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
      headers
    });
  }

  /**
   * PUT request helper
   */
  async put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
      headers
    });
  }

  /**
   * PATCH request helper
   */
  async patch<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PATCH',
      url,
      data,
      headers
    });
  }

  /**
   * DELETE request helper
   */
  async delete<T>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url,
      headers
    });
  }

  /**
   * Build full URL
   */
  protected buildURL(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    const cleanUrl = url.startsWith('/') ? url : `/${url}`;
    return `${this.baseURL}${cleanUrl}`;
  }

  /**
   * Merge headers
   */
  protected mergeHeaders(requestHeaders?: Record<string, string>): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      ...this.defaultHeaders,
      ...requestHeaders
    };
  }

  /**
   * Handle request timeout
   */
  protected createTimeoutPromise<T>(timeoutMs: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  /**
   * Create error response
   */
  protected createErrorResponse<T>(error: Error): ApiResponse<T> {
    return {
      data: null,
      error: {
        message: error.message,
        details: error.stack
      },
      status: 0,
      statusText: 'Network Error'
    };
  }

  /**
   * Validate response
   */
  protected validateResponse(response: any): boolean {
    return response && typeof response === 'object';
  }

  /**
   * Parse response data
   */
  protected parseResponseData(data: any): any {
    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch {
        return data;
      }
    }
    return data;
  }

  /**
   * Check if request should be retried
   */
  protected shouldRetry(error: any, attempt: number, maxRetries: number): boolean {
    if (attempt >= maxRetries) {
      return false;
    }

    // Retry on network errors or 5xx status codes
    if (error.code === 'NETWORK_ERROR' || 
        (error.status >= 500 && error.status < 600)) {
      return true;
    }

    return false;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  protected calculateRetryDelay(attempt: number, baseDelay: number = 1000): number {
    return baseDelay * Math.pow(2, attempt - 1);
  }

  /**
   * Retry request with exponential backoff
   */
  protected async retryRequest<T>(
    requestFn: () => Promise<ApiResponse<T>>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<ApiResponse<T>> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;

        if (!this.shouldRetry(error, attempt, maxRetries)) {
          break;
        }

        const delay = this.calculateRetryDelay(attempt, baseDelay);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }
}
