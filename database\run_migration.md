# 🗄️ Database Migration: Add NIK, Departemen, Jabatan

## 📋 **Migration Overview**

Menambahkan 3 kolom baru ke tabel `users`:
- **NIK**: Nomor Induk <PERSON> (Indonesian National ID)
- **Departemen**: Department/Division tempat karyawan bekerja
- **Jabatan**: Job title/position karyawan

## 🚀 **Cara Menjalankan Migration**

### **Method 1: Supabase Dashboard (RECOMMENDED)**

1. **Login ke Supabase Dashboard**
   - Buka https://supabase.com/dashboard
   - Login dengan akun Anda
   - Pilih project Mining Operations App

2. **Buka SQL Editor**
   - Klik menu "SQL Editor" di sidebar kiri
   - Klik "New query"

3. **Copy & Paste SQL Script**
   ```sql
   -- Migration: Add NIK, Departemen, and Jabatan to users table
   -- Date: 2025-01-15
   
   -- Add new columns to users table
   ALTER TABLE public.users 
   ADD COLUMN IF NOT EXISTS nik character varying(16) NULL,
   ADD COLUMN IF NOT EXISTS departemen character varying(100) NULL,
   ADD COLUMN IF NOT EXISTS jabatan character varying(100) NULL;
   
   -- Add unique constraint for NIK (Indonesian National ID)
   ALTER TABLE public.users 
   ADD CONSTRAINT users_nik_key UNIQUE (nik);
   
   -- Add indexes for better query performance
   CREATE INDEX IF NOT EXISTS idx_users_nik ON public.users USING btree (nik);
   CREATE INDEX IF NOT EXISTS idx_users_departemen ON public.users USING btree (departemen);
   CREATE INDEX IF NOT EXISTS idx_users_jabatan ON public.users USING btree (jabatan);
   
   -- Add comments for documentation
   COMMENT ON COLUMN public.users.nik IS 'Nomor Induk Kependudukan (Indonesian National ID Number)';
   COMMENT ON COLUMN public.users.departemen IS 'Department/Division where the employee works';
   COMMENT ON COLUMN public.users.jabatan IS 'Job title/position of the employee';
   ```

4. **Run Migration**
   - Klik tombol "Run" (atau Ctrl+Enter)
   - Tunggu sampai selesai
   - Pastikan tidak ada error

5. **Verify Migration**
   ```sql
   -- Verify the changes
   SELECT column_name, data_type, is_nullable, column_default
   FROM information_schema.columns 
   WHERE table_name = 'users' 
     AND table_schema = 'public'
     AND column_name IN ('nik', 'departemen', 'jabatan')
   ORDER BY ordinal_position;
   ```

### **Method 2: Using Migration File**

1. **File Location**: `database/migrations/add_user_fields.sql`
2. **Copy content** dari file tersebut
3. **Paste ke Supabase SQL Editor**
4. **Run migration**

## ✅ **Verification Steps**

### **1. Check Table Structure**
```sql
\d+ public.users
```

### **2. Check New Columns**
```sql
SELECT 
  column_name,
  data_type,
  character_maximum_length,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
  AND table_schema = 'public'
ORDER BY ordinal_position;
```

### **3. Test Insert/Update**
```sql
-- Test update existing user
UPDATE public.users 
SET 
  nik = '1234567890123456',
  departemen = 'Production',
  jabatan = 'Supervisor'
WHERE email = '<EMAIL>';

-- Verify update
SELECT id, full_name, nik, departemen, jabatan 
FROM public.users 
WHERE email = '<EMAIL>';
```

## 📱 **App Changes Made**

### **1. Database Types Updated**
- ✅ `src/types/database.ts` - Added NIK, Departemen, Jabatan fields

### **2. UI Updated**
- ✅ `src/screens/ProfileScreenWithAuth.tsx` - Display new fields in profile

### **3. Context Updated**
- ✅ `src/contexts/AuthContext.tsx` - Already supports new fields via UserProfile type

## 🎯 **Expected Results**

### **Before Migration:**
```
Profile Information:
- Employee ID: EMP001
- Phone: +62-xxx-xxxx
- Location: Assigned
- Hire Date: 15/01/2023
```

### **After Migration:**
```
Profile Information:
- Employee ID: EMP001
- NIK: Not set
- Departemen: Not set  
- Jabatan: Not set
- Phone: +62-xxx-xxxx
- Location: Assigned
- Hire Date: 15/01/2023
```

## 🔧 **Troubleshooting**

### **Error: "relation already exists"**
- **Solution**: Kolom sudah ada, migration aman diabaikan

### **Error: "constraint already exists"**
- **Solution**: Constraint sudah ada, migration aman diabaikan

### **Error: "permission denied"**
- **Solution**: Pastikan menggunakan akun dengan akses admin ke database

## 📊 **Sample Data for Testing**

```sql
-- Update sample users with Indonesian data
UPDATE public.users SET 
  nik = '3201234567890001',
  departemen = 'Produksi',
  jabatan = 'Supervisor Shift'
WHERE role = 'supervisor';

UPDATE public.users SET 
  nik = '3201234567890002', 
  departemen = 'Keselamatan',
  jabatan = 'Safety Officer'
WHERE role = 'safety_officer';

UPDATE public.users SET 
  nik = '3201234567890003',
  departemen = 'Maintenance', 
  jabatan = 'Teknisi Mekanik'
WHERE role = 'maintenance_tech';
```

---

**🎯 RESULT: Database siap dengan field NIK, Departemen, dan Jabatan!**

**✅ Status**: Ready to Deploy  
**🗄️ Migration**: Safe and Reversible  
**📱 App**: Updated and Compatible  
**🔧 Testing**: Sample data provided
