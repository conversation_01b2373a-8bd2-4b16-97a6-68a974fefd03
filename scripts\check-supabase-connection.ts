#!/usr/bin/env npx ts-node

/**
 * Supabase Database Connection Checker
 * 
 * This script checks the connection to Supabase database and verifies
 * that all tables and functions are working properly.
 */

import { supabase, DatabaseService } from '../src/services/supabase';
import { databaseService } from '../src/services/DatabaseService';

interface ConnectionTestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

class SupabaseConnectionChecker {
  private results: ConnectionTestResult[] = [];

  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({ test, status, message, details });
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${test}: ${message}`);
    if (details) {
      console.log(`   Details:`, details);
    }
  }

  async checkBasicConnection(): Promise<void> {
    try {
      console.log('\n🔍 Testing basic Supabase connection...');
      
      // Test 1: Basic connection
      const { data, error } = await supabase.from('users').select('count').limit(1);
      
      if (error) {
        this.addResult('Basic Connection', 'FAIL', `Connection failed: ${error.message}`, error);
      } else {
        this.addResult('Basic Connection', 'PASS', 'Successfully connected to Supabase');
      }
    } catch (error) {
      this.addResult('Basic Connection', 'FAIL', `Connection error: ${error}`, error);
    }
  }

  async checkAuthentication(): Promise<void> {
    try {
      console.log('\n🔐 Testing authentication system...');
      
      // Test 1: Check current session
      const { data: session, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        this.addResult('Session Check', 'WARNING', `Session error: ${sessionError.message}`);
      } else if (session.session) {
        this.addResult('Session Check', 'PASS', 'Active session found');
        
        // Test current user profile
        const user = await DatabaseService.getCurrentUser();
        if (user && user.profile) {
          this.addResult('User Profile', 'PASS', `User profile loaded: ${user.profile.full_name} (${user.profile.role})`);
        } else {
          this.addResult('User Profile', 'WARNING', 'No user profile found');
        }
      } else {
        this.addResult('Session Check', 'WARNING', 'No active session (not logged in)');
      }
    } catch (error) {
      this.addResult('Authentication', 'FAIL', `Auth error: ${error}`, error);
    }
  }

  async checkDatabaseTables(): Promise<void> {
    try {
      console.log('\n📊 Testing database tables...');
      
      const tables = [
        'users',
        'locations', 
        'equipment',
        'production_reports',
        'safety_incidents',
        'maintenance_records',
        'shift_reports',
        'activity_documentation'
      ];

      for (const table of tables) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);
            
          if (error) {
            this.addResult(`Table: ${table}`, 'FAIL', `Table access failed: ${error.message}`);
          } else {
            this.addResult(`Table: ${table}`, 'PASS', `Table accessible (${data?.length || 0} sample records)`);
          }
        } catch (tableError) {
          this.addResult(`Table: ${table}`, 'FAIL', `Table error: ${tableError}`);
        }
      }
    } catch (error) {
      this.addResult('Database Tables', 'FAIL', `Tables check failed: ${error}`, error);
    }
  }

  async checkDataIntegrity(): Promise<void> {
    try {
      console.log('\n🔍 Testing data integrity...');
      
      // Test 1: Check locations
      const { data: locations, error: locError } = await supabase
        .from('locations')
        .select('*');
        
      if (locError) {
        this.addResult('Locations Data', 'FAIL', `Locations query failed: ${locError.message}`);
      } else {
        this.addResult('Locations Data', 'PASS', `Found ${locations?.length || 0} locations`);
      }

      // Test 2: Check equipment
      const { data: equipment, error: eqError } = await supabase
        .from('equipment')
        .select('*')
        .limit(5);
        
      if (eqError) {
        this.addResult('Equipment Data', 'FAIL', `Equipment query failed: ${eqError.message}`);
      } else {
        this.addResult('Equipment Data', 'PASS', `Found ${equipment?.length || 0} equipment records`);
      }

      // Test 3: Check recent production data
      const { data: production, error: prodError } = await supabase
        .from('production_reports')
        .select('*')
        .order('report_date', { ascending: false })
        .limit(5);
        
      if (prodError) {
        this.addResult('Production Data', 'FAIL', `Production query failed: ${prodError.message}`);
      } else {
        this.addResult('Production Data', 'PASS', `Found ${production?.length || 0} recent production records`);
      }

    } catch (error) {
      this.addResult('Data Integrity', 'FAIL', `Data integrity check failed: ${error}`, error);
    }
  }

  async checkRealTimeFeatures(): Promise<void> {
    try {
      console.log('\n⚡ Testing real-time features...');
      
      // Test real-time subscription
      const channel = supabase
        .channel('test-channel')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'equipment' }, 
          (payload) => {
            console.log('Real-time event received:', payload);
          }
        );

      const subscribeResult = await channel.subscribe();

      if (subscribeResult) {
        this.addResult('Real-time Subscription', 'PASS', 'Real-time subscription successful');

        // Clean up
        setTimeout(() => {
          supabase.removeChannel(channel);
        }, 1000);
      } else {
        this.addResult('Real-time Subscription', 'WARNING', 'Subscription failed');
      }
      
    } catch (error) {
      this.addResult('Real-time Features', 'FAIL', `Real-time test failed: ${error}`, error);
    }
  }

  async checkLocalDatabase(): Promise<void> {
    try {
      console.log('\n💾 Testing local SQLite database...');
      
      // Test local database health
      const isHealthy = await databaseService.isDatabaseHealthy();
      
      if (isHealthy) {
        this.addResult('Local Database', 'PASS', 'Local SQLite database is healthy');
        
        // Get storage info
        const storageInfo = await databaseService.getStorageInfo();
        this.addResult('Local Storage', 'PASS', 
          `Local storage: ${storageInfo.totalRecords} total records, ${storageInfo.unsyncedRecords} unsynced`);
      } else {
        this.addResult('Local Database', 'FAIL', 'Local SQLite database has issues');
      }
      
    } catch (error) {
      this.addResult('Local Database', 'FAIL', `Local database check failed: ${error}`, error);
    }
  }

  async checkDashboardData(): Promise<void> {
    try {
      console.log('\n📈 Testing dashboard data...');
      
      const dashboardData = await DatabaseService.getDashboardData();
      
      if (dashboardData) {
        this.addResult('Dashboard Data', 'PASS', 'Dashboard data loaded successfully');
      } else {
        this.addResult('Dashboard Data', 'WARNING', 'Dashboard data is empty');
      }
      
    } catch (error) {
      this.addResult('Dashboard Data', 'FAIL', `Dashboard data check failed: ${error}`, error);
    }
  }

  printSummary(): void {
    console.log('\n' + '='.repeat(60));
    console.log('📋 SUPABASE CONNECTION CHECK SUMMARY');
    console.log('='.repeat(60));
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️  Warnings: ${warnings}`);
    console.log(`📊 Total Tests: ${this.results.length}`);
    
    if (failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }
    
    if (warnings > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.results
        .filter(r => r.status === 'WARNING')
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }
    
    console.log('\n' + '='.repeat(60));
    
    if (failed === 0) {
      console.log('🎉 All critical tests passed! Supabase connection is healthy.');
    } else {
      console.log('🚨 Some tests failed. Please check the issues above.');
    }
  }

  async runAllChecks(): Promise<void> {
    console.log('🚀 Starting Supabase Database Connection Check...');
    console.log('Database URL:', 'https://ohqbaimnhwvdfrmxvhxv.supabase.co');
    
    await this.checkBasicConnection();
    await this.checkAuthentication();
    await this.checkDatabaseTables();
    await this.checkDataIntegrity();
    await this.checkRealTimeFeatures();
    await this.checkLocalDatabase();
    await this.checkDashboardData();
    
    this.printSummary();
  }
}

// Run the checker
async function main() {
  const checker = new SupabaseConnectionChecker();
  await checker.runAllChecks();
}

if (require.main === module) {
  main().catch(console.error);
}

export { SupabaseConnectionChecker };
