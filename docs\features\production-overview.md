# 📊 Production Overview Feature

> **🔧 Updated**: January 2025 - Mining formulas standardized and corrected
> **✅ Status**: Production Ready with accurate calculations
> **🎯 Key Fix**: 60% error in Fuel Ratio calculations resolved

## 📋 Table of Contents
- [Feature Overview](#feature-overview)
- [User Interface](#user-interface)
- [Data Visualization](#data-visualization)
- [Metrics & KPIs](#metrics--kpis)
- [Time Period Analysis](#time-period-analysis)
- [Technical Implementation](#technical-implementation)

## 🎯 Feature Overview

The Production Overview is the central dashboard of the Mining Operations App, providing real-time insights into production performance, equipment utilization, and operational efficiency through the `ProductionOverviewScreen` component.

> **📋 Comprehensive Documentation**: For detailed technical implementation, API integration, performance optimizations, and troubleshooting, see [Production Metrics System Documentation](../production-metrics-system.md)

### Key Capabilities
- **Real-time Metrics**: Live production data updates
- **Multi-period Analysis**: Daily, weekly, monthly, and yearly views
- **Interactive Charts**: Touch-enabled data visualization
- **Performance Tracking**: Achievement vs. target monitoring
- **Trend Analysis**: Historical performance patterns
- **Offline Support**: Works without internet connectivity

### User Roles & Access
- **Operators**: View current shift data and input production metrics
- **Supervisors**: Monitor team performance and approve reports
- **Managers**: Access comprehensive analytics and reports
- **Executives**: High-level KPIs and strategic insights

## 📱 User Interface

### Screen Layout
```
┌─────────────────────────────────────────────────────────┐
│  🏗️ Production Overview                    ⚙️ Settings  │
├─────────────────────────────────────────────────────────┤
│  📅 Production - July 23, 2025                         │
│  🟢 All synced                                          │
├─────────────────────────────────────────────────────────┤
│                Production Metrics                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │ Overburden  │  │ Ore Prod.   │  │ Strip Ratio │    │
│  │ 6,100 Bcm   │  │ 3,550 tons  │  │    1.72     │    │
│  │ 76.3% ↗+2.1%│  │ 71.0% ↗+1.8%│  │   Target    │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
├─────────────────────────────────────────────────────────┤
│                    Analytics                            │
│  [Production] [Impact] [Fuel] [Strip Ratio]           │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │                Chart Area                       │   │
│  │  2.50 ┌─────────────────────────────────────   │   │
│  │       │                                         │   │
│  │  1.88 │     ●─────●─────●─────●                │   │
│  │       │                                         │   │
│  │  1.25 │                                         │   │
│  │       │                                         │   │
│  │  0.63 │                                         │   │
│  │       │                                         │   │
│  │  0.00 └─────────────────────────────────────   │   │
│  │        20    21    22    23                     │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ● Actual Strip Ratio  ● Target Strip Ratio           │
├─────────────────────────────────────────────────────────┤
│ [Daily] [Weekly] [Monthly] [Yearly]                    │
└─────────────────────────────────────────────────────────┘
```

### Navigation Elements
- **Header**: App title, current date, settings access
- **Sync Status**: Online/offline indicator with sync status
- **Period Selector**: Time period toggle (Daily/Weekly/Monthly/Yearly)
- **Tab Navigation**: Analytics category selection
- **Pull-to-Refresh**: Gesture-based data refresh

### Interactive Elements
- **Metric Cards**: Tap to view detailed breakdown
- **Chart Points**: Touch to see specific values
- **Period Buttons**: Switch between time periods
- **Tab Buttons**: Change analytics view
- **Settings Icon**: Access configuration options

## 📈 Data Visualization

### Chart Types & Analytics Tabs

#### 1. Production Tab
**Purpose**: Track overburden and ore production volumes
```
Metrics Displayed:
- Overburden Production (Bcm)
- Ore Production (tons)
- Daily/Weekly/Monthly totals
- Achievement percentages

Chart Features:
- Dual-line chart (OB vs Ore)
- Color coding: Blue (OB), Green (Ore)
- Target lines for comparison
- Trend indicators
```

#### 2. Impact Tab
**Purpose**: Analyze strip ratio and operational efficiency
```
Metrics Displayed:
- Strip Ratio (OB/Ore ratio)
- Target vs Actual comparison
- Efficiency trends
- Performance indicators

Chart Features:
- Single-line chart with target line
- Color coding: Brown (Actual), Gray (Target)
- Ratio calculations
- Performance zones
```

#### 3. Fuel Tab
**Purpose**: Monitor fuel consumption and efficiency
```
Metrics Displayed:
- Actual Fuel Consumption (Liters)
- Planned Fuel Budget (Liters)
- Efficiency percentage
- Cost implications

Chart Features:
- Dual-line chart (Actual vs Plan)
- Color coding: Purple (Actual), Gray (Plan)
- Efficiency indicators
- Budget variance
```

#### 4. Strip Ratio Tab
**Purpose**: Detailed strip ratio analysis
```
Metrics Displayed:
- Daily strip ratio values
- Target strip ratio (2.5)
- Variance from target
- Trend analysis

Chart Features:
- Line chart with target reference
- Color coding: Brown (Actual), Gray (Target)
- Performance zones (Good/Warning/Critical)
- Historical comparison
```

### Chart Configuration
```typescript
const chartConfig = {
  backgroundColor: Colors.surface,
  backgroundGradientFrom: Colors.surface,
  backgroundGradientTo: Colors.surface,
  decimalPlaces: 2,
  color: (opacity = 1) => `rgba(81, 150, 244, ${opacity})`,
  labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
  style: {
    borderRadius: Layout.borderRadius.md,
  },
  propsForDots: {
    r: '4',
    strokeWidth: '2',
    stroke: Colors.primary,
  },
  propsForBackgroundLines: {
    strokeDasharray: '',
    stroke: `rgba(0, 0, 0, 0.1)`,
    strokeWidth: 1,
  },
};
```

## 📊 Metrics & KPIs

### Production Metrics Cards

#### 1. Overburden Card
```typescript
{
  id: 'overburden',
  name: 'Overburden',
  icon: 'layers-outline',
  unit: 'Bcm',
  plan: 8000,
  actual: 6100,
  achievementPercentage: 76.3,
  trendPercentage: 2.1,
  trendDirection: 'up',
  isPositiveMetric: true
}
```

#### 2. Ore Production Card
```typescript
{
  id: 'ore_production',
  name: 'Ore Production',
  icon: 'cube-outline',
  unit: 'tons',
  plan: 5000,
  actual: 3550,
  achievementPercentage: 71.0,
  trendPercentage: 1.8,
  trendDirection: 'up',
  isPositiveMetric: true
}
```

#### 3. Strip Ratio Card
```typescript
{
  id: 'strip_ratio',
  name: 'Strip Ratio',
  icon: 'analytics-outline',
  unit: 'ratio',
  plan: 2.5,
  actual: 1.72,
  achievementPercentage: 100,
  trendPercentage: 0,
  trendDirection: 'stable',
  isPositiveMetric: true
}
```

### KPI Calculations
```typescript
// Strip Ratio Calculation
const stripRatio = actualOverburden / actualOre;

// Achievement Percentage
const achievementPercentage = (actual / plan) * 100;

// Trend Calculation (compared to previous period)
const trendPercentage = ((current - previous) / previous) * 100;

// Efficiency Metrics
const fuelEfficiency = (actualFuel / planFuel) * 100;
const productionEfficiency = (totalActual / totalPlan) * 100;
```

## ⏰ Time Period Analysis

### Daily View
- **Data Range**: Last 30 days
- **Granularity**: Daily data points
- **Chart Labels**: Day numbers (1, 2, 3, ..., 30)
- **Use Case**: Operational monitoring and daily performance tracking

### Weekly View
- **Data Range**: Last 12 weeks
- **Granularity**: Weekly aggregated data
- **Chart Labels**: Week numbers (W1, W2, W3, ..., W12)
- **Use Case**: Short-term trend analysis and weekly planning

### Monthly View
- **Data Range**: Last 12 months
- **Granularity**: Monthly aggregated data
- **Chart Labels**: Month abbreviations (Jan, Feb, Mar, ...)
- **Use Case**: Medium-term performance analysis and budgeting

### Yearly View
- **Data Range**: Available years in database
- **Granularity**: Yearly aggregated data
- **Chart Labels**: Year numbers (2023, 2024, 2025, ...)
- **Use Case**: Long-term strategic analysis and planning

### Data Aggregation Logic
```typescript
// Daily aggregation (no aggregation needed)
const dailyData = rawData.filter(item => 
  item.date >= startDate && item.date <= endDate
);

// Weekly aggregation
const weeklyData = rawData.reduce((acc, item) => {
  const week = item.week;
  const existing = acc.find(w => w.week === week);
  if (existing) {
    existing.actual_ob += item.actual_ob;
    existing.actual_ore += item.actual_ore;
    existing.actual_fuel += item.actual_fuel;
  } else {
    acc.push({
      week,
      actual_ob: item.actual_ob,
      actual_ore: item.actual_ore,
      actual_fuel: item.actual_fuel,
    });
  }
  return acc;
}, []);

// Monthly aggregation
const monthlyData = rawData.reduce((acc, item) => {
  const month = item.monthly;
  // Similar aggregation logic...
}, []);
```

## 🛠️ Technical Implementation

### Component Architecture
```
ProductionOverviewScreen
├── Header Component
├── SyncStatusIndicator
├── ProductionMetricsCards
│   ├── MetricCard (Overburden)
│   ├── MetricCard (Ore Production)
│   └── MetricCard (Strip Ratio)
├── AnalyticsSection
│   ├── TabSelector
│   └── EnhancedChart
├── PeriodSelector
└── RefreshControl
```

### State Management
```typescript
interface ProductionOverviewState {
  selectedPeriod: TimePeriod;
  selectedAnalyticsTab: AnalyticsTab;
  loading: boolean;
  refreshing: boolean;
  productionMetrics: ProductionMetric[];
  dailyData: any[];
  chartData: ChartData;
  error: string | null;
}
```

### Data Flow
```
1. Component Mount → loadProductionData()
2. Check Local Database → SQLite query
3. If No Data → Generate Sample Data
4. Process Data → getCardDataByPeriod()
5. Generate Charts → getChartDataByAnalyticsTab()
6. Update State → setProductionMetrics(), setChartData()
7. Render UI → Display metrics and charts
```

### Error Handling
- **Network Errors**: Graceful fallback to cached data
- **Data Validation**: Comprehensive input validation
- **Chart Errors**: Error boundaries with retry functionality
- **Loading States**: Professional loading indicators
- **Empty States**: Informative no-data messages

### Performance Optimization
- **Data Caching**: Local SQLite storage
- **Chart Optimization**: Efficient rendering with react-native-chart-kit
- **Memory Management**: Proper cleanup and garbage collection
- **Lazy Loading**: Load data on demand
- **Debounced Updates**: Prevent excessive re-renders

---

**Related Documentation**:
- [Charts & Analytics](charts-analytics.md)
- [Offline Sync](offline-sync.md)
- [Database Design](../architecture/database-design.md)
