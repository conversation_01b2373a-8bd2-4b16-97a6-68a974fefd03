# Dashboard Implementation - Mining Operations App

## 🏠 **Dashboard Architecture**

### **Overview**
The dashboard serves as the central hub for mining operations, providing real-time insights, quick actions, and key performance indicators. It's designed with role-based content and responsive layouts.

### **Key Features**
- **Real-time metrics** with auto-refresh
- **Role-based widgets** and permissions
- **Interactive charts** and visualizations
- **Quick action buttons** for common tasks
- **Activity feed** with recent events
- **Weather integration** for operational planning
- **Notification center** with priority alerts

## 📁 **File Structure**

```
src/features/dashboard/
├── components/
│   ├── DashboardHeader.tsx
│   ├── QuickStatsCard.tsx
│   ├── ProductionChart.tsx
│   ├── EquipmentStatusChart.tsx
│   ├── ActivityFeed.tsx
│   ├── QuickActions.tsx
│   ├── WeatherWidget.tsx
│   └── NotificationBell.tsx
├── hooks/
│   ├── useDashboardData.ts
│   ├── useRealTimeUpdates.ts
│   └── useWeatherData.ts
├── screens/
│   ├── DashboardScreen.tsx
│   └── NotificationsScreen.tsx
├── services/
│   ├── DashboardService.ts
│   ├── WeatherService.ts
│   └── NotificationService.ts
└── types/
    └── dashboard.types.ts
```

## 🎯 **Domain Models**

### **src/models/Dashboard.ts**
```typescript
export interface DashboardMetrics {
  production: ProductionMetrics;
  equipment: EquipmentMetrics;
  safety: SafetyMetrics;
  maintenance: MaintenanceMetrics;
  lastUpdated: string;
}

export interface ProductionMetrics {
  todayProduction: number;
  todayTarget: number;
  productionRate: number;
  efficiency: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
}

export interface EquipmentMetrics {
  totalEquipment: number;
  activeEquipment: number;
  maintenanceEquipment: number;
  inactiveEquipment: number;
  utilizationRate: number;
  alertsCount: number;
}

export interface SafetyMetrics {
  safetyScore: number;
  daysWithoutIncident: number;
  openIncidents: number;
  pendingInspections: number;
  trainingCompliance: number;
}

export interface MaintenanceMetrics {
  scheduledToday: number;
  completedToday: number;
  overdueTasks: number;
  partsInventory: number;
  availableTechnicians: number;
}

export interface ActivityItem {
  id: string;
  type: 'incident' | 'equipment' | 'production' | 'maintenance' | 'safety';
  title: string;
  description: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  status?: string;
  location?: string;
  userId?: string;
  userName?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  icon: string;
  color: string;
  route: string;
  permission?: string;
  badge?: number;
}

export interface WeatherData {
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
  visibility: number;
  icon: string;
  location: string;
  lastUpdated: string;
}

export interface NotificationItem {
  id: string;
  title: string;
  message: string;
  type: 'alert' | 'info' | 'warning' | 'success';
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  isRead: boolean;
  actionUrl?: string;
  actionText?: string;
}

// Chart data interfaces
export interface ChartDataPoint {
  label: string;
  value: number;
  date?: string;
  target?: number;
}

export interface ProductionChartData {
  daily: ChartDataPoint[];
  weekly: ChartDataPoint[];
  monthly: ChartDataPoint[];
}

export interface EquipmentStatusData {
  active: number;
  maintenance: number;
  inactive: number;
  colors: string[];
}
```

## 🔧 **Dashboard Service**

### **src/features/dashboard/services/DashboardService.ts**
```typescript
import { supabase } from '../../../services/supabase';
import { 
  DashboardMetrics, 
  ActivityItem, 
  ProductionChartData, 
  EquipmentStatusData,
  ChartDataPoint 
} from '../../../models/Dashboard';

export class DashboardService {
  // Get dashboard metrics for current user's location
  static async getDashboardMetrics(locationId?: string): Promise<DashboardMetrics> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // Get production metrics
      const production = await this.getProductionMetrics(today, yesterday, locationId);
      
      // Get equipment metrics
      const equipment = await this.getEquipmentMetrics(locationId);
      
      // Get safety metrics
      const safety = await this.getSafetyMetrics(locationId);
      
      // Get maintenance metrics
      const maintenance = await this.getMaintenanceMetrics(today, locationId);

      return {
        production,
        equipment,
        safety,
        maintenance,
        lastUpdated: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Failed to fetch dashboard metrics: ${error.message}`);
    }
  }

  // Get production metrics
  private static async getProductionMetrics(
    today: string, 
    yesterday: string, 
    locationId?: string
  ): Promise<ProductionMetrics> {
    // Today's production
    let todayQuery = supabase
      .from('production_records')
      .select('quantity')
      .gte('recorded_at', today)
      .lt('recorded_at', new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]);

    if (locationId) {
      todayQuery = todayQuery.eq('location_id', locationId);
    }

    const { data: todayData } = await todayQuery;
    const todayProduction = todayData?.reduce((sum, record) => sum + record.quantity, 0) || 0;

    // Yesterday's production for comparison
    let yesterdayQuery = supabase
      .from('production_records')
      .select('quantity')
      .gte('recorded_at', yesterday)
      .lt('recorded_at', today);

    if (locationId) {
      yesterdayQuery = yesterdayQuery.eq('location_id', locationId);
    }

    const { data: yesterdayData } = await yesterdayQuery;
    const yesterdayProduction = yesterdayData?.reduce((sum, record) => sum + record.quantity, 0) || 0;

    // Get today's target
    let targetQuery = supabase
      .from('production_targets')
      .select('target_quantity')
      .eq('target_date', today);

    if (locationId) {
      targetQuery = targetQuery.eq('location_id', locationId);
    }

    const { data: targetData } = await targetQuery;
    const todayTarget = targetData?.reduce((sum, target) => sum + target.target_quantity, 0) || 0;

    // Calculate metrics
    const trendPercentage = yesterdayProduction > 0 
      ? ((todayProduction - yesterdayProduction) / yesterdayProduction) * 100 
      : 0;
    
    const trend = trendPercentage > 2 ? 'up' : trendPercentage < -2 ? 'down' : 'stable';
    const efficiency = todayTarget > 0 ? (todayProduction / todayTarget) * 100 : 0;
    const productionRate = todayProduction / 24; // tons per hour (assuming 24-hour operation)

    return {
      todayProduction,
      todayTarget,
      productionRate,
      efficiency,
      trend,
      trendPercentage: Math.abs(trendPercentage),
    };
  }

  // Get equipment metrics
  private static async getEquipmentMetrics(locationId?: string): Promise<EquipmentMetrics> {
    let query = supabase
      .from('equipment')
      .select('status, current_location_id');

    if (locationId) {
      query = query.eq('current_location_id', locationId);
    }

    const { data: equipmentData } = await query;
    
    const totalEquipment = equipmentData?.length || 0;
    const activeEquipment = equipmentData?.filter(eq => eq.status === 'Active').length || 0;
    const maintenanceEquipment = equipmentData?.filter(eq => eq.status === 'Maintenance').length || 0;
    const inactiveEquipment = equipmentData?.filter(eq => eq.status === 'Inactive').length || 0;
    
    const utilizationRate = totalEquipment > 0 ? (activeEquipment / totalEquipment) * 100 : 0;

    // Get alerts count
    const { data: alertsData } = await supabase
      .from('equipment_alerts')
      .select('id')
      .eq('is_active', true);
    
    const alertsCount = alertsData?.length || 0;

    return {
      totalEquipment,
      activeEquipment,
      maintenanceEquipment,
      inactiveEquipment,
      utilizationRate,
      alertsCount,
    };
  }

  // Get safety metrics
  private static async getSafetyMetrics(locationId?: string): Promise<SafetyMetrics> {
    // Get last incident date
    let incidentQuery = supabase
      .from('safety_incidents')
      .select('occurred_at')
      .order('occurred_at', { ascending: false })
      .limit(1);

    if (locationId) {
      incidentQuery = incidentQuery.eq('location_id', locationId);
    }

    const { data: lastIncident } = await incidentQuery;
    const lastIncidentDate = lastIncident?.[0]?.occurred_at;
    
    const daysWithoutIncident = lastIncidentDate 
      ? Math.floor((Date.now() - new Date(lastIncidentDate).getTime()) / (1000 * 60 * 60 * 24))
      : 365; // Default to 365 if no incidents

    // Get open incidents
    let openIncidentsQuery = supabase
      .from('safety_incidents')
      .select('id')
      .in('status', ['Reported', 'Under Investigation']);

    if (locationId) {
      openIncidentsQuery = openIncidentsQuery.eq('location_id', locationId);
    }

    const { data: openIncidentsData } = await openIncidentsQuery;
    const openIncidents = openIncidentsData?.length || 0;

    // Calculate safety score (simplified algorithm)
    const safetyScore = Math.min(100, Math.max(0, 
      100 - (openIncidents * 5) - Math.max(0, (30 - daysWithoutIncident) * 2)
    ));

    return {
      safetyScore,
      daysWithoutIncident,
      openIncidents,
      pendingInspections: 0, // TODO: Implement when inspection system is ready
      trainingCompliance: 94, // TODO: Implement when training system is ready
    };
  }

  // Get maintenance metrics
  private static async getMaintenanceMetrics(
    today: string, 
    locationId?: string
  ): Promise<MaintenanceMetrics> {
    // Get today's scheduled maintenance
    let scheduledQuery = supabase
      .from('work_orders')
      .select('id, status')
      .gte('scheduled_start', today)
      .lt('scheduled_start', new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]);

    const { data: scheduledData } = await scheduledQuery;
    const scheduledToday = scheduledData?.length || 0;
    const completedToday = scheduledData?.filter(wo => wo.status === 'Completed').length || 0;

    // Get overdue tasks
    const { data: overdueData } = await supabase
      .from('work_orders')
      .select('id')
      .lt('scheduled_end', new Date().toISOString())
      .neq('status', 'Completed');

    const overdueTasks = overdueData?.length || 0;

    return {
      scheduledToday,
      completedToday,
      overdueTasks,
      partsInventory: 87, // TODO: Implement when parts inventory is ready
      availableTechnicians: 8, // TODO: Implement when technician scheduling is ready
    };
  }

  // Get recent activities
  static async getRecentActivities(
    limit: number = 10, 
    locationId?: string
  ): Promise<ActivityItem[]> {
    const activities: ActivityItem[] = [];

    // Get recent incidents
    let incidentQuery = supabase
      .from('safety_incidents')
      .select(`
        id, title, description, occurred_at, severity, status,
        location:locations(name),
        reporter:user_profiles!reported_by(first_name, last_name)
      `)
      .order('occurred_at', { ascending: false })
      .limit(3);

    if (locationId) {
      incidentQuery = incidentQuery.eq('location_id', locationId);
    }

    const { data: incidents } = await incidentQuery;
    
    incidents?.forEach(incident => {
      activities.push({
        id: incident.id,
        type: 'incident',
        title: incident.title,
        description: incident.description,
        timestamp: incident.occurred_at,
        severity: incident.severity,
        status: incident.status,
        location: incident.location?.name,
        userName: `${incident.reporter?.first_name} ${incident.reporter?.last_name}`,
      });
    });

    // Get recent equipment alerts
    const { data: alerts } = await supabase
      .from('equipment_alerts')
      .select(`
        id, title, message, triggered_at, severity,
        equipment:equipment(name, equipment_number)
      `)
      .eq('is_active', true)
      .order('triggered_at', { ascending: false })
      .limit(3);

    alerts?.forEach(alert => {
      activities.push({
        id: alert.id,
        type: 'equipment',
        title: alert.title,
        description: `${alert.equipment?.name} (${alert.equipment?.equipment_number}): ${alert.message}`,
        timestamp: alert.triggered_at,
        severity: alert.severity,
      });
    });

    // Sort by timestamp and limit
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  // Get production chart data
  static async getProductionChartData(
    period: 'daily' | 'weekly' | 'monthly',
    locationId?: string
  ): Promise<ChartDataPoint[]> {
    const now = new Date();
    let startDate: Date;
    let groupBy: string;

    switch (period) {
      case 'daily':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // Last 7 days
        groupBy = 'DATE(recorded_at)';
        break;
      case 'weekly':
        startDate = new Date(now.getTime() - 12 * 7 * 24 * 60 * 60 * 1000); // Last 12 weeks
        groupBy = 'DATE_TRUNC(\'week\', recorded_at)';
        break;
      case 'monthly':
        startDate = new Date(now.getTime() - 12 * 30 * 24 * 60 * 60 * 1000); // Last 12 months
        groupBy = 'DATE_TRUNC(\'month\', recorded_at)';
        break;
    }

    let query = supabase
      .from('production_records')
      .select('recorded_at, quantity')
      .gte('recorded_at', startDate.toISOString());

    if (locationId) {
      query = query.eq('location_id', locationId);
    }

    const { data } = await query;

    // Group data by period
    const groupedData = new Map<string, number>();
    
    data?.forEach(record => {
      const date = new Date(record.recorded_at);
      let key: string;
      
      switch (period) {
        case 'daily':
          key = date.toISOString().split('T')[0];
          break;
        case 'weekly':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
          break;
        case 'monthly':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
      }
      
      groupedData.set(key, (groupedData.get(key) || 0) + record.quantity);
    });

    // Convert to chart data points
    return Array.from(groupedData.entries()).map(([date, value]) => ({
      label: this.formatChartLabel(date, period),
      value,
      date,
    }));
  }

  // Get equipment status data for pie chart
  static async getEquipmentStatusData(locationId?: string): Promise<EquipmentStatusData> {
    let query = supabase
      .from('equipment')
      .select('status');

    if (locationId) {
      query = query.eq('current_location_id', locationId);
    }

    const { data } = await query;

    const statusCounts = {
      active: 0,
      maintenance: 0,
      inactive: 0,
    };

    data?.forEach(equipment => {
      switch (equipment.status) {
        case 'Active':
          statusCounts.active++;
          break;
        case 'Maintenance':
        case 'Repair':
          statusCounts.maintenance++;
          break;
        default:
          statusCounts.inactive++;
      }
    });

    return {
      active: statusCounts.active,
      maintenance: statusCounts.maintenance,
      inactive: statusCounts.inactive,
      colors: ['#10B981', '#F59E0B', '#EF4444'], // Green, Orange, Red
    };
  }

  private static formatChartLabel(date: string, period: 'daily' | 'weekly' | 'monthly'): string {
    const dateObj = new Date(date);
    
    switch (period) {
      case 'daily':
        return dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case 'weekly':
        return `Week ${Math.ceil(dateObj.getDate() / 7)}`;
      case 'monthly':
        return dateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      default:
        return date;
    }
  }
}
```

This completes the dashboard service implementation. The next section will cover the dashboard UI components and screens.
