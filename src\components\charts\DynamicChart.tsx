import React, { memo, useState, useMemo, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Animated,
} from 'react-native';
import { Line<PERSON>hart, BarChart } from 'react-native-chart-kit';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { Layout } from '../../constants/layout';
import { ShadowPresets } from '../../utils/shadowHelper';
import { TimePeriod } from '../../models/Production';
import {
  ChartType,
  ChartMetricConfig,
  CHART_METRICS,
  getChartConfig,
  getChartDimensions,
} from './ChartConfig';
import { ChartDataProcessor, ProductionDataItem } from './ChartDataProcessor';
import ChartSkeleton from './ChartSkeleton';

const { width: screenWidth } = Dimensions.get('window');

interface DynamicChartProps {
  data: ProductionDataItem[];
  timePeriod: TimePeriod;
  isLoading?: boolean;
  error?: string | null;
  onRetry?: () => void;
  isDarkMode?: boolean;
  onMetricChange?: (metric: ChartMetricConfig) => void;
  onChartTypeChange?: (chartType: ChartType) => void;
}

const DynamicChart: React.FC<DynamicChartProps> = memo(({
  data,
  timePeriod,
  isLoading = false,
  error = null,
  onRetry,
  isDarkMode = false,
  onMetricChange,
  onChartTypeChange,
}) => {
  const [activeMetricIndex, setActiveMetricIndex] = useState(0);
  const [activeChartType, setActiveChartType] = useState<ChartType>('line');
  const [tabAnimation] = useState(new Animated.Value(0));
  const scrollViewRef = useRef<ScrollView>(null);

  const activeMetric = CHART_METRICS[activeMetricIndex];

  // Memoized chart data processing
  const chartData = useMemo(() => {
    if (!data || data.length === 0) {
      return ChartDataProcessor.processProductionData([], activeMetric, timePeriod);
    }
    return ChartDataProcessor.processProductionData(data, activeMetric, timePeriod);
  }, [data, activeMetric, timePeriod]);

  // Memoized chart configuration
  const chartConfig = useMemo(() => 
    getChartConfig(activeMetric, isDarkMode),
    [activeMetric, isDarkMode]
  );

  // Memoized chart dimensions
  const chartDimensions = useMemo(() => 
    getChartDimensions(chartData.labels.length, screenWidth),
    [chartData.labels.length]
  );

  // Handle metric tab change
  const handleMetricChange = useCallback((index: number) => {
    if (index === activeMetricIndex) return;

    setActiveMetricIndex(index);
    const newMetric = CHART_METRICS[index];
    setActiveChartType(newMetric.chartType);

    // Animate tab transition
    Animated.timing(tabAnimation, {
      toValue: index,
      duration: 200,
      useNativeDriver: true,
    }).start();

    onMetricChange?.(newMetric);
  }, [activeMetricIndex, tabAnimation, onMetricChange]);

  // Handle chart type change
  const handleChartTypeChange = useCallback((chartType: ChartType) => {
    setActiveChartType(chartType);
    onChartTypeChange?.(chartType);
  }, [onChartTypeChange]);

  // Handle chart data point press
  const handleDataPointPress = useCallback((data: any) => {
    console.log('Chart data point pressed:', data);
    // TODO: Show detailed tooltip or modal
  }, []);

  // Render chart based on type
  const renderChart = useCallback(() => {
    if (!chartData.datasets.length || chartData.datasets[0].data.every(val => val === 0)) {
      return (
        <View style={styles.noDataContainer}>
          <Ionicons name="bar-chart-outline" size={48} color={Colors.textSecondary} />
          <Text style={styles.noDataText}>No data available</Text>
          <Text style={styles.noDataSubtext}>
            Data will appear here once production metrics are recorded
          </Text>
        </View>
      );
    }

    const commonProps = {
      data: chartData,
      width: chartDimensions.width,
      height: chartDimensions.height,
      chartConfig,
      style: styles.chart,
      onDataPointClick: handleDataPointPress,
      withInnerLines: true,
      withOuterLines: true,
      withVerticalLines: true,
      withHorizontalLines: true,
    };

    switch (activeChartType) {
      case 'line':
        return (
          <LineChart
            {...commonProps}
            bezier
            withDots={true}
            withShadow={false}
            segments={4}
          />
        );
      
      case 'bar':
        return (
          <BarChart
            {...commonProps}
            showValuesOnTopOfBars={false}
            fromZero={true}
            yAxisLabel=""
            yAxisSuffix=""
          />
        );
      
      case 'area':
        return (
          <LineChart
            {...commonProps}
            bezier
            withDots={true}
            withShadow={true}
            segments={4}
          />
        );
      
      default:
        return <LineChart {...commonProps} />;
    }
  }, [chartData, chartDimensions, chartConfig, activeChartType, activeMetric, handleDataPointPress]);

  // Loading state
  if (isLoading) {
    return <ChartSkeleton showTabs={true} showLegend={true} />;
  }

  // Error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color={Colors.error} />
          <Text style={styles.errorText}>Failed to load chart data</Text>
          <Text style={styles.errorSubtext}>{error}</Text>
          {onRetry && (
            <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Metric Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.tabContainer}
        contentContainerStyle={styles.tabContent}
      >
        {CHART_METRICS.map((metric, index) => (
          <TouchableOpacity
            key={metric.id}
            style={[
              styles.tab,
              activeMetricIndex === index && [
                styles.activeTab,
                { borderBottomColor: metric.color }
              ],
            ]}
            onPress={() => handleMetricChange(index)}
          >
            <Ionicons
              name={metric.icon as any}
              size={16}
              color={activeMetricIndex === index ? metric.color : Colors.textSecondary}
            />
            <Text
              style={[
                styles.tabText,
                activeMetricIndex === index && [
                  styles.activeTabText,
                  { color: metric.color }
                ],
              ]}
            >
              {metric.shortName}
            </Text>
            <Text style={styles.tabUnit}>{metric.unit}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Chart Type Selector */}
      <View style={styles.chartTypeContainer}>
        {(['line', 'bar', 'area'] as ChartType[]).map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.chartTypeButton,
              activeChartType === type && styles.activeChartTypeButton,
            ]}
            onPress={() => handleChartTypeChange(type)}
          >
            <Ionicons
              name={
                type === 'line' ? 'trending-up' :
                type === 'bar' ? 'bar-chart' : 'analytics'
              }
              size={16}
              color={activeChartType === type ? Colors.primary : Colors.textSecondary}
            />
          </TouchableOpacity>
        ))}
      </View>

      {/* Chart Container */}
      <View style={styles.chartWrapper}>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={true}
          persistentScrollbar={true}
          style={styles.chartScrollView}
          contentContainerStyle={styles.chartScrollContent}
          decelerationRate="fast"
        >
          {renderChart()}
        </ScrollView>
      </View>

      {/* Legend */}
      {chartData.legend && chartData.legend.length > 0 && (
        <View style={styles.legendContainer}>
          {chartData.legend.map((legendItem, index) => (
            <View key={index} style={styles.legendItem}>
              <View
                style={[
                  styles.legendDot,
                  {
                    backgroundColor: index === 0 ? activeMetric.color : '#9CA3AF'
                  }
                ]}
              />
              <Text style={styles.legendText}>{legendItem}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.lg,
    ...ShadowPresets.card,
  },
  tabContainer: {
    marginBottom: Layout.spacing.md,
  },
  tabContent: {
    gap: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.xs,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.sm,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    gap: Layout.spacing.xs,
    minWidth: 100,
  },
  activeTab: {
    backgroundColor: `${Colors.primary}10`,
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  activeTabText: {
    fontWeight: '600',
  },
  tabUnit: {
    fontSize: 10,
    color: Colors.textSecondary,
    opacity: 0.7,
  },
  chartTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: Layout.spacing.md,
    gap: Layout.spacing.sm,
  },
  chartTypeButton: {
    padding: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.sm,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  activeChartTypeButton: {
    backgroundColor: `${Colors.primary}15`,
    borderColor: Colors.primary,
  },
  chartWrapper: {
    marginBottom: Layout.spacing.md,
  },
  chartScrollView: {
    flex: 1,
  },
  chartScrollContent: {
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.sm,
  },
  chart: {
    borderRadius: Layout.borderRadius.md,
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 220,
    width: screenWidth - 64,
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginTop: Layout.spacing.sm,
  },
  noDataSubtext: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: Layout.spacing.xs,
    opacity: 0.7,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 220,
    padding: Layout.spacing.lg,
  },
  errorText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.error,
    marginTop: Layout.spacing.sm,
    textAlign: 'center',
  },
  errorSubtext: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: Layout.spacing.xs,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.sm,
    marginTop: Layout.spacing.md,
  },
  retryButtonText: {
    color: Colors.textInverse,
    fontSize: 14,
    fontWeight: '600',
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Layout.spacing.lg,
    flexWrap: 'wrap',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Layout.spacing.xs,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
});

export default DynamicChart;
