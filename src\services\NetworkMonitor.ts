import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import PlatformService from './PlatformService';

export interface NetworkStatus {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
  isWifiEnabled: boolean;
  strength: number | null;
}

// Simple event system for React Native
interface EventListeners {
  [eventName: string]: Array<(...args: any[]) => void>;
}

export class NetworkMonitor {
  private static instance: NetworkMonitor;
  private currentStatus: NetworkStatus;
  private unsubscribe: (() => void) | null = null;
  private listeners: EventListeners = {};

  private constructor() {
    this.currentStatus = {
      isConnected: false,
      isInternetReachable: false,
      type: 'unknown',
      isWifiEnabled: false,
      strength: null,
    };
  }

  static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor();
    }
    return NetworkMonitor.instance;
  }

  async initialize(): Promise<void> {
    try {
      if (PlatformService.isWeb()) {
        // Web platform initialization
        this.initializeWebNetworkMonitoring();
      } else {
        // Native platform initialization
        const state = await NetInfo.fetch();
        this.updateNetworkStatus(state);

        // Subscribe to network state changes
        this.unsubscribe = NetInfo.addEventListener((state) => {
          this.updateNetworkStatus(state);
        });
      }

      console.log('🌐 Network monitor initialized');
    } catch (error) {
      console.error('❌ Failed to initialize network monitor:', error);
      // Fallback to assume online
      this.currentStatus = {
        isConnected: true,
        isInternetReachable: true,
        type: 'unknown',
        isWifiEnabled: false,
        strength: null,
      };
    }
  }

  private initializeWebNetworkMonitoring(): void {
    // Check if web APIs are available
    if (typeof navigator === 'undefined' || typeof window === 'undefined') {
      console.warn('Web APIs not available, falling back to default status');
      this.currentStatus = {
        isConnected: true,
        isInternetReachable: true,
        type: 'wifi',
        isWifiEnabled: true,
        strength: null,
      };
      return;
    }

    // Set initial status for web
    this.currentStatus = {
      isConnected: navigator.onLine,
      isInternetReachable: navigator.onLine,
      type: 'wifi', // Assume wifi for web
      isWifiEnabled: true,
      strength: null,
    };

    // Listen for online/offline events
    const handleOnline = () => {
      const previousStatus = { ...this.currentStatus };
      this.currentStatus.isConnected = true;
      this.currentStatus.isInternetReachable = true;

      console.log('🌐 Network status changed: ONLINE');
      this.emit('networkChange', this.currentStatus);
      this.emit('online', this.currentStatus);
    };

    const handleOffline = () => {
      const previousStatus = { ...this.currentStatus };
      this.currentStatus.isConnected = false;
      this.currentStatus.isInternetReachable = false;

      console.log('🌐 Network status changed: OFFLINE');
      this.emit('networkChange', this.currentStatus);
      this.emit('offline', this.currentStatus);
    };

    // Only add event listeners if window is available
    if (typeof window !== 'undefined') {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      // Store cleanup function
      this.unsubscribe = () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    } else {
      // No cleanup needed if no listeners were added
      this.unsubscribe = () => {};
    }
  }

  private updateNetworkStatus(state: NetInfoState): void {
    const previousStatus = { ...this.currentStatus };
    
    this.currentStatus = {
      isConnected: state.isConnected ?? false,
      isInternetReachable: state.isInternetReachable ?? false,
      type: state.type,
      isWifiEnabled: state.type === 'wifi',
      strength: this.getSignalStrength(state),
    };

    // Log network changes
    if (previousStatus.isConnected !== this.currentStatus.isConnected) {
      console.log(
        `🌐 Network status changed: ${this.currentStatus.isConnected ? 'ONLINE' : 'OFFLINE'}`
      );
    }

    // Emit events for status changes
    this.emit('networkChange', this.currentStatus);

    if (this.currentStatus.isConnected && !previousStatus.isConnected) {
      this.emit('online', this.currentStatus);
    } else if (!this.currentStatus.isConnected && previousStatus.isConnected) {
      this.emit('offline', this.currentStatus);
    }
  }

  private getSignalStrength(state: NetInfoState): number | null {
    if (state.type === 'wifi' && state.details) {
      // @ts-ignore - WiFi signal strength
      return state.details.strength || null;
    } else if (state.type === 'cellular' && state.details) {
      // @ts-ignore - Cellular signal strength
      return state.details.cellularGeneration || null;
    }
    return null;
  }

  getCurrentStatus(): NetworkStatus {
    return { ...this.currentStatus };
  }

  isOnline(): boolean {
    return this.currentStatus.isConnected && this.currentStatus.isInternetReachable;
  }

  isOffline(): boolean {
    return !this.isOnline();
  }

  getConnectionType(): string {
    return this.currentStatus.type;
  }

  isWiFiConnection(): boolean {
    return this.currentStatus.isWifiEnabled;
  }

  isCellularConnection(): boolean {
    return this.currentStatus.type === 'cellular';
  }

  async checkConnectivity(): Promise<boolean> {
    try {
      const state = await NetInfo.fetch();
      return state.isConnected && state.isInternetReachable;
    } catch (error) {
      console.error('❌ Failed to check connectivity:', error);
      return false;
    }
  }

  async waitForConnection(timeout: number = 30000): Promise<boolean> {
    return new Promise((resolve) => {
      if (this.isOnline()) {
        resolve(true);
        return;
      }

      const timeoutId = setTimeout(() => {
        this.removeListener('online', onlineHandler);
        resolve(false);
      }, timeout);

      const onlineHandler = () => {
        clearTimeout(timeoutId);
        this.removeListener('online', onlineHandler);
        resolve(true);
      };

      this.once('online', onlineHandler);
    });
  }

  destroy(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.removeAllListeners();
    console.log('🌐 Network monitor destroyed');
  }

  // Custom event system implementation
  on(eventName: string, listener: (...args: any[]) => void): void {
    if (!this.listeners[eventName]) {
      this.listeners[eventName] = [];
    }
    this.listeners[eventName].push(listener);
  }

  once(eventName: string, listener: (...args: any[]) => void): void {
    const onceWrapper = (...args: any[]) => {
      listener(...args);
      this.removeListener(eventName, onceWrapper);
    };
    this.on(eventName, onceWrapper);
  }

  emit(eventName: string, ...args: any[]): void {
    const eventListeners = this.listeners[eventName];
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${eventName}:`, error);
        }
      });
    }
  }

  removeListener(eventName: string, listener: (...args: any[]) => void): void {
    const eventListeners = this.listeners[eventName];
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  removeAllListeners(eventName?: string): void {
    if (eventName) {
      delete this.listeners[eventName];
    } else {
      this.listeners = {};
    }
  }
}

export default NetworkMonitor;
