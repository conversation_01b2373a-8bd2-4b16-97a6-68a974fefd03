# 🚀 COMPLETE DEVELOPMENT SETUP GUIDE

> **📍 Master Guide for Cross-Computer Development**
> **🎯 Updated**: January 2025 - Includes latest fixes and mining formula standardization
> **⏱️ Setup Time**: 15-30 minutes
> **✅ Status**: Production Ready (95% complete)

## 📋 Table of Contents
- [🏃‍♂️ Quick Start (5 Minutes)](#quick-start-5-minutes)
- [📋 Prerequisites](#prerequisites)
- [🔧 Installation Steps](#installation-steps)
- [🌍 Environment Configuration](#environment-configuration)
- [🗄️ Database Setup](#database-setup)
- [🛠️ Development Tools](#development-tools)
- [🧪 Testing Setup](#testing-setup)
- [📊 Current Application Status](#current-application-status)
- [🔍 Troubleshooting](#troubleshooting)
- [🎯 Next Steps](#next-steps)

---

## 🏃‍♂️ QUICK START (5 MINUTES)

### **⚡ Super Quick Setup**
```bash
# 1. Clone & Install
git clone <repository-url>
cd MiningOperationsApp
npm install

# 2. Environment Setup
cp .env.example .env
# Edit .env with Supabase credentials

# 3. Start Development
npx expo install --fix
npm start
```

**✅ Done! App should be running on http://localhost:8081**

## 📋 Prerequisites

### System Requirements
```yaml
Operating System:
  - macOS 10.15+ (for iOS development)
  - Windows 10+ or Linux Ubuntu 18.04+
  - 8GB RAM minimum, 16GB recommended
  - 50GB free disk space

Software Requirements:
  - Node.js 18.0+ (LTS recommended)
  - npm 8.0+ or Yarn 1.22+
  - Git 2.30+
  - VS Code or preferred IDE
```

### Mobile Development Tools
```yaml
Android Development:
  - Android Studio 2022.1+
  - Android SDK API Level 31+
  - Android Emulator or physical device
  - Java JDK 11+

iOS Development (macOS only):
  - Xcode 14.0+
  - iOS Simulator or physical device
  - CocoaPods 1.11+
  - Command Line Tools for Xcode
```

## 🚀 Installation Steps

### 1. Clone Repository
```bash
# Clone the repository
git clone https://github.com/your-org/mining-operations-app.git
cd mining-operations-app

# Verify repository structure
ls -la
```

### 2. Install Node.js Dependencies
```bash
# Install dependencies
npm install

# Or using Yarn
yarn install

# Verify installation
npm list --depth=0
```

### 3. React Native Environment Setup

#### For Android
```bash
# Install React Native CLI globally
npm install -g @react-native-community/cli

# Verify Android SDK installation
npx react-native doctor

# Check Android environment
echo $ANDROID_HOME
echo $ANDROID_SDK_ROOT
```

#### For iOS (macOS only)
```bash
# Install CocoaPods
sudo gem install cocoapods

# Install iOS dependencies
cd ios
pod install
cd ..

# Verify iOS setup
npx react-native doctor
```

### 4. Development Tools Installation
```bash
# Install global development tools
npm install -g typescript
npm install -g @typescript-eslint/parser
npm install -g prettier
npm install -g tsx

# Install debugging tools
npm install -g flipper
npm install -g reactotron-cli
```

## ⚙️ Environment Configuration

### 1. Environment Variables
```bash
# Copy environment template
cp .env.example .env

# Edit environment file
nano .env
```

### 2. Environment File Configuration
```bash
# .env file content
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# API Configuration
API_BASE_URL=https://api.miningapp.com
API_TIMEOUT=30000

# App Configuration
APP_NAME=Mining Operations
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1

# Development Configuration
DEBUG_MODE=true
LOG_LEVEL=debug
ENABLE_FLIPPER=true

# Feature Flags
ENABLE_OFFLINE_MODE=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=false
```

### 3. TypeScript Configuration
```json
// tsconfig.json
{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "resolveJsonModule": true,
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@components/*": ["components/*"],
      "@screens/*": ["screens/*"],
      "@services/*": ["services/*"],
      "@utils/*": ["utils/*"],
      "@constants/*": ["constants/*"],
      "@types/*": ["types/*"]
    }
  },
  "include": [
    "src/**/*",
    "index.js",
    "__tests__/**/*"
  ],
  "exclude": [
    "node_modules",
    "android",
    "ios"
  ]
}
```

---

## 📊 CURRENT APPLICATION STATUS

### **✅ COMPLETED FEATURES (95% Production Ready)**

#### **🏠 Core Application**
- ✅ **Modern Dashboard Screen** - Layered design dengan background images
- ✅ **Production Overview** - Real-time metrics dan analytics
- ✅ **User Authentication** - Login/logout dengan role-based access
- ✅ **Navigation System** - Tab navigation dengan proper routing

#### **📊 Data & Analytics**
- ✅ **Production Metrics** - Strip Ratio, Fuel Ratio calculations (FIXED Jan 2025)
- ✅ **Interactive Charts** - Line, Bar, Area charts dengan error handling
- ✅ **Real-time Data** - Live production monitoring
- ✅ **Achievement Tracking** - Plan vs Actual dengan trend indicators

#### **🔧 Backend & Database**
- ✅ **Supabase Integration** - PostgreSQL dengan PostGIS
- ✅ **22 Database Tables** - Complete mining operations schema
- ✅ **460+ Production Records** - Historical data dari 2023-2025
- ✅ **Row Level Security** - Proper access control

#### **🛠️ Development Tools**
- ✅ **Testing Framework** - Jest dengan React Native Testing Library (8+ tests)
- ✅ **Error Monitoring** - Comprehensive error tracking
- ✅ **Performance Monitoring** - Screen load, API response tracking
- ✅ **Security Services** - Session management, audit logging

#### **📱 Mobile Features**
- ✅ **Camera Integration** - Photo capture dengan GPS
- ✅ **Location Services** - GPS tracking untuk equipment
- ✅ **Offline Support** - AsyncStorage dengan sync capabilities
- ✅ **Push Notifications** - Real-time alerts

### **🔧 RECENT MAJOR FIXES (January 2025)**
- ✅ **Mining Formulas Standardization** - Fixed 60% error in Fuel Ratio calculations
- ✅ **Database Schema Corrections** - Updated all calculation formulas
- ✅ **Service Dependencies** - Fixed all missing Expo modules
- ✅ **Testing Setup** - Complete Jest configuration working
- ✅ **Performance Optimization** - Caching dan optimization services

### **🧮 CRITICAL: Mining Formulas (STANDARDIZED)**
```typescript
// Correct formulas now used across all services
Strip Ratio (SR) = Overburden (Bcm) / Ore (tons)
Fuel Ratio (FR) = Fuel (L) / (OB (Bcm) + (Ore (tons) / 3.39))
Total Material = OB (Bcm) + (Ore (tons) / 3.39)
```

---

## 🗄️ Database Setup

### 1. Supabase Project Setup
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Initialize project
supabase init

# Link to existing project
supabase link --project-ref your-project-ref
```

### 2. Database Schema Setup
```bash
# Run database migrations
supabase db push

# Generate TypeScript types
supabase gen types typescript --local > src/types/supabase.ts

# Seed database with sample data
npm run db:seed
```

### 3. Local Database Setup
```bash
# Start local Supabase instance (optional)
supabase start

# Check status
supabase status

# Access local dashboard
# http://localhost:54323
```

### 4. Database Scripts
```bash
# Populate dummy data
npm run db:populate

# Check database connection
npm run db:check

# Reset database (development only)
npm run db:reset
```

## 🛠️ Development Tools

### 1. VS Code Extensions
```json
// .vscode/extensions.json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-react-native"
  ]
}
```

### 2. VS Code Settings
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "files.associations": {
    "*.tsx": "typescriptreact"
  }
}
```

### 3. Debugging Configuration
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Android",
      "type": "reactnative",
      "request": "launch",
      "platform": "android"
    },
    {
      "name": "Debug iOS",
      "type": "reactnative",
      "request": "launch",
      "platform": "ios"
    },
    {
      "name": "Attach to packager",
      "type": "reactnative",
      "request": "attach"
    }
  ]
}
```

### 4. Git Configuration
```bash
# Configure Git hooks
npx husky install

# Add pre-commit hooks
npx husky add .husky/pre-commit "npm run lint"
npx husky add .husky/pre-commit "npm run type-check"
npx husky add .husky/pre-push "npm test"
```

## 🚀 Running the Application

### Development Commands
```bash
# Start Metro bundler
npm start

# Run on Android
npm run android

# Run on iOS (macOS only)
npm run ios

# Run on specific device
npm run android -- --deviceId=device_id
npm run ios -- --simulator="iPhone 14 Pro"

# Clean and rebuild
npm run clean
npm run android -- --reset-cache
```

### Build Commands
```bash
# Build for development
npm run build:dev

# Build for staging
npm run build:staging

# Build for production
npm run build:prod

# Generate APK (Android)
npm run build:android:apk

# Generate AAB (Android)
npm run build:android:aab
```

### Testing Commands
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Run specific test file
npm test -- ProductionOverviewScreen.test.tsx
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Metro Bundler Issues
```bash
# Clear Metro cache
npx react-native start --reset-cache

# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules
npm install
```

#### 2. Android Build Issues
```bash
# Clean Android build
cd android
./gradlew clean
cd ..

# Fix Android SDK issues
echo 'export ANDROID_HOME=$HOME/Library/Android/sdk' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/emulator' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/tools/bin' >> ~/.zshrc
echo 'export PATH=$PATH:$ANDROID_HOME/platform-tools' >> ~/.zshrc
source ~/.zshrc
```

#### 3. iOS Build Issues (macOS)
```bash
# Clean iOS build
cd ios
rm -rf build
xcodebuild clean
cd ..

# Reinstall pods
cd ios
rm -rf Pods
rm Podfile.lock
pod install
cd ..
```

#### 4. TypeScript Issues
```bash
# Regenerate TypeScript types
npm run type-gen

# Check TypeScript configuration
npx tsc --noEmit

# Fix import issues
npm run lint -- --fix
```

#### 5. Database Connection Issues
```bash
# Check environment variables
echo $SUPABASE_URL
echo $SUPABASE_ANON_KEY

# Test database connection
npm run db:check

# Regenerate database types
supabase gen types typescript > src/types/supabase.ts
```

### Performance Optimization
```bash
# Enable Hermes (Android)
# Edit android/app/build.gradle
# enableHermes: true

# Enable Flipper for debugging
# Already configured in debug builds

# Optimize bundle size
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res
```

### Development Workflow
```bash
# Daily development routine
git pull origin main
npm install
npm start
npm run android  # or ios

# Before committing
npm run lint
npm run type-check
npm test
git add .
git commit -m "feat: add new feature"
git push origin feature-branch
```

## 🖼️ Dashboard Header Management Setup

### Database Setup
1. **Run Database Scripts:**
   ```sql
   -- In Supabase SQL Editor, run in order:
   \i database/setup_complete_database.sql
   \i database/setup_header_storage_bucket.sql
   ```

2. **Create Admin Users:**
   - Create users via Supabase Auth UI
   - Update profiles with admin roles:
   ```sql
   UPDATE users
   SET departemen = 'Administration', jabatan = 'System Administrator'
   WHERE email = '<EMAIL>';
   ```

3. **Verify Setup:**
   ```sql
   SELECT * FROM get_admin_users();
   SELECT * FROM get_dashboard_header_images();
   ```

### Storage Configuration
- **Header Bucket**: Dedicated `header` bucket created automatically
- **Folder Structure**: `header/images/dashboard_header_*.jpg`
- **Policies**: Public read, authenticated upload, admin delete
- **File Limits**: 10MB max, image types only

### Feature Access
- **Dashboard Quick Actions**: "Headers" button (admin only)
- **Profile Admin Settings**: "Dashboard Headers" menu
- **Smart Navigation**: Returns to source screen

### Testing Checklist
- [ ] Admin can access header management
- [ ] Non-admin users see access denied
- [ ] Image upload works from gallery
- [ ] Eye toggle shows/hides images
- [ ] Delete removes from storage
- [ ] Dashboard displays images correctly
- [ ] Creator tracking works
- [ ] Navigation flows properly

---

**Next Steps**:
- [Coding Standards](coding-standards.md)
- [Testing Guide](testing.md)
- [Debugging Tips](debugging.md)
