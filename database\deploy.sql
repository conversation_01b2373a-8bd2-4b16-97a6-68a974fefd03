-- Mining Operations App - Complete Database Deployment Script
-- Execute this script in Supabase SQL Editor to set up the complete database

-- =============================================
-- STEP 1: EXTENSIONS AND TYPES
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create custom types/enums
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('supervisor', 'operator', 'safety_officer', 'maintenance_tech', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE location_type AS ENUM ('mine_site', 'processing_plant', 'office', 'warehouse');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE equipment_type AS ENUM ('excavator', 'dump_truck', 'drill', 'conveyor', 'crusher', 'loader', 'bulldozer', 'grader');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE equipment_status AS ENUM ('operational', 'maintenance', 'down', 'retired');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE incident_severity AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE incident_type AS ENUM ('injury', 'near_miss', 'equipment_failure', 'environmental', 'security');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE incident_status AS ENUM ('reported', 'investigating', 'resolved', 'closed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE maintenance_type AS ENUM ('preventive', 'corrective', 'emergency', 'inspection');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE maintenance_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE shift_type AS ENUM ('day', 'night', 'swing');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE shift_status AS ENUM ('scheduled', 'checked_in', 'checked_out', 'absent');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =============================================
-- STEP 2: CREATE TABLES
-- =============================================

-- Locations table
CREATE TABLE IF NOT EXISTS locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    location_type location_type NOT NULL,
    coordinates POINT,
    address TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role user_role NOT NULL DEFAULT 'operator',
    location_id UUID REFERENCES locations(id),
    avatar_url TEXT,
    employee_id VARCHAR(50) UNIQUE,
    hire_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Equipment table
CREATE TABLE IF NOT EXISTS equipment (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    model VARCHAR(255),
    serial_number VARCHAR(255) UNIQUE,
    manufacturer VARCHAR(255),
    equipment_type equipment_type NOT NULL,
    location_id UUID REFERENCES locations(id),
    status equipment_status DEFAULT 'operational',
    purchase_date DATE,
    last_maintenance_date DATE,
    next_maintenance_due DATE,
    specifications JSONB,
    operating_hours INTEGER DEFAULT 0,
    fuel_capacity DECIMAL(10,2),
    max_load_capacity DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Safety incidents table
CREATE TABLE IF NOT EXISTS safety_incidents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    incident_number VARCHAR(50) UNIQUE NOT NULL,
    reported_by UUID REFERENCES users(id) NOT NULL,
    incident_date TIMESTAMP WITH TIME ZONE NOT NULL,
    location_id UUID REFERENCES locations(id) NOT NULL,
    severity incident_severity NOT NULL,
    incident_type incident_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    corrective_actions TEXT,
    equipment_id UUID REFERENCES equipment(id),
    injured_person_name VARCHAR(255),
    witnesses TEXT[],
    status incident_status DEFAULT 'reported',
    investigation_notes TEXT,
    resolved_date TIMESTAMP WITH TIME ZONE,
    attachments TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Production reports table
CREATE TABLE IF NOT EXISTS production_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    report_number VARCHAR(50) UNIQUE NOT NULL,
    created_by UUID REFERENCES users(id) NOT NULL,
    report_date DATE NOT NULL,
    shift shift_type NOT NULL,
    location_id UUID REFERENCES locations(id) NOT NULL,
    production_metrics JSONB NOT NULL,
    equipment_used UUID[],
    total_tonnage DECIMAL(12,2),
    operating_hours DECIMAL(8,2),
    downtime_hours DECIMAL(8,2),
    fuel_consumed DECIMAL(10,2),
    notes TEXT,
    weather_conditions VARCHAR(255),
    crew_size INTEGER,
    approved_by UUID REFERENCES users(id),
    approved_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Maintenance records table
CREATE TABLE IF NOT EXISTS maintenance_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    maintenance_number VARCHAR(50) UNIQUE NOT NULL,
    equipment_id UUID REFERENCES equipment(id) NOT NULL,
    performed_by UUID REFERENCES users(id) NOT NULL,
    maintenance_type maintenance_type NOT NULL,
    scheduled_date DATE NOT NULL,
    completed_date DATE,
    description TEXT NOT NULL,
    parts_used JSONB,
    labor_hours DECIMAL(8,2),
    total_cost DECIMAL(10,2),
    next_maintenance_due DATE,
    status maintenance_status DEFAULT 'scheduled',
    notes TEXT,
    attachments TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shifts table
CREATE TABLE IF NOT EXISTS shifts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    location_id UUID REFERENCES locations(id) NOT NULL,
    shift_type shift_type NOT NULL,
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    supervisor_id UUID REFERENCES users(id) NOT NULL,
    planned_crew_size INTEGER DEFAULT 0,
    actual_crew_size INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(location_id, shift_date, shift_type)
);

-- User shifts (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_shifts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) NOT NULL,
    shift_id UUID REFERENCES shifts(id) NOT NULL,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    status shift_status DEFAULT 'scheduled',
    overtime_hours DECIMAL(4,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, shift_id)
);

-- =============================================
-- STEP 3: CREATE INDEXES
-- =============================================

-- Create indexes for performance (only if they don't exist)
DO $$ BEGIN
    CREATE INDEX IF NOT EXISTS idx_users_location_id ON users(location_id);
    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
    CREATE INDEX IF NOT EXISTS idx_users_employee_id ON users(employee_id);
    CREATE INDEX IF NOT EXISTS idx_equipment_location_id ON equipment(location_id);
    CREATE INDEX IF NOT EXISTS idx_equipment_type ON equipment(equipment_type);
    CREATE INDEX IF NOT EXISTS idx_equipment_status ON equipment(status);
    CREATE INDEX IF NOT EXISTS idx_safety_incidents_location_id ON safety_incidents(location_id);
    CREATE INDEX IF NOT EXISTS idx_safety_incidents_incident_date ON safety_incidents(incident_date);
    CREATE INDEX IF NOT EXISTS idx_safety_incidents_severity ON safety_incidents(severity);
    CREATE INDEX IF NOT EXISTS idx_production_reports_location_id ON production_reports(location_id);
    CREATE INDEX IF NOT EXISTS idx_production_reports_report_date ON production_reports(report_date);
    CREATE INDEX IF NOT EXISTS idx_maintenance_records_equipment_id ON maintenance_records(equipment_id);
    CREATE INDEX IF NOT EXISTS idx_shifts_location_id ON shifts(location_id);
    CREATE INDEX IF NOT EXISTS idx_shifts_shift_date ON shifts(shift_date);
END $$;

-- =============================================
-- STEP 4: CREATE FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_locations_updated_at ON locations;
CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_equipment_updated_at ON equipment;
CREATE TRIGGER update_equipment_updated_at BEFORE UPDATE ON equipment FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_safety_incidents_updated_at ON safety_incidents;
CREATE TRIGGER update_safety_incidents_updated_at BEFORE UPDATE ON safety_incidents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_production_reports_updated_at ON production_reports;
CREATE TRIGGER update_production_reports_updated_at BEFORE UPDATE ON production_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_maintenance_records_updated_at ON maintenance_records;
CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON maintenance_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_shifts_updated_at ON shifts;
CREATE TRIGGER update_shifts_updated_at BEFORE UPDATE ON shifts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_shifts_updated_at ON user_shifts;
CREATE TRIGGER update_user_shifts_updated_at BEFORE UPDATE ON user_shifts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create sequences for auto-numbering
CREATE SEQUENCE IF NOT EXISTS incident_number_seq START 1;
CREATE SEQUENCE IF NOT EXISTS maintenance_number_seq START 1;
CREATE SEQUENCE IF NOT EXISTS report_number_seq START 1;

-- Function to generate incident numbers
CREATE OR REPLACE FUNCTION generate_incident_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.incident_number = 'INC-' || TO_CHAR(NEW.incident_date, 'YYYY') || '-' || LPAD(nextval('incident_number_seq')::text, 6, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate maintenance numbers
CREATE OR REPLACE FUNCTION generate_maintenance_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.maintenance_number = 'MAINT-' || TO_CHAR(NEW.scheduled_date, 'YYYY') || '-' || LPAD(nextval('maintenance_number_seq')::text, 6, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate report numbers
CREATE OR REPLACE FUNCTION generate_report_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.report_number = 'RPT-' || TO_CHAR(NEW.report_date, 'YYYY-MM-DD') || '-' || UPPER(LEFT(NEW.shift::text, 1)) || '-' || LPAD(nextval('report_number_seq')::text, 4, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply auto-numbering triggers
DROP TRIGGER IF EXISTS generate_incident_number_trigger ON safety_incidents;
CREATE TRIGGER generate_incident_number_trigger BEFORE INSERT ON safety_incidents FOR EACH ROW EXECUTE FUNCTION generate_incident_number();

DROP TRIGGER IF EXISTS generate_maintenance_number_trigger ON maintenance_records;
CREATE TRIGGER generate_maintenance_number_trigger BEFORE INSERT ON maintenance_records FOR EACH ROW EXECUTE FUNCTION generate_maintenance_number();

DROP TRIGGER IF EXISTS generate_report_number_trigger ON production_reports;
CREATE TRIGGER generate_report_number_trigger BEFORE INSERT ON production_reports FOR EACH ROW EXECUTE FUNCTION generate_report_number();

-- =============================================
-- STEP 5: ENABLE RLS AND CREATE POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE production_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE shifts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_shifts ENABLE ROW LEVEL SECURITY;

-- Helper functions for RLS
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS user_role AS $$
BEGIN
    RETURN (SELECT role FROM users WHERE id = auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_user_location()
RETURNS UUID AS $$
BEGIN
    RETURN (SELECT location_id FROM users WHERE id = auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own profile" ON users;
DROP POLICY IF EXISTS "Users can update their own profile" ON users;
DROP POLICY IF EXISTS "Supervisors and admins can view all users in their location" ON users;
DROP POLICY IF EXISTS "Admins can manage all users" ON users;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Supervisors and admins can view all users in their location" ON users FOR SELECT USING (
    get_user_role() IN ('supervisor', 'admin') AND location_id = get_user_location()
);
CREATE POLICY "Admins can manage all users" ON users FOR ALL USING (get_user_role() = 'admin');

-- Drop existing location policies
DROP POLICY IF EXISTS "Users can view their assigned location" ON locations;
DROP POLICY IF EXISTS "Admins can manage locations" ON locations;

-- Locations table policies
CREATE POLICY "Users can view their assigned location" ON locations FOR SELECT USING (
    id = get_user_location() OR get_user_role() IN ('admin', 'supervisor')
);
CREATE POLICY "Admins can manage locations" ON locations FOR ALL USING (get_user_role() = 'admin');

-- Drop existing equipment policies
DROP POLICY IF EXISTS "Users can view equipment at their location" ON equipment;
DROP POLICY IF EXISTS "Maintenance techs can update equipment" ON equipment;
DROP POLICY IF EXISTS "Supervisors and admins can manage equipment" ON equipment;

-- Equipment table policies
CREATE POLICY "Users can view equipment at their location" ON equipment FOR SELECT USING (
    location_id = get_user_location() OR get_user_role() IN ('admin', 'maintenance_tech')
);
CREATE POLICY "Maintenance techs can update equipment" ON equipment FOR UPDATE USING (
    get_user_role() IN ('maintenance_tech', 'admin')
);
CREATE POLICY "Supervisors and admins can manage equipment" ON equipment FOR ALL USING (
    get_user_role() IN ('supervisor', 'admin')
);

-- Drop existing safety incident policies
DROP POLICY IF EXISTS "Users can view incidents at their location" ON safety_incidents;
DROP POLICY IF EXISTS "Users can create incidents" ON safety_incidents;
DROP POLICY IF EXISTS "Safety officers and admins can manage incidents" ON safety_incidents;

-- Safety incidents policies
CREATE POLICY "Users can view incidents at their location" ON safety_incidents FOR SELECT USING (
    location_id = get_user_location() OR get_user_role() IN ('admin', 'safety_officer')
);
CREATE POLICY "Users can create incidents" ON safety_incidents FOR INSERT WITH CHECK (
    location_id = get_user_location()
);
CREATE POLICY "Safety officers and admins can manage incidents" ON safety_incidents FOR ALL USING (
    get_user_role() IN ('safety_officer', 'admin')
);

-- Drop existing production report policies
DROP POLICY IF EXISTS "Users can view reports from their location" ON production_reports;
DROP POLICY IF EXISTS "Users can create reports for their location" ON production_reports;
DROP POLICY IF EXISTS "Supervisors can manage reports" ON production_reports;

-- Production reports policies
CREATE POLICY "Users can view reports from their location" ON production_reports FOR SELECT USING (
    location_id = get_user_location() OR get_user_role() IN ('admin', 'supervisor')
);
CREATE POLICY "Users can create reports for their location" ON production_reports FOR INSERT WITH CHECK (
    location_id = get_user_location()
);
CREATE POLICY "Supervisors can manage reports" ON production_reports FOR ALL USING (
    get_user_role() IN ('supervisor', 'admin')
);

-- Drop existing maintenance record policies
DROP POLICY IF EXISTS "Users can view maintenance records for equipment at their location" ON maintenance_records;
DROP POLICY IF EXISTS "Maintenance techs can manage maintenance records" ON maintenance_records;

-- Maintenance records policies
CREATE POLICY "Users can view maintenance records for equipment at their location" ON maintenance_records FOR SELECT USING (
    equipment_id IN (SELECT id FROM equipment WHERE location_id = get_user_location()) OR get_user_role() IN ('admin', 'maintenance_tech')
);
CREATE POLICY "Maintenance techs can manage maintenance records" ON maintenance_records FOR ALL USING (
    get_user_role() IN ('maintenance_tech', 'admin')
);

-- Drop existing shift policies
DROP POLICY IF EXISTS "Users can view shifts at their location" ON shifts;
DROP POLICY IF EXISTS "Supervisors can manage shifts" ON shifts;

-- Shifts policies
CREATE POLICY "Users can view shifts at their location" ON shifts FOR SELECT USING (
    location_id = get_user_location() OR get_user_role() IN ('admin', 'supervisor')
);
CREATE POLICY "Supervisors can manage shifts" ON shifts FOR ALL USING (
    get_user_role() IN ('supervisor', 'admin')
);

-- Drop existing user shift policies
DROP POLICY IF EXISTS "Users can view their own shifts" ON user_shifts;
DROP POLICY IF EXISTS "Users can update their own shift status" ON user_shifts;
DROP POLICY IF EXISTS "Supervisors can manage user shifts" ON user_shifts;

-- User shifts policies
CREATE POLICY "Users can view their own shifts" ON user_shifts FOR SELECT USING (
    user_id = auth.uid() OR get_user_role() IN ('admin', 'supervisor')
);
CREATE POLICY "Users can update their own shift status" ON user_shifts FOR UPDATE USING (
    user_id = auth.uid()
);
CREATE POLICY "Supervisors can manage user shifts" ON user_shifts FOR ALL USING (
    get_user_role() IN ('supervisor', 'admin')
);

-- =============================================
-- STEP 6: INSERT SAMPLE DATA
-- =============================================

-- Insert sample locations
INSERT INTO locations (id, name, description, location_type, address) VALUES
('550e8400-e29b-41d4-a716-************', 'North Mine Site', 'Primary excavation site in the northern sector', 'mine_site', '1234 Mining Road, North Sector'),
('550e8400-e29b-41d4-a716-************', 'Processing Plant Alpha', 'Main ore processing facility', 'processing_plant', '5678 Industrial Ave, Central'),
('550e8400-e29b-41d4-a716-************', 'South Mine Site', 'Secondary excavation site in the southern sector', 'mine_site', '9012 Mining Road, South Sector'),
('550e8400-e29b-41d4-a716-************', 'Central Office', 'Administrative headquarters', 'office', '3456 Corporate Blvd, Downtown'),
('550e8400-e29b-41d4-a716-446655440005', 'Equipment Warehouse', 'Parts and equipment storage facility', 'warehouse', '7890 Storage St, Industrial Zone')
ON CONFLICT (id) DO NOTHING;

-- Insert sample equipment
INSERT INTO equipment (id, name, model, serial_number, manufacturer, equipment_type, location_id, status, purchase_date, specifications, operating_hours, fuel_capacity, max_load_capacity) VALUES
('660e8400-e29b-41d4-a716-************', 'Excavator Alpha-1', 'CAT 390F', 'CAT390F-001', 'Caterpillar', 'excavator', '550e8400-e29b-41d4-a716-************', 'operational', '2022-06-15', '{"engine_power": "402 HP", "bucket_capacity": "2.3 m³", "operating_weight": "90 tons"}', 2450, 680.0, 45.0),
('660e8400-e29b-41d4-a716-************', 'Dump Truck Beta-1', 'Volvo A60H', 'VOLVO-A60H-001', 'Volvo', 'dump_truck', '550e8400-e29b-41d4-a716-************', 'operational', '2022-08-20', '{"engine_power": "469 HP", "payload": "55 tons", "fuel_tank": "400L"}', 1890, 400.0, 55.0),
('660e8400-e29b-41d4-a716-************', 'Drill Gamma-1', 'Atlas Copco ROC D65', 'ATLAS-ROC-D65-001', 'Atlas Copco', 'drill', '550e8400-e29b-41d4-a716-************', 'maintenance', '2021-11-10', '{"drilling_diameter": "89-127mm", "drilling_depth": "32m", "compressor": "25 bar"}', 3200, 300.0, 0.0),
('660e8400-e29b-41d4-a716-************', 'Conveyor Delta-1', 'Metso CV100', 'METSO-CV100-001', 'Metso', 'conveyor', '550e8400-e29b-41d4-a716-************', 'operational', '2023-01-05', '{"belt_width": "1200mm", "capacity": "1000 t/h", "length": "500m"}', 1200, 0.0, 1000.0),
('660e8400-e29b-41d4-a716-446655440005', 'Loader Epsilon-1', 'Komatsu WA600-8', 'KOMATSU-WA600-001', 'Komatsu', 'loader', '550e8400-e29b-41d4-a716-************', 'operational', '2022-12-01', '{"engine_power": "469 HP", "bucket_capacity": "5.5 m³", "operating_weight": "38 tons"}', 1650, 520.0, 25.0)
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- DEPLOYMENT COMPLETE
-- =============================================

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE 'Mining Operations App database deployment completed successfully!';
    RAISE NOTICE 'Project ID: ohqbaimnhwvdfrmxvhxv';
    RAISE NOTICE 'Database URL: https://ohqbaimnhwvdfrmxvhxv.supabase.co';
    RAISE NOTICE 'Sample data has been inserted for testing.';
    RAISE NOTICE 'You can now use the React Native app with this database.';
END $$;
