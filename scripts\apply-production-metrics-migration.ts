/**
 * <PERSON><PERSON><PERSON> to apply the daily production metrics migration to Supabase
 * Run this script to create the new table and set up the database structure
 */

import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';

// You'll need to set these environment variables or replace with your actual values
const SUPABASE_URL = process.env.SUPABASE_URL || 'your-supabase-url';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'your-service-key';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function applyMigration() {
  try {
    console.log('Starting database migration for daily production metrics...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../database/migrations/001_create_daily_production_metrics.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Applying migration...');
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      console.error('Migration failed:', error);
      throw error;
    }
    
    console.log('Migration applied successfully!');
    
    // Verify the table was created
    const { data: tableInfo, error: tableError } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .limit(1);
    
    if (tableError && tableError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Table verification failed:', tableError);
      throw tableError;
    }
    
    console.log('Table verification successful!');
    
    // Check if we have any locations to work with
    const { data: locations, error: locationError } = await supabase
      .from('locations')
      .select('id, name')
      .limit(5);
    
    if (locationError) {
      console.warn('Could not check locations:', locationError);
    } else {
      console.log(`Found ${locations?.length || 0} locations in the database`);
      if (locations && locations.length > 0) {
        console.log('Available locations:');
        locations.forEach(loc => console.log(`  - ${loc.name} (${loc.id})`));
      }
    }
    
    console.log('\n=== Migration Complete ===');
    console.log('The daily_production_metrics table has been created successfully.');
    console.log('You can now:');
    console.log('1. Import your CSV production data using the import script');
    console.log('2. Use the new detailed production metrics in your app');
    console.log('3. The existing production_reports table remains unchanged for compatibility');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

async function rollbackMigration() {
  try {
    console.log('Rolling back daily production metrics migration...');
    
    const rollbackSQL = `
      -- Drop the daily_production_metrics table and related objects
      DROP TABLE IF EXISTS daily_production_metrics CASCADE;
      DROP FUNCTION IF EXISTS update_daily_production_metrics_updated_at() CASCADE;
      
      -- Note: This will remove all data in the daily_production_metrics table
    `;
    
    const { error } = await supabase.rpc('exec_sql', {
      sql: rollbackSQL
    });
    
    if (error) {
      console.error('Rollback failed:', error);
      throw error;
    }
    
    console.log('Rollback completed successfully!');
    
  } catch (error) {
    console.error('Rollback failed:', error);
    process.exit(1);
  }
}

async function checkMigrationStatus() {
  try {
    console.log('Checking migration status...');
    
    // Check if the table exists
    const { data, error } = await supabase
      .from('daily_production_metrics')
      .select('count(*)')
      .limit(1);
    
    if (error) {
      if (error.code === '42P01') { // Table does not exist
        console.log('❌ Migration not applied - daily_production_metrics table does not exist');
        return false;
      } else {
        console.error('Error checking migration status:', error);
        return false;
      }
    }
    
    console.log('✅ Migration applied - daily_production_metrics table exists');
    
    // Get record count
    const { count, error: countError } = await supabase
      .from('daily_production_metrics')
      .select('*', { count: 'exact', head: true });
    
    if (!countError) {
      console.log(`📊 Table contains ${count || 0} records`);
    }
    
    return true;
    
  } catch (error) {
    console.error('Error checking migration status:', error);
    return false;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--rollback')) {
    await rollbackMigration();
  } else if (args.includes('--status')) {
    await checkMigrationStatus();
  } else {
    await applyMigration();
  }
}

// Export functions for use in other scripts
export { applyMigration, rollbackMigration, checkMigrationStatus };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
