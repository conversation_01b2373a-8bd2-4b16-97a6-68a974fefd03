-- Activity Documentation Table Schema
-- This table stores activity documentation items for the dashboard carousel

-- Create the activity_documentation table
CREATE TABLE IF NOT EXISTS activity_documentation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    activity_date TIMESTAMP WITH TIME ZONE NOT NULL,
    location_id UUID REFERENCES locations(id),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activity_documentation_location_id ON activity_documentation(location_id);
CREATE INDEX IF NOT EXISTS idx_activity_documentation_created_by ON activity_documentation(created_by);
CREATE INDEX IF NOT EXISTS idx_activity_documentation_activity_date ON activity_documentation(activity_date);
CREATE INDEX IF NOT EXISTS idx_activity_documentation_is_active ON activity_documentation(is_active);
CREATE INDEX IF NOT EXISTS idx_activity_documentation_display_order ON activity_documentation(display_order);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_activity_documentation_updated_at 
    BEFORE UPDATE ON activity_documentation 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS)
ALTER TABLE activity_documentation ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view active activity documentation from their location
CREATE POLICY "Users can view activity documentation from their location" ON activity_documentation
    FOR SELECT USING (
        is_active = true AND 
        location_id IN (
            SELECT location_id FROM users WHERE id = auth.uid()
        )
    );

-- Policy: Users can insert activity documentation for their location
CREATE POLICY "Users can insert activity documentation for their location" ON activity_documentation
    FOR INSERT WITH CHECK (
        location_id IN (
            SELECT location_id FROM users WHERE id = auth.uid()
        ) AND
        created_by = auth.uid()
    );

-- Policy: Users can update their own activity documentation
CREATE POLICY "Users can update their own activity documentation" ON activity_documentation
    FOR UPDATE USING (created_by = auth.uid());

-- Insert sample data
INSERT INTO activity_documentation (title, description, image_url, activity_date, location_id, created_by, display_order) VALUES
('Equipment Maintenance Check', 'Routine maintenance performed on Excavator Alpha-1', 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400&h=300&fit=crop', '2025-01-18 08:30:00', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 1),
('Safety Training Session', 'Monthly safety briefing conducted for all operators', 'https://images.unsplash.com/photo-1577962917302-cd874c4e31d2?w=400&h=300&fit=crop', '2025-01-17 14:00:00', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 2),
('Production Milestone', 'Achieved 10,000 tons of ore extraction this month', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop', '2025-01-16 16:45:00', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 3),
('Environmental Inspection', 'Quarterly environmental compliance check completed', 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop', '2025-01-15 10:15:00', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 4),
('New Equipment Arrival', 'Latest dump truck model delivered to site', 'https://images.unsplash.com/photo-1581094288338-2314dddb7ece?w=400&h=300&fit=crop', '2025-01-14 12:00:00', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 5);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON activity_documentation TO authenticated;
GRANT USAGE ON SEQUENCE activity_documentation_id_seq TO authenticated;
