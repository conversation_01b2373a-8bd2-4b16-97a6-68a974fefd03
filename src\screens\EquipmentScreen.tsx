import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  TextInput,
  Modal,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';
import { supabase } from '../config/supabase';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';

interface Equipment {
  id: string;
  name: string;
  model: string;
  equipment_type: string;
  status: 'operational' | 'maintenance' | 'down';
  location_id: string;
  location?: { name: string };
  manufacturer: string;
  operating_hours: number;
  fuel_capacity: number;
  last_maintenance_date?: string;
  next_maintenance_due?: string;
  specifications?: any;
}

const EquipmentScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'All' | 'operational' | 'maintenance' | 'down'>('All');
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadEquipment();
  }, []);

  const loadEquipment = async () => {
    try {
      setLoading(true);
      // TODO: Replace with direct supabase calls
      const { data, error } = await supabase.from('equipment').select('*');
      if (error) throw error;
      setEquipment(data || []);
    } catch (error) {
      Alert.alert('Error', 'Failed to load equipment data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadEquipment().finally(() => setRefreshing(false));
  }, []);

  const updateEquipmentStatus = async (equipmentId: string, newStatus: Equipment['status']) => {
    try {
      // TODO: Replace with direct supabase calls
      const { error } = await supabase.from('equipment').update({ status: newStatus }).eq('id', equipmentId);
      if (error) throw error;
      await loadEquipment(); // Reload data
      setModalVisible(false);
      Alert.alert('Success', 'Equipment status updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update equipment status');
    }
  };

  const getStatusColor = (status: Equipment['status']) => {
    switch (status) {
      case 'operational': return '#4CAF50';
      case 'maintenance': return '#FF9800';
      case 'down': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getStatusDisplayName = (status: Equipment['status']) => {
    switch (status) {
      case 'operational': return 'Active';
      case 'maintenance': return 'Maintenance';
      case 'down': return 'Down';
      default: return status;
    }
  };

  const getTypeIcon = (type: string): keyof typeof Ionicons.glyphMap => {
    switch (type.toLowerCase()) {
      case 'excavator': return 'construct';
      case 'dump_truck': return 'car';
      case 'drill': return 'hammer';
      case 'loader': return 'cube';
      case 'crusher': return 'settings';
      case 'conveyor': return 'swap-horizontal';
      case 'bulldozer': return 'construct';
      case 'grader': return 'construct';
      default: return 'construct';
    }
  };

  const getTypeDisplayName = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const filteredEquipment = equipment.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.model.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'All' || item.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const FilterButton: React.FC<{ title: string; isSelected: boolean; onPress: () => void }> = 
    ({ title, isSelected, onPress }) => (
    <TouchableOpacity
      style={[styles.filterButton, isSelected && styles.filterButtonSelected]}
      onPress={onPress}
    >
      <Text style={[styles.filterButtonText, isSelected && styles.filterButtonTextSelected]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const EquipmentCard: React.FC<{ item: Equipment }> = ({ item }) => (
    <TouchableOpacity
      style={styles.equipmentCard}
      onPress={() => {
        setSelectedEquipment(item);
        setModalVisible(true);
      }}
    >
      <View style={styles.equipmentHeader}>
        <View style={styles.equipmentInfo}>
          <View style={[styles.equipmentIcon, { backgroundColor: getStatusColor(item.status) + '20' }]}>
            <Ionicons name={getTypeIcon(item.equipment_type)} size={24} color={getStatusColor(item.status)} />
          </View>
          <View style={styles.equipmentDetails}>
            <Text style={styles.equipmentName}>{item.name || 'Unknown Equipment'}</Text>
            <Text style={styles.equipmentId}>{item.model || 'Unknown Model'} • {getTypeDisplayName(item.equipment_type)}</Text>
            <View style={styles.locationContainer}>
              <Ionicons name="location-outline" size={14} color={Colors.textSecondary} />
              <Text style={styles.equipmentLocation}> {item.location?.name || 'Unknown Location'}</Text>
            </View>
          </View>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusDisplayName(item.status)}</Text>
        </View>
      </View>
      
      <View style={styles.equipmentMetrics}>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>Operating Hours</Text>
          <Text style={styles.metricValue}>{item.operating_hours || 0}h</Text>
        </View>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>Manufacturer</Text>
          <Text style={styles.metricValue}>{item.manufacturer || 'Unknown'}</Text>
        </View>
        {(item.fuel_capacity && item.fuel_capacity > 0) ? (
          <View style={styles.metric}>
            <Text style={styles.metricLabel}>Fuel Capacity</Text>
            <Text style={styles.metricValue}>{item.fuel_capacity}L</Text>
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" translucent={true} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Equipment Management</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color={Colors.textInverse} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={Colors.textLight} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search equipment..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={Colors.textLight}
          />
        </View>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <FilterButton
            title="All"
            isSelected={selectedFilter === 'All'}
            onPress={() => setSelectedFilter('All')}
          />
          <FilterButton
            title="Operational"
            isSelected={selectedFilter === 'operational'}
            onPress={() => setSelectedFilter('operational')}
          />
          <FilterButton
            title="Maintenance"
            isSelected={selectedFilter === 'maintenance'}
            onPress={() => setSelectedFilter('maintenance')}
          />
          <FilterButton
            title="Down"
            isSelected={selectedFilter === 'down'}
            onPress={() => setSelectedFilter('down')}
          />
        </ScrollView>
      </View>

      {/* Equipment List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading equipment...</Text>
        </View>
      ) : (
        <ScrollView 
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {filteredEquipment.length > 0 ? (
            filteredEquipment.map((item) => (
              <EquipmentCard key={item.id} item={item} />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="construct-outline" size={64} color={Colors.textLight} />
              <Text style={styles.emptyText}>No equipment found</Text>
              <Text style={styles.emptySubtext}>
                {searchQuery ? 'Try adjusting your search' : 'Equipment data will appear here'}
              </Text>
            </View>
          )}
        </ScrollView>
      )}

      {/* Equipment Detail Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedEquipment && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>{selectedEquipment.name}</Text>
                  <TouchableOpacity onPress={() => setModalVisible(false)}>
                    <Ionicons name="close" size={24} color={Colors.textPrimary} />
                  </TouchableOpacity>
                </View>
                
                <View style={styles.modalBody}>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Model:</Text>
                    <Text style={styles.detailValue}>{selectedEquipment.model || 'Unknown Model'}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Type:</Text>
                    <Text style={styles.detailValue}>{getTypeDisplayName(selectedEquipment.equipment_type)}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Status:</Text>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedEquipment.status) }]}>
                      <Text style={styles.statusText}>{getStatusDisplayName(selectedEquipment.status)}</Text>
                    </View>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Manufacturer:</Text>
                    <Text style={styles.detailValue}>{selectedEquipment.manufacturer || 'Unknown'}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Location:</Text>
                    <Text style={styles.detailValue}>{selectedEquipment.location?.name || 'Unknown'}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Operating Hours:</Text>
                    <Text style={styles.detailValue}>{selectedEquipment.operating_hours || 0}h</Text>
                  </View>
                </View>

                <View style={styles.modalActions}>
                  <TouchableOpacity 
                    style={[styles.actionButton, { backgroundColor: '#FF9800' }]}
                    onPress={() => {
                      Alert.alert(
                        'Update Status',
                        'Change equipment status:',
                        [
                          { text: 'Cancel', style: 'cancel' },
                          { text: 'Operational', onPress: () => updateEquipmentStatus(selectedEquipment.id, 'operational') },
                          { text: 'Maintenance', onPress: () => updateEquipmentStatus(selectedEquipment.id, 'maintenance') },
                          { text: 'Down', onPress: () => updateEquipmentStatus(selectedEquipment.id, 'down') },
                        ]
                      );
                    }}
                  >
                    <Text style={styles.actionButtonText}>Update Status</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={[styles.actionButton, { backgroundColor: Colors.primary }]}
                    onPress={() => setModalVisible(false)}
                  >
                    <Text style={styles.actionButtonText}>Close</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + Layout.spacing.md : Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.primary,
  },
  headerTitle: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.accent,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.md,
    backgroundColor: Colors.surface,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  searchInput: {
    flex: 1,
    marginLeft: Layout.spacing.sm,
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
  },
  filtersContainer: {
    paddingHorizontal: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.surface,
  },
  filterButton: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    marginRight: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.sm,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  filterButtonSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
  },
  filterButtonTextSelected: {
    color: Colors.textInverse,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  equipmentCard: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  equipmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Layout.spacing.md,
  },
  equipmentInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  equipmentIcon: {
    width: 48,
    height: 48,
    borderRadius: Layout.borderRadius.sm,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  equipmentDetails: {
    flex: 1,
  },
  equipmentName: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  equipmentId: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: 2,
  },
  equipmentLocation: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  statusBadge: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: 4,
    borderRadius: Layout.borderRadius.sm,
  },
  statusText: {
    fontSize: Layout.fontSize.xs,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  equipmentMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metric: {
    flex: 1,
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: Layout.fontSize.xs,
    color: Colors.textLight,
    marginBottom: 2,
  },
  metricValue: {
    fontSize: Layout.fontSize.sm,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.xl,
  },
  loadingText: {
    marginTop: Layout.spacing.md,
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.xl,
  },
  emptyText: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textSecondary,
    marginTop: Layout.spacing.md,
  },
  emptySubtext: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    textAlign: 'center',
    marginTop: Layout.spacing.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.lg,
    margin: Layout.spacing.lg,
    maxHeight: '80%',
    width: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  modalBody: {
    marginBottom: Layout.spacing.lg,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  detailLabel: {
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    paddingVertical: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    marginHorizontal: Layout.spacing.sm,
  },
  actionButtonText: {
    fontSize: Layout.fontSize.sm,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
});

export default EquipmentScreen;
