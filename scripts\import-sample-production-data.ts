/**
 * <PERSON>ple script to import production data
 * This script demonstrates how to import your CSV production data into the database
 */

import { DatabaseService } from '../src/services/supabase';
import { ProductionDataImporter } from '../src/utils/productionDataImporter';

// Sample CSV data based on your format
const sampleCSVData = `Monthly,Week,Date,Actual OB,Plan OB,Actual ORE,Plan ORE,Actual Rain,Plan Rain,Actual Slippery,Plan Slippery,Actual Fuel,Plan Fuel
December 2023,49,2023-12-01,8500,10000,5200,6000,2.5,2.0,1.0,0.5,2400,2200
December 2023,49,2023-12-02,9200,10000,5800,6000,1.5,2.0,0.5,0.5,2350,2200
December 2023,49,2023-12-03,10500,10000,6200,6000,0.0,2.0,0.0,0.5,2180,2200
December 2023,50,2023-12-04,7800,10000,4900,6000,4.0,2.0,2.5,0.5,2500,2200
December 2023,50,2023-12-05,8900,10000,5500,6000,3.0,2.0,1.5,0.5,2380,2200
December 2023,50,2023-12-06,9800,10000,6100,6000,1.0,2.0,0.5,0.5,2250,2200
December 2023,50,2023-12-07,10200,10000,6300,6000,0.5,2.0,0.0,0.5,2150,2200
December 2023,50,2023-12-08,9500,10000,5900,6000,2.0,2.0,1.0,0.5,2300,2200
December 2023,50,2023-12-09,8700,10000,5400,6000,3.5,2.0,2.0,0.5,2450,2200
December 2023,50,2023-12-10,9100,10000,5700,6000,2.5,2.0,1.5,0.5,2350,2200
January 2024,1,2024-01-01,8000,10000,5000,6000,3.0,2.0,2.0,0.5,2500,2200
January 2024,1,2024-01-02,9000,10000,5600,6000,2.0,2.0,1.0,0.5,2300,2200
January 2024,1,2024-01-03,10000,10000,6000,6000,1.5,2.0,0.5,0.5,2200,2200
January 2024,1,2024-01-04,9500,10000,5800,6000,2.5,2.0,1.5,0.5,2350,2200
January 2024,1,2024-01-05,8800,10000,5400,6000,3.0,2.0,2.0,0.5,2400,2200
January 2024,2,2024-01-08,9200,10000,5700,6000,2.0,2.0,1.0,0.5,2280,2200
January 2024,2,2024-01-09,10500,10000,6200,6000,1.0,2.0,0.5,0.5,2150,2200
January 2024,2,2024-01-10,9800,10000,6000,6000,1.5,2.0,0.5,0.5,2220,2200
January 2024,2,2024-01-11,8900,10000,5500,6000,2.5,2.0,1.5,0.5,2380,2200
January 2024,2,2024-01-12,9600,10000,5900,6000,2.0,2.0,1.0,0.5,2250,2200`;

async function importSampleData() {
  try {
    console.log('Starting sample data import...');
    
    // First, get or create a location
    const locations = await DatabaseService.getLocations();
    let locationId: string;
    
    if (locations.length > 0) {
      locationId = locations[0].id;
      console.log(`Using existing location: ${locations[0].name}`);
    } else {
      // Create a sample location if none exists
      const newLocation = await DatabaseService.createLocation({
        name: 'Main Mining Site',
        description: 'Primary mining operation location',
        location_type: 'mine_site' as any,
        address: 'Mining Site Address',
        is_active: true
      });
      locationId = newLocation.id;
      console.log(`Created new location: ${newLocation.name}`);
    }

    // Import the sample data
    const result = await ProductionDataImporter.importCSVData(
      sampleCSVData,
      locationId,
      (progress, message) => {
        console.log(`Progress: ${progress}% - ${message}`);
      }
    );

    console.log('\n=== Import Results ===');
    console.log(`Successfully imported: ${result.success} records`);
    
    if (result.errors.length > 0) {
      console.log(`Errors encountered: ${result.errors.length}`);
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

    // Verify the import by querying the data
    console.log('\n=== Verification ===');
    const importedData = await DatabaseService.getDailyProductionMetrics(
      '2023-12-01',
      '2024-01-31',
      locationId
    );
    
    console.log(`Total records in database: ${importedData.length}`);
    
    if (importedData.length > 0) {
      console.log('Sample records:');
      importedData.slice(0, 3).forEach(record => {
        console.log(`  ${record.date}: OB ${record.actual_ob}/${record.plan_ob}, ORE ${record.actual_ore}/${record.plan_ore}`);
      });
    }

    // Test aggregated metrics
    console.log('\n=== Aggregated Metrics Test ===');
    const aggregated = await DatabaseService.getProductionMetricsAggregated(
      'monthly',
      '2023-12-01',
      '2024-01-31',
      locationId
    );
    
    console.log('Overburden Volume:', aggregated.overburdenVolume);
    console.log('Ore Volume:', aggregated.oreVolume);
    console.log('Rain Impact:', aggregated.rainImpact);
    console.log('Fuel Consumption:', aggregated.fuelConsumption);

  } catch (error) {
    console.error('Import failed:', error);
  }
}

async function generateTemplate() {
  console.log('Generating CSV template...');
  const template = ProductionDataImporter.generateCSVTemplate();
  console.log('\n=== CSV Template ===');
  console.log(template);
  console.log('\nSave this template as a .csv file and fill in your data.');
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--template')) {
    await generateTemplate();
  } else {
    await importSampleData();
  }
}

// Export functions for use in other scripts
export { importSampleData, generateTemplate };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
