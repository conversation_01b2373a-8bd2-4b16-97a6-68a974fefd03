-- =====================================================
-- Setup Header Storage Bucket for Dashboard Images
-- Run this in Supabase SQL Editor
-- =====================================================

-- Step 1: Create header storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'header',
    'header',
    true,
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Step 2: Create storage policies for header bucket
-- Allow authenticated users to upload header images
CREATE POLICY "Authenticated users can upload header images" ON storage.objects
FOR INSERT WITH CHECK (
    bucket_id = 'header' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = 'images'
);

-- Allow public read access to header images
CREATE POLICY "Public read access for header images" ON storage.objects
FOR SELECT USING (
    bucket_id = 'header'
);

-- Allow admin users to delete header images
CREATE POLICY "Admin users can delete header images" ON storage.objects
FOR DELETE USING (
    bucket_id = 'header' 
    AND EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.is_active = true
        AND (
            users.departemen IN ('Administration', 'Management') 
            OR LOWER(users.jabatan) LIKE '%admin%'
            OR LOWER(users.jabatan) LIKE '%manager%'
        )
    )
);

-- Step 3: Verify bucket creation
SELECT 
    'Header Storage Bucket Setup:' AS status,
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'header';

-- Step 4: Verify policies
SELECT 
    'Storage Policies for Header Bucket:' AS status,
    policyname,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'objects' 
AND policyname LIKE '%header%';

-- Step 5: Test folder structure
-- This will be created automatically when first image is uploaded
-- Folder structure: header/images/dashboard_header_timestamp.jpg

-- Success message
SELECT 
    '✅ Header storage bucket setup complete!' AS message,
    'Bucket: header' AS bucket_info,
    'Folder: images/' AS folder_structure,
    'Access: Public read, Admin write/delete' AS permissions,
    'File types: JPEG, PNG, WebP' AS supported_formats,
    'Size limit: 10MB per file' AS size_limit;

-- =====================================================
-- Migration Notes:
-- =====================================================
-- 
-- OLD STRUCTURE (profile-photos bucket):
-- profile-photos/header/dashboard_header_timestamp.jpg
-- 
-- NEW STRUCTURE (header bucket):
-- header/images/dashboard_header_timestamp.jpg
-- 
-- Benefits:
-- 1. Separate bucket for header images
-- 2. Better organization and security
-- 3. Dedicated policies for header management
-- 4. Easier backup and maintenance
-- 5. Clear separation from profile photos
-- =====================================================
