-- =====================================================
-- Mining Operations Database - Sample Data
-- =====================================================
-- File: 05-sample-data.sql
-- Description: Sample data for testing and development
-- Dependencies: All previous schema files
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- POPULATE PRODUCTION CALENDAR
-- =====================================================

-- Populate calendar for 2024
SELECT populate_production_calendar(2024);

-- Add some holidays
UPDATE production_calendar SET 
    is_holiday = true, 
    holiday_name = 'New Year Day',
    holiday_type = 'National'
WHERE calendar_date = '2024-01-01';

UPDATE production_calendar SET 
    is_holiday = true, 
    holiday_name = 'Independence Day',
    holiday_type = 'National'
WHERE calendar_date = '2024-08-17';

UPDATE production_calendar SET 
    is_holiday = true, 
    holiday_name = 'Christmas Day',
    holiday_type = 'National'
WHERE calendar_date = '2024-12-25';

-- =====================================================
-- SAMPLE PRODUCTION TARGETS
-- =====================================================

-- Monthly targets for 2024
INSERT INTO production_targets_calendar (
    location, target_name, period_type, period_start_date, period_end_date, 
    year_number, month_number, target_ob, target_ore, target_sr, target_fr, 
    target_fuel, planned_working_days, created_by
) VALUES
-- Pit A targets
('Pit A', 'January 2024 Target', 'Monthly', '2024-01-01', '2024-01-31', 2024, 1, 45000, 18000, 2.50, 0.85, 53550, 31, 'System'),
('Pit A', 'February 2024 Target', 'Monthly', '2024-02-01', '2024-02-29', 2024, 2, 42000, 17000, 2.47, 0.83, 48970, 29, 'System'),
('Pit A', 'March 2024 Target', 'Monthly', '2024-03-01', '2024-03-31', 2024, 3, 48000, 19000, 2.53, 0.87, 58290, 31, 'System'),

-- Pit B targets
('Pit B', 'January 2024 Target', 'Monthly', '2024-01-01', '2024-01-31', 2024, 1, 38000, 15000, 2.53, 0.88, 46640, 31, 'System'),
('Pit B', 'February 2024 Target', 'Monthly', '2024-02-01', '2024-02-29', 2024, 2, 36000, 14500, 2.48, 0.86, 43430, 29, 'System'),
('Pit B', 'March 2024 Target', 'Monthly', '2024-03-01', '2024-03-31', 2024, 3, 40000, 16000, 2.50, 0.89, 49840, 31, 'System'),

-- Pit C targets
('Pit C', 'January 2024 Target', 'Monthly', '2024-01-01', '2024-01-31', 2024, 1, 32000, 13000, 2.46, 0.82, 36900, 31, 'System'),
('Pit C', 'February 2024 Target', 'Monthly', '2024-02-01', '2024-02-29', 2024, 2, 30000, 12500, 2.40, 0.80, 34000, 29, 'System'),
('Pit C', 'March 2024 Target', 'Monthly', '2024-03-01', '2024-03-31', 2024, 3, 35000, 14000, 2.50, 0.84, 41160, 31, 'System');

-- Yearly targets for 2024
INSERT INTO production_targets_calendar (
    location, target_name, period_type, period_start_date, period_end_date, 
    year_number, target_ob, target_ore, target_sr, target_fr, 
    target_fuel, planned_working_days, created_by
) VALUES
('Pit A', '2024 Annual Target', 'Yearly', '2024-01-01', '2024-12-31', 2024, 540000, 216000, 2.50, 0.85, 642600, 365, 'System'),
('Pit B', '2024 Annual Target', 'Yearly', '2024-01-01', '2024-12-31', 2024, 456000, 180000, 2.53, 0.88, 559680, 365, 'System'),
('Pit C', '2024 Annual Target', 'Yearly', '2024-01-01', '2024-12-31', 2024, 384000, 156000, 2.46, 0.82, 442800, 365, 'System');

-- =====================================================
-- SAMPLE DAILY MINING REPORTS
-- =====================================================

-- January 2024 data for Pit A
INSERT INTO daily_mining_report (
    location, report_date, shift_1_ob, shift_2_ob, plan_ob, 
    shift_1_ore, shift_2_ore, plan_ore, fuel_actual, fuel_plan, 
    plan_fr, plan_sr, rain_actual, slippery_actual, 
    equipment_availability, equipment_utilization, 
    shift_1_personnel, shift_2_personnel, 
    reported_by, status, created_by
) VALUES
-- Week 1
('Pit A', '2024-01-01', 0, 0, 0, 0, 0, 0, 0, 0, 0.85, 2.50, 0, 0, 0, 0, 0, 0, 'Holiday', 'Approved', 'System'), -- Holiday
('Pit A', '2024-01-02', 850.5, 920.3, 1800, 450.2, 380.7, 800, 1250.5, 1360, 0.85, 2.50, 2.5, 1.0, 95.5, 87.2, 12, 11, 'John Supervisor', 'Approved', 'System'),
('Pit A', '2024-01-03', 780.0, 890.0, 1750, 420.0, 410.0, 850, 1180.0, 1326, 0.85, 2.50, 0, 0, 98.2, 92.1, 12, 12, 'John Supervisor', 'Approved', 'System'),
('Pit A', '2024-01-04', 920.5, 850.2, 1850, 480.3, 390.5, 820, 1320.8, 1394, 0.85, 2.50, 1.5, 0.5, 96.8, 89.5, 11, 12, 'John Supervisor', 'Approved', 'System'),
('Pit A', '2024-01-05', 810.0, 940.5, 1800, 440.0, 420.8, 830, 1285.2, 1360, 0.85, 2.50, 0, 0, 97.5, 91.3, 12, 12, 'John Supervisor', 'Approved', 'System'),

-- Week 2
('Pit A', '2024-01-08', 875.2, 895.8, 1820, 465.5, 395.2, 840, 1298.5, 1378, 0.85, 2.50, 3.0, 2.0, 94.2, 85.8, 12, 11, 'Sarah Manager', 'Approved', 'System'),
('Pit A', '2024-01-09', 820.0, 910.5, 1780, 430.0, 405.5, 810, 1265.8, 1346, 0.85, 2.50, 4.5, 3.5, 92.5, 83.2, 11, 12, 'Sarah Manager', 'Approved', 'System'),
('Pit A', '2024-01-10', 890.5, 825.0, 1850, 470.2, 385.8, 830, 1310.2, 1394, 0.85, 2.50, 1.0, 0, 96.8, 90.5, 12, 12, 'Sarah Manager', 'Approved', 'System'),
('Pit A', '2024-01-11', 805.8, 925.2, 1800, 425.5, 415.0, 820, 1275.5, 1360, 0.85, 2.50, 0, 0, 98.1, 93.2, 12, 11, 'Sarah Manager', 'Approved', 'System'),
('Pit A', '2024-01-12', 860.0, 880.5, 1790, 450.0, 400.2, 825, 1290.8, 1354, 0.85, 2.50, 2.0, 1.5, 95.8, 88.7, 12, 12, 'Sarah Manager', 'Approved', 'System');

-- Sample data for Pit B
INSERT INTO daily_mining_report (
    location, report_date, shift_1_ob, shift_2_ob, plan_ob, 
    shift_1_ore, shift_2_ore, plan_ore, fuel_actual, fuel_plan, 
    plan_fr, plan_sr, rain_actual, slippery_actual, 
    equipment_availability, equipment_utilization, 
    shift_1_personnel, shift_2_personnel, 
    reported_by, status, created_by
) VALUES
('Pit B', '2024-01-02', 720.5, 780.3, 1520, 380.2, 340.7, 680, 1085.5, 1158, 0.88, 2.53, 2.5, 1.0, 94.5, 86.2, 10, 9, 'Mike Wilson', 'Approved', 'System'),
('Pit B', '2024-01-03', 680.0, 790.0, 1480, 350.0, 360.0, 720, 1020.0, 1126, 0.88, 2.53, 0, 0, 97.2, 91.1, 10, 10, 'Mike Wilson', 'Approved', 'System'),
('Pit B', '2024-01-04', 820.5, 750.2, 1580, 420.3, 330.5, 700, 1180.8, 1190, 0.88, 2.53, 1.5, 0.5, 95.8, 88.5, 9, 10, 'Mike Wilson', 'Approved', 'System'),
('Pit B', '2024-01-05', 710.0, 840.5, 1520, 380.0, 370.8, 710, 1125.2, 1158, 0.88, 2.53, 0, 0, 96.5, 90.3, 10, 10, 'Mike Wilson', 'Approved', 'System');

-- Sample data for Pit C
INSERT INTO daily_mining_report (
    location, report_date, shift_1_ob, shift_2_ob, plan_ob, 
    shift_1_ore, shift_2_ore, plan_ore, fuel_actual, fuel_plan, 
    plan_fr, plan_sr, rain_actual, slippery_actual, 
    equipment_availability, equipment_utilization, 
    shift_1_personnel, shift_2_personnel, 
    reported_by, status, created_by
) VALUES
('Pit C', '2024-01-02', 620.5, 680.3, 1320, 320.2, 290.7, 580, 925.5, 1008, 0.82, 2.46, 2.5, 1.0, 93.5, 85.2, 8, 7, 'Lisa Davis', 'Approved', 'System'),
('Pit C', '2024-01-03', 580.0, 690.0, 1280, 300.0, 310.0, 620, 920.0, 976, 0.82, 2.46, 0, 0, 96.2, 90.1, 8, 8, 'Lisa Davis', 'Approved', 'System'),
('Pit C', '2024-01-04', 720.5, 650.2, 1380, 370.3, 280.5, 600, 1080.8, 1050, 0.82, 2.46, 1.5, 0.5, 94.8, 87.5, 7, 8, 'Lisa Davis', 'Approved', 'System'),
('Pit C', '2024-01-05', 610.0, 740.5, 1320, 330.0, 320.8, 610, 1025.2, 1008, 0.82, 2.46, 0, 0, 95.5, 89.3, 8, 8, 'Lisa Davis', 'Approved', 'System');

-- Add some recent data with different statuses
INSERT INTO daily_mining_report (
    location, report_date, shift_1_ob, shift_2_ob, plan_ob, 
    shift_1_ore, shift_2_ore, plan_ore, fuel_actual, fuel_plan, 
    plan_fr, plan_sr, rain_actual, slippery_actual, 
    equipment_availability, equipment_utilization, 
    shift_1_personnel, shift_2_personnel, 
    reported_by, status, weather_impact_level, created_by
) VALUES
-- Recent data with various statuses
('Pit A', CURRENT_DATE - 2, 890.0, 920.5, 1850, 470.0, 400.2, 840, 1320.8, 1394, 0.85, 2.50, 0, 0, 97.5, 91.8, 12, 12, 'John Supervisor', 'Approved', 'None', 'System'),
('Pit A', CURRENT_DATE - 1, 850.5, 880.0, 1800, 450.5, 385.8, 820, 1285.2, 1360, 0.85, 2.50, 1.5, 0.5, 96.2, 89.5, 12, 11, 'John Supervisor', 'Submitted', 'Low', 'System'),
('Pit A', CURRENT_DATE, 0, 0, 1820, 0, 0, 830, 0, 1378, 0.85, 2.50, 0, 0, 0, 0, 0, 0, 'John Supervisor', 'Draft', 'None', 'System'),

('Pit B', CURRENT_DATE - 2, 780.0, 820.5, 1580, 390.0, 350.2, 720, 1180.8, 1190, 0.88, 2.53, 0, 0, 96.5, 90.8, 10, 10, 'Mike Wilson', 'Approved', 'None', 'System'),
('Pit B', CURRENT_DATE - 1, 750.5, 780.0, 1520, 380.5, 335.8, 700, 1125.2, 1158, 0.88, 2.53, 2.5, 1.5, 94.2, 87.5, 10, 9, 'Mike Wilson', 'Submitted', 'Medium', 'System'),
('Pit B', CURRENT_DATE, 0, 0, 1540, 0, 0, 710, 0, 1174, 0.88, 2.53, 0, 0, 0, 0, 0, 0, 'Mike Wilson', 'Draft', 'None', 'System');

-- =====================================================
-- UPDATE FUEL COSTS
-- =====================================================

-- Update fuel costs based on fuel consumption and price
UPDATE daily_mining_report SET 
    fuel_price_per_liter = 1.25,
    fuel_cost_actual = fuel_actual * 1.25,
    fuel_cost_plan = fuel_plan * 1.25
WHERE fuel_actual > 0;

-- =====================================================
-- SAMPLE QUERIES FOR VERIFICATION
-- =====================================================

-- Verify sample data
DO $$
DECLARE
    report_count INTEGER;
    location_count INTEGER;
    target_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO report_count FROM daily_mining_report;
    SELECT COUNT(DISTINCT location) INTO location_count FROM daily_mining_report;
    SELECT COUNT(*) INTO target_count FROM production_targets_calendar;
    
    RAISE NOTICE '================================================';
    RAISE NOTICE 'SAMPLE DATA LOADED SUCCESSFULLY';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Daily Reports: % records', report_count;
    RAISE NOTICE 'Locations: % locations', location_count;
    RAISE NOTICE 'Production Targets: % targets', target_count;
    RAISE NOTICE 'Calendar populated for 2024';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Sample queries to try:';
    RAISE NOTICE '1. SELECT * FROM daily_mining_report WHERE location = ''Pit A'' ORDER BY report_date;';
    RAISE NOTICE '2. SELECT * FROM v_weekly_production_summary WHERE location = ''Pit A'';';
    RAISE NOTICE '3. SELECT * FROM v_monthly_production_summary WHERE year_number = 2024;';
    RAISE NOTICE '4. SELECT * FROM v_production_dashboard WHERE report_date >= CURRENT_DATE - 7;';
    RAISE NOTICE '================================================';
END $$;

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('005', 'Sample data for testing and development')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- SAMPLE ANALYTICAL QUERIES
-- =====================================================

-- Show recent production summary
/*
SELECT 
    location,
    report_date,
    total_actual_material,
    total_plan_material,
    total_achievement_percent || '%' as achievement,
    actual_sr,
    actual_fr,
    status
FROM daily_mining_report 
WHERE report_date >= CURRENT_DATE - 7
ORDER BY location, report_date;
*/

-- Show monthly performance
/*
SELECT 
    location,
    month_name,
    month_actual_material,
    month_plan_material,
    month_total_achievement_percent || '%' as achievement,
    month_actual_sr,
    month_actual_fr
FROM v_monthly_production_summary 
WHERE year_number = 2024
ORDER BY location, month_number;
*/

-- Show production dashboard
/*
SELECT 
    location,
    report_date,
    total_achievement_percent || '%' as daily_achievement,
    month_total_achievement_percent || '%' as mtd_achievement,
    year_total_achievement_percent || '%' as ytd_achievement,
    actual_sr,
    actual_fr,
    weather_impact_level,
    status
FROM v_production_dashboard 
WHERE report_date >= CURRENT_DATE - 10
ORDER BY location, report_date DESC;
*/
