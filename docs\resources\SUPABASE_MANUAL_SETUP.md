# 🗄️ Supabase Manual Setup Guide

> **📝 File**: `SUPABASE_MANUAL_SETUP.md`  
> **📅 Created**: 15 January 2025  
> **🔄 Last Updated**: 15 January 2025  
> **👤 Author**: Augment AI Agent  
> **📋 Version**: v1.0  
> **✅ Status**: Setup Required  
> **🎯 Purpose**: Manual setup guide for Supabase storage bucket and policies

---

## 🚨 ERROR: BUCKET NOT FOUND

### **❌ Current Error:**
```bash
Error uploading image: Bucket not found
```

### **🔍 Root Cause:**
- Storage bucket `avatars` belum dibuat di Supabase
- SQL script belum dijalankan
- Storage policies belum dikonfigurasi

---

## 🛠️ MANUAL SETUP STEPS

### **📋 Step 1: Create Storage Bucket**

#### **🌐 Via Supabase Dashboard:**
1. **Login ke Supabase Dashboard**: https://supabase.com/dashboard
2. **Pilih Project**: Pilih project Mining Operations App
3. **Navigate ke Storage**: Klik "Storage" di sidebar kiri
4. **Create New Bucket**:
   - Click "New bucket"
   - Bucket name: `avatars`
   - Public bucket: ✅ **CHECKED** (penting!)
   - File size limit: `5242880` (5MB)
   - Allowed MIME types: `image/jpeg,image/png,image/webp,image/gif`
5. **Click "Save"**

#### **📝 Via SQL Editor:**
```sql
-- Run this in Supabase SQL Editor
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars', 
  'avatars', 
  true,
  5242880,
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;
```

### **📋 Step 2: Configure Storage Policies**

#### **🔐 Via SQL Editor (REQUIRED):**
```sql
-- 1. Allow authenticated users to upload their own avatars
CREATE POLICY "Users can upload their own avatar" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- 2. Allow authenticated users to update their own avatars
CREATE POLICY "Users can update their own avatar" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- 3. Allow authenticated users to delete their own avatars
CREATE POLICY "Users can delete their own avatar" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- 4. Allow public access to view avatars
CREATE POLICY "Public can view avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');
```

### **📋 Step 3: Update Users Table**

#### **🗃️ Ensure avatar_url Column Exists:**
```sql
-- Add avatar_url column if not exists
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS avatar_url TEXT;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_users_avatar_url ON users(avatar_url);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

---

## ✅ VERIFICATION STEPS

### **📋 1. Verify Bucket Creation:**
```sql
-- Run in SQL Editor to check bucket exists
SELECT * FROM storage.buckets WHERE id = 'avatars';

-- Expected result:
-- id: avatars
-- name: avatars  
-- public: true
-- file_size_limit: 5242880
-- allowed_mime_types: {image/jpeg,image/png,image/webp,image/gif}
```

### **📋 2. Verify Storage Policies:**
```sql
-- Check policies exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'objects' 
AND policyname LIKE '%avatar%';

-- Expected: 4 policies
-- - Users can upload their own avatar (INSERT)
-- - Users can update their own avatar (UPDATE)  
-- - Users can delete their own avatar (DELETE)
-- - Public can view avatars (SELECT)
```

### **📋 3. Verify Users Table:**
```sql
-- Check avatar_url column exists
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'users' AND column_name = 'avatar_url';

-- Expected result:
-- column_name: avatar_url
-- data_type: text
-- is_nullable: YES
```

### **📋 4. Test Upload via Dashboard:**
1. **Go to Storage > avatars bucket**
2. **Try uploading a test image**
3. **Should upload successfully**
4. **Image should be publicly accessible**

---

## 🔧 TROUBLESHOOTING

### **❌ "Bucket not found" Error:**
```bash
Solution:
1. ✅ Create bucket via Dashboard or SQL
2. ✅ Ensure bucket name is exactly 'avatars'
3. ✅ Make sure bucket is public
4. ✅ Refresh browser and try again
```

### **❌ "Access denied" Error:**
```bash
Solution:
1. ✅ Run all 4 storage policies SQL commands
2. ✅ Ensure user is authenticated
3. ✅ Check bucket policies in Dashboard
4. ✅ Verify user ID matches folder structure
```

### **❌ "Column avatar_url does not exist":**
```bash
Solution:
1. ✅ Run ALTER TABLE command to add column
2. ✅ Check users table structure
3. ✅ Verify column was created successfully
```

### **❌ Upload still failing:**
```bash
Debug steps:
1. Check browser console for detailed errors
2. Verify Supabase project URL and API key
3. Test with small image file first
4. Check network connectivity
5. Try uploading via Supabase Dashboard first
```

---

## 📱 TESTING AFTER SETUP

### **🧪 1. Quick Test:**
```bash
1. Complete all setup steps above
2. Open Mining Operations App
3. Navigate to Profile screen
4. Tap profile image
5. Select Camera or Gallery
6. Take/select photo
7. Should upload successfully now
```

### **🔍 2. Verify Results:**
```bash
✅ No "Bucket not found" error
✅ Upload completes successfully
✅ Success message appears
✅ Profile image updates in app
✅ Image visible in Supabase Storage dashboard
✅ Public URL accessible
```

---

## 🎯 COMPLETE SETUP CHECKLIST

### **☑️ Supabase Dashboard Tasks:**
```bash
□ Login to Supabase Dashboard
□ Navigate to correct project
□ Go to Storage section
□ Create 'avatars' bucket
□ Set bucket as public
□ Set file size limit to 5MB
□ Set allowed MIME types
```

### **☑️ SQL Editor Tasks:**
```bash
□ Run bucket creation SQL (if not done via Dashboard)
□ Run all 4 storage policy SQL commands
□ Run users table ALTER commands
□ Run verification queries
□ Confirm all results are correct
```

### **☑️ App Testing Tasks:**
```bash
□ Test profile image upload
□ Verify no "Bucket not found" error
□ Confirm upload success
□ Check image updates across screens
□ Test with different image formats
□ Verify public URL accessibility
```

---

**🎯 RESULT: After completing these steps, the "Bucket not found" error will be resolved!**

**✅ Status**: Setup Required  
**🗄️ Bucket**: Must be created manually  
**🔐 Policies**: Must be configured via SQL  
**🗃️ Database**: Users table must have avatar_url column  
**🧪 Testing**: Upload should work after setup
