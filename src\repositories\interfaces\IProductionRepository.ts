/**
 * Production Repository Interface
 * Defines the contract for production data access operations
 */

import {
  ProductionMetric,
  AggregatedMetrics,
  CreateProductionMetricDto,
  UpdateProductionMetricDto,
  GetProductionMetricsRequest,
  TimePeriod,
  DateRange,
  FilterOptions,
  SortOptions,
  PaginationOptions,
} from '../../models/Production';

export interface IProductionRepository {
  /**
   * Get daily production metrics for a specific date range
   */
  getDailyMetrics(
    startDate: string,
    endDate: string,
    locationId?: string
  ): Promise<ProductionMetric[]>;

  /**
   * Get aggregated production metrics for a specific period
   */
  getAggregatedMetrics(
    period: TimePeriod,
    startDate?: string,
    endDate?: string,
    locationId?: string
  ): Promise<AggregatedMetrics>;

  /**
   * Get production metrics with advanced filtering and pagination
   */
  getMetricsWithFilters(
    filters: FilterOptions,
    sort?: SortOptions,
    pagination?: PaginationOptions
  ): Promise<{
    data: ProductionMetric[];
    total: number;
    page: number;
    totalPages: number;
  }>;

  /**
   * Get production metrics for current production month
   */
  getCurrentProductionMonthMetrics(locationId?: string): Promise<{
    productionMonth: { name: string };
    dailyData: ProductionMetric[];
    dateRange: {
      startDate: string;
      endDate: string;
      currentEndDate: string;
      daysCount: number;
    };
  }>;

  /**
   * Get production metrics by monthly field
   */
  getMetricsByMonthly(monthlyField: string): Promise<ProductionMetric[]>;

  /**
   * Create a new production metric
   */
  createMetric(metric: CreateProductionMetricDto): Promise<ProductionMetric>;

  /**
   * Update an existing production metric
   */
  updateMetric(updates: UpdateProductionMetricDto): Promise<ProductionMetric>;

  /**
   * Delete a production metric
   */
  deleteMetric(id: string): Promise<void>;

  /**
   * Bulk create production metrics
   */
  bulkCreateMetrics(metrics: CreateProductionMetricDto[]): Promise<ProductionMetric[]>;

  /**
   * Get production metrics count for a date range
   */
  getMetricsCount(
    startDate?: string,
    endDate?: string,
    locationId?: string
  ): Promise<number>;

  /**
   * Get latest production metric
   */
  getLatestMetric(locationId?: string): Promise<ProductionMetric | null>;

  /**
   * Get production metrics for a specific date
   */
  getMetricsByDate(date: string, locationId?: string): Promise<ProductionMetric[]>;

  /**
   * Check if production data exists for a date range
   */
  hasDataForDateRange(
    startDate: string,
    endDate: string,
    locationId?: string
  ): Promise<boolean>;

  /**
   * Get production trends (comparison with previous period)
   */
  getProductionTrends(
    currentPeriod: DateRange,
    previousPeriod: DateRange,
    locationId?: string
  ): Promise<{
    current: AggregatedMetrics;
    previous: AggregatedMetrics;
    trends: {
      overburden: 'up' | 'down' | 'stable';
      ore: 'up' | 'down' | 'stable';
      fuel: 'up' | 'down' | 'stable';
      achievement: 'up' | 'down' | 'stable';
    };
  }>;

  /**
   * Get production summary for dashboard
   */
  getProductionSummary(
    period: TimePeriod,
    locationId?: string
  ): Promise<{
    totalProduction: number;
    averageAchievement: number;
    bestPerformingDay: ProductionMetric | null;
    worstPerformingDay: ProductionMetric | null;
    totalRainImpact: number;
    equipmentUtilization: number;
  }>;

  /**
   * Search production metrics by criteria
   */
  searchMetrics(
    query: string,
    filters?: FilterOptions
  ): Promise<ProductionMetric[]>;

  /**
   * Get production metrics statistics
   */
  getMetricsStatistics(
    startDate: string,
    endDate: string,
    locationId?: string
  ): Promise<{
    min: Partial<ProductionMetric>;
    max: Partial<ProductionMetric>;
    avg: Partial<ProductionMetric>;
    total: Partial<ProductionMetric>;
    count: number;
  }>;

  /**
   * Validate production metric data
   */
  validateMetricData(metric: CreateProductionMetricDto | UpdateProductionMetricDto): Promise<{
    isValid: boolean;
    errors: string[];
  }>;

  /**
   * Get production calendar data
   */
  getProductionCalendar(): Promise<Array<{
    id: string;
    name: string;
    startDate: string;
    endDate: string;
    year: number;
    month: number;
  }>>;

  /**
   * Get current production month
   */
  getCurrentProductionMonth(): Promise<{
    id: string;
    name: string;
    startDate: string;
    endDate: string;
    year: number;
    month: number;
  } | null>;
}

/**
 * Repository Error Types
 */
export class RepositoryError extends Error {
  constructor(
    message: string,
    public operation: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'RepositoryError';
  }
}

export class NotFoundError extends RepositoryError {
  constructor(resource: string, id: string) {
    super(`${resource} with id ${id} not found`, 'find');
    this.name = 'NotFoundError';
  }
}

export class ValidationError extends RepositoryError {
  constructor(message: string, public field: string) {
    super(message, 'validate');
    this.name = 'ValidationError';
  }
}

export class ConnectionError extends RepositoryError {
  constructor(message: string) {
    super(message, 'connect');
    this.name = 'ConnectionError';
  }
}
