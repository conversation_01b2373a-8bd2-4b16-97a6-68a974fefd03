import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Modal,
  TextInput,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';
import { Report } from '../types';
import { supabase } from '../config/supabase';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';

const ReportsScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();
  const [selectedFilter, setSelectedFilter] = useState<'All' | 'Production' | 'Safety' | 'Equipment' | 'Incident'>('All');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [newReport, setNewReport] = useState({
    title: '',
    type: 'Production' as Report['type'],
    summary: '',
  });

  const [reports, setReports] = useState<Report[]>([]);

  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    try {
      setLoading(true);
      // TODO: Replace with direct supabase calls
      const data: Report[] = []; // Placeholder
      setReports(data);
    } catch (error) {
      Alert.alert('Error', 'Failed to load reports. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadReports();
    setRefreshing(false);
  };

  const getReportTypeColor = (type: Report['type']) => {
    switch (type) {
      case 'Production': return Colors.primary;
      case 'Safety': return Colors.secondary;
      case 'Equipment': return Colors.accent;
      case 'Incident': return Colors.warning;
      default: return Colors.textLight;
    }
  };

  const getReportTypeIcon = (type: Report['type']): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case 'Production': return 'bar-chart';
      case 'Safety': return 'shield-checkmark';
      case 'Equipment': return 'construct';
      case 'Incident': return 'warning';
      default: return 'document';
    }
  };

  const getStatusColor = (status: Report['status']) => {
    switch (status) {
      case 'Draft': return Colors.textLight;
      case 'Submitted': return Colors.warning;
      case 'Approved': return Colors.accent;
      case 'Rejected': return Colors.secondary;
      default: return Colors.textLight;
    }
  };

  const filteredReports = reports.filter(report => 
    selectedFilter === 'All' || report.type === selectedFilter
  );

  const handleCreateReport = () => {
    if (!newReport.title || !newReport.summary) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const report: Report = {
      id: Date.now().toString(),
      title: newReport.title,
      type: newReport.type,
      date: new Date().toISOString().split('T')[0],
      status: 'Draft',
      createdBy: 'Current User',
      summary: newReport.summary,
    };

    setReports(prev => [report, ...prev]);
    setNewReport({ title: '', type: 'Production', summary: '' });
    setCreateModalVisible(false);
    Alert.alert('Success', 'Report created successfully');
  };

  const FilterButton: React.FC<{ title: string; isSelected: boolean; onPress: () => void }> = 
    ({ title, isSelected, onPress }) => (
    <TouchableOpacity
      style={[styles.filterButton, isSelected && styles.filterButtonSelected]}
      onPress={onPress}
    >
      <Text style={[styles.filterButtonText, isSelected && styles.filterButtonTextSelected]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const ReportCard: React.FC<{ report: Report }> = ({ report }) => (
    <TouchableOpacity
      style={[styles.reportCard, { borderLeftColor: getReportTypeColor(report.type) }]}
      onPress={() => {
        setSelectedReport(report);
        setModalVisible(true);
      }}
    >
      <View style={styles.reportHeader}>
        <View style={styles.reportInfo}>
          <View style={[styles.reportIcon, { backgroundColor: getReportTypeColor(report.type) + '20' }]}>
            <Ionicons name={getReportTypeIcon(report.type)} size={24} color={getReportTypeColor(report.type)} />
          </View>
          <View style={styles.reportDetails}>
            <Text style={styles.reportTitle}>{report.title}</Text>
            <Text style={styles.reportMeta}>{report.date} • {report.createdBy}</Text>
          </View>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(report.status) }]}>
          <Text style={styles.statusText}>{report.status}</Text>
        </View>
      </View>
      <Text style={styles.reportSummary} numberOfLines={2}>
        {report.summary}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" translucent={true} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Reports Dashboard</Text>
        <TouchableOpacity 
          style={styles.createButton}
          onPress={() => setCreateModalVisible(true)}
        >
          <Ionicons name="add" size={24} color={Colors.textInverse} />
        </TouchableOpacity>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <FilterButton
            title="All"
            isSelected={selectedFilter === 'All'}
            onPress={() => setSelectedFilter('All')}
          />
          <FilterButton
            title="Production"
            isSelected={selectedFilter === 'Production'}
            onPress={() => setSelectedFilter('Production')}
          />
          <FilterButton
            title="Safety"
            isSelected={selectedFilter === 'Safety'}
            onPress={() => setSelectedFilter('Safety')}
          />
          <FilterButton
            title="Equipment"
            isSelected={selectedFilter === 'Equipment'}
            onPress={() => setSelectedFilter('Equipment')}
          />
          <FilterButton
            title="Incident"
            isSelected={selectedFilter === 'Incident'}
            onPress={() => setSelectedFilter('Incident')}
          />
        </ScrollView>
      </View>

      {/* Reports List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading reports...</Text>
        </View>
      ) : (
        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
        >
          {filteredReports.length > 0 ? (
            filteredReports.map((report) => (
              <ReportCard key={report.id} report={report} />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="document-outline" size={64} color={Colors.textLight} />
              <Text style={styles.emptyTitle}>No Reports Found</Text>
              <Text style={styles.emptySubtitle}>
                {selectedFilter === 'All'
                  ? 'No reports available at the moment.'
                  : `No ${selectedFilter.toLowerCase()} reports found.`
                }
              </Text>
            </View>
          )}
        </ScrollView>
      )}

      {/* Report Detail Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedReport && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>{selectedReport.title}</Text>
                  <TouchableOpacity onPress={() => setModalVisible(false)}>
                    <Ionicons name="close" size={24} color={Colors.textPrimary} />
                  </TouchableOpacity>
                </View>

                <View style={styles.modalBody}>
                  <View style={styles.reportTypeContainer}>
                    <View style={[styles.reportIcon, { backgroundColor: getReportTypeColor(selectedReport.type) + '20' }]}>
                      <Ionicons name={getReportTypeIcon(selectedReport.type)} size={32} color={getReportTypeColor(selectedReport.type)} />
                    </View>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedReport.status) }]}>
                      <Text style={styles.statusText}>{selectedReport.status}</Text>
                    </View>
                  </View>

                  <Text style={styles.reportSummary}>{selectedReport.summary}</Text>

                  <View style={styles.reportMetadata}>
                    <View style={styles.metadataRow}>
                      <Ionicons name="calendar" size={16} color={Colors.textLight} />
                      <Text style={styles.metadataText}>{selectedReport.date}</Text>
                    </View>
                    <View style={styles.metadataRow}>
                      <Ionicons name="person" size={16} color={Colors.textLight} />
                      <Text style={styles.metadataText}>{selectedReport.createdBy}</Text>
                    </View>
                    <View style={styles.metadataRow}>
                      <Ionicons name="document" size={16} color={Colors.textLight} />
                      <Text style={styles.metadataText}>{selectedReport.type} Report</Text>
                    </View>
                  </View>
                </View>

                <View style={styles.modalActions}>
                  <TouchableOpacity style={[styles.actionButton, { backgroundColor: Colors.info }]}>
                    <Text style={styles.actionButtonText}>Download PDF</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.actionButton, { backgroundColor: Colors.primary }]}>
                    <Text style={styles.actionButtonText}>Edit Report</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>

      {/* Create Report Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={createModalVisible}
        onRequestClose={() => setCreateModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Create New Report</Text>
              <TouchableOpacity onPress={() => setCreateModalVisible(false)}>
                <Ionicons name="close" size={24} color={Colors.textPrimary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Report Type</Text>
                <View style={styles.typeSelector}>
                  {(['Production', 'Safety', 'Equipment', 'Incident'] as const).map((type) => (
                    <TouchableOpacity
                      key={type}
                      style={[
                        styles.typeButton,
                        newReport.type === type && { backgroundColor: getReportTypeColor(type) }
                      ]}
                      onPress={() => setNewReport(prev => ({ ...prev, type }))}
                    >
                      <Text style={[
                        styles.typeButtonText,
                        newReport.type === type && { color: Colors.textInverse }
                      ]}>
                        {type}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Report Title *</Text>
                <TextInput
                  style={styles.textInput}
                  value={newReport.title}
                  onChangeText={(text) => setNewReport(prev => ({ ...prev, title: text }))}
                  placeholder="Enter report title"
                  placeholderTextColor={Colors.textLight}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Summary *</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={newReport.summary}
                  onChangeText={(text) => setNewReport(prev => ({ ...prev, summary: text }))}
                  placeholder="Enter report summary and key findings"
                  placeholderTextColor={Colors.textLight}
                  multiline
                  numberOfLines={6}
                />
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: Colors.border }]}
                onPress={() => setCreateModalVisible(false)}
              >
                <Text style={[styles.actionButtonText, { color: Colors.textPrimary }]}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: Colors.primary }]}
                onPress={handleCreateReport}
              >
                <Text style={styles.actionButtonText}>Create Report</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: (StatusBar.currentHeight || 0) + Layout.spacing.md,
    paddingHorizontal: Layout.spacing.md,
    paddingBottom: Layout.spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  createButton: {
    backgroundColor: Colors.primaryLight,
    borderRadius: Layout.borderRadius.full,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterButton: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.full,
    backgroundColor: Colors.background,
    marginRight: Layout.spacing.sm,
  },
  filterButtonSelected: {
    backgroundColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  filterButtonTextSelected: {
    color: Colors.textInverse,
  },
  content: {
    flex: 1,
    padding: Layout.spacing.md,
  },
  reportCard: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
    borderLeftWidth: 4,
    ...Layout.shadow,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Layout.spacing.sm,
  },
  reportInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  reportIcon: {
    width: 48,
    height: 48,
    borderRadius: Layout.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  reportDetails: {
    flex: 1,
  },
  reportTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  reportMeta: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
  },
  statusBadge: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: 4,
    borderRadius: Layout.borderRadius.full,
  },
  statusText: {
    fontSize: Layout.fontSize.xs,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  reportSummary: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.surface,
    borderTopLeftRadius: Layout.borderRadius.xl,
    borderTopRightRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
  },
  modalTitle: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  modalBody: {
    marginBottom: Layout.spacing.lg,
  },
  reportTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  reportMetadata: {
    marginTop: Layout.spacing.md,
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  metadataText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
    marginLeft: Layout.spacing.sm,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    paddingVertical: Layout.spacing.md,
    borderRadius: Layout.borderRadius.lg,
    alignItems: 'center',
    marginHorizontal: Layout.spacing.xs,
  },
  actionButtonText: {
    fontSize: Layout.fontSize.sm,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  inputGroup: {
    marginBottom: Layout.spacing.md,
  },
  inputLabel: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.sm,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    backgroundColor: Colors.background,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  typeButton: {
    flex: 1,
    paddingVertical: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
    marginHorizontal: Layout.spacing.xs,
    marginBottom: Layout.spacing.sm,
  },
  typeButtonText: {
    fontSize: Layout.fontSize.xs,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.xl,
  },
  loadingText: {
    marginTop: Layout.spacing.md,
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.xl,
    marginTop: Layout.spacing.xxl,
  },
  emptyTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.sm,
  },
  emptySubtitle: {
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default ReportsScreen;
