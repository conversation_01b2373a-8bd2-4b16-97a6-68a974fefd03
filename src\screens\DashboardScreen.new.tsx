// ===== IMPOR PUSTAKA =====
// Impor React inti untuk manajemen state dan lifecycle
import React, { useState, useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';

// Komponen UI React Native untuk membangun antarmuka
import {
  View,          // Komponen kontainer dasar
  Text,          // Komponen untuk menampilkan teks
  ScrollView,    // Kontainer yang dapat di-scroll
  TouchableOpacity, // Tombol yang dapat di-tap dengan umpan balik
  StyleSheet,    // Untuk membuat gaya/styles
  Image,         // Komponen untuk menampilkan gambar
  Platform,      // Untuk deteksi platform (iOS/Android)
  StatusBar,     // Untuk mengatur bilah status
  ImageBackground, // Latar belakang dengan gambar
  Alert,         // Untuk menampilkan dialog peringatan
} from 'react-native';

// Area aman untuk menghindari notch dan status bar
import { SafeAreaView } from 'react-native-safe-area-context';

// Pustaka ikon dari Expo
import { Ionicons } from '@expo/vector-icons';

// Hook navigasi untuk berpindah antar layar
import { useNavigation } from '@react-navigation/native';

// Komponen gradien untuk latar belakang gradien
import { LinearGradient } from 'expo-linear-gradient';

// Komponen efek blur untuk efek kabur
import { BlurView } from 'expo-blur';

// Layanan untuk data produksi dan dashboard
import { productionService, DashboardStats, ProductionOverview } from '../services/productionService';
import CachedProductionService from '../services/CachedProductionService';
import SmartCacheManager from '../services/SmartCacheManager';

// Konteks untuk autentikasi dan tema
import { useAuth } from '../contexts/AuthContext';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';

// Layanan untuk mengelola header dashboard
import DashboardHeaderService from '../services/DashboardHeaderService';

// ===== ANTARMUKA & TIPE DATA =====
// Antarmuka untuk mendefinisikan struktur item menu di Aksi Cepat
interface UltraMenuItem {
  id: string;        // Pengenal unik untuk setiap item menu
  title: string;     // Nama/judul yang ditampilkan di menu
  icon: string;      // Nama ikon dari Ionicons
  bgColor: string;   // Warna latar belakang untuk kontainer ikon
  onPress: () => void; // Fungsi yang dipanggil ketika menu di-ketuk
}

// ===== KOMPONEN UTAMA DASHBOARD =====
const DashboardScreen = () => {
  // ===== HOOK & KONTEKS =====
  const navigation = useNavigation();              // Hook untuk navigasi antar layar
  const { profile, user, isAdmin } = useAuth();   // Konteks untuk data pengguna dan autentikasi
  const { isDarkMode } = useTheme();               // Hook untuk deteksi mode gelap
  const colors = useThemeColors();                 // Hook untuk mendapatkan warna sesuai tema

  // ===== MANAJEMEN STATE =====
  const [currentImageIndex, setCurrentImageIndex] = useState(0); // Indeks gambar header yang sedang aktif
  // State untuk menyimpan statistik dashboard (metrik KPI)
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalProduction: 0,      // Total produksi tambang
    totalSites: 0,           // Jumlah total lokasi tambang
    activeEquipment: 0,      // Jumlah peralatan yang aktif
    averageEfficiency: 0,    // Rata-rata efisiensi operasional
    todayProduction: 0,      // Produksi hari ini
    weeklyProduction: 0,     // Produksi mingguan
    monthlyProduction: 0     // Produksi bulanan
  });

  // State untuk menyimpan ringkasan produksi tambang (OB, Ore, SR, FR)
  const [productionOverview, setProductionOverview] = useState<ProductionOverview>({
    // OB (Overburden) - Material penutup yang harus dibuang
    ob_plan_today: 0, ob_actual_today: 0, ob_actual_yesterday: 0, ob_achievement_percent: 0,
    // Ore - Bijih mineral yang ditambang
    ore_plan_today: 0, ore_actual_today: 0, ore_actual_yesterday: 0, ore_achievement_percent: 0,
    // SR (Stripping Ratio) - Rasio material buangan terhadap bijih
    sr_plan_today: 0, sr_actual_today: 0, sr_actual_yesterday: 0, sr_performance_percent: 0,
    // FR (Fleet Ratio) - Rasio kinerja armada peralatan
    fr_plan_today: 0, fr_actual_today: 0, fr_actual_yesterday: 0, fr_performance_percent: 0,
    // Metrik peralatan
    active_equipment: 0,     // Peralatan yang sedang beroperasi
    total_equipment: 0,      // Total peralatan yang tersedia
    overall_efficiency: 0    // Efisiensi keseluruhan operasi
  });

  const [loading, setLoading] = useState(true);        // State untuk indikator pemuatan
  const [refreshing, setRefreshing] = useState(false); // State untuk tarik-untuk-menyegarkan

  // ===== INISIALISASI LAYANAN =====
  // Inisialisasi layanan untuk caching data produksi (pola singleton)
  const cachedProductionService = CachedProductionService.getInstance();
  // Layanan untuk mengelola caching pintar dengan TTL dan pembatalan
  const cacheManager = SmartCacheManager.getInstance();
  // Layanan untuk mengelola gambar header dashboard
  const dashboardHeaderService = DashboardHeaderService.getInstance();

  // ===== FUNGSI PEMBANTU =====
  // Fungsi untuk mendapatkan gambar profil pengguna dengan prioritas cadangan
  const getUserProfileImage = () => {
    // Prioritas 1: Avatar yang diunggah pengguna (dari basis data)
    if (profile?.avatar_url) {
      return { uri: profile.avatar_url };
    }

    // Prioritas 2: Buat avatar berdasarkan nama pengguna
    if (profile?.full_name) {
      // Ambil inisial nama untuk cadangan (tidak digunakan di sini tapi bisa untuk fallback)
      const initials = profile.full_name
        .split(' ')                    // Pisahkan nama berdasarkan spasi
        .map(name => name.charAt(0))   // Ambil huruf pertama setiap kata
        .join('')                      // Gabungkan menjadi string
        .toUpperCase()                 // Ubah ke huruf besar
        .substring(0, 2);              // Ambil maksimal 2 huruf

      // Gunakan layanan UI Avatars untuk membuat avatar otomatis
      const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.full_name)}&size=200&background=2563eb&color=ffffff&bold=true&format=png`;
      return { uri: avatarUrl };
    }

    // Priority 3: Default avatar based on role
    const roleAvatars = {
      supervisor: 'https://randomuser.me/api/portraits/men/32.jpg',
      operator: 'https://randomuser.me/api/portraits/men/45.jpg',
      safety_officer: 'https://randomuser.me/api/portraits/women/68.jpg',
      maintenance_tech: 'https://randomuser.me/api/portraits/men/22.jpg',
      admin: 'https://randomuser.me/api/portraits/women/44.jpg'
    };

    const roleAvatar = profile?.departemen ? roleAvatars[profile.departemen] : null;
    if (roleAvatar) {
      return { uri: roleAvatar };
    }

    // Priority 4: Generic default avatar
    return { uri: 'https://randomuser.me/api/portraits/men/1.jpg' };
  };

  // Helper function to get user display name
  const getUserDisplayName = () => {
    if (profile?.full_name) {
      return profile.full_name;
    }
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name;
    }
    if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'User';
  };

  // Helper function to get greeting based on time
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  // Background images from database
  const [backgroundImages, setBackgroundImages] = useState<string[]>([
    'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80', // Default fallback
  ]);

  // Load header images from database
  const loadHeaderImages = async () => {
    try {
      console.log('🖼️ Loading dashboard header images...');
      const headerImages = await dashboardHeaderService.getDashboardHeaderImages();

      if (headerImages && headerImages.length > 0) {
        const imageUrls = headerImages.map(img => img.image_url);
        setBackgroundImages(imageUrls);
        console.log(`✅ Loaded ${imageUrls.length} header images from database`);
      } else {
        console.log('ℹ️ No header images found, using defaults');
      }
    } catch (error) {
      console.error('❌ Error loading header images:', error);
      // Keep default images on error
    }
  };

  // Rotate background images every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % backgroundImages.length
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [backgroundImages.length]);

  // Fetch dashboard data with caching
  const fetchDashboardData = async (forceRefresh: boolean = false) => {
    try {
      setLoading(true);
      console.log(`🔄 Fetching dashboard data... (Force refresh: ${forceRefresh})`);

      if (forceRefresh) {
        // Clear cache for fresh data
        await cacheManager.invalidate('dashboard');
        await cacheManager.invalidate('production_overview');
      }

      const [stats, overview] = await Promise.all([
        cachedProductionService.getDashboardStats(),
        cachedProductionService.getProductionOverview()
      ]);

      console.log('📊 Dashboard stats:', stats);
      console.log('🏭 Production overview:', overview);

      setDashboardStats(stats);
      setProductionOverview(overview);

      console.log('✅ Dashboard data loaded successfully (cached)');

      // Show cache metrics in development
      if (__DEV__) {
        const metrics = cacheManager.getMetrics();
        console.log(`📊 Cache metrics - Hit rate: ${metrics.hitRate.toFixed(1)}%, Size: ${(metrics.cacheSize/1024).toFixed(1)}KB`);
      }

    } catch (error) {
      console.error('❌ Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Pull to refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData(true); // Force refresh
  };

  useEffect(() => {
    fetchDashboardData();
    loadHeaderImages(); // Load header images from database

    // Preload critical data in background
    cachedProductionService.preloadCriticalData().catch(console.error);
  }, []);

  // Refresh header images when screen comes into focus
  // This ensures images update when user returns from header management
  useFocusEffect(
    React.useCallback(() => {
      console.log('🔄 Dashboard focused, refreshing header images...');
      // Clear cache and reload to get latest visibility changes
      dashboardHeaderService.clearCache().then(() => {
        loadHeaderImages();
      });
    }, [])
  );

  // ===== DATA MENU AKSI CEPAT =====
  // Konfigurasi item menu untuk Aksi Cepat (tata letak grid 5x2)
  // Setiap item memiliki ikon, warna, dan fungsi navigasi
  const ultraMenuItems: UltraMenuItem[] = [
    // Menu 1: Ringkasan Produksi - Menampilkan detail produksi tambang
    {
      id: '1',
      title: 'Production',
      icon: 'trending-up',           // Ikon tren naik untuk produksi
      bgColor: '#6366f1',            // Warna indigo untuk produksi
      onPress: () => navigation.navigate('ProductionOverview' as never)
    },
    // Menu 2: Manajemen Peralatan - Mengelola peralatan tambang
    {
      id: '2',
      title: 'Equipment',
      icon: 'hardware-chip',         // Ikon chip untuk peralatan
      bgColor: '#ec4899',            // Warna pink untuk peralatan
      onPress: () => navigation.navigate('Equipment' as never)
    },
    // Menu 3: Manajemen Keselamatan - Mengelola keselamatan kerja
    {
      id: '3',
      title: 'Safety',
      icon: 'shield-checkmark',      // Ikon perisai untuk keselamatan
      bgColor: '#06b6d4',            // Warna cyan untuk keselamatan
      onPress: () => navigation.navigate('Safety' as never)
    },
    // Menu 4: Integrasi SAP - Integrasi dengan sistem SAP
    {
      id: '4',
      title: 'SAP',
      icon: 'server',                // Ikon server untuk SAP
      bgColor: '#10b981',            // Warna emerald untuk SAP
      onPress: () => navigation.navigate('SAPIntegration' as never)
    },
    {
      id: '5',
      title: 'Attendance',
      icon: 'people-circle',
      bgColor: '#f59e0b',
      onPress: () => navigation.navigate('Attendance' as never)
    },
    // Second Row
    {
      id: '6',
      title: 'Reports',
      icon: 'stats-chart',
      bgColor: '#8b5cf6',
      onPress: () => navigation.navigate('Reports' as never)
    },
    {
      id: '7',
      title: 'Headers',
      icon: 'images',
      bgColor: '#ef4444',
      onPress: () => {
        // Check if user is admin before navigating
        if (isAdmin) {
          // Navigate to header management with source info
          (navigation as any).navigate('Profile', {
            screen: 'DashboardHeaderManagement',
            params: { source: 'Dashboard' }
          });
        } else {
          Alert.alert(
            'Access Denied',
            'This feature is only available for administrators.',
            [{ text: 'OK' }]
          );
        }
      }
    },
    {
      id: '8',
      title: 'Analytics',
      icon: 'analytics',
      bgColor: '#06d6a0',
      onPress: () => navigation.navigate('Analytics' as never)
    },
    {
      id: '9',
      title: 'Settings',
      icon: 'settings',
      bgColor: '#ffd60a',
      onPress: () => navigation.navigate('Profile' as never)
    },
    {
      id: '10',
      title: 'Help',
      icon: 'help-circle',
      bgColor: '#003566',
      onPress: () => (navigation as any).navigate('Profile', {
        screen: 'HelpSupport'
      })
    }
  ];

  // ===== DATA STATISTIK PRODUKSI =====
  // Fungsi pembantu untuk memformat angka (1000 -> 1.0K)
  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;  // Konversi ke format K (ribuan)
    }
    return num.toFixed(1);
  };

  const formatRatio = (num: number): string => {
    return num.toFixed(2);
  };

  const getChangeIndicator = (current: number, previous: number): string => {
    if (previous === 0) return '';
    const change = ((current - previous) / previous) * 100;
    return change > 0 ? `+${change.toFixed(1)}%` : `${change.toFixed(1)}%`;
  };

  const getChangeColor = (current: number, previous: number): string => {
    if (previous === 0) return '#666666';
    return current >= previous ? '#10b981' : '#ef4444';
  };

  const productionStatsData = [
    {
      title: 'OB Volume',
      plan: loading ? '...' : `${formatNumber(productionOverview.ob_plan_today)}`,
      actual: loading ? '...' : `${formatNumber(productionOverview.ob_actual_today)}`,
      achievement: loading ? '...' : `${productionOverview.ob_achievement_percent.toFixed(1)}`,
      change: loading ? '' : getChangeIndicator(productionOverview.ob_actual_today, productionOverview.ob_actual_yesterday),
      changeColor: getChangeColor(productionOverview.ob_actual_today, productionOverview.ob_actual_yesterday),
      icon: 'layers-outline',
      color: '#8b5cf6',
      unit: 'BCM'
    },
    {
      title: 'Ore Volume',
      plan: loading ? '...' : `${formatNumber(productionOverview.ore_plan_today)}`,
      actual: loading ? '...' : `${formatNumber(productionOverview.ore_actual_today)}`,
      achievement: loading ? '...' : `${productionOverview.ore_achievement_percent.toFixed(1)}`,
      change: loading ? '' : getChangeIndicator(productionOverview.ore_actual_today, productionOverview.ore_actual_yesterday),
      changeColor: getChangeColor(productionOverview.ore_actual_today, productionOverview.ore_actual_yesterday),
      icon: 'diamond-outline',
      color: '#f59e0b',
      unit: 'tons'
    },
    {
      title: 'Stripping Ratio',
      plan: loading ? '...' : formatRatio(productionOverview.sr_plan_today),
      actual: loading ? '...' : formatRatio(productionOverview.sr_actual_today),
      achievement: loading ? '...' : `${productionOverview.sr_performance_percent.toFixed(1)}`,
      change: loading ? '' : getChangeIndicator(productionOverview.sr_actual_today, productionOverview.sr_actual_yesterday),
      changeColor: getChangeColor(productionOverview.sr_actual_yesterday, productionOverview.sr_actual_today), // Lower is better for SR
      icon: 'analytics-outline',
      color: '#06b6d4',
      unit: 'ratio'
    },
    {
      title: 'Fuel Efficiency',
      plan: loading ? '...' : formatRatio(productionOverview.fr_plan_today),
      actual: loading ? '...' : formatRatio(productionOverview.fr_actual_today),
      achievement: loading ? '...' : `${productionOverview.fr_performance_percent.toFixed(1)}`,
      change: loading ? '' : getChangeIndicator(productionOverview.fr_actual_today, productionOverview.fr_actual_yesterday),
      changeColor: getChangeColor(productionOverview.fr_actual_yesterday, productionOverview.fr_actual_today), // Lower is better for FR
      icon: 'speedometer-outline',
      color: '#ef4444',
      unit: 'L/BCM'
    },
    {
      title: 'Equipment Status',
      plan: loading ? '...' : productionOverview.total_equipment.toString(),
      actual: loading ? '...' : productionOverview.active_equipment.toString(),
      achievement: loading ? '...' : `${((productionOverview.active_equipment / productionOverview.total_equipment) * 100).toFixed(1)}`,
      change: '',
      changeColor: '#10b981',
      icon: 'hardware-chip-outline',
      color: '#667eea',
      unit: 'units'
    },
    {
      title: 'Overall Efficiency',
      plan: '100.0',
      actual: loading ? '...' : `${productionOverview.overall_efficiency.toFixed(1)}`,
      achievement: loading ? '...' : `${productionOverview.overall_efficiency.toFixed(1)}`,
      change: '',
      changeColor: productionOverview.overall_efficiency >= 100 ? '#10b981' : '#ef4444',
      icon: 'trending-up-outline',
      color: '#10b981',
      unit: '%'
    }
  ];

  const renderUltraMenuItem = (item: UltraMenuItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.cleanMenuItem}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.cleanMenuCard, {
        backgroundColor: 'transparent', // Transparan - hanya icon dan text yang terlihat
        borderColor: 'transparent' // Hapus border
      }]}>
        <View style={styles.cleanMenuContent}>
          {/* Clean Icon */}
          <View style={[styles.cleanIconContainer, { backgroundColor: item.bgColor }]}>
            <Ionicons
              name={item.icon as any}
              size={30}
              color="#FFFFFF"
            />
          </View>

          {/* Clean Typography - Only add theme-aware color */}
          <Text style={[styles.cleanMenuTitle, { color: colors.textPrimary }]}>{item.title}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const getGradientColors = (baseColor: string): [string, string] => {
    const gradientMap: { [key: string]: [string, string] } = {
      '#667eea': ['#667eea', '#764ba2'],
      '#f093fb': ['#f093fb', '#f5576c'],
      '#4facfe': ['#4facfe', '#00f2fe'],
      '#43e97b': ['#43e97b', '#38f9d7'],
      '#fa709a': ['#fa709a', '#fee140'],
      '#a8edea': ['#a8edea', '#fed6e3'],
    };
    return gradientMap[baseColor] || [baseColor, baseColor];
  };

  const renderProductionCard = (item: any, index: number) => {
    const achievementPercent = parseFloat(item.achievement) || 0;
    const isPositive = achievementPercent >= 100;
    const progressWidth = Math.min(achievementPercent, 150); // Cap at 150% for visual purposes

    return (
      <BlurView key={index} intensity={20} style={[styles.modernProductionCard, { backgroundColor: isDarkMode ? 'rgba(15, 23, 42, 0.9)' : 'rgba(255, 255, 255, 0.98)' }]}>
        {/* Header with Icon and Change Indicator */}
        <View style={styles.modernCardHeader}>
          <View style={[styles.modernIconContainer, { backgroundColor: item.color + '15' }]}>
            <Ionicons name={item.icon} size={18} color={item.color} />
          </View>
          {item.change && (
            <View style={[styles.changeIndicatorBadge, {
              backgroundColor: item.changeColor + '15',
              borderColor: item.changeColor + '30'
            }]}>
              <Text style={[styles.changeIndicatorText, { color: item.changeColor }]}>
                {item.change}
              </Text>
            </View>
          )}
        </View>

        {/* Title */}
        <Text style={[styles.modernCardTitle, { color: colors.textPrimary }]}>{item.title}</Text>

        {/* Plan and Actual Values */}
        <View style={styles.modernMetricsContainer}>
          <View style={styles.modernMetricRow}>
            <Text style={[styles.modernMetricLabel, { color: colors.textSecondary }]}>Plan:</Text>
            <Text style={[styles.modernMetricValue, { color: colors.textPrimary }]}>{item.plan} {item.unit.toLowerCase()}</Text>
          </View>
          <View style={styles.modernMetricRow}>
            <Text style={[styles.modernMetricLabel, { color: colors.textSecondary }]}>Actual:</Text>
            <Text style={[styles.modernMetricValueActual, { color: colors.textPrimary }]}>{item.actual} {item.unit.toLowerCase()}</Text>
          </View>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View style={[
              styles.progressBarFill,
              {
                width: `${Math.min(progressWidth, 100)}%`,
                backgroundColor: isPositive ? '#10b981' : '#ef4444'
              }
            ]} />
          </View>
        </View>

        {/* Achievement Footer */}
        <View style={styles.modernCardFooter}>
          <Text style={styles.achievementText}>Target Achieved</Text>
          <Text style={[styles.achievementPercentage, {
            color: isPositive ? '#10b981' : '#ef4444'
          }]}>
            {achievementPercent.toFixed(1)}%
          </Text>
        </View>
      </BlurView>
    );
  };

  // ===== GAYA DINAMIS YANG SADAR TEMA =====
  // Membuat gaya yang dapat beradaptasi dengan tema gelap/terang
  const dynamicStyles = StyleSheet.create({
    // Kontainer utama layar dashboard
    container: {
      flex: 1,
      backgroundColor: colors.background,  // Warna latar belakang sesuai tema
    },
    // Pembungkus konten dengan radius sudut dan bayangan
    contentWrapper: {
      flex: 1,
      backgroundColor: colors.background,
      borderTopLeftRadius: 30,         // Radius sudut kiri atas
      borderTopRightRadius: 30,        // Radius sudut kanan atas
      marginTop: -30,                  // Overlay di atas header
      shadowColor: colors.cardShadow,  // Warna bayangan sesuai tema
      shadowOffset: { width: 0, height: -3 },
      shadowOpacity: isDarkMode ? 0.4 : 0.15,  // Opacity bayangan sesuai tema
      shadowRadius: 12,
      elevation: 8,                    // Elevasi untuk Android
      overflow: 'hidden',              // Penting untuk menerapkan border radius
    },
    // Konten utama dengan padding yang disesuaikan
    content: {
      flex: 1,
      paddingHorizontal: 24,           // Perlebar padding kanan kiri
      paddingTop: 15,                  // Kurangi paddingTop untuk menaikkan Aksi Cepat
      backgroundColor: 'transparent',  // Transparan karena wrapper sudah ada latar belakang
    },
    // Judul seksi dengan gaya yang konsisten
    sectionTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: colors.textPrimary,       // Warna teks utama sesuai tema
      marginBottom: 16,
    },
    statCard: {
      flex: 1,
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 16,
      marginHorizontal: 6,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    statValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center',
    },

    // Only keep essential dynamic styles for theme support
  });

  // ===== RENDER KOMPONEN UTAMA =====
  return (
    // Area aman untuk menghindari notch dan status bar
    <SafeAreaView style={dynamicStyles.container}>
      {/* Konfigurasi status bar */}
      <StatusBar
        barStyle={isDarkMode ? "light-content" : "light-content"}  // Konten terang untuk kedua tema
        backgroundColor="transparent"                               // Latar belakang transparan
        translucent                                                // Transparan penuh
      />

      {/* Header dengan latar belakang yang berputar */}
      <ImageBackground
        source={{ uri: backgroundImages[currentImageIndex] }}     // Gambar latar belakang dinamis
        style={styles.header}                                     // Gaya header
        imageStyle={styles.headerImage}                           // Gaya gambar header
      >
        {/* Overlay gelap di atas gambar header */}
        <View style={styles.headerOverlay}>
          <View style={styles.headerContent}>
            {/* Foto profil di sebelah kiri */}
            <View style={styles.profileSection}>
              <TouchableOpacity
                onPress={() => navigation.navigate('Profile' as never)}  // Navigasi ke halaman profil
                style={styles.profileImageContainer}
              >
                <Image
                  source={getUserProfileImage()}                         // Gambar profil dinamis
                  style={styles.profileImage}
                />
                {/* Indikator status online */}
                <View style={styles.onlineIndicator} />
              </TouchableOpacity>
            </View>

            {/* Ikon lonceng di sebelah kanan */}
            <TouchableOpacity style={styles.notificationButton}>
              <Ionicons name="notifications-outline" size={28} color="#FFF" />
            </TouchableOpacity>
          </View>

          {/* Teks sambutan */}
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>{getTimeBasedGreeting()}</Text>      {/* Sapaan berdasarkan waktu */}
            <Text style={styles.userNameText}>{getUserDisplayName()}</Text>       {/* Nama pengguna */}
            {profile?.departemen && (
              <Text style={styles.userRoleText}>
                {/* Format departemen: hapus underscore dan kapitalisasi */}
                {profile.departemen.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
            )}
          </View>
        </View>
      </ImageBackground>

      {/* Konten dengan pembungkus radius sudut */}
      <View style={dynamicStyles.contentWrapper}>
        <ScrollView style={dynamicStyles.content} showsVerticalScrollIndicator={false}>
        {/* Aksi Cepat - Grid 5x2 */}
        <View style={styles.quickActionsSection}>
          <Text style={dynamicStyles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {/* Render setiap item menu aksi cepat */}
            {ultraMenuItems.map(renderUltraMenuItem)}
          </View>
        </View>

        {/* Kartu Ringkasan Produksi */}
        <View style={styles.statsSection}>
          <Text style={dynamicStyles.sectionTitle}>Production Overview</Text>
          <View style={styles.productionGrid}>
            {/* Render setiap kartu data produksi */}
            {productionStatsData.map(renderProductionCard)}
          </View>
        </View>

        {/* Aktivitas Terkini */}
        <View style={styles.recentSection}>
          <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>Recent Activity</Text>
          <BlurView intensity={10} style={[styles.activityCard, { backgroundColor: isDarkMode ? 'rgba(15, 23, 42, 0.9)' : 'rgba(255, 255, 255, 0.9)' }]}>
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="checkmark-circle" size={20} color="#43e97b" />
              </View>
              <View style={styles.activityContent}>
                <Text style={[styles.activityTitle, { color: colors.textPrimary }]}>Equipment Maintenance Completed</Text>
                <Text style={[styles.activityTime, { color: colors.textSecondary }]}>2 hours ago</Text>
              </View>
            </View>
            <View style={[styles.activityItem, { borderBottomWidth: 0 }]}>
              <View style={styles.activityIcon}>
                <Ionicons name="trending-up" size={20} color="#667eea" />
              </View>
              <View style={styles.activityContent}>
                <Text style={[styles.activityTitle, { color: colors.textPrimary }]}>Production Target Achieved</Text>
                <Text style={[styles.activityTime, { color: colors.textSecondary }]}>4 hours ago</Text>
              </View>
            </View>
          </BlurView>
        </View>
      </ScrollView>
      </View>
    </SafeAreaView>
  );
};

// ===== GAYA STATIS =====
// Gaya yang tidak berubah berdasarkan tema (kebanyakan untuk layout dan ukuran)
const styles = StyleSheet.create({
  // Kontainer utama (akan diganti dengan dynamicStyles.container)
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',  // Akan diganti dengan warna tema dinamis
  },
  // Header dengan gambar latar belakang
  header: {
    height: 300,                 // Tinggi header tetap
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight + 20 : 50,  // Padding atas sesuai platform
    paddingHorizontal: 20,       // Padding horizontal
    paddingBottom: 120,          // Padding bawah untuk ruang konten
  },
  // Gaya gambar header
  headerImage: {
    borderBottomLeftRadius: 0,   // Tidak ada radius sudut kiri bawah
    borderBottomRightRadius: 0,  // Tidak ada radius sudut kanan bawah
  },
  // Overlay di atas gambar header
  headerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0)',  // Transparan (overlay akan ditambahkan via gradien)
    borderBottomLeftRadius: 25,           // Radius sudut kiri bawah
    borderBottomRightRadius: 25,          // Radius sudut kanan bawah
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingTop: 0,
    marginBottom: 10,
  },
  welcomeSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  welcomeText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
    marginBottom: 4,
  },
  userNameText: {
    fontSize: 28,
    color: '#FFFFFF',
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  userRoleText: {
    fontSize: 14,
    color: '#E5E7EB',
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
    marginTop: 2,
  },

  profileSection: {
    position: 'relative',
  },
  profileImageContainer: {
    position: 'relative',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 3,
    borderColor: '#FFF',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#10B981',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },

  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuContainer: {
    paddingHorizontal: 15,
    paddingVertical: 20,
  },

  // Removed conflicting content style - using dynamicStyles.content instead
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 16,
    marginLeft: 4,
  },
  statsSection: {
    paddingHorizontal: 0, // Remove padding karena sudah ada di content
    marginBottom: 28, // Kurangi margin bottom
    marginTop: 0, // Naikkan posisi - hapus marginTop
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  productionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statsCard: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  statsContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  statsTextContainer: {
    flex: 1,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: '800',
    color: '#1A1A1A',
    marginBottom: 2,
  },
  statsTitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  // Production Card Styles
  productionCard: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 6,
  },
  productionCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  productionIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  productionTitleContainer: {
    flex: 1,
  },
  productionTitle: {
    fontSize: 13,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 1,
  },
  productionSubtitle: {
    fontSize: 10,
    fontWeight: '500',
    color: '#666666',
    textTransform: 'uppercase',
    letterSpacing: 0.3,
  },
  productionUnit: {
    fontSize: 9,
    fontWeight: '600',
    color: '#888888',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    textAlign: 'center',
  },
  productionMetrics: {
    marginBottom: 12,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 11,
    fontWeight: '500',
    color: '#666666',
  },
  metricValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333333',
  },
  metricActual: {
    fontWeight: '700',
    color: '#1A1A1A',
  },
  productionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  achievementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementLabel: {
    fontSize: 10,
    fontWeight: '500',
    color: '#666666',
    marginRight: 4,
  },
  achievementValue: {
    fontSize: 12,
    fontWeight: '700',
  },
  changeIndicator: {
    fontSize: 10,
    fontWeight: '600',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    backgroundColor: 'rgba(255,255,255,0.8)',
  },

  // Modern Production Card Styles (Following Design Reference)
  modernProductionCard: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.4)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  modernCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  modernIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  changeIndicatorBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    borderWidth: 1,
  },
  changeIndicatorText: {
    fontSize: 11,
    fontWeight: '600',
  },
  modernCardTitle: {
    fontSize: 16, // Increased untuk professional mining operations
    fontWeight: '800', // Extra bold untuk mining data
    color: '#1A1A1A',
    marginBottom: 18, // Increased margin
    textAlign: 'center', // Center alignment untuk professional look
  },
  modernMetricsContainer: {
    marginBottom: 16,
  },
  modernMetricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  modernMetricLabel: {
    fontSize: 14, // Increased untuk readability
    fontWeight: '600', // Bolder untuk professional mining
    color: '#666666',
  },
  modernMetricValue: {
    fontSize: 14, // Increased untuk better visibility
    fontWeight: '700', // Bolder untuk mining data
    color: '#333333',
  },
  modernMetricValueActual: {
    fontSize: 14, // Increased untuk emphasis
    fontWeight: '800', // Extra bold untuk actual values
    color: '#1A1A1A',
  },
  progressBarContainer: {
    marginBottom: 12,
  },
  progressBarBackground: {
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  modernCardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  achievementText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
  },
  achievementPercentage: {
    fontSize: 14,
    fontWeight: '700',
  },

  quickActionsSection: {
    paddingHorizontal: 0, // Remove padding karena sudah ada di content
    marginBottom: 20, // Kurangi margin untuk spacing yang lebih rapat
    marginTop: 0, // Naikkan posisi - hapus marginTop
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingVertical: 10,
  },
  modernMenuScroll: {
    paddingLeft: 10,
  },
  modernMenuScrollContent: {
    paddingRight: 10,
    alignItems: 'center',
  },
  menuScrollView: {
    paddingVertical: 10,
  },
  menuScrollContent: {
    paddingHorizontal: 10,
    paddingRight: 30,
    alignItems: 'center',
  },
  cleanMenuItem: {
    width: '18%', // 5 columns with spacing
    marginBottom: 15,
    alignItems: 'center',
  },
  cleanMenuCard: {
    backgroundColor: 'transparent', // Transparan untuk Quick Actions
    borderRadius: 12,
    padding: 0,
    overflow: 'hidden',
    borderWidth: 0, // Hapus border
    borderColor: 'transparent', // Transparan border
  },
  cleanMenuContent: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: 6,
    minHeight: 80,
    justifyContent: 'center',
  },
  cleanIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8, // Increased margin untuk spacing yang lebih baik
    // Hapus shadow untuk tampilan transparan yang clean
  },
  cleanMenuTitle: {
    fontSize: 10,
    fontWeight: '600',
    color: '#1A1A1A', // Will be overridden by theme-aware color in render
    textAlign: 'center',
    letterSpacing: -0.2,
    lineHeight: 10,
  },

  recentSection: {
    paddingHorizontal: 0, // Remove padding karena sudah ada di content
    marginBottom: 28, // Kurangi margin untuk konsistensi spacing
  },
  activityCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  activityIcon: {
    width: 50,
    height: 50,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '400',
  }
});

export default DashboardScreen;
