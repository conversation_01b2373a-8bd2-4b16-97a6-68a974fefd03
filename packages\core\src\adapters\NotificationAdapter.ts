import { NotificationConfig, ScheduledNotificationConfig } from '../types';

/**
 * Abstract Notification Adapter Interface
 * Platform-specific implementations should extend this class
 */
export abstract class NotificationAdapter {
  protected isEnabled: boolean = true;
  protected defaultDuration: number = 5000; // 5 seconds

  /**
   * Enable or disable notifications
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Set default notification duration
   */
  setDefaultDuration(duration: number): void {
    this.defaultDuration = duration;
  }

  /**
   * Show immediate notification
   */
  abstract show(notification: NotificationConfig): Promise<void>;

  /**
   * Schedule notification for later
   */
  abstract schedule(notification: ScheduledNotificationConfig): Promise<string>;

  /**
   * Cancel scheduled notification
   */
  abstract cancel(id: string): Promise<void>;

  /**
   * Clear all notifications
   */
  abstract clearAll(): Promise<void>;

  /**
   * Show info notification
   */
  async showInfo(title: string, message: string, duration?: number): Promise<void> {
    if (!this.isEnabled) return;

    await this.show({
      title,
      message,
      type: 'info',
      duration: duration || this.defaultDuration
    });
  }

  /**
   * Show success notification
   */
  async showSuccess(title: string, message: string, duration?: number): Promise<void> {
    if (!this.isEnabled) return;

    await this.show({
      title,
      message,
      type: 'success',
      duration: duration || this.defaultDuration
    });
  }

  /**
   * Show warning notification
   */
  async showWarning(title: string, message: string, duration?: number): Promise<void> {
    if (!this.isEnabled) return;

    await this.show({
      title,
      message,
      type: 'warning',
      duration: duration || this.defaultDuration
    });
  }

  /**
   * Show error notification
   */
  async showError(title: string, message: string, duration?: number): Promise<void> {
    if (!this.isEnabled) return;

    await this.show({
      title,
      message,
      type: 'error',
      duration: duration || this.defaultDuration
    });
  }

  /**
   * Show production update notification
   */
  async showProductionUpdate(
    metric: string,
    value: number,
    unit: string,
    achievement?: number
  ): Promise<void> {
    if (!this.isEnabled) return;

    const achievementText = achievement ? ` (${achievement.toFixed(1)}% of target)` : '';
    
    await this.showInfo(
      'Production Update',
      `${metric}: ${value.toLocaleString()} ${unit}${achievementText}`
    );
  }

  /**
   * Show sync status notification
   */
  async showSyncStatus(status: 'syncing' | 'synced' | 'failed', details?: string): Promise<void> {
    if (!this.isEnabled) return;

    switch (status) {
      case 'syncing':
        await this.showInfo('Sync Status', 'Synchronizing data...', 2000);
        break;
      case 'synced':
        await this.showSuccess('Sync Status', 'All data synchronized', 3000);
        break;
      case 'failed':
        await this.showError('Sync Status', details || 'Synchronization failed');
        break;
    }
  }

  /**
   * Show equipment alert
   */
  async showEquipmentAlert(
    equipmentName: string,
    status: 'maintenance' | 'repair' | 'down',
    message?: string
  ): Promise<void> {
    if (!this.isEnabled) return;

    const statusMessages = {
      maintenance: 'scheduled for maintenance',
      repair: 'requires repair',
      down: 'is currently down'
    };

    const title = 'Equipment Alert';
    const defaultMessage = `${equipmentName} ${statusMessages[status]}`;
    
    await this.showWarning(title, message || defaultMessage);
  }

  /**
   * Show safety alert
   */
  async showSafetyAlert(message: string, severity: 'low' | 'medium' | 'high' | 'critical'): Promise<void> {
    if (!this.isEnabled) return;

    const title = `Safety Alert - ${severity.toUpperCase()}`;
    
    switch (severity) {
      case 'low':
      case 'medium':
        await this.showWarning(title, message);
        break;
      case 'high':
      case 'critical':
        await this.showError(title, message, 10000); // Longer duration for critical alerts
        break;
    }
  }

  /**
   * Schedule daily production reminder
   */
  async scheduleDailyReminder(time: Date, message: string): Promise<string> {
    return this.schedule({
      title: 'Daily Production Reminder',
      message,
      type: 'info',
      scheduledTime: time,
      repeat: 'daily'
    });
  }

  /**
   * Schedule maintenance reminder
   */
  async scheduleMaintenanceReminder(
    equipmentName: string,
    scheduledDate: Date,
    hoursBeforeAlert: number = 24
  ): Promise<string> {
    const alertTime = new Date(scheduledDate.getTime() - (hoursBeforeAlert * 60 * 60 * 1000));
    
    return this.schedule({
      title: 'Maintenance Reminder',
      message: `${equipmentName} maintenance scheduled in ${hoursBeforeAlert} hours`,
      type: 'warning',
      scheduledTime: alertTime
    });
  }

  /**
   * Validate notification config
   */
  protected validateNotificationConfig(config: NotificationConfig): void {
    if (!config.title || config.title.trim().length === 0) {
      throw new Error('Notification title is required');
    }

    if (!config.message || config.message.trim().length === 0) {
      throw new Error('Notification message is required');
    }

    if (config.duration && config.duration < 0) {
      throw new Error('Notification duration must be positive');
    }
  }

  /**
   * Validate scheduled notification config
   */
  protected validateScheduledNotificationConfig(config: ScheduledNotificationConfig): void {
    this.validateNotificationConfig(config);

    if (!config.scheduledTime || !(config.scheduledTime instanceof Date)) {
      throw new Error('Scheduled time must be a valid Date object');
    }

    if (config.scheduledTime <= new Date()) {
      throw new Error('Scheduled time must be in the future');
    }
  }

  /**
   * Generate unique notification ID
   */
  protected generateNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Format notification message
   */
  protected formatMessage(message: string, maxLength: number = 200): string {
    if (message.length <= maxLength) {
      return message;
    }
    
    return message.substring(0, maxLength - 3) + '...';
  }

  /**
   * Get notification icon based on type
   */
  protected getNotificationIcon(type: 'info' | 'success' | 'warning' | 'error'): string {
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    
    return icons[type] || icons.info;
  }
}
