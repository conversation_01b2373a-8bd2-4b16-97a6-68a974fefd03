import DatabaseOnlyService from './DatabaseOnlyService';
import { ProductionDataItem } from '../components/charts/ChartDataProcessor';
import { TimePeriod } from '../models/Production';

export interface ProductionDataFilter {
  startDate?: string;
  endDate?: string;
  locationId?: string;
  timePeriod?: TimePeriod;
}

export interface ProductionMetrics {
  totalMaterial: number;
  totalOverburden: number;
  totalOre: number;
  totalFuel: number;
  avgStrippingRatio: number;
  avgFuelRatio: number;
  operationalDays: number;
}

export class ProductionDataService {
  private static instance: ProductionDataService;
  private databaseService: DatabaseOnlyService;

  private constructor() {
    this.databaseService = DatabaseOnlyService.getInstance();
  }

  static getInstance(): ProductionDataService {
    if (!ProductionDataService.instance) {
      ProductionDataService.instance = new ProductionDataService();
    }
    return ProductionDataService.instance;
  }

  // Initialize service and test database connection
  async initialize(): Promise<boolean> {
    try {
      console.log('🏭 Initializing Production Data Service (Database Only)...');

      // Test database connection
      const connectionOk = await this.databaseService.testConnection();

      if (connectionOk) {
        console.log('✅ Production Data Service initialized successfully');
        return true;
      } else {
        console.log('⚠️ Production Data Service initialized with limited connectivity');
        return false;
      }
    } catch (error) {
      console.error('❌ Failed to initialize Production Data Service:', error);
      return false;
    }
  }

  // Get production data from database only
  async getProductionData(filter?: ProductionDataFilter): Promise<ProductionDataItem[]> {
    try {
      // Get data directly from database
      const data = await this.databaseService.getProductionData(
        filter?.startDate,
        filter?.endDate
      );

      // Apply additional filters if provided
      let filteredData = data as any[];
      if (filter) {
        filteredData = this.applyFilters(data as any[], filter);
      }

      console.log(`📊 Retrieved ${filteredData.length} production records from database`);
      return filteredData;
    } catch (error) {
      console.error('❌ Failed to get production data from database:', error);
      return [];
    }
  }

  // Get production data for specific date range
  async getProductionDataByDateRange(startDate: string, endDate: string): Promise<ProductionDataItem[]> {
    console.log(`📅 Fetching production data for range: ${startDate} to ${endDate}`);
    return this.databaseService.getProductionDataByDateRange(startDate, endDate);
  }

  // Get production data for specific time period
  async getProductionDataByPeriod(timePeriod: TimePeriod): Promise<ProductionDataItem[]> {
    const filter = this.getDateRangeForPeriod(timePeriod);
    return this.getProductionData(filter);
  }

  // Calculate production metrics
  async getProductionMetrics(filter?: ProductionDataFilter): Promise<ProductionMetrics> {
    const data = await this.getProductionData(filter);
    
    if (data.length === 0) {
      return {
        totalMaterial: 0,
        totalOverburden: 0,
        totalOre: 0,
        totalFuel: 0,
        avgStrippingRatio: 0,
        avgFuelRatio: 0,
        operationalDays: 0,
      };
    }

    const totalOverburden = data.reduce((sum, item) => sum + (item.actual_ob || 0), 0);
    const totalOre = data.reduce((sum, item) => sum + (item.actual_ore || 0), 0);
    const totalFuel = data.reduce((sum, item) => sum + (item.actual_fuel || 0), 0);
    const totalMaterial = data.reduce((sum, item) => sum + (item.total_material || 0), 0);
    
    const validRatioData = data.filter(item => item.stripping_ratio && item.fuel_ratio);
    const avgStrippingRatio = validRatioData.length > 0 
      ? validRatioData.reduce((sum, item) => sum + item.stripping_ratio!, 0) / validRatioData.length
      : 0;
    const avgFuelRatio = validRatioData.length > 0
      ? validRatioData.reduce((sum, item) => sum + item.fuel_ratio!, 0) / validRatioData.length
      : 0;

    return {
      totalMaterial,
      totalOverburden,
      totalOre,
      totalFuel,
      avgStrippingRatio,
      avgFuelRatio,
      operationalDays: data.length,
    };
  }

  // Get data summary from database
  async getDataSummary() {
    try {
      // return await this.databaseService.getDataSummary(); // Method not implemented yet
      return {
        totalRecords: 0,
        avgProduction: 0,
        avgEfficiency: 0
      };
    } catch (error) {
      console.error('❌ Failed to get data summary:', error);
      return {
        totalRecords: 0,
        dateRange: { start: '', end: '' },
        lastUpdated: new Date().toISOString(),
      };
    }
  }

  // Get sync status (simplified for database-only mode)
  async getSyncStatus() {
    const connectionOk = await this.databaseService.testConnection();
    return {
      isOnline: connectionOk,
      isSyncing: false,
      lastSync: Date.now(),
      queueSize: 0,
    };
  }

  // Private helper methods
  private applyFilters(data: ProductionDataItem[], filter: ProductionDataFilter): ProductionDataItem[] {
    let filteredData = [...data];

    if (filter.startDate) {
      filteredData = filteredData.filter(item => item.date >= filter.startDate!);
    }

    if (filter.endDate) {
      filteredData = filteredData.filter(item => item.date <= filter.endDate!);
    }

    if (filter.locationId) {
      filteredData = filteredData.filter(item => (item as any).location_id === filter.locationId);
    }

    return filteredData;
  }

  private getDateRangeForPeriod(timePeriod: TimePeriod): ProductionDataFilter {
    const now = new Date();
    const filter: ProductionDataFilter = { timePeriod };

    switch (timePeriod) {
      case 'Daily':
        // Last 30 days
        const thirtyDaysAgo = new Date(now);
        thirtyDaysAgo.setDate(now.getDate() - 30);
        filter.startDate = thirtyDaysAgo.toISOString().split('T')[0];
        filter.endDate = now.toISOString().split('T')[0];
        break;
      
      case 'Weekly':
        // Last 12 weeks
        const twelveWeeksAgo = new Date(now);
        twelveWeeksAgo.setDate(now.getDate() - (12 * 7));
        filter.startDate = twelveWeeksAgo.toISOString().split('T')[0];
        filter.endDate = now.toISOString().split('T')[0];
        break;
      
      case 'Monthly':
        // Last 12 months
        const twelveMonthsAgo = new Date(now);
        twelveMonthsAgo.setMonth(now.getMonth() - 12);
        filter.startDate = twelveMonthsAgo.toISOString().split('T')[0];
        filter.endDate = now.toISOString().split('T')[0];
        break;
      
      case 'Yearly':
        // Last 3 years
        const threeYearsAgo = new Date(now);
        threeYearsAgo.setFullYear(now.getFullYear() - 3);
        filter.startDate = threeYearsAgo.toISOString().split('T')[0];
        filter.endDate = now.toISOString().split('T')[0];
        break;
    }

    return filter;
  }
}

export default ProductionDataService;
