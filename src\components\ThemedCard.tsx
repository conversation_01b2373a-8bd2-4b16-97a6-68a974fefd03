import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { Layout } from '../constants/layout';

interface ThemedCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'small' | 'medium' | 'large';
}

const ThemedCard: React.FC<ThemedCardProps> = ({ 
  children, 
  style, 
  variant = 'default',
  padding = 'medium'
}) => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();

  const getPaddingValue = () => {
    switch (padding) {
      case 'none': return 0;
      case 'small': return Layout.spacing.sm;
      case 'medium': return Layout.spacing.md;
      case 'large': return Layout.spacing.lg;
      default: return Layout.spacing.md;
    }
  };

  const getCardStyle = () => {
    const baseStyle = {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.lg,
      padding: getPaddingValue(),
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          shadowColor: colors.cardShadow,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: isDarkMode ? 0.4 : 0.15,
          shadowRadius: 8,
          elevation: 6,
          borderWidth: isDarkMode ? 1 : 0,
          borderColor: colors.cardBorder,
        };
      
      case 'outlined':
        return {
          ...baseStyle,
          borderWidth: 1,
          borderColor: colors.cardBorder,
          shadowColor: colors.cardShadow,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: isDarkMode ? 0.2 : 0.05,
          shadowRadius: 2,
          elevation: 1,
        };
      
      default:
        return {
          ...baseStyle,
          shadowColor: colors.cardShadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: isDarkMode ? 0.3 : 0.1,
          shadowRadius: 4,
          elevation: 3,
          borderWidth: isDarkMode ? 1 : 0,
          borderColor: colors.cardBorder,
        };
    }
  };

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
};

export default ThemedCard;
