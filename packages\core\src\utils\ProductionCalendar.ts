// Production Calendar Utility
// Handles production calendar logic where months may start on different dates

export interface ProductionMonth {
  name: string;           // e.g., "July 2025"
  startDate: string;      // e.g., "2025-06-30" (ISO date string)
  endDate: string;        // e.g., "2025-07-29" (ISO date string)
  year: number;
  month: number;          // 1-12
}

// Production calendar configuration
// In a real application, this would come from a database or configuration service
const PRODUCTION_CALENDAR_2025: ProductionMonth[] = [
  {
    name: "January 2025",
    startDate: "2024-12-30",
    endDate: "2025-01-29",
    year: 2025,
    month: 1
  },
  {
    name: "February 2025", 
    startDate: "2025-01-30",
    endDate: "2025-02-26",
    year: 2025,
    month: 2
  },
  {
    name: "March 2025",
    startDate: "2025-02-27",
    endDate: "2025-03-29",
    year: 2025,
    month: 3
  },
  {
    name: "April 2025",
    startDate: "2025-03-30",
    endDate: "2025-04-29",
    year: 2025,
    month: 4
  },
  {
    name: "May 2025",
    startDate: "2025-04-30",
    endDate: "2025-05-29",
    year: 2025,
    month: 5
  },
  {
    name: "June 2025",
    startDate: "2025-05-30",
    endDate: "2025-06-29",
    year: 2025,
    month: 6
  },
  {
    name: "July 2025",
    startDate: "2025-06-30",
    endDate: "2025-07-29",
    year: 2025,
    month: 7
  },
  {
    name: "August 2025",
    startDate: "2025-07-30",
    endDate: "2025-08-29",
    year: 2025,
    month: 8
  },
  {
    name: "September 2025",
    startDate: "2025-08-30",
    endDate: "2025-09-29",
    year: 2025,
    month: 9
  },
  {
    name: "October 2025",
    startDate: "2025-09-30",
    endDate: "2025-10-29",
    year: 2025,
    month: 10
  },
  {
    name: "November 2025",
    startDate: "2025-10-30",
    endDate: "2025-11-29",
    year: 2025,
    month: 11
  },
  {
    name: "December 2025",
    startDate: "2025-11-30",
    endDate: "2025-12-29",
    year: 2025,
    month: 12
  }
];

/**
 * Production Calendar utility class
 */
export class ProductionCalendar {
  private static calendar: ProductionMonth[] = PRODUCTION_CALENDAR_2025;

  /**
   * Get production month for a given date
   */
  static getProductionMonth(date: string | Date): ProductionMonth | null {
    const dateStr = typeof date === 'string' ? date : date.toISOString().split('T')[0];
    
    return this.calendar.find(month => 
      dateStr >= month.startDate && dateStr <= month.endDate
    ) || null;
  }

  /**
   * Get production month name for a date
   */
  static getProductionMonthName(date: string | Date): string {
    const month = this.getProductionMonth(date);
    return month ? month.name : 'Unknown Month';
  }

  /**
   * Get all production months for a year
   */
  static getProductionYear(year: number): ProductionMonth[] {
    return this.calendar.filter(month => month.year === year);
  }

  /**
   * Get current production month
   */
  static getCurrentProductionMonth(): ProductionMonth | null {
    return this.getProductionMonth(new Date());
  }

  /**
   * Get production week number for a date
   */
  static getProductionWeek(date: string | Date): number {
    const dateStr = typeof date === 'string' ? date : date.toISOString().split('T')[0];
    const targetDate = new Date(dateStr);
    
    // Find the production month
    const month = this.getProductionMonth(dateStr);
    if (!month) return 1;
    
    // Calculate week within the production month
    const monthStart = new Date(month.startDate);
    const daysDiff = Math.floor((targetDate.getTime() - monthStart.getTime()) / (1000 * 60 * 60 * 24));
    
    return Math.floor(daysDiff / 7) + 1;
  }

  /**
   * Get date range for a production month
   */
  static getMonthDateRange(year: number, month: number): { startDate: string; endDate: string } | null {
    const prodMonth = this.calendar.find(m => m.year === year && m.month === month);
    if (!prodMonth) return null;
    
    return {
      startDate: prodMonth.startDate,
      endDate: prodMonth.endDate
    };
  }

  /**
   * Check if a date is within a production month
   */
  static isDateInProductionMonth(date: string | Date, year: number, month: number): boolean {
    const dateRange = this.getMonthDateRange(year, month);
    if (!dateRange) return false;
    
    const dateStr = typeof date === 'string' ? date : date.toISOString().split('T')[0];
    return dateStr >= dateRange.startDate && dateStr <= dateRange.endDate;
  }

  /**
   * Get production days in a month
   */
  static getProductionDaysInMonth(year: number, month: number): number {
    const dateRange = this.getMonthDateRange(year, month);
    if (!dateRange) return 0;
    
    const startDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    
    return Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  }

  /**
   * Get all dates in a production month
   */
  static getDatesInProductionMonth(year: number, month: number): string[] {
    const dateRange = this.getMonthDateRange(year, month);
    if (!dateRange) return [];
    
    const dates: string[] = [];
    const currentDate = new Date(dateRange.startDate);
    const endDate = new Date(dateRange.endDate);
    
    while (currentDate <= endDate) {
      dates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return dates;
  }

  /**
   * Get previous production month
   */
  static getPreviousProductionMonth(year: number, month: number): ProductionMonth | null {
    const currentIndex = this.calendar.findIndex(m => m.year === year && m.month === month);
    if (currentIndex <= 0) return null;
    
    return this.calendar[currentIndex - 1];
  }

  /**
   * Get next production month
   */
  static getNextProductionMonth(year: number, month: number): ProductionMonth | null {
    const currentIndex = this.calendar.findIndex(m => m.year === year && m.month === month);
    if (currentIndex === -1 || currentIndex >= this.calendar.length - 1) return null;
    
    return this.calendar[currentIndex + 1];
  }

  /**
   * Format production month for display
   */
  static formatProductionMonth(year: number, month: number): string {
    const prodMonth = this.calendar.find(m => m.year === year && m.month === month);
    return prodMonth ? prodMonth.name : `${year}-${month.toString().padStart(2, '0')}`;
  }

  /**
   * Set custom production calendar
   */
  static setProductionCalendar(calendar: ProductionMonth[]): void {
    this.calendar = calendar.sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return a.month - b.month;
    });
  }

  /**
   * Get full production calendar
   */
  static getProductionCalendar(): ProductionMonth[] {
    return [...this.calendar];
  }
}
