{"name": "@mining-ops/core", "version": "1.0.0", "description": "Core business logic and shared utilities for Mining Operations Platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["mining", "operations", "production", "core", "business-logic"], "author": "Mining Operations Team", "license": "MIT", "dependencies": {"crypto-js": "^4.2.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.5.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "files": ["dist", "README.md"], "publishConfig": {"access": "restricted"}}