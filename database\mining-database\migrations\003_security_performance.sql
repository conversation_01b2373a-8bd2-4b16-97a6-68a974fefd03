-- =====================================================
-- Mining Operations Database - Migration 003
-- =====================================================
-- Migration: 003_security_performance.sql
-- Description: Row Level Security policies and performance optimization
-- Version: 1.0
-- Date: 2024-01-20
-- Dependencies: 002_equipment_safety.sql
-- =====================================================

-- Check if previous migration was applied
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM schema_migrations WHERE version = '002') THEN
        RAISE EXCEPTION 'Migration 002 must be applied before running migration 003';
    END IF;
END $$;

-- Record this migration start
INSERT INTO schema_migrations (version, description) 
VALUES ('003', 'Row Level Security policies and performance optimization')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- EXECUTE SECURITY AND PERFORMANCE MODULES
-- =====================================================

DO $$
DECLARE
    start_time TIMESTAMPTZ := NOW();
    end_time TIMESTAMPTZ;
    execution_time INTEGER;
BEGIN
    RAISE NOTICE 'Starting Migration 003: Security and Performance';
    RAISE NOTICE 'Start time: %', start_time;
    
    -- 1. Row Level Security Policies
    RAISE NOTICE 'Executing 09-rls-policies.sql...';
    \i ../09-rls-policies.sql
    
    -- 2. Performance Indexes and Optimization
    RAISE NOTICE 'Executing 10-indexes-performance.sql...';
    \i ../10-indexes-performance.sql
    
    -- Calculate execution time
    end_time := NOW();
    execution_time := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    -- Update migration record
    UPDATE schema_migrations 
    SET 
        applied_at = end_time,
        checksum = md5(random()::text),
        execution_time_ms = execution_time
    WHERE version = '003';
    
    RAISE NOTICE 'Migration 003 completed successfully';
    RAISE NOTICE 'End time: %', end_time;
    RAISE NOTICE 'Execution time: % ms', execution_time;
END $$;

-- =====================================================
-- ADDITIONAL RLS POLICIES FOR IUT/OTT TABLES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE iut_inspections ENABLE ROW LEVEL SECURITY;
ALTER TABLE ott_observations ENABLE ROW LEVEL SECURITY;

-- IUT Inspections RLS Policies
CREATE POLICY iut_inspections_select ON iut_inspections FOR SELECT USING (
    inspector_id = auth.uid() OR
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    (get_current_user_role() = 'Shift Supervisor' AND user_can_access_location(location))
);

CREATE POLICY iut_inspections_insert ON iut_inspections FOR INSERT WITH CHECK (
    inspector_id = auth.uid() AND
    (user_has_permission(auth.uid(), 'safety.inspect', location) OR
     get_current_user_role() IN ('Safety Officer', 'Shift Supervisor'))
);

CREATE POLICY iut_inspections_update ON iut_inspections FOR UPDATE USING (
    inspector_id = auth.uid() OR
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer')
);

-- OTT Observations RLS Policies
CREATE POLICY ott_observations_select ON ott_observations FOR SELECT USING (
    observer_id = auth.uid() OR
    observed_person_id = auth.uid() OR
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    (get_current_user_role() = 'Shift Supervisor' AND user_can_access_location(location))
);

CREATE POLICY ott_observations_insert ON ott_observations FOR INSERT WITH CHECK (
    observer_id = auth.uid() AND
    (user_has_permission(auth.uid(), 'safety.observe', location) OR
     get_current_user_role() IN ('Safety Officer', 'Shift Supervisor'))
);

CREATE POLICY ott_observations_update ON ott_observations FOR UPDATE USING (
    observer_id = auth.uid() OR
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer')
);

-- =====================================================
-- ADDITIONAL PERFORMANCE INDEXES
-- =====================================================

-- IUT/OTT specific indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_iut_inspections_compliance_low 
ON iut_inspections(inspection_date, overall_compliance_rate) 
WHERE overall_compliance_rate < 90;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ott_observations_unsafe_behaviors 
ON ott_observations(observation_date, safety_compliance_rate) 
WHERE unsafe_behaviors_count > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mining_certifications_iut_ott 
ON mining_certifications(certificate_type, valid_until) 
WHERE certificate_type IN ('IUT', 'OTT');

-- =====================================================
-- CREATE VIEWS FOR IUT/OTT REPORTING
-- =====================================================

-- IUT Inspection Summary View
CREATE VIEW v_iut_inspection_summary AS
SELECT 
    up.full_name as inspector_name,
    up.employee_id,
    ii.inspection_date,
    ii.location,
    e.equipment_number,
    ii.inspection_type,
    ii.total_items_checked,
    ii.compliant_items,
    ii.non_compliant_items,
    ii.critical_findings,
    ii.overall_compliance_rate,
    ii.follow_up_required,
    ii.status,
    CASE 
        WHEN ii.overall_compliance_rate >= 95 THEN 'Excellent'
        WHEN ii.overall_compliance_rate >= 85 THEN 'Good'
        WHEN ii.overall_compliance_rate >= 70 THEN 'Satisfactory'
        ELSE 'Needs Improvement'
    END as compliance_rating
FROM iut_inspections ii
JOIN user_profiles up ON ii.inspector_id = up.id
LEFT JOIN equipment e ON ii.equipment_id = e.id
ORDER BY ii.inspection_date DESC;

-- OTT Observation Summary View
CREATE VIEW v_ott_observation_summary AS
SELECT 
    observer.full_name as observer_name,
    observer.employee_id as observer_employee_id,
    observed.full_name as observed_person_name,
    observed.employee_id as observed_employee_id,
    oo.observation_date,
    oo.location,
    oo.work_area,
    oo.observed_task,
    oo.safe_behaviors_count,
    oo.unsafe_behaviors_count,
    oo.total_behaviors_observed,
    oo.safety_compliance_rate,
    oo.feedback_provided,
    oo.coaching_required,
    oo.training_recommended,
    CASE 
        WHEN oo.safety_compliance_rate = 100 THEN 'Excellent'
        WHEN oo.safety_compliance_rate >= 90 THEN 'Good'
        WHEN oo.safety_compliance_rate >= 80 THEN 'Satisfactory'
        ELSE 'Needs Improvement'
    END as safety_rating
FROM ott_observations oo
JOIN user_profiles observer ON oo.observer_id = observer.id
LEFT JOIN user_profiles observed ON oo.observed_person_id = observed.id
ORDER BY oo.observation_date DESC;

-- Combined IUT/OTT Dashboard View
CREATE VIEW v_iut_ott_dashboard AS
SELECT 
    'IUT' as activity_type,
    ii.inspection_date as activity_date,
    ii.location,
    up.full_name as responsible_person,
    ii.overall_compliance_rate as compliance_rate,
    ii.critical_findings as critical_issues,
    ii.follow_up_required,
    ii.status
FROM iut_inspections ii
JOIN user_profiles up ON ii.inspector_id = up.id

UNION ALL

SELECT 
    'OTT' as activity_type,
    oo.observation_date as activity_date,
    oo.location,
    up.full_name as responsible_person,
    oo.safety_compliance_rate as compliance_rate,
    oo.unsafe_behaviors_count as critical_issues,
    oo.coaching_required as follow_up_required,
    oo.status
FROM ott_observations oo
JOIN user_profiles up ON oo.observer_id = up.id

ORDER BY activity_date DESC;

-- =====================================================
-- UPDATE SAFETY METRICS TO INCLUDE IUT/OTT
-- =====================================================

-- Add IUT/OTT columns to safety_metrics table
ALTER TABLE safety_metrics 
ADD COLUMN IF NOT EXISTS iut_inspections_completed INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS iut_average_compliance_rate DECIMAL(5, 2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS iut_critical_findings INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS ott_observations_completed INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS ott_average_safety_rate DECIMAL(5, 2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS ott_unsafe_behaviors_identified INTEGER DEFAULT 0;

-- =====================================================
-- CREATE FUNCTIONS FOR IUT/OTT REPORTING
-- =====================================================

-- Function to calculate IUT compliance trends
CREATE OR REPLACE FUNCTION get_iut_compliance_trend(
    location_param VARCHAR(200),
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    inspection_date DATE,
    inspections_count INTEGER,
    average_compliance_rate DECIMAL(5, 2),
    critical_findings_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ii.inspection_date,
        COUNT(*)::INTEGER as inspections_count,
        AVG(ii.overall_compliance_rate)::DECIMAL(5, 2) as average_compliance_rate,
        SUM(ii.critical_findings)::INTEGER as critical_findings_count
    FROM iut_inspections ii
    WHERE ii.location = location_param
    AND ii.inspection_date >= CURRENT_DATE - days_back
    GROUP BY ii.inspection_date
    ORDER BY ii.inspection_date DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate OTT safety trends
CREATE OR REPLACE FUNCTION get_ott_safety_trend(
    location_param VARCHAR(200),
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    observation_date DATE,
    observations_count INTEGER,
    average_safety_rate DECIMAL(5, 2),
    unsafe_behaviors_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        oo.observation_date,
        COUNT(*)::INTEGER as observations_count,
        AVG(oo.safety_compliance_rate)::DECIMAL(5, 2) as average_safety_rate,
        SUM(oo.unsafe_behaviors_count)::INTEGER as unsafe_behaviors_count
    FROM ott_observations oo
    WHERE oo.location = location_param
    AND oo.observation_date >= CURRENT_DATE - days_back
    GROUP BY oo.observation_date
    ORDER BY oo.observation_date DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- UPDATE RLS DISABLE/ENABLE FUNCTIONS
-- =====================================================

-- Update disable RLS function to include new tables
CREATE OR REPLACE FUNCTION disable_rls_for_maintenance()
RETURNS void AS $$
BEGIN
    -- Only Super Admin can disable RLS
    IF get_current_user_role() != 'Super Admin' THEN
        RAISE EXCEPTION 'Only Super Admin can disable RLS';
    END IF;
    
    -- Disable RLS on all tables (including new ones)
    ALTER TABLE daily_mining_report DISABLE ROW LEVEL SECURITY;
    ALTER TABLE production_calendar DISABLE ROW LEVEL SECURITY;
    ALTER TABLE production_targets_calendar DISABLE ROW LEVEL SECURITY;
    ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
    ALTER TABLE user_permissions DISABLE ROW LEVEL SECURITY;
    ALTER TABLE user_sessions DISABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment DISABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment_metrics DISABLE ROW LEVEL SECURITY;
    ALTER TABLE maintenance_schedules DISABLE ROW LEVEL SECURITY;
    ALTER TABLE work_orders DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_incidents DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_inspections DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training_attendees DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_metrics DISABLE ROW LEVEL SECURITY;
    ALTER TABLE mining_certifications DISABLE ROW LEVEL SECURITY;
    ALTER TABLE certificate_renewals DISABLE ROW LEVEL SECURITY;
    ALTER TABLE iut_inspections DISABLE ROW LEVEL SECURITY;
    ALTER TABLE ott_observations DISABLE ROW LEVEL SECURITY;
    ALTER TABLE audit_logs DISABLE ROW LEVEL SECURITY;
    ALTER TABLE app_settings DISABLE ROW LEVEL SECURITY;
    
    RAISE NOTICE 'RLS disabled for maintenance (including IUT/OTT tables)';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update enable RLS function to include new tables
CREATE OR REPLACE FUNCTION enable_rls_after_maintenance()
RETURNS void AS $$
BEGIN
    -- Only Super Admin can enable RLS
    IF get_current_user_role() != 'Super Admin' THEN
        RAISE EXCEPTION 'Only Super Admin can enable RLS';
    END IF;
    
    -- Re-enable RLS on all tables (including new ones)
    ALTER TABLE daily_mining_report ENABLE ROW LEVEL SECURITY;
    ALTER TABLE production_calendar ENABLE ROW LEVEL SECURITY;
    ALTER TABLE production_targets_calendar ENABLE ROW LEVEL SECURITY;
    ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE maintenance_schedules ENABLE ROW LEVEL SECURITY;
    ALTER TABLE work_orders ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_incidents ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_inspections ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training_attendees ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE mining_certifications ENABLE ROW LEVEL SECURITY;
    ALTER TABLE certificate_renewals ENABLE ROW LEVEL SECURITY;
    ALTER TABLE iut_inspections ENABLE ROW LEVEL SECURITY;
    ALTER TABLE ott_observations ENABLE ROW LEVEL SECURITY;
    ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
    ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;
    
    RAISE NOTICE 'RLS re-enabled after maintenance (including IUT/OTT tables)';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    rls_enabled_count INTEGER;
    policy_count INTEGER;
    index_count INTEGER;
    view_count INTEGER;
BEGIN
    -- Count RLS enabled tables
    SELECT COUNT(*) INTO rls_enabled_count
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' 
    AND c.relrowsecurity = true;
    
    -- Count policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE schemaname = 'public';
    
    -- Count indexes
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes
    WHERE schemaname = 'public';
    
    -- Count views
    SELECT COUNT(*) INTO view_count
    FROM information_schema.views
    WHERE table_schema = 'public';
    
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MIGRATION 003 VERIFICATION';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Tables with RLS enabled: %', rls_enabled_count;
    RAISE NOTICE 'Total RLS policies: %', policy_count;
    RAISE NOTICE 'Total indexes: %', index_count;
    RAISE NOTICE 'Total views: %', view_count;
    RAISE NOTICE 'IUT/OTT tables secured: %', 
        CASE WHEN EXISTS(SELECT 1 FROM pg_policies WHERE tablename IN ('iut_inspections', 'ott_observations'))
        THEN 'Yes' ELSE 'No' END;
    RAISE NOTICE '================================================';
END $$;
