-- =====================================================
-- Create Demo Admin User for Testing
-- Run this after creating the user via Supabase Auth UI
-- =====================================================

-- Step 1: Create admin user via Supabase Auth UI first
-- Email: <EMAIL>
-- Password: Admin123!
-- Then get the user ID and replace it below

-- Step 2: Insert admin profile (replace USER_ID with actual UUID from auth.users)
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    employee_id,
    nik,
    departemen,
    jabatan,
    is_active,
    created_at,
    updated_at
) VALUES (
    'REPLACE_WITH_ACTUAL_USER_ID', -- Get this from Supabase Auth > Users
    '<EMAIL>',
    'System Administrator',
    '+62-812-3456-7890',
    'ADMIN001',
    '3201234567890001',
    'Administration',
    'System Administrator',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    full_name = EXCLUDED.full_name,
    updated_at = NOW();

-- Step 3: Create additional demo admin users
-- Manager User
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    employee_id,
    nik,
    departemen,
    jabatan,
    is_active,
    created_at,
    updated_at
) VALUES (
    'REPLACE_WITH_MANAGER_USER_ID', -- Get this from Supabase Auth > Users
    '<EMAIL>',
    'Operations Manager',
    '+62-812-2222-2222',
    'MGR001',
    '3202222222222222',
    'Management',
    'Operations Manager',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    full_name = EXCLUDED.full_name,
    updated_at = NOW();

-- IT Admin User
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    employee_id,
    nik,
    departemen,
    jabatan,
    is_active,
    created_at,
    updated_at
) VALUES (
    'REPLACE_WITH_IT_ADMIN_USER_ID', -- Get this from Supabase Auth > Users
    '<EMAIL>',
    'IT Administrator',
    '+62-812-3333-3333',
    'IT001',
    '3203333333333333',
    'IT',
    'IT Administrator',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    full_name = EXCLUDED.full_name,
    updated_at = NOW();

-- Step 4: Verify admin users
SELECT 
    'Admin Users Created:' AS status,
    id,
    email,
    full_name,
    departemen,
    jabatan,
    is_user_admin(id) AS is_admin_detected
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
ORDER BY email;

-- Step 5: Test admin functionality
SELECT 'Testing admin functions:' AS test_section;

-- Test get_admin_users function
SELECT 'get_admin_users() result:' AS function_test;
SELECT * FROM get_admin_users();

-- Test dashboard header images access
SELECT 'Dashboard header images:' AS images_test;
SELECT 
    id,
    title,
    display_order,
    is_active
FROM dashboard_header_images
ORDER BY display_order;

-- Success message
SELECT 
    '✅ Demo admin users setup complete!' AS message,
    'You can now login with:' AS instruction,
    '<EMAIL> / Admin123!' AS credentials1,
    '<EMAIL> / Manager123!' AS credentials2,
    '<EMAIL> / ITAdmin123!' AS credentials3;
