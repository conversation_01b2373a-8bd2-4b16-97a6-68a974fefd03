import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions, Easing } from 'react-native';
import { Colors } from '../../constants/colors';

interface Particle {
  id: number;
  x: Animated.Value;
  y: Animated.Value;
  opacity: Animated.Value;
  scale: Animated.Value;
}

interface ParticleEffectProps {
  particleCount?: number;
  duration?: number;
  colors?: string[];
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ParticleEffect: React.FC<ParticleEffectProps> = ({
  particleCount = 8,
  duration = 3000,
  colors = [Colors.primary, Colors.secondary, Colors.accent, Colors.warning],
}) => {
  const particles = useRef<Particle[]>([]);

  useEffect(() => {
    // Initialize particles
    particles.current = Array.from({ length: particleCount }, (_, index) => ({
      id: index,
      x: new Animated.Value(Math.random() * screenWidth),
      y: new Animated.Value(Math.random() * 200), // Limit to content area
      opacity: new Animated.Value(0),
      scale: new Animated.Value(0),
    }));

    // Start particle animations
    const animateParticles = () => {
      const animations = particles.current.map((particle) => {
        return Animated.loop(
          Animated.sequence([
            // Enhanced fade in and scale up
            Animated.parallel([
              Animated.timing(particle.opacity, {
                toValue: 0.4,
                duration: duration * 0.3,
                easing: Easing.out(Easing.quad),
                useNativeDriver: true,
              }),
              Animated.spring(particle.scale, {
                toValue: 1,
                tension: 120,
                friction: 10,
                useNativeDriver: true,
              }),
            ]),
            // Enhanced float and fade out
            Animated.parallel([
              Animated.timing(particle.y, {
                toValue: -120,
                duration: duration * 0.5,
                easing: Easing.out(Easing.cubic),
                useNativeDriver: true,
              }),
              Animated.timing(particle.opacity, {
                toValue: 0,
                duration: duration * 0.3,
                delay: duration * 0.3,
                easing: Easing.in(Easing.quad),
                useNativeDriver: true,
              }),
            ]),
            // Reset position
            Animated.timing(particle.y, {
              toValue: Math.random() * 200,
              duration: 0,
              useNativeDriver: true,
            }),
          ])
        );
      });

      Animated.stagger(200, animations).start();
    };

    animateParticles();

    return () => {
      particles.current.forEach((particle) => {
        particle.x.stopAnimation();
        particle.y.stopAnimation();
        particle.opacity.stopAnimation();
        particle.scale.stopAnimation();
      });
    };
  }, [particleCount, duration]);

  return (
    <View style={styles.container} pointerEvents="none">
      {particles.current.map((particle, index) => (
        <Animated.View
          key={particle.id}
          style={[
            styles.particle,
            {
              backgroundColor: colors[index % colors.length],
              transform: [
                { translateX: particle.x },
                { translateY: particle.y },
                { scale: particle.scale },
              ],
              opacity: particle.opacity,
            },
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  particle: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
  },
});

export default ParticleEffect;
