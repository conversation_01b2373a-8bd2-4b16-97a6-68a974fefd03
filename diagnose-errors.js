const fs = require('fs');
const path = require('path');

console.log('🔍 Mining Operations App - Error Diagnosis Tool');
console.log('===============================================');

// Check package.json dependencies
function checkDependencies() {
  console.log('\n📦 Checking dependencies...');
  
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ package.json not found');
    return;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  const requiredDeps = [
    'expo-device',
    'expo-battery', 
    'expo-location',
    'expo-sharing',
    'expo-image-picker',
    'expo-file-system',
    'buffer',
    'react-native-get-random-values'
  ];
  
  const missing = [];
  const present = [];
  
  requiredDeps.forEach(dep => {
    if (dependencies[dep]) {
      present.push(`✅ ${dep}@${dependencies[dep]}`);
    } else {
      missing.push(`❌ ${dep} - MISSING`);
    }
  });
  
  console.log('\n✅ Present dependencies:');
  present.forEach(dep => console.log(`  ${dep}`));
  
  if (missing.length > 0) {
    console.log('\n❌ Missing dependencies:');
    missing.forEach(dep => console.log(`  ${dep}`));
  } else {
    console.log('\n✅ All required dependencies are present');
  }
}

// Check file structure
function checkFileStructure() {
  console.log('\n📁 Checking file structure...');
  
  const requiredFiles = [
    'src/utils/polyfills.ts',
    'src/services/CachedProductionService.ts',
    'src/services/MobileEnhancementService.ts',
    'src/services/productionService.ts',
    'src/services/DatabaseOnlyService.ts'
  ];
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(path.join(__dirname, file))) {
      console.log(`  ✅ ${file}`);
    } else {
      console.log(`  ❌ ${file} - MISSING`);
    }
  });
}

// Check for common issues in files
function checkFileContents() {
  console.log('\n🔍 Checking file contents for common issues...');
  
  // Check if polyfills are imported in App.tsx
  const appTsxPath = path.join(__dirname, 'App.tsx');
  if (fs.existsSync(appTsxPath)) {
    const appContent = fs.readFileSync(appTsxPath, 'utf8');
    if (appContent.includes("import './src/utils/polyfills'")) {
      console.log('  ✅ Polyfills imported in App.tsx');
    } else {
      console.log('  ❌ Polyfills NOT imported in App.tsx');
    }
  }
  
  // Check CachedProductionService for Buffer usage
  const cachedServicePath = path.join(__dirname, 'src/services/CachedProductionService.ts');
  if (fs.existsSync(cachedServicePath)) {
    const content = fs.readFileSync(cachedServicePath, 'utf8');
    if (content.includes('Buffer.from')) {
      console.log('  ❌ CachedProductionService still uses Buffer.from');
    } else if (content.includes('simpleHash')) {
      console.log('  ✅ CachedProductionService uses simpleHash instead of Buffer');
    } else {
      console.log('  ⚠️  CachedProductionService hash method unclear');
    }
    
    if (content.includes('productionService.getDashboardStats')) {
      console.log('  ✅ CachedProductionService uses correct service import');
    } else {
      console.log('  ❌ CachedProductionService may have incorrect service import');
    }
  }
  
  // Check MobileEnhancementService for conditional imports
  const mobileServicePath = path.join(__dirname, 'src/services/MobileEnhancementService.ts');
  if (fs.existsSync(mobileServicePath)) {
    const content = fs.readFileSync(mobileServicePath, 'utf8');
    if (content.includes('require(\'expo-device\')')) {
      console.log('  ✅ MobileEnhancementService uses conditional imports');
    } else {
      console.log('  ❌ MobileEnhancementService may have direct imports causing errors');
    }
  }
}

// Check node_modules
function checkNodeModules() {
  console.log('\n📚 Checking node_modules...');
  
  const nodeModulesPath = path.join(__dirname, 'node_modules');
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('  ❌ node_modules directory not found - run npm install');
    return;
  }
  
  const expoModules = [
    'expo-device',
    'expo-battery',
    'expo-location',
    'expo-sharing'
  ];
  
  expoModules.forEach(module => {
    const modulePath = path.join(nodeModulesPath, module);
    if (fs.existsSync(modulePath)) {
      console.log(`  ✅ ${module} installed`);
    } else {
      console.log(`  ❌ ${module} NOT installed`);
    }
  });
}

// Generate fix recommendations
function generateRecommendations() {
  console.log('\n💡 Recommendations:');
  
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  const recommendations = [];
  
  if (!dependencies['expo-device']) {
    recommendations.push('Install missing Expo dependencies: npx expo install expo-device expo-battery expo-location expo-sharing');
  }
  
  if (!dependencies['buffer']) {
    recommendations.push('Install polyfills: npm install buffer react-native-get-random-values');
  }
  
  const appTsxPath = path.join(__dirname, 'App.tsx');
  if (fs.existsSync(appTsxPath)) {
    const appContent = fs.readFileSync(appTsxPath, 'utf8');
    if (!appContent.includes("import './src/utils/polyfills'")) {
      recommendations.push('Add polyfill import to App.tsx: import \'./src/utils/polyfills\';');
    }
  }
  
  if (!fs.existsSync(path.join(__dirname, 'src/utils/polyfills.ts'))) {
    recommendations.push('Create polyfills.ts file in src/utils/');
  }
  
  if (recommendations.length === 0) {
    console.log('  ✅ No immediate issues detected');
    console.log('  💡 If errors persist:');
    console.log('    1. Clear Metro cache: npm start -- --reset-cache');
    console.log('    2. Clear node_modules: rm -rf node_modules && npm install');
    console.log('    3. Restart development server');
  } else {
    recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
  }
}

// Run all checks
function runDiagnosis() {
  checkDependencies();
  checkFileStructure();
  checkFileContents();
  checkNodeModules();
  generateRecommendations();
  
  console.log('\n🎯 Quick Fix Command:');
  console.log('  bash fix-all-errors.sh');
  console.log('\n📋 Manual Steps:');
  console.log('  1. node update-package-json.js');
  console.log('  2. npm install');
  console.log('  3. npx expo install --fix');
  console.log('  4. npm start -- --reset-cache');
}

// Run the diagnosis
runDiagnosis();
