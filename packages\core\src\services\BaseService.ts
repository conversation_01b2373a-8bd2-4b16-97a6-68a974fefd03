import { StorageAdapter, NetworkAdapter, NotificationAdapter } from '../types';
import { Logger } from '../utils/Logger';
import { ServiceContainer } from '../utils/ServiceContainer';

export abstract class BaseService {
  protected logger: Logger;
  protected container: ServiceContainer;

  constructor() {
    this.logger = Logger.getInstance();
    this.container = ServiceContainer.getInstance();
  }

  /**
   * Get storage adapter from container
   */
  protected getStorage(): StorageAdapter {
    return this.container.resolve<StorageAdapter>('storage');
  }

  /**
   * Get network adapter from container
   */
  protected getNetwork(): NetworkAdapter {
    return this.container.resolve<NetworkAdapter>('network');
  }

  /**
   * Get notification adapter from container
   */
  protected getNotifications(): NotificationAdapter {
    return this.container.resolve<NotificationAdapter>('notifications');
  }

  /**
   * Get repository from container
   */
  protected getRepository(name: string): any {
    return this.container.resolve(`${name}Repository`);
  }

  /**
   * Handle service errors consistently
   */
  protected handleError(error: any, context: string): never {
    this.logger.error(`${context}: ${error.message}`, error);
    throw error;
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(params: Record<string, any>, requiredFields: string[]): void {
    const missing = requiredFields.filter(field => 
      params[field] === undefined || params[field] === null || params[field] === ''
    );

    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  /**
   * Sanitize data for storage/transmission
   */
  protected sanitizeData(data: any): any {
    if (data === null || data === undefined) {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item));
    }

    if (typeof data === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        // Remove undefined values
        if (value !== undefined) {
          sanitized[key] = this.sanitizeData(value);
        }
      }
      return sanitized;
    }

    return data;
  }

  /**
   * Create standardized response
   */
  protected createResponse<T>(data: T, message?: string): { data: T; message?: string; timestamp: string } {
    return {
      data,
      message,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Create standardized error response
   */
  protected createErrorResponse(error: Error, context?: string): { error: string; context?: string; timestamp: string } {
    return {
      error: error.message,
      context,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Retry operation with exponential backoff
   */
  protected async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          break;
        }

        const delay = baseDelay * Math.pow(2, attempt);
        this.logger.warn(`Operation failed, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`, error);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Execute operation with timeout
   */
  protected async withTimeout<T>(
    operation: Promise<T>,
    timeoutMs: number,
    timeoutMessage: string = 'Operation timed out'
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs);
    });

    return Promise.race([operation, timeoutPromise]);
  }

  /**
   * Batch process items with concurrency control
   */
  protected async batchProcess<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    batchSize: number = 10,
    onProgress?: (processed: number, total: number) => void
  ): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map(processor));
      results.push(...batchResults);
      
      if (onProgress) {
        onProgress(Math.min(i + batchSize, items.length), items.length);
      }
    }

    return results;
  }

  /**
   * Debounce function calls
   */
  protected debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * Throttle function calls
   */
  protected throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0;
    
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func.apply(this, args);
      }
    };
  }

  /**
   * Cache operation results
   */
  protected async cached<T>(
    key: string,
    operation: () => Promise<T>,
    ttlMs: number = 300000 // 5 minutes default
  ): Promise<T> {
    const storage = this.getStorage();
    const cacheKey = `cache:${key}`;
    
    try {
      const cached = await storage.load(cacheKey);
      if (cached && cached.timestamp && (Date.now() - cached.timestamp) < ttlMs) {
        return cached.data;
      }
    } catch (error) {
      // Cache miss or error, continue to execute operation
    }

    const result = await operation();
    
    try {
      await storage.save(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
    } catch (error) {
      this.logger.warn('Failed to cache result', error);
    }

    return result;
  }

  /**
   * Clear cache by pattern
   */
  protected async clearCache(pattern: string): Promise<void> {
    const storage = this.getStorage();
    
    try {
      const keys = await storage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(`cache:${pattern}`));
      
      await Promise.all(cacheKeys.map(key => storage.delete(key)));
      
      this.logger.info(`Cleared ${cacheKeys.length} cache entries matching pattern: ${pattern}`);
    } catch (error) {
      this.logger.warn('Failed to clear cache', error);
    }
  }
}
