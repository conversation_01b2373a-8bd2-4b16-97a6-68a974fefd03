import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Animated } from 'react-native';
import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color = Colors.primary,
  text = 'Loading...'
}) => (
  <View style={styles.spinnerContainer}>
    <ActivityIndicator size={size} color={color} />
    {text && <Text style={styles.loadingText}>{text}</Text>}
  </View>
);

interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style
}) => {
  const animatedValue = new Animated.Value(0);

  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();
    return () => animation.stop();
  }, []);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
          opacity,
        },
        style,
      ]}
    />
  );
};

interface CardSkeletonProps {
  showTitle?: boolean;
  showSubtitle?: boolean;
  lines?: number;
}

export const CardSkeleton: React.FC<CardSkeletonProps> = ({
  showTitle = true,
  showSubtitle = false,
  lines = 3
}) => (
  <View style={styles.cardSkeleton}>
    {showTitle && <Skeleton height={24} style={{ marginBottom: 8 }} />}
    {showSubtitle && <Skeleton height={16} width="70%" style={{ marginBottom: 12 }} />}
    {Array.from({ length: lines }).map((_, index) => (
      <Skeleton
        key={index}
        height={14}
        width={index === lines - 1 ? '60%' : '100%'}
        style={{ marginBottom: 6 }}
      />
    ))}
  </View>
);

interface ChartSkeletonProps {
  height?: number;
}

export const ChartSkeleton: React.FC<ChartSkeletonProps> = ({ height = 220 }) => (
  <View style={styles.chartSkeletonContainer}>
    <Skeleton height={20} width="40%" style={{ marginBottom: 16, alignSelf: 'center' }} />
    <View style={[styles.chartSkeleton, { height }]}>
      <View style={styles.chartBars}>
        {Array.from({ length: 7 }).map((_, index) => (
          <Skeleton
            key={index}
            width={20}
            height={Math.random() * 100 + 50}
            style={{ marginHorizontal: 4 }}
          />
        ))}
      </View>
    </View>
    <View style={styles.chartLegend}>
      <Skeleton height={12} width={80} style={{ marginHorizontal: 8 }} />
      <Skeleton height={12} width={100} style={{ marginHorizontal: 8 }} />
    </View>
  </View>
);

interface MetricCardSkeletonProps {
  count?: number;
}

export const MetricCardSkeleton: React.FC<MetricCardSkeletonProps> = ({ count = 3 }) => (
  <View style={styles.metricsContainer}>
    {Array.from({ length: count }).map((_, index) => (
      <View key={index} style={styles.metricCardSkeleton}>
        <Skeleton height={16} width="60%" style={{ marginBottom: 8 }} />
        <Skeleton height={32} width="80%" style={{ marginBottom: 4 }} />
        <Skeleton height={12} width="40%" />
      </View>
    ))}
  </View>
);

interface FullScreenLoadingProps {
  text?: string;
  subtext?: string;
}

export const FullScreenLoading: React.FC<FullScreenLoadingProps> = ({
  text = 'Loading...',
  subtext
}) => (
  <View style={styles.fullScreenContainer}>
    <ActivityIndicator size="large" color={Colors.primary} />
    <Text style={styles.fullScreenText}>{text}</Text>
    {subtext && <Text style={styles.fullScreenSubtext}>{subtext}</Text>}
  </View>
);

interface PullToRefreshLoadingProps {
  isRefreshing: boolean;
  onRefresh: () => void;
  children: React.ReactNode;
}

export const PullToRefreshLoading: React.FC<PullToRefreshLoadingProps> = ({
  isRefreshing,
  onRefresh,
  children
}) => (
  <View style={styles.pullToRefreshContainer}>
    {children}
  </View>
);

const styles = StyleSheet.create({
  spinnerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.lg,
  },
  loadingText: {
    marginTop: Layout.spacing.sm,
    fontSize: 14,
    color: Colors.textSecondary,
  },
  skeleton: {
    backgroundColor: Colors.border,
  },
  cardSkeleton: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
    ...Layout.shadow,
  },
  chartSkeletonContainer: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.lg,
    ...Layout.shadow,
  },
  chartSkeleton: {
    borderRadius: Layout.borderRadius.sm,
    backgroundColor: Colors.background,
    padding: Layout.spacing.md,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  chartBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    width: '100%',
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: Layout.spacing.md,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Layout.spacing.lg,
  },
  metricCardSkeleton: {
    flex: 1,
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginHorizontal: Layout.spacing.xs,
    alignItems: 'center',
    ...Layout.shadow,
  },
  fullScreenContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: Layout.spacing.xl,
  },
  fullScreenText: {
    marginTop: Layout.spacing.lg,
    fontSize: 18,
    fontWeight: '600',
    color: Colors.textPrimary,
    textAlign: 'center',
  },
  fullScreenSubtext: {
    marginTop: Layout.spacing.sm,
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  pullToRefreshContainer: {
    flex: 1,
  },
});

export default {
  LoadingSpinner,
  Skeleton,
  CardSkeleton,
  ChartSkeleton,
  MetricCardSkeleton,
  FullScreenLoading,
  PullToRefreshLoading,
};
