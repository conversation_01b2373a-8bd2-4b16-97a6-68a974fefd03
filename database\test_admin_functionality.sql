-- =====================================================
-- Test Admin Functionality for Dashboard Header Management
-- Run these queries to test admin access and functionality
-- =====================================================

-- 1. Check if dashboard_header_images table exists
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_name = 'dashboard_header_images';

-- 2. Check table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'dashboard_header_images'
ORDER BY ordinal_position;

-- 3. Check if functions exist
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_name LIKE '%dashboard_header%'
OR routine_name = 'is_user_admin'
ORDER BY routine_name;

-- 4. Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'dashboard_header_images';

-- 5. Check default header images
SELECT 
    id,
    title,
    description,
    LEFT(image_url, 50) || '...' AS image_url_preview,
    display_order,
    is_active,
    created_at
FROM dashboard_header_images
ORDER BY display_order;

-- 6. Test get_dashboard_header_images function
SELECT 'Testing get_dashboard_header_images function:' AS test_name;
SELECT * FROM get_dashboard_header_images();

-- 7. Check current user (if logged in)
SELECT 
    auth.uid() AS current_user_id,
    auth.email() AS current_user_email;

-- 8. Check admin users
SELECT 'Current admin users:' AS info;
SELECT 
    id,
    email,
    full_name,
    departemen,
    jabatan,
    is_active
FROM users 
WHERE is_active = true
AND (
    departemen IN ('Administration', 'Management') 
    OR LOWER(jabatan) LIKE '%admin%'
    OR LOWER(jabatan) LIKE '%manager%'
)
ORDER BY full_name;

-- 9. Test admin function for current user (if logged in)
SELECT 
    'Admin check for current user:' AS info,
    is_user_admin() AS is_current_user_admin;

-- 10. Test admin function for specific users
SELECT 
    u.email,
    u.full_name,
    u.departemen,
    u.jabatan,
    is_user_admin(u.id) AS is_admin
FROM users u
WHERE u.is_active = true
ORDER BY is_user_admin(u.id) DESC, u.full_name;

-- 11. Test get_admin_users function
SELECT 'Testing get_admin_users function:' AS test_name;
SELECT * FROM get_admin_users();

-- 12. Count statistics
SELECT 
    'Dashboard Header Images Statistics:' AS info,
    COUNT(*) AS total_images,
    COUNT(*) FILTER (WHERE is_active = true) AS active_images,
    COUNT(*) FILTER (WHERE is_active = false) AS inactive_images
FROM dashboard_header_images;

-- 13. Test adding header image (will fail if not admin)
-- Uncomment to test (replace with actual values)
/*
SELECT add_dashboard_header_image(
    'Test Header Image',
    'This is a test header image',
    'https://example.com/test-image.jpg',
    NULL,
    NULL
) AS new_image_id;
*/

-- 14. Check storage bucket (if exists)
-- This might not work depending on your Supabase setup
/*
SELECT 
    name,
    id,
    created_at,
    updated_at
FROM storage.buckets 
WHERE name = 'profile-photos';
*/

-- 15. Sample queries for admin operations

-- Create a test admin user (replace with actual user ID)
/*
INSERT INTO users (
    id,
    email,
    full_name,
    departemen,
    jabatan,
    is_active
) VALUES (
    'test-admin-uuid-here',
    '<EMAIL>',
    'Test Administrator',
    'Administration',
    'Test Administrator',
    true
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    updated_at = NOW();
*/

-- Test toggle image status (will fail if not admin)
/*
SELECT toggle_dashboard_header_image_status(
    (SELECT id FROM dashboard_header_images LIMIT 1),
    false
) AS toggle_result;
*/

-- Test update display order (will fail if not admin)
/*
SELECT update_dashboard_header_image_order(
    (SELECT id FROM dashboard_header_images LIMIT 1),
    99
) AS update_result;
*/

-- Test delete image (will fail if not admin)
/*
SELECT delete_dashboard_header_image(
    (SELECT id FROM dashboard_header_images WHERE title = 'Test Header Image' LIMIT 1)
) AS delete_result;
*/

-- 16. Verify permissions
SELECT 
    'Checking permissions...' AS info,
    has_table_privilege('dashboard_header_images', 'SELECT') AS can_select,
    has_function_privilege('get_dashboard_header_images()', 'EXECUTE') AS can_execute_get,
    has_function_privilege('is_user_admin(uuid)', 'EXECUTE') AS can_execute_admin_check;

-- 17. Final verification
SELECT 
    'Setup Verification:' AS status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM dashboard_header_images) THEN '✅ Table has data'
        ELSE '❌ Table is empty'
    END AS table_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_dashboard_header_images') THEN '✅ Functions exist'
        ELSE '❌ Functions missing'
    END AS functions_status,
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'dashboard_header_images') THEN '✅ RLS policies active'
        ELSE '❌ RLS policies missing'
    END AS security_status;

-- Success message
SELECT 
    '🎉 Dashboard Header Management Test Complete!' AS message,
    'Check the results above to verify everything is working correctly.' AS instruction;
