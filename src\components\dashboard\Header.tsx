import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Layout } from '../../constants/layout';

interface HeaderProps {
  profileImage: string;
  role: string;
  department: string;
  title: string;
  onNotificationPress: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  profileImage,
  role,
  department,
  title,
  onNotificationPress
}) => {
  return (
    <View style={styles.topHeaderBar}>
      <View style={styles.profileSection}>
        <View style={styles.profileImageContainer}>
          <Image
            source={{ uri: profileImage }}
            style={styles.profileImage}
          />
        </View>
        <View style={styles.profileInfo}>
          <Text style={styles.profileRole}>{role}</Text>
          <Text style={styles.profileDepartment}>{department}</Text>
        </View>
      </View>

      <View style={styles.titleSection}>
        <Text style={styles.headerTitle}>{title}</Text>
      </View>

      <View style={styles.notificationSection}>
        <TouchableOpacity 
          style={styles.notificationButton}
          onPress={onNotificationPress}
        >
          <Ionicons name="notifications-outline" size={24} color="#FF5252" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  topHeaderBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingBottom: Layout.spacing.xl,
    minHeight: 80,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileImageContainer: {
    marginRight: Layout.spacing.md,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: '#4DD0E1',
    resizeMode: 'cover',
  },
  profileInfo: {
    justifyContent: 'center',
  },
  profileRole: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E2E2E',
    marginBottom: 2,
  },
  profileDepartment: {
    fontSize: 14,
    color: '#2E2E2E',
    fontWeight: '400',
  },
  titleSection: {
    flex: 2,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E2E2E',
    textAlign: 'center',
  },
  notificationSection: {
    flex: 1,
    alignItems: 'flex-end',
  },
  notificationButton: {
    padding: Layout.spacing.sm,
  },
});
