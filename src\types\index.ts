// Type definitions for the Mining Operations App

export interface ProductionData {
  id: string;
  date: string;
  tonnage: number;
  units: number;
  shift: 'Day' | 'Night';
  location: string;
}

export interface Equipment {
  id: string;
  name: string;
  type: 'Excavator' | 'Truck' | 'Drill' | 'Loader' | 'Crusher';
  status: 'Active' | 'Maintenance' | 'Inactive' | 'Alert';
  location: string;
  lastMaintenance: string;
  nextMaintenance: string;
  operatingHours: number;
  fuelLevel?: number;
}

export interface SafetyAlert {
  id: string;
  type: 'Critical' | 'Warning' | 'Info';
  title: string;
  description: string;
  location: string;
  timestamp: string;
  status: 'Active' | 'Resolved' | 'In Progress';
  reportedBy: string;
}

export interface User {
  id: string;
  name: string;
  role: 'Supervisor' | 'Operator' | 'Manager' | 'Safety Officer';
  department: string;
  email: string;
  phone: string;
  avatar?: string;
}

export interface Report {
  id: string;
  title: string;
  type: 'Production' | 'Safety' | 'Equipment' | 'Incident';
  date: string;
  status: 'Draft' | 'Submitted' | 'Approved' | 'Rejected';
  createdBy: string;
  summary: string;
}

export interface DashboardStats {
  totalProduction: number;
  activeEquipment: number;
  safetyAlerts: number;
  efficiency: number;
  dailyTarget: number;
  monthlyTarget: number;
}

export interface ProductionMetric {
  id: string;
  name: string;
  icon: string;
  unit: string;
  plan: number;
  actual: number;
  achievementPercentage: number;
  trendPercentage: number;
  trendDirection: 'up' | 'down' | 'stable';
  isPositiveMetric: boolean; // true for production metrics, false for negative impacts
}

export interface ProductionData {
  date: string;
  overburdenVolume: number;
  oreVolume: number;
  rainImpact: number;
  slipperyConditions: number;
  fuelConsumption: number;
}

export interface ProductionPeriodData {
  daily: ProductionData[];
  weekly: ProductionData[];
  monthly: ProductionData[];
  yearly: ProductionData[];
}

export interface ChartDataPoint {
  x: string;
  y: number;
  label?: string;
}

// New interfaces for detailed production metrics
export interface DailyProductionMetrics {
  id: string;
  date: string;
  monthly: string;
  week: number;
  actual_ob: number;
  plan_ob: number;
  actual_ore: number;
  plan_ore: number;
  actual_rain: number;
  plan_rain: number;
  actual_slippery: number;
  plan_slippery: number;
  actual_fuel: number;
  plan_fuel: number;
  location_id: string;
  created_by: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Daily Production Metrics interface for the new detailed production tracking
export interface DailyProductionMetrics {
  id: string;
  date: string;
  monthly: string;
  week: number;
  actual_ob: number;
  plan_ob: number;
  actual_ore: number;
  plan_ore: number;
  actual_rain: number;
  plan_rain: number;
  actual_slippery: number;
  plan_slippery: number;
  actual_fuel: number;
  plan_fuel: number;
  location_id: string;
  notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  // Joined data from related tables
  location?: {
    name: string;
    location_type?: string;
  };
  creator?: {
    full_name: string;
    employee_id?: string;
  };
}

export interface ProductionMetricsInput {
  date: string;
  monthly: string;
  week: number;
  actual_ob: number;
  plan_ob: number;
  actual_ore: number;
  plan_ore: number;
  actual_rain: number;
  plan_rain: number;
  actual_slippery: number;
  plan_slippery: number;
  actual_fuel: number;
  plan_fuel: number;
  location_id: string;
  notes?: string;
}

// Individual metric value with plan vs actual comparison
export interface ProductionMetricValue {
  actual: number;
  target: number;
  unit: string;
  achievementPercentage: number;
  trend?: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
  };
}

export interface ProductionMetricsAggregated {
  overburdenVolume: ProductionMetricValue;
  oreVolume: ProductionMetricValue;
  rainImpact: ProductionMetricValue;
  slipperyConditions: ProductionMetricValue;
  fuelConsumption: ProductionMetricValue;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  dateRange: {
    startDate: string;
    endDate: string;
  };
  totalRecords: number;
}

// Enhanced Production Metric interface with plan vs actual comparison
export interface EnhancedProductionMetric {
  id: string;
  name: string;
  icon: string;
  unit: string;
  plan: number;
  actual: number;
  achievementPercentage: number;
  trendPercentage: number;
  trendDirection: 'up' | 'down' | 'stable';
  isPositiveMetric: boolean; // true for production metrics, false for negative impacts
  category: 'production' | 'weather' | 'efficiency';
}

export interface CSVProductionData {
  Monthly: string;
  Week: string;
  Date: string;
  'Actual OB': string;
  'Plan OB': string;
  'Actual ORE': string;
  'Plan ORE': string;
  'Actual Rain': string;
  'Plan Rain': string;
  'Actual Slippery': string;
  'Plan Slippery': string;
  'Actual Fuel': string;
  'Plan Fuel': string;
}
