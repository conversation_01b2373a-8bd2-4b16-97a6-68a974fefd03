import { TimePeriod } from '../../models/Production';
import MiningCalculationService from '../../services/MiningCalculationService';
import { ChartData, ChartDataPoint, ChartMetricConfig, MetricType } from './ChartConfig';

export interface ProductionDataItem {
  date: string;
  actual_ob: number;
  plan_ob: number;
  actual_ore: number;
  plan_ore: number;
  actual_fuel: number;
  plan_fuel: number;
  stripping_ratio?: number;
  fuel_ratio?: number;
  total_material?: number;
}

export class ChartDataProcessor {
  /**
   * Process raw production data into chart-ready format
   */
  static processProductionData(
    data: ProductionDataItem[],
    metric: ChartMetricConfig,
    timePeriod: TimePeriod
  ): ChartData {
    if (!data || data.length === 0) {
      return this.getEmptyChartData();
    }

    // Sort data by date
    const sortedData = [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    // Aggregate data based on time period
    const aggregatedData = this.aggregateDataByPeriod(sortedData, timePeriod);
    
    // Extract metric-specific data
    const chartPoints = this.extractMetricData(aggregatedData, metric);
    
    // Generate labels
    const labels = this.generateLabels(aggregatedData, timePeriod);
    
    // Create datasets
    const datasets = this.createDatasets(chartPoints, metric);
    
    return {
      labels,
      datasets,
      legend: datasets.map(d => d.label),
    };
  }

  /**
   * Aggregate data based on time period
   */
  private static aggregateDataByPeriod(
    data: ProductionDataItem[],
    timePeriod: TimePeriod
  ): ProductionDataItem[] {
    switch (timePeriod) {
      case 'Daily':
        return data; // No aggregation needed for daily
      
      case 'Weekly':
        return this.aggregateByWeek(data);
      
      case 'Monthly':
        return this.aggregateByMonth(data);
      
      case 'Yearly':
        return this.aggregateByYear(data);
      
      default:
        return data;
    }
  }

  /**
   * Aggregate data by week
   */
  private static aggregateByWeek(data: ProductionDataItem[]): ProductionDataItem[] {
    const weeklyData = new Map<string, ProductionDataItem[]>();
    
    data.forEach(item => {
      const date = new Date(item.date);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay()); // Start of week (Sunday)
      const weekKey = weekStart.toISOString().split('T')[0];
      
      if (!weeklyData.has(weekKey)) {
        weeklyData.set(weekKey, []);
      }
      weeklyData.get(weekKey)!.push(item);
    });

    return Array.from(weeklyData.entries()).map(([weekStart, items]) => 
      this.aggregateItems(items, weekStart)
    );
  }

  /**
   * Aggregate data by month
   */
  private static aggregateByMonth(data: ProductionDataItem[]): ProductionDataItem[] {
    const monthlyData = new Map<string, ProductionDataItem[]>();
    
    data.forEach(item => {
      const date = new Date(item.date);
      const monthKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-01`;
      
      if (!monthlyData.has(monthKey)) {
        monthlyData.set(monthKey, []);
      }
      monthlyData.get(monthKey)!.push(item);
    });

    return Array.from(monthlyData.entries()).map(([monthStart, items]) => 
      this.aggregateItems(items, monthStart)
    );
  }

  /**
   * Aggregate data by year
   */
  private static aggregateByYear(data: ProductionDataItem[]): ProductionDataItem[] {
    const yearlyData = new Map<string, ProductionDataItem[]>();
    
    data.forEach(item => {
      const date = new Date(item.date);
      const yearKey = `${date.getFullYear()}-01-01`;
      
      if (!yearlyData.has(yearKey)) {
        yearlyData.set(yearKey, []);
      }
      yearlyData.get(yearKey)!.push(item);
    });

    return Array.from(yearlyData.entries()).map(([yearStart, items]) => 
      this.aggregateItems(items, yearStart)
    );
  }

  /**
   * Aggregate multiple items into a single item
   */
  private static aggregateItems(items: ProductionDataItem[], date: string): ProductionDataItem {
    const totalActualOb = items.reduce((sum, item) => sum + (item.actual_ob || 0), 0);
    const totalPlanOb = items.reduce((sum, item) => sum + (item.plan_ob || 0), 0);
    const totalActualOre = items.reduce((sum, item) => sum + (item.actual_ore || 0), 0);
    const totalPlanOre = items.reduce((sum, item) => sum + (item.plan_ore || 0), 0);
    const totalActualFuel = items.reduce((sum, item) => sum + (item.actual_fuel || 0), 0);
    const totalPlanFuel = items.reduce((sum, item) => sum + (item.plan_fuel || 0), 0);

    // Calculate derived metrics using standardized formulas
    const strippingRatio = MiningCalculationService.calculateStripRatio(totalActualOb, totalActualOre);
    const fuelRatio = MiningCalculationService.calculateFuelRatio(totalActualFuel, totalActualOb, totalActualOre);
    const totalMaterial = MiningCalculationService.calculateTotalMaterial(totalActualOb, totalActualOre);

    return {
      date,
      actual_ob: totalActualOb,
      plan_ob: totalPlanOb,
      actual_ore: totalActualOre,
      plan_ore: totalPlanOre,
      actual_fuel: totalActualFuel,
      plan_fuel: totalPlanFuel,
      stripping_ratio: strippingRatio,
      fuel_ratio: fuelRatio,
      total_material: totalMaterial,
    };
  }

  /**
   * Extract metric-specific data points
   */
  private static extractMetricData(
    data: ProductionDataItem[],
    metric: ChartMetricConfig
  ): ChartDataPoint[] {
    return data.map(item => {
      let value = 0;
      let actualValue = 0;
      let planValue = 0;

      switch (metric.id) {
        case 'overburden':
          actualValue = item.actual_ob || 0;
          planValue = item.plan_ob || 0;
          value = actualValue;
          break;
        case 'ore_production':
          actualValue = item.actual_ore || 0;
          planValue = item.plan_ore || 0;
          value = actualValue;
          break;
        case 'fuel_usage':
          actualValue = item.actual_fuel || 0;
          planValue = item.plan_fuel || 0;
          value = actualValue;
          break;
        case 'stripping_ratio':
          value = item.stripping_ratio || 0;
          actualValue = value;
          planValue = 2.5; // Standard target
          break;
        case 'fuel_ratio':
          value = item.fuel_ratio || 0;
          actualValue = value;
          planValue = 1.2; // Standard target
          break;
        case 'total_material':
          value = item.total_material || 0;
          actualValue = value;
          planValue = MiningCalculationService.calculateTotalMaterial(item.plan_ob || 0, item.plan_ore || 0);
          break;
        default:
          value = 0;
      }

      return {
        label: item.date,
        value: this.validateNumber(value),
        date: item.date,
        actualValue: this.validateNumber(actualValue),
        planValue: this.validateNumber(planValue),
      };
    });
  }

  /**
   * Generate labels based on time period
   */
  private static generateLabels(data: ProductionDataItem[], timePeriod: TimePeriod): string[] {
    return data.map(item => {
      const date = new Date(item.date);
      
      switch (timePeriod) {
        case 'Daily':
          return date.getDate().toString();
        case 'Weekly':
          const weekNum = Math.ceil(date.getDate() / 7);
          return `W${weekNum}`;
        case 'Monthly':
          return date.toLocaleDateString('en-US', { month: 'short' });
        case 'Yearly':
          return date.getFullYear().toString();
        default:
          return date.getDate().toString();
      }
    });
  }

  /**
   * Create chart datasets
   */
  private static createDatasets(points: ChartDataPoint[], metric: ChartMetricConfig) {
    const actualData = points.map(p => p.actualValue || p.value);
    const planData = points.map(p => p.planValue || 0);

    const datasets = [
      {
        data: actualData,
        color: (opacity = 1) => `${metric.color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
        strokeWidth: 2,
        label: `Actual ${metric.shortName}`,
        gradient: {
          from: metric.gradientFrom,
          to: metric.gradientTo,
        },
      },
    ];

    // Add plan dataset if it has meaningful data
    if (planData.some(value => value > 0)) {
      datasets.push({
        data: planData,
        color: (opacity = 1) => `#9CA3AF${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
        strokeWidth: 1,
        label: `Plan ${metric.shortName}`,
        gradient: {
          from: '#9CA3AF',
          to: '#6B7280',
        },
      });
    }

    return datasets;
  }

  /**
   * Validate and sanitize numeric values
   */
  private static validateNumber(value: number): number {
    if (isNaN(value) || !isFinite(value)) {
      return 0;
    }
    return Math.max(0, value); // Ensure non-negative values
  }

  /**
   * Get empty chart data for loading/error states
   */
  private static getEmptyChartData(): ChartData {
    return {
      labels: ['No Data'],
      datasets: [{
        data: [0],
        color: () => '#E5E7EB',
        strokeWidth: 1,
        label: 'No Data',
      }],
      legend: ['No Data'],
    };
  }
}
