-- =====================================================
-- Mining Operations Database - Migration 001
-- =====================================================
-- Migration: 001_initial_schema.sql
-- Description: Initial database schema setup
-- Version: 1.0
-- Date: 2024-01-20
-- Dependencies: None
-- =====================================================

-- =====================================================
-- MIGRATION METADATA
-- =====================================================

-- Create migration tracking table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(50) PRIMARY KEY,
    description TEXT,
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    applied_by VARCHAR(100) DEFAULT current_user,
    checksum VARCHAR(64),
    execution_time_ms INTEGER
);

-- Record this migration start
INSERT INTO schema_migrations (version, description) 
VALUES ('001', 'Initial schema setup for Mining Operations App')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- EXECUTE SCHEMA FILES IN ORDER
-- =====================================================

-- Start timing
DO $$
DECLARE
    start_time TIMESTAMPTZ := NOW();
    end_time TIMESTAMPTZ;
    execution_time INTEGER;
BEGIN
    RAISE NOTICE 'Starting Migration 001: Initial Schema Setup';
    RAISE NOTICE 'Start time: %', start_time;
    
    -- 1. Core Setup (Extensions, Types, Functions)
    RAISE NOTICE 'Executing 01-core-setup.sql...';
    \i ../01-core-setup.sql
    
    -- 2. Production Calendar (Calendar and Targets)
    RAISE NOTICE 'Executing 02-production-calendar.sql...';
    \i ../02-production-calendar.sql
    
    -- 3. Daily Mining Report (Main Data Table)
    RAISE NOTICE 'Executing 03-daily-mining-report.sql...';
    \i ../03-daily-mining-report.sql
    
    -- 4. Analytical Views (Reporting Views)
    RAISE NOTICE 'Executing 04-analytical-views.sql...';
    \i ../04-analytical-views.sql
    
    -- 5. User Management (Authentication & Authorization)
    RAISE NOTICE 'Executing 06-user-management.sql...';
    \i ../06-user-management.sql
    
    -- Calculate execution time
    end_time := NOW();
    execution_time := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    -- Update migration record with completion status
    UPDATE schema_migrations 
    SET 
        applied_at = end_time,
        checksum = md5(random()::text),
        execution_time_ms = execution_time
    WHERE version = '001';
    
    RAISE NOTICE 'Migration 001 completed successfully';
    RAISE NOTICE 'End time: %', end_time;
    RAISE NOTICE 'Execution time: % ms', execution_time;
END $$;

-- =====================================================
-- POST-MIGRATION SETUP
-- =====================================================

-- Create default admin user (if using direct database setup)
DO $$
BEGIN
    -- Only create if no users exist
    IF NOT EXISTS (SELECT 1 FROM user_profiles LIMIT 1) THEN
        INSERT INTO user_profiles (
            id, 
            email, 
            first_name, 
            last_name, 
            role, 
            department, 
            employee_id,
            is_active
        ) VALUES (
            '00000000-0000-0000-0000-000000000001',
            '<EMAIL>',
            'System',
            'Administrator',
            'Super Admin',
            'IT',
            'SYS001',
            true
        );
        
        RAISE NOTICE 'Default admin user created: <EMAIL>';
    END IF;
END $$;

-- Create default location if none exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM locations WHERE name = 'Default Mining Site') THEN
        -- Insert into production_calendar first (if table exists)
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'production_calendar') THEN
            INSERT INTO production_targets_calendar (
                location,
                target_name,
                period_type,
                period_start_date,
                period_end_date,
                year_number,
                month_number,
                target_ob,
                target_ore,
                target_sr,
                target_fr,
                created_by
            ) VALUES (
                'Default Mining Site',
                'Default Monthly Target',
                'Monthly',
                DATE_TRUNC('month', CURRENT_DATE),
                DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day',
                EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER,
                EXTRACT(MONTH FROM CURRENT_DATE)::INTEGER,
                30000,
                12000,
                2.50,
                0.85,
                '00000000-0000-0000-0000-000000000001'
            );
            
            RAISE NOTICE 'Default production target created for Default Mining Site';
        END IF;
    END IF;
END $$;

-- Create essential app settings
INSERT INTO app_settings (setting_key, setting_category, setting_value, description, data_type) VALUES
('app.initialized', 'system', 'true', 'Application initialization flag', 'boolean'),
('app.schema_version', 'system', '"001"', 'Current database schema version', 'string'),
('app.installation_date', 'system', to_jsonb(NOW()::text), 'Application installation timestamp', 'string'),
('app.name', 'system', '"Mining Operations App"', 'Application name', 'string'),
('app.version', 'system', '"1.0.0"', 'Application version', 'string')
ON CONFLICT (setting_key) DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify all core tables were created
DO $$
DECLARE
    table_count INTEGER;
    expected_core_tables TEXT[] := ARRAY[
        'user_profiles', 'user_permissions', 'user_sessions', 
        'production_calendar', 'production_targets_calendar',
        'daily_mining_report', 'audit_logs', 'app_settings'
    ];
    missing_tables TEXT[] := '{}';
    table_name TEXT;
BEGIN
    -- Check if all expected tables exist
    FOREACH table_name IN ARRAY expected_core_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = table_name
        ) THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE';
    
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MIGRATION 001 VERIFICATION';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Total tables created: %', table_count;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE WARNING 'Missing expected tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE 'All core tables created successfully';
    END IF;
    
    RAISE NOTICE '================================================';
END $$;

-- =====================================================
-- FINAL MIGRATION STATUS
-- =====================================================

DO $$
DECLARE
    migration_info RECORD;
BEGIN
    SELECT * INTO migration_info 
    FROM schema_migrations 
    WHERE version = '001';
    
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MIGRATION 001 COMPLETED SUCCESSFULLY';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Version: %', migration_info.version;
    RAISE NOTICE 'Description: %', migration_info.description;
    RAISE NOTICE 'Applied at: %', migration_info.applied_at;
    RAISE NOTICE 'Applied by: %', migration_info.applied_by;
    RAISE NOTICE 'Execution time: % ms', migration_info.execution_time_ms;
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Run 002_equipment_safety.sql for equipment and safety modules';
    RAISE NOTICE '2. Run 003_security_performance.sql for RLS and performance';
    RAISE NOTICE '3. Load sample data with 004_sample_data.sql (optional)';
    RAISE NOTICE '================================================';
END $$;
