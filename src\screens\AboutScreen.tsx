import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Linking,
  StyleSheet,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Layout } from '../constants';

interface AboutScreenProps {
  navigation: any;
}

const AboutScreen: React.FC<AboutScreenProps> = ({ navigation }) => {
  const appVersion = '1.0.0';
  const buildNumber = '2025.01.15';
  const releaseDate = 'January 15, 2025';

  const handleOpenLink = (url: string) => {
    Linking.openURL(url).catch(() => {
      console.warn('Failed to open URL:', url);
    });
  };

  const InfoItem = ({ 
    label, 
    value, 
    icon 
  }: {
    label: string;
    value: string;
    icon: string;
  }) => (
    <View style={styles.infoItem}>
      <View style={styles.infoIcon}>
        <Ionicons name={icon as any} size={20} color={Colors.primary} />
      </View>
      <View style={styles.infoContent}>
        <Text style={styles.infoLabel}>{label}</Text>
        <Text style={styles.infoValue}>{value}</Text>
      </View>
    </View>
  );

  const LinkItem = ({ 
    title, 
    description, 
    url, 
    icon 
  }: {
    title: string;
    description: string;
    url: string;
    icon: string;
  }) => (
    <TouchableOpacity 
      style={styles.linkItem} 
      onPress={() => handleOpenLink(url)}
    >
      <View style={styles.linkIcon}>
        <Ionicons name={icon as any} size={20} color={Colors.primary} />
      </View>
      <View style={styles.linkContent}>
        <Text style={styles.linkTitle}>{title}</Text>
        <Text style={styles.linkDescription}>{description}</Text>
      </View>
      <Ionicons name="open-outline" size={20} color={Colors.textLight} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>About</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* App Logo & Name */}
        <View style={styles.appHeader}>
          <View style={styles.appLogo}>
            <Ionicons name="business" size={48} color={Colors.primary} />
          </View>
          <Text style={styles.appName}>Mining Operations</Text>
          <Text style={styles.appTagline}>Professional Mining Management System</Text>
        </View>

        {/* App Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Information</Text>
          
          <InfoItem
            label="Version"
            value={appVersion}
            icon="information-circle"
          />

          <InfoItem
            label="Build Number"
            value={buildNumber}
            icon="code-working"
          />

          <InfoItem
            label="Release Date"
            value={releaseDate}
            icon="calendar"
          />

          <InfoItem
            label="Platform"
            value="React Native / Expo"
            icon="phone-portrait"
          />

          <InfoItem
            label="Database"
            value="Supabase PostgreSQL"
            icon="server"
          />
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Features</Text>
          
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.featureText}>Real-time Production Monitoring</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.featureText}>Safety Incident Reporting</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.featureText}>Equipment Management</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.featureText}>Comprehensive Reporting</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.featureText}>User Profile Management</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.featureText}>Offline Capability</Text>
            </View>
          </View>
        </View>

        {/* Company Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Company</Text>
          
          <LinkItem
            title="Company Website"
            description="Visit our official website"
            url="https://mining-company.com"
            icon="globe"
          />

          <LinkItem
            title="Privacy Policy"
            description="Read our privacy policy"
            url="https://mining-company.com/privacy"
            icon="shield-checkmark"
          />

          <LinkItem
            title="Terms of Service"
            description="View terms and conditions"
            url="https://mining-company.com/terms"
            icon="document-text"
          />

          <LinkItem
            title="Contact Us"
            description="Get in touch with our team"
            url="mailto:<EMAIL>"
            icon="mail"
          />
        </View>

        {/* Technical Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Technical Details</Text>
          
          <View style={styles.techInfo}>
            <Text style={styles.techTitle}>Built with:</Text>
            <Text style={styles.techText}>• React Native & Expo SDK</Text>
            <Text style={styles.techText}>• TypeScript for type safety</Text>
            <Text style={styles.techText}>• Supabase for backend services</Text>
            <Text style={styles.techText}>• Real-time data synchronization</Text>
            <Text style={styles.techText}>• Offline-first architecture</Text>
            <Text style={styles.techText}>• Modern UI/UX design</Text>
          </View>
        </View>

        {/* Copyright */}
        <View style={styles.copyright}>
          <Text style={styles.copyrightText}>
            © 2025 Mining Operations App
          </Text>
          <Text style={styles.copyrightText}>
            All rights reserved
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  placeholder: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  appHeader: {
    alignItems: 'center',
    paddingVertical: Layout.spacing.xl,
  },
  appLogo: {
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  appName: {
    fontSize: Layout.fontSize.xl,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  appTagline: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    textAlign: 'center',
  },
  section: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  infoIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    marginBottom: Layout.spacing.xs,
  },
  infoValue: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
  },
  linkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  linkIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  linkContent: {
    flex: 1,
  },
  linkTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  linkDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  featuresList: {
    backgroundColor: Colors.backgroundLight,
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  featureText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.text,
    marginLeft: Layout.spacing.sm,
  },
  techInfo: {
    backgroundColor: Colors.backgroundLight,
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
  },
  techTitle: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.sm,
  },
  techText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    marginBottom: Layout.spacing.xs,
  },
  copyright: {
    alignItems: 'center',
    paddingVertical: Layout.spacing.xl,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    marginTop: Layout.spacing.lg,
  },
  copyrightText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    textAlign: 'center',
  },
});

export default AboutScreen;
