import { ValidationResult, ValidationError, ProductionMetric } from '../types';

/**
 * Production data validation utilities
 */
export class ProductionValidator {
  /**
   * Validate production metric data
   */
  static validate(data: Partial<ProductionMetric>): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields validation
    if (!data.date) {
      errors.push({
        field: 'date',
        message: 'Date is required',
        value: data.date
      });
    } else if (!this.isValidDate(data.date)) {
      errors.push({
        field: 'date',
        message: 'Invalid date format. Expected YYYY-MM-DD',
        value: data.date
      });
    }

    if (!data.location_id) {
      errors.push({
        field: 'location_id',
        message: 'Location ID is required',
        value: data.location_id
      });
    }

    if (!data.created_by) {
      errors.push({
        field: 'created_by',
        message: 'Created by user ID is required',
        value: data.created_by
      });
    }

    // Numeric fields validation
    const numericFields = [
      'actual_ob', 'plan_ob', 'actual_ore', 'plan_ore',
      'actual_fuel', 'plan_fuel', 'actual_rain', 'plan_rain',
      'actual_slippery', 'plan_slippery'
    ];

    for (const field of numericFields) {
      const value = (data as any)[field];
      if (value !== undefined && value !== null) {
        if (!this.isValidNumber(value)) {
          errors.push({
            field,
            message: `${field} must be a valid number`,
            value
          });
        } else if (value < 0) {
          errors.push({
            field,
            message: `${field} cannot be negative`,
            value
          });
        }
      }
    }

    // Business logic validation
    if (data.actual_ob !== undefined && data.actual_ore !== undefined) {
      if (data.actual_ob > 0 && data.actual_ore === 0) {
        errors.push({
          field: 'actual_ore',
          message: 'Ore production cannot be zero when overburden is produced',
          value: data.actual_ore
        });
      }
    }

    // Plan vs actual validation
    if (data.actual_ob !== undefined && data.plan_ob !== undefined) {
      const variance = Math.abs(data.actual_ob - data.plan_ob);
      const threshold = data.plan_ob * 0.5; // 50% variance threshold
      
      if (variance > threshold) {
        errors.push({
          field: 'actual_ob',
          message: `Overburden actual (${data.actual_ob}) varies significantly from plan (${data.plan_ob})`,
          value: data.actual_ob
        });
      }
    }

    if (data.actual_ore !== undefined && data.plan_ore !== undefined) {
      const variance = Math.abs(data.actual_ore - data.plan_ore);
      const threshold = data.plan_ore * 0.5; // 50% variance threshold
      
      if (variance > threshold) {
        errors.push({
          field: 'actual_ore',
          message: `Ore actual (${data.actual_ore}) varies significantly from plan (${data.plan_ore})`,
          value: data.actual_ore
        });
      }
    }

    // String fields validation
    if (data.notes && data.notes.length > 1000) {
      errors.push({
        field: 'notes',
        message: 'Notes cannot exceed 1000 characters',
        value: data.notes
      });
    }

    if (data.weather_conditions && data.weather_conditions.length > 255) {
      errors.push({
        field: 'weather_conditions',
        message: 'Weather conditions cannot exceed 255 characters',
        value: data.weather_conditions
      });
    }

    // Week validation
    if (data.week !== undefined) {
      if (!Number.isInteger(data.week) || data.week < 1 || data.week > 53) {
        errors.push({
          field: 'week',
          message: 'Week must be an integer between 1 and 53',
          value: data.week
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate date format (YYYY-MM-DD)
   */
  private static isValidDate(dateString: string): boolean {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
      return false;
    }

    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * Validate if value is a valid number
   */
  private static isValidNumber(value: any): boolean {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
  }

  /**
   * Validate bulk production data
   */
  static validateBulk(dataArray: Partial<ProductionMetric>[]): {
    isValid: boolean;
    results: Array<{ index: number; result: ValidationResult }>;
    summary: {
      total: number;
      valid: number;
      invalid: number;
    };
  } {
    const results = dataArray.map((data, index) => ({
      index,
      result: this.validate(data)
    }));

    const validCount = results.filter(r => r.result.isValid).length;
    const invalidCount = results.length - validCount;

    return {
      isValid: invalidCount === 0,
      results,
      summary: {
        total: results.length,
        valid: validCount,
        invalid: invalidCount
      }
    };
  }

  /**
   * Sanitize production data
   */
  static sanitize(data: any): Partial<ProductionMetric> {
    const sanitized: any = {};

    // Copy valid fields only
    const validFields = [
      'date', 'location_id', 'created_by',
      'actual_ob', 'plan_ob', 'actual_ore', 'plan_ore',
      'actual_fuel', 'plan_fuel', 'actual_rain', 'plan_rain',
      'actual_slippery', 'plan_slippery',
      'monthly', 'week', 'notes', 'weather_conditions'
    ];

    for (const field of validFields) {
      if (data[field] !== undefined && data[field] !== null) {
        sanitized[field] = data[field];
      }
    }

    // Sanitize numeric fields
    const numericFields = [
      'actual_ob', 'plan_ob', 'actual_ore', 'plan_ore',
      'actual_fuel', 'plan_fuel', 'actual_rain', 'plan_rain',
      'actual_slippery', 'plan_slippery', 'week'
    ];

    for (const field of numericFields) {
      if (sanitized[field] !== undefined) {
        const num = Number(sanitized[field]);
        if (!isNaN(num) && isFinite(num)) {
          sanitized[field] = num;
        } else {
          delete sanitized[field];
        }
      }
    }

    // Sanitize string fields
    const stringFields = ['notes', 'weather_conditions', 'monthly'];
    for (const field of stringFields) {
      if (sanitized[field] !== undefined) {
        sanitized[field] = String(sanitized[field]).trim();
      }
    }

    // Sanitize date
    if (sanitized.date) {
      sanitized.date = String(sanitized.date).trim();
    }

    return sanitized;
  }
}
