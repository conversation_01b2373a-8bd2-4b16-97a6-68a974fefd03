// Core package exports for Mining Operations Platform

// Types
export * from './types';
export * from './types/database';
export * from './types/api';
export * from './types/common';

// Services
export { BaseService } from './services/BaseService';
export { ProductionService } from './services/ProductionService';

// Adapters
export { StorageAdapter } from './adapters/StorageAdapter';
export { NetworkAdapter } from './adapters/NetworkAdapter';
export { NotificationAdapter } from './adapters/NotificationAdapter';

// Utils
export { ProductionCalculator } from './utils/ProductionCalculator';
export { ProductionValidator } from './utils/ProductionValidator';
export { ProductionCalendar } from './utils/ProductionCalendar';
export { Logger, LogLevel } from './utils/Logger';
export { ServiceContainer } from './utils/ServiceContainer';

// Re-export commonly used types for convenience
export type {
  ProductionMetric,
  Equipment,
  User,
  Location,
  ApiResponse,
  ChartData,
  SyncOperation,
  ValidationResult,
  FilterCriteria,
  SortCriteria,
  PaginationOptions,
  ProductionMonth
} from './types';
