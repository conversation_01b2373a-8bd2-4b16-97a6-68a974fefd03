# MiningOperationsApp

Aplikasi manajemen operasi pertambangan dengan fitur-fitur lengkap untuk monitoring dan kontrol operasional tambang.

## Fitur Utama

- Dashboard monitoring produksi
- Manajemen peralatan tambang
- Sistem keamanan dan keselamatan
- Analisis data produksi
- Offline support dan sinkronisasi
- Role-based access control

## Teknologi

- React Native / Expo
- TypeScript
- Supabase (Database & Authentication)
- Chart.js untuk visualisasi data

## Instalasi

1. Clone repository
```bash
git clone https://github.com/Wahyuwidiyanto/MiningOperationsApp.git
cd MiningOperationsApp
```

2. Install dependencies
```bash
npm install
```

3. Jalankan aplikasi
```bash
npx expo start
```

## Struktur Project

- `/src` - Source code utama aplikasi
- `/assets` - Gambar dan asset statis
- `/docs` - Dokumentasi project
- `/database` - Schema dan migrations database
- `/packages` - Shared packages dan modules

## Dokumentasi

Dokumentasi lengkap dapat ditemukan di folder `/docs`
