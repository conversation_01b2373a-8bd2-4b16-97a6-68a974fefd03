-- Production Calendar Table Schema for Supabase
-- This SQL script creates the production_calendar table and related configurations

-- =============================================
-- CREATE PRODUCTION CALENDAR TABLE
-- =============================================

-- Create production_calendar table
CREATE TABLE IF NOT EXISTS production_calendar (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  year INTEGER NOT NULL,
  month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Index for name lookups (most common query)
CREATE INDEX IF NOT EXISTS idx_production_calendar_name ON production_calendar(name);

-- Index for year and month queries
CREATE INDEX IF NOT EXISTS idx_production_calendar_year_month ON production_calendar(year, month);

-- Index for date range queries (finding current production month)
CREATE INDEX IF NOT EXISTS idx_production_calendar_dates ON production_calendar(start_date, end_date);

-- Index for date range overlap queries
CREATE INDEX IF NOT EXISTS idx_production_calendar_date_range ON production_calendar USING GIST (daterange(start_date, end_date, '[]'));

-- =============================================
-- CREATE TRIGGERS
-- =============================================

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_production_calendar_updated_at 
    BEFORE UPDATE ON production_calendar 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable Row Level Security
ALTER TABLE production_calendar ENABLE ROW LEVEL SECURITY;

-- =============================================
-- CREATE RLS POLICIES
-- =============================================

-- Policy for viewing production calendar (all users can view)
CREATE POLICY "Users can view production calendar" ON production_calendar
    FOR SELECT USING (true);

-- Policy for inserting production calendar (authenticated users only)
CREATE POLICY "Authenticated users can insert production calendar" ON production_calendar
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy for updating production calendar (users can update their own entries)
CREATE POLICY "Users can update their own production calendar entries" ON production_calendar
    FOR UPDATE USING (auth.uid() = created_by);

-- Policy for deleting production calendar (users can delete their own entries)
CREATE POLICY "Users can delete their own production calendar entries" ON production_calendar
    FOR DELETE USING (auth.uid() = created_by);

-- =============================================
-- CREATE HELPER FUNCTIONS
-- =============================================

-- Function to get current production month
CREATE OR REPLACE FUNCTION get_current_production_month()
RETURNS TABLE (
  id UUID,
  name VARCHAR(50),
  start_date DATE,
  end_date DATE,
  year INTEGER,
  month INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  created_by UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT pc.*
  FROM production_calendar pc
  WHERE CURRENT_DATE >= pc.start_date 
    AND CURRENT_DATE <= pc.end_date
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to get production month by name
CREATE OR REPLACE FUNCTION get_production_month_by_name(month_name VARCHAR(50))
RETURNS TABLE (
  id UUID,
  name VARCHAR(50),
  start_date DATE,
  end_date DATE,
  year INTEGER,
  month INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  created_by UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT pc.*
  FROM production_calendar pc
  WHERE pc.name = month_name
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate days in production month up to current date
CREATE OR REPLACE FUNCTION calculate_production_month_days(month_name VARCHAR(50))
RETURNS INTEGER AS $$
DECLARE
  prod_month RECORD;
  end_date_calc DATE;
  days_count INTEGER;
BEGIN
  -- Get production month
  SELECT * INTO prod_month
  FROM production_calendar
  WHERE name = month_name
  LIMIT 1;
  
  IF NOT FOUND THEN
    RETURN 0;
  END IF;
  
  -- Calculate end date (current date or production month end date, whichever is earlier)
  end_date_calc := LEAST(CURRENT_DATE, prod_month.end_date);
  
  -- Calculate days count
  days_count := end_date_calc - prod_month.start_date + 1;
  
  RETURN GREATEST(0, days_count);
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- CREATE VIEWS FOR COMMON QUERIES
-- =============================================

-- View for current production month with calculated fields
CREATE OR REPLACE VIEW current_production_month_view AS
SELECT 
  pc.*,
  CURRENT_DATE as today,
  LEAST(CURRENT_DATE, pc.end_date) as current_end_date,
  calculate_production_month_days(pc.name) as days_count,
  CASE 
    WHEN CURRENT_DATE > pc.end_date THEN 'completed'
    WHEN CURRENT_DATE < pc.start_date THEN 'future'
    ELSE 'current'
  END as status
FROM production_calendar pc
WHERE CURRENT_DATE >= pc.start_date 
  AND CURRENT_DATE <= pc.end_date;

-- View for all production months with status
CREATE OR REPLACE VIEW production_calendar_with_status AS
SELECT 
  pc.*,
  CASE 
    WHEN CURRENT_DATE > pc.end_date THEN 'completed'
    WHEN CURRENT_DATE < pc.start_date THEN 'future'
    ELSE 'current'
  END as status,
  calculate_production_month_days(pc.name) as days_count
FROM production_calendar pc
ORDER BY pc.year, pc.month;

-- =============================================
-- SAMPLE DATA INSERT (OPTIONAL)
-- =============================================

-- Uncomment the following section to insert sample data
/*
INSERT INTO production_calendar (name, start_date, end_date, year, month) VALUES
('December 2024', '2024-11-30', '2024-12-29', 2024, 12),
('January 2025', '2024-12-30', '2025-01-29', 2025, 1),
('February 2025', '2025-01-30', '2025-02-26', 2025, 2),
('March 2025', '2025-02-27', '2025-03-29', 2025, 3),
('April 2025', '2025-03-30', '2025-04-29', 2025, 4),
('May 2025', '2025-04-30', '2025-05-29', 2025, 5),
('June 2025', '2025-05-30', '2025-06-29', 2025, 6),
('July 2025', '2025-06-30', '2025-07-29', 2025, 7),
('August 2025', '2025-07-30', '2025-08-29', 2025, 8),
('September 2025', '2025-08-30', '2025-09-29', 2025, 9),
('October 2025', '2025-09-30', '2025-10-29', 2025, 10),
('November 2025', '2025-10-30', '2025-11-29', 2025, 11),
('December 2025', '2025-11-30', '2025-12-29', 2025, 12)
ON CONFLICT (name) DO NOTHING;
*/

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Query to verify table creation
-- SELECT table_name, column_name, data_type 
-- FROM information_schema.columns 
-- WHERE table_name = 'production_calendar' 
-- ORDER BY ordinal_position;

-- Query to verify indexes
-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'production_calendar';

-- Query to verify RLS policies
-- SELECT policyname, permissive, roles, cmd, qual 
-- FROM pg_policies 
-- WHERE tablename = 'production_calendar';

-- Query to test current production month function
-- SELECT * FROM get_current_production_month();

-- Query to test production month view
-- SELECT * FROM current_production_month_view;

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================

COMMENT ON TABLE production_calendar IS 'Production calendar defining custom production month periods';
COMMENT ON COLUMN production_calendar.name IS 'Production month name (e.g., "July 2025")';
COMMENT ON COLUMN production_calendar.start_date IS 'Production month start date (e.g., "2025-06-30")';
COMMENT ON COLUMN production_calendar.end_date IS 'Production month end date (e.g., "2025-07-29")';
COMMENT ON COLUMN production_calendar.year IS 'Calendar year of the production month';
COMMENT ON COLUMN production_calendar.month IS 'Calendar month number (1-12)';

COMMENT ON FUNCTION get_current_production_month() IS 'Returns the current production month based on today''s date';
COMMENT ON FUNCTION get_production_month_by_name(VARCHAR) IS 'Returns production month by name';
COMMENT ON FUNCTION calculate_production_month_days(VARCHAR) IS 'Calculates days in production month up to current date';

COMMENT ON VIEW current_production_month_view IS 'View showing current production month with calculated fields';
COMMENT ON VIEW production_calendar_with_status IS 'View showing all production months with status indicators';

-- =============================================
-- SETUP COMPLETE
-- =============================================

-- Display setup completion message
DO $$
BEGIN
  RAISE NOTICE 'Production Calendar setup completed successfully!';
  RAISE NOTICE 'Table: production_calendar created with RLS enabled';
  RAISE NOTICE 'Indexes: Created for optimal query performance';
  RAISE NOTICE 'Functions: Helper functions for common operations';
  RAISE NOTICE 'Views: Convenient views for current month and status';
  RAISE NOTICE 'Next step: Insert production calendar data using the application';
END $$;
