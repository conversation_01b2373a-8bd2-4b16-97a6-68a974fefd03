# 🔄 Mining Operations Database - Migrations

Sistem migrasi database untuk Mining Operations App dengan pendekatan bertahap dan aman.

## 📋 **Overview**

Migrations memungkinkan deployment database secara bertahap dengan tracking perubahan dan rollback capability. Setiap migration memiliki versi, deskripsi, dan dependency yang jelas.

## 📁 **Migration Files**

```
migrations/
├── README.md                    # Dokumentasi migrations
├── 001_initial_schema.sql       # Schema dasar (core, production, users)
├── 002_equipment_safety.sql     # Equipment & safety management
├── 003_security_performance.sql # RLS policies & performance
├── 004_sample_data.sql         # Sample data untuk testing
└── migrate.sql                 # Migration runner script
```

## 🎯 **Migration Structure**

### **001_initial_schema.sql**
**Core database foundation**
- Extensions dan types
- Production calendar dan targets
- Daily mining report
- User management
- Basic analytical views

**Tables Created:**
- `user_profiles`, `user_permissions`, `user_sessions`
- `production_calendar`, `production_targets_calendar`
- `daily_mining_report`
- `audit_logs`, `app_settings`
- `schema_migrations`

### **002_equipment_safety.sql**
**Equipment and safety modules**
- Equipment registry dan metrics
- Maintenance schedules dan work orders
- Safety incidents dan training
- **IUT (Inspeksi Umum Terencana)** management
- **OTT (Observasi Tugas Terencana)** management
- Mining certifications (Greencard, IUT, OTT)

**Tables Created:**
- `equipment`, `equipment_metrics`
- `maintenance_schedules`, `work_orders`
- `safety_incidents`, `safety_inspections`, `safety_training`
- `mining_certifications`, `certificate_renewals`
- `iut_inspections`, `ott_observations`
- `safety_metrics`

### **003_security_performance.sql**
**Security and optimization**
- Row Level Security (RLS) policies
- Performance indexes
- Materialized views
- Monitoring views
- Maintenance functions

**Features Added:**
- 30+ RLS policies untuk data security
- 60+ performance indexes
- Materialized views untuk analytics
- Database monitoring tools

### **004_sample_data.sql**
**Sample data for testing**
- Production data untuk multiple locations
- Equipment dengan maintenance records
- Safety incidents dan training programs
- IUT inspections dan OTT observations
- User accounts dengan different roles

## 🚀 **How to Run Migrations**

### **Method 1: Sequential Migration (Recommended)**

```bash
# Connect to database
psql "your-database-connection-string"

# Run migrations in order
\i migrations/001_initial_schema.sql
\i migrations/002_equipment_safety.sql
\i migrations/003_security_performance.sql
\i migrations/004_sample_data.sql  # Optional
```

### **Method 2: Using Migration Runner**

```bash
# Run all migrations at once
\i migrations/migrate.sql
```

### **Method 3: Individual Migration**

```bash
# Run specific migration
\i migrations/002_equipment_safety.sql
```

## ✅ **Migration Tracking**

Setiap migration dicatat dalam tabel `schema_migrations`:

```sql
-- Check migration status
SELECT * FROM schema_migrations ORDER BY version;

-- Expected output:
-- version | description                              | applied_at | execution_time_ms
-- 001     | Initial schema setup                     | 2024-01-20 | 2500
-- 002     | Equipment and safety modules with IUT/OTT| 2024-01-20 | 3200
-- 003     | RLS policies and performance             | 2024-01-20 | 1800
-- 004     | Sample data for testing                  | 2024-01-20 | 1200
```

## 🔍 **Verification Queries**

### **Check Migration Status**
```sql
-- Verify all migrations applied
SELECT 
    version,
    description,
    applied_at,
    execution_time_ms
FROM schema_migrations 
ORDER BY version;
```

### **Verify Database Objects**
```sql
-- Count tables
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'public';
-- Expected: 21+ tables

-- Count views
SELECT COUNT(*) as view_count 
FROM information_schema.views 
WHERE table_schema = 'public';
-- Expected: 12+ views

-- Count indexes
SELECT COUNT(*) as index_count 
FROM pg_indexes 
WHERE schemaname = 'public';
-- Expected: 60+ indexes
```

### **Test Core Functionality**
```sql
-- Test daily reports
SELECT COUNT(*) FROM daily_mining_report;

-- Test equipment
SELECT COUNT(*) FROM equipment;

-- Test IUT inspections
SELECT COUNT(*) FROM iut_inspections;

-- Test OTT observations
SELECT COUNT(*) FROM ott_observations;

-- Test certifications
SELECT COUNT(*) FROM mining_certifications;
```

## 🎯 **IUT & OTT Definitions**

### **IUT - Inspeksi Umum Terencana**
**Planned General Inspection**
- Systematic inspection of equipment, work areas, and procedures
- Compliance checking against safety standards
- Documentation of findings and corrective actions
- Regular scheduled inspections with checklists

**Key Features:**
- Inspector certification tracking
- Compliance rate calculation
- Critical findings management
- Follow-up action tracking

### **OTT - Observasi Tugas Terencana**
**Planned Task Observation**
- Behavioral safety observation of workers
- Task-specific safety compliance monitoring
- Immediate feedback and coaching
- Positive reinforcement of safe behaviors

**Key Features:**
- Observer certification tracking
- Behavioral assessment scoring
- Feedback delivery tracking
- Training recommendation system

## 🔧 **Migration Dependencies**

```
001_initial_schema.sql
    ↓
002_equipment_safety.sql
    ↓
003_security_performance.sql
    ↓
004_sample_data.sql (optional)
```

**Dependency Checking:**
- Each migration checks if previous migration was applied
- Prevents out-of-order execution
- Ensures data integrity

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **1. Migration Already Applied**
```sql
-- Error: Migration 002 already exists
-- Solution: Check migration status
SELECT * FROM schema_migrations WHERE version = '002';
```

#### **2. Missing Dependencies**
```sql
-- Error: Migration 001 must be applied first
-- Solution: Run migrations in order
\i migrations/001_initial_schema.sql
```

#### **3. Permission Errors**
```sql
-- Error: Permission denied
-- Solution: Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE your_db TO your_user;
```

#### **4. RLS Blocking Queries**
```sql
-- Temporarily disable RLS for troubleshooting
SELECT disable_rls_for_maintenance();
-- Remember to re-enable: SELECT enable_rls_after_maintenance();
```

## 📊 **Migration Performance**

**Typical Execution Times:**
- Migration 001: ~2.5 seconds (core schema)
- Migration 002: ~3.2 seconds (equipment & safety)
- Migration 003: ~1.8 seconds (security & performance)
- Migration 004: ~1.2 seconds (sample data)

**Total Time: ~8.7 seconds**

## 🔄 **Rollback Strategy**

While migrations don't include automatic rollback, you can:

1. **Backup before migration**
```bash
pg_dump your_database > backup_before_migration.sql
```

2. **Manual rollback**
```sql
-- Drop specific tables/objects created by migration
-- Restore from backup if needed
```

3. **Version control**
```sql
-- Update schema_migrations to previous version
DELETE FROM schema_migrations WHERE version = '002';
```

## 📱 **Mobile App Integration**

After running migrations, your database is ready for:

```javascript
// React Native with Supabase
const { data, error } = await supabase
  .from('v_iut_inspection_summary')
  .select('*')
  .limit(10);

// IUT inspection data
const { data, error } = await supabase
  .from('iut_inspections')
  .insert([inspectionData]);

// OTT observation data
const { data, error } = await supabase
  .from('ott_observations')
  .insert([observationData]);
```

## 🎉 **Post-Migration Steps**

1. **Verify all migrations applied successfully**
2. **Test core functionality with sample queries**
3. **Configure app settings for your environment**
4. **Setup user accounts and permissions**
5. **Populate production targets and calendar**
6. **Connect mobile app and test integration**

---

**🏭 Your Mining Operations Database is Ready!**

The migration system ensures a reliable, trackable, and safe deployment process for your mining operations management system with comprehensive IUT and OTT functionality.
