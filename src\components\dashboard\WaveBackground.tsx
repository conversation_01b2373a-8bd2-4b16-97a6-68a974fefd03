import React from 'react';
import { View, StyleSheet } from 'react-native';

interface WaveBackgroundProps {
  colors?: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
}

export const WaveBackground: React.FC<WaveBackgroundProps> = ({
  colors = {
    primary: '#7CB342',
    secondary: '#8BC34A',
    tertiary: '#AED581'
  }
}) => {
  return (
    <View style={styles.waveContainer}>
      <View style={[styles.waveLayer1, { backgroundColor: colors.primary }]} />
      <View style={[styles.waveLayer2, { backgroundColor: colors.secondary }]} />
      <View style={[styles.waveLayer3, { backgroundColor: colors.tertiary }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  waveContainer: {
    height: 180,
    position: 'relative',
    overflow: 'hidden',
    marginBottom: 0,
  },
  waveLayer1: {
    position: 'absolute',
    bottom: 0,
    left: -50,
    right: -50,
    height: 100,
    borderTopLeftRadius: 100,
    borderTopRightRadius: 100,
    transform: [{ scaleX: 1.8 }, { scaleY: 1.2 }],
  },
  waveLayer2: {
    position: 'absolute',
    bottom: 20,
    left: -30,
    right: -30,
    height: 80,
    borderTopLeftRadius: 80,
    borderTopRightRadius: 80,
    transform: [{ scaleX: 1.5 }, { scaleY: 1.1 }],
    opacity: 0.9,
  },
  waveLayer3: {
    position: 'absolute',
    bottom: 40,
    left: -20,
    right: -20,
    height: 60,
    borderTopLeftRadius: 60,
    borderTopRightRadius: 60,
    transform: [{ scaleX: 1.3 }, { scaleY: 1.0 }],
    opacity: 0.8,
  },
});
