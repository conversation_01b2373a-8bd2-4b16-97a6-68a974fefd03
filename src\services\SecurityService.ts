import AsyncStorage from '@react-native-async-storage/async-storage';
// import * as Crypto from 'expo-crypto'; // Commented out until expo-crypto is installed
// import * as SecureStore from 'expo-secure-store'; // Commented out until expo-secure-store is installed
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

export interface SecurityConfig {
  sessionTimeout: number; // minutes
  maxLoginAttempts: number;
  lockoutDuration: number; // minutes
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    maxAge: number; // days
  };
  dataEncryption: {
    enabled: boolean;
    algorithm: string;
  };
  auditLogging: {
    enabled: boolean;
    retentionDays: number;
  };
}

export interface SecurityEvent {
  id: string;
  timestamp: number;
  eventType: 'login' | 'logout' | 'failed_login' | 'data_access' | 'data_modification' | 'security_violation';
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, any>;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface DataClassification {
  level: 'public' | 'internal' | 'confidential' | 'restricted';
  requiresEncryption: boolean;
  accessControl: string[];
  retentionPeriod: number; // days
}

export class SecurityService {
  private static instance: SecurityService;
  private config: SecurityConfig;
  private sessionStartTime: number = 0;
  private loginAttempts: Map<string, { count: number; lastAttempt: number }> = new Map();

  private constructor() {
    this.config = {
      sessionTimeout: 30, // 30 minutes
      maxLoginAttempts: 5,
      lockoutDuration: 15, // 15 minutes
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAge: 90 // 90 days
      },
      dataEncryption: {
        enabled: true,
        algorithm: 'AES-256-GCM'
      },
      auditLogging: {
        enabled: true,
        retentionDays: 365
      }
    };
  }

  static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  // Session Management
  async startSession(userId: string): Promise<void> {
    this.sessionStartTime = Date.now();
    await this.storeSecurely('session_start', this.sessionStartTime.toString());
    await this.storeSecurely('session_user', userId);
    
    await this.logSecurityEvent({
      eventType: 'login',
      userId,
      details: { sessionStart: this.sessionStartTime },
      riskLevel: 'low'
    });

    console.log('🔐 Security session started');
  }

  async validateSession(): Promise<boolean> {
    try {
      const sessionStart = await this.getSecurely('session_start');
      if (!sessionStart) return false;

      const elapsed = Date.now() - parseInt(sessionStart);
      const timeoutMs = this.config.sessionTimeout * 60 * 1000;

      if (elapsed > timeoutMs) {
        await this.endSession();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      return false;
    }
  }

  async extendSession(): Promise<void> {
    this.sessionStartTime = Date.now();
    await this.storeSecurely('session_start', this.sessionStartTime.toString());
  }

  async endSession(): Promise<void> {
    const userId = await this.getSecurely('session_user');
    
    await this.removeSecurely('session_start');
    await this.removeSecurely('session_user');
    
    if (userId) {
      await this.logSecurityEvent({
        eventType: 'logout',
        userId,
        details: { sessionEnd: Date.now() },
        riskLevel: 'low'
      });
    }

    console.log('🔐 Security session ended');
  }

  // Login Attempt Management
  async recordLoginAttempt(identifier: string, success: boolean): Promise<void> {
    const now = Date.now();
    const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: 0 };

    if (success) {
      // Reset attempts on successful login
      this.loginAttempts.delete(identifier);
    } else {
      // Increment failed attempts
      attempts.count++;
      attempts.lastAttempt = now;
      this.loginAttempts.set(identifier, attempts);

      await this.logSecurityEvent({
        eventType: 'failed_login',
        details: { 
          identifier, 
          attemptCount: attempts.count,
          timestamp: now 
        },
        riskLevel: attempts.count >= this.config.maxLoginAttempts ? 'high' : 'medium'
      });
    }
  }

  async isAccountLocked(identifier: string): Promise<boolean> {
    const attempts = this.loginAttempts.get(identifier);
    if (!attempts || attempts.count < this.config.maxLoginAttempts) {
      return false;
    }

    const lockoutMs = this.config.lockoutDuration * 60 * 1000;
    const timeSinceLastAttempt = Date.now() - attempts.lastAttempt;

    if (timeSinceLastAttempt > lockoutMs) {
      // Lockout period expired
      this.loginAttempts.delete(identifier);
      return false;
    }

    return true;
  }

  // Password Security
  validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const policy = this.config.passwordPolicy;

    if (password.length < policy.minLength) {
      errors.push(`Password must be at least ${policy.minLength} characters long`);
    }

    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Check for common weak passwords
    const commonPasswords = ['password', '123456', 'admin', 'mining123'];
    if (commonPasswords.some(common => password.toLowerCase().includes(common))) {
      errors.push('Password contains common weak patterns');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  async hashPassword(password: string): Promise<string> {
    // Placeholder implementation until expo-crypto is installed
    const salt = Math.random().toString(36);
    return password + salt; // Simple concatenation for now
  }

  // Data Encryption
  async encryptSensitiveData(data: string, classification: DataClassification): Promise<string> {
    if (!classification.requiresEncryption || !this.config.dataEncryption.enabled) {
      return data;
    }

    try {
      // Placeholder implementation until expo-crypto is installed
      return `encrypted:${data}_encrypted`;
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt sensitive data');
    }
  }

  async decryptSensitiveData(encryptedData: string): Promise<string> {
    if (!encryptedData.startsWith('encrypted:')) {
      return encryptedData;
    }

    try {
      // In a real implementation, use proper decryption
      return encryptedData.replace('encrypted:', '');
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt sensitive data');
    }
  }

  // Data Classification
  classifyData(dataType: string): DataClassification {
    const classifications: Record<string, DataClassification> = {
      'user_credentials': {
        level: 'restricted',
        requiresEncryption: true,
        accessControl: ['admin', 'system'],
        retentionPeriod: 2555 // 7 years
      },
      'production_data': {
        level: 'confidential',
        requiresEncryption: true,
        accessControl: ['admin', 'supervisor', 'operator'],
        retentionPeriod: 1825 // 5 years
      },
      'safety_incidents': {
        level: 'confidential',
        requiresEncryption: true,
        accessControl: ['admin', 'safety_officer', 'supervisor'],
        retentionPeriod: 2555 // 7 years
      },
      'equipment_data': {
        level: 'internal',
        requiresEncryption: false,
        accessControl: ['admin', 'maintenance_tech', 'supervisor'],
        retentionPeriod: 1095 // 3 years
      },
      'user_profile': {
        level: 'internal',
        requiresEncryption: true,
        accessControl: ['admin', 'user_self'],
        retentionPeriod: 365 // 1 year after account deletion
      }
    };

    return classifications[dataType] || {
      level: 'internal',
      requiresEncryption: false,
      accessControl: ['admin'],
      retentionPeriod: 365
    };
  }

  // Access Control
  async checkDataAccess(userId: string, userRole: string, dataType: string): Promise<boolean> {
    const classification = this.classifyData(dataType);
    
    // Admin always has access
    if (userRole === 'admin') return true;
    
    // Check if user role is in access control list
    if (classification.accessControl.includes(userRole)) return true;
    
    // Check if user has specific access to their own data
    if (classification.accessControl.includes('user_self')) {
      // Additional logic to verify user is accessing their own data
      return true;
    }

    await this.logSecurityEvent({
      eventType: 'security_violation',
      userId,
      details: { 
        violation: 'unauthorized_data_access',
        dataType,
        userRole,
        requiredAccess: classification.accessControl
      },
      riskLevel: 'high'
    });

    return false;
  }

  // Security Monitoring
  async detectAnomalousActivity(userId: string, activity: string): Promise<boolean> {
    // Simple anomaly detection - in production, use more sophisticated algorithms
    const recentEvents = await this.getRecentSecurityEvents(userId, 24); // Last 24 hours
    
    // Check for unusual patterns
    const loginEvents = recentEvents.filter(e => e.eventType === 'login');
    const dataAccessEvents = recentEvents.filter(e => e.eventType === 'data_access');
    
    // Flag if too many logins from different locations
    if (loginEvents.length > 10) {
      await this.logSecurityEvent({
        eventType: 'security_violation',
        userId,
        details: { 
          violation: 'excessive_login_attempts',
          count: loginEvents.length,
          timeframe: '24_hours'
        },
        riskLevel: 'medium'
      });
      return true;
    }

    // Flag if accessing too much data
    if (dataAccessEvents.length > 100) {
      await this.logSecurityEvent({
        eventType: 'security_violation',
        userId,
        details: { 
          violation: 'excessive_data_access',
          count: dataAccessEvents.length,
          timeframe: '24_hours'
        },
        riskLevel: 'medium'
      });
      return true;
    }

    return false;
  }

  // Audit Logging
  async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): Promise<void> {
    if (!this.config.auditLogging.enabled) return;

    const securityEvent: SecurityEvent = {
      id: await this.generateSecureId(),
      timestamp: Date.now(),
      ...event
    };

    try {
      // Store security events
      const events = await this.getStoredSecurityEvents();
      events.push(securityEvent);
      
      // Keep only events within retention period
      const retentionMs = this.config.auditLogging.retentionDays * 24 * 60 * 60 * 1000;
      const cutoff = Date.now() - retentionMs;
      const filteredEvents = events.filter(e => e.timestamp > cutoff);
      
      await AsyncStorage.setItem('security_events', JSON.stringify(filteredEvents));
      
      // Log high-risk events immediately
      if (event.riskLevel === 'high' || event.riskLevel === 'critical') {
        console.warn('🚨 High-risk security event:', securityEvent);
      }
      
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  async getSecurityEvents(userId?: string, hours?: number): Promise<SecurityEvent[]> {
    const events = await this.getStoredSecurityEvents();
    
    let filteredEvents = events;
    
    if (userId) {
      filteredEvents = filteredEvents.filter(e => e.userId === userId);
    }
    
    if (hours) {
      const cutoff = Date.now() - (hours * 60 * 60 * 1000);
      filteredEvents = filteredEvents.filter(e => e.timestamp > cutoff);
    }
    
    return filteredEvents.sort((a, b) => b.timestamp - a.timestamp);
  }

  // Secure Storage Utilities
  private async storeSecurely(key: string, value: string): Promise<void> {
    // Placeholder implementation until expo-secure-store is installed
    console.log(`Storing securely: ${key}`);
  }

  private async getSecurely(key: string): Promise<string | null> {
    // Placeholder implementation until expo-secure-store is installed
    console.log(`Getting securely: ${key}`);
    return null;
  }

  private async removeSecurely(key: string): Promise<void> {
    // Placeholder implementation until expo-secure-store is installed
    console.log(`Removing securely: ${key}`);
  }

  private async generateSecureId(): Promise<string> {
    // Placeholder implementation until expo-crypto is installed
    return `${Date.now()}_${Math.random()}`;
  }

  private async getStoredSecurityEvents(): Promise<SecurityEvent[]> {
    try {
      const stored = await AsyncStorage.getItem('security_events');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get stored security events:', error);
      return [];
    }
  }

  private async getRecentSecurityEvents(userId: string, hours: number): Promise<SecurityEvent[]> {
    return this.getSecurityEvents(userId, hours);
  }

  // Network Security
  async validateNetworkSecurity(): Promise<{ secure: boolean; warnings: string[] }> {
    const warnings: string[] = [];
    
    try {
      const networkState = await NetInfo.fetch();
      
      // Check if using secure connection
      if (networkState.type === 'wifi') {
        // In a real implementation, check if WiFi is secured
        warnings.push('Ensure WiFi network is secured with WPA2/WPA3');
      }
      
      if (networkState.type === 'cellular') {
        // Cellular is generally secure
      }
      
      // Check if VPN is recommended for this network
      if (this.shouldRecommendVPN(networkState)) {
        warnings.push('Consider using VPN for additional security');
      }
      
      return {
        secure: warnings.length === 0,
        warnings
      };
    } catch (error) {
      return {
        secure: false,
        warnings: ['Unable to verify network security']
      };
    }
  }

  private shouldRecommendVPN(networkState: any): boolean {
    // Recommend VPN for public networks or unknown networks
    return networkState.type === 'wifi' && !networkState.details?.isConnectionExpensive;
  }

  // Compliance & Reporting
  async generateSecurityReport(startDate: string, endDate: string): Promise<any> {
    const events = await this.getSecurityEvents();
    const filteredEvents = events.filter(e => {
      const eventDate = new Date(e.timestamp);
      return eventDate >= new Date(startDate) && eventDate <= new Date(endDate);
    });

    return {
      period: { startDate, endDate },
      summary: {
        totalEvents: filteredEvents.length,
        loginAttempts: filteredEvents.filter(e => e.eventType === 'login').length,
        failedLogins: filteredEvents.filter(e => e.eventType === 'failed_login').length,
        securityViolations: filteredEvents.filter(e => e.eventType === 'security_violation').length,
        highRiskEvents: filteredEvents.filter(e => e.riskLevel === 'high' || e.riskLevel === 'critical').length
      },
      events: filteredEvents,
      recommendations: this.generateSecurityRecommendations(filteredEvents)
    };
  }

  private generateSecurityRecommendations(events: SecurityEvent[]): string[] {
    const recommendations: string[] = [];
    
    const failedLogins = events.filter(e => e.eventType === 'failed_login').length;
    if (failedLogins > 10) {
      recommendations.push('Consider implementing additional authentication factors');
    }
    
    const violations = events.filter(e => e.eventType === 'security_violation').length;
    if (violations > 0) {
      recommendations.push('Review user access permissions and provide additional security training');
    }
    
    return recommendations;
  }

  // Mining-specific security checks
  async validateMiningOperationAccess(userId: string, operationType: string, locationId?: string): Promise<boolean> {
    const userRole = await this.getUserRole(userId);
    const userLocation = await this.getUserLocation(userId);

    // Check location-based access
    if (locationId && userLocation !== locationId && userRole !== 'admin') {
      await this.logSecurityEvent({
        eventType: 'security_violation',
        userId,
        details: {
          violation: 'location_access_denied',
          requestedLocation: locationId,
          userLocation,
          operationType
        },
        riskLevel: 'medium'
      });
      return false;
    }

    // Check operation-specific permissions
    const operationPermissions: Record<string, string[]> = {
      'production_data_modify': ['admin', 'supervisor'],
      'safety_incident_create': ['admin', 'supervisor', 'safety_officer'],
      'equipment_maintenance': ['admin', 'maintenance_tech', 'supervisor'],
      'user_management': ['admin'],
      'reports_export': ['admin', 'supervisor']
    };

    const allowedRoles = operationPermissions[operationType] || [];
    if (!allowedRoles.includes(userRole)) {
      await this.logSecurityEvent({
        eventType: 'security_violation',
        userId,
        details: {
          violation: 'operation_access_denied',
          operationType,
          userRole,
          allowedRoles
        },
        riskLevel: 'high'
      });
      return false;
    }

    return true;
  }

  private async getUserRole(userId: string): Promise<string> {
    // In real implementation, fetch from database
    return 'operator'; // Placeholder
  }

  private async getUserLocation(userId: string): Promise<string> {
    // In real implementation, fetch from database
    return 'location-1'; // Placeholder
  }
}

export default SecurityService;
