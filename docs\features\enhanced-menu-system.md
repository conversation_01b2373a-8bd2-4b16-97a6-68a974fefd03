# Enhanced Menu System Documentation

## Overview

The Enhanced Menu System provides a modern, animated, and interactive menu grid for the Mining Operations App dashboard. This system includes sophisticated animations, loading states, particle effects, and notification badges to create an engaging user experience.

## Features

### 🎨 **Visual Enhancements**
- **Staggered Entrance Animations**: Menu items appear with 120ms cascading delay using cubic easing
- **Interactive Press Animations**: Enhanced scale (0.92) and rotation (back easing) effects
- **Advanced Shimmer Effects**: Multi-dimensional shimmer with opacity, translateX, and scaleX animations
- **Gradient Overlays**: Category-based color overlays (30% opacity) on background images
- **Dynamic Shadow Animations**: Smooth shadow transitions with quadratic easing
- **Built-in Animated API**: Uses React Native's native Animated API for optimal performance

### 🔔 **Notification System**
- **Badge Notifications**: Red circular badges with notification counts
- **Smart Badge Display**: Shows "9+" for counts over 9
- **Contextual Notifications**: Applied to relevant menu items (Attendance, Bakomsel)

### ⚡ **Loading & Performance**
- **Enhanced Skeleton Loading**: Dual-animation system with shimmer + pulse effects
- **Optimized Particle Effects**: Floating particles with cubic easing and reduced opacity
- **Hardware-Accelerated Animations**: All animations use native drivers for 60fps performance
- **Progressive Loading**: 1.5s skeleton → staggered menu appearance → particle activation
- **No External Dependencies**: Uses React Native's built-in Animated API only

## Component Architecture

### Core Components

#### 1. AnimatedMenuItem (`src/components/menu/AnimatedMenuItem.tsx`)

Enhanced menu item component with comprehensive animation support:

```typescript
interface AnimatedMenuItemProps {
  id: string;
  title: string;
  icon: string;
  color: string;
  onPress: () => void;
  index: number;
  hasNotification?: boolean;
  notificationCount?: number;
}
```

**Key Features:**
- Entrance animations with stagger effect
- Press/release interaction animations
- Shimmer effect overlay
- Notification badge support
- Accessibility compliance

#### 2. MenuGridSkeleton (`src/components/menu/MenuGridSkeleton.tsx`)

Loading skeleton component for menu grid:

```typescript
interface MenuGridSkeletonProps {
  itemCount?: number; // Default: 11
}
```

**Features:**
- Animated shimmer effect
- Configurable item count
- Matches menu grid layout
- Smooth transition to actual content

#### 3. ParticleEffect (`src/components/effects/ParticleEffect.tsx`)

Ambient particle animation system:

```typescript
interface ParticleEffectProps {
  particleCount?: number;  // Default: 8
  duration?: number;       // Default: 3000ms
  colors?: string[];       // Default: theme colors
}
```

**Features:**
- Floating particle animations
- Configurable particle count and colors
- Loop animations with random positioning
- Non-interactive overlay (pointerEvents: none)

## Animation System

### Entrance Animations

```typescript
// Staggered entrance with 100ms delay per item
const delay = index * 100;

Animated.parallel([
  Animated.timing(fadeAnim, {
    toValue: 1,
    duration: 600,
    delay,
    useNativeDriver: true,
  }),
  Animated.spring(scaleAnim, {
    toValue: 1,
    delay,
    tension: 100,
    friction: 8,
    useNativeDriver: true,
  }),
]).start();
```

### Interaction Animations

```typescript
// Press animation
const handlePressIn = () => {
  Animated.parallel([
    Animated.spring(pressScaleAnim, {
      toValue: 0.95,
      tension: 300,
      friction: 10,
      useNativeDriver: true,
    }),
    Animated.timing(rotateAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }),
  ]).start();
};
```

### Shimmer Effect

```typescript
// Continuous shimmer animation
const shimmerAnimation = Animated.loop(
  Animated.sequence([
    Animated.timing(shimmerAnim, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    }),
    Animated.timing(shimmerAnim, {
      toValue: 0,
      duration: 1500,
      useNativeDriver: true,
    }),
  ])
);
```

## Implementation in DashboardScreen

### Enhanced Menu Rendering

```typescript
const renderEnhancedMenu = () => {
  if (menuLoading) {
    return <MenuGridSkeleton itemCount={11} />;
  }

  return (
    <View style={styles.menuContainer}>
      {showParticles && <ParticleEffect particleCount={6} duration={4000} />}
      <View style={styles.menuGrid}>
        {menuItems.map((item, index) => (
          <AnimatedMenuItem
            key={item.id}
            {...item}
            index={index}
          />
        ))}
      </View>
    </View>
  );
};
```

### Loading State Management

```typescript
const [menuLoading, setMenuLoading] = useState(true);
const [showParticles, setShowParticles] = useState(false);

useEffect(() => {
  const timer = setTimeout(() => {
    setMenuLoading(false);
    setShowParticles(true);
  }, 1500);

  return () => clearTimeout(timer);
}, []);
```

## Visual Hierarchy

```
🏔️ Header Background (Mining landscape) - UNCHANGED
  ├── 🌫️ Dark overlay
  ├── 👤 Profile section
  ├── 🔍 Search bar
  └── 📅 Date display

📱 Content Area
  ├── 🏷️ Tab navigation
  ├── 🎯 Enhanced Menu Grid ⭐ NEW
  │   ├── ⚡ Particle effects (background)
  │   ├── 📱 Animated menu items
  │   │   ├── 🖼️ Background image (menu bg.png)
  │   │   ├── 🌈 Gradient overlay (category color)
  │   │   ├── 🌫️ Icon overlay (semi-transparent)
  │   │   ├── 📱 Ionicon (white)
  │   │   ├── ✨ Shimmer effect
  │   │   └── 🔔 Notification badge (if applicable)
  │   └── 📝 Menu title
  └── 📺 Video section
```

## Performance Optimizations

### Native Driver Usage
- All transform and opacity animations use native driver
- Smooth 60fps animations on both platforms
- Reduced JavaScript thread blocking

### Animation Cleanup
- Proper cleanup of animation listeners
- Memory leak prevention
- Efficient re-rendering

### Conditional Rendering
- Particles only render when menu is loaded
- Skeleton only shows during loading state
- Optimized component mounting/unmounting

## Accessibility Features

### Screen Reader Support
```typescript
<TouchableOpacity
  accessible={true}
  accessibilityLabel={`${title} menu item`}
  accessibilityRole="button"
>
```

### Reduced Motion Support
- Animations respect system accessibility settings
- Fallback to static states when needed
- Maintained functionality without animations

## Customization Options

### Animation Timing
- Entrance delay: 100ms per item
- Press animation: 200ms duration
- Shimmer cycle: 3000ms total
- Particle cycle: 4000ms

### Visual Customization
- Gradient overlay opacity: 30%
- Icon overlay opacity: 40%
- Notification badge colors: Error red with white text
- Shadow presets: Button shadow from shadow system

## Benefits

### User Experience
1. **Engaging Interactions**: Responsive animations provide immediate feedback
2. **Professional Feel**: Sophisticated animations enhance perceived quality
3. **Visual Hierarchy**: Staggered animations guide user attention
4. **Loading Feedback**: Skeleton screens prevent perceived lag

### Technical Benefits
1. **Performance**: Hardware-accelerated animations
2. **Maintainability**: Modular component architecture
3. **Scalability**: Easy to add new menu items with animations
4. **Accessibility**: Full screen reader and reduced motion support

## Related Documentation

- [Dashboard Screen](dashboard-screen.md) - Main implementation
- [Shadow System](../development/shadow-system.md) - Shadow effects
- [Design System](../design/design-system.md) - Color and styling
- [Animation Guidelines](../development/animation-guidelines.md) - Animation standards

## File References

### Implementation Files
- `src/components/menu/AnimatedMenuItem.tsx` - Enhanced menu item
- `src/components/menu/MenuGridSkeleton.tsx` - Loading skeleton
- `src/components/effects/ParticleEffect.tsx` - Particle animations
- `src/screens/DashboardScreen.tsx` - Main integration

### Assets
- `assets/menu bg.png` - Menu item background image
