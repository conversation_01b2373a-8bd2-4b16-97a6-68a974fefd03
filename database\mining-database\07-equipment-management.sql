-- =====================================================
-- Mining Operations Database - Equipment Management
-- =====================================================
-- File: 07-equipment-management.sql
-- Description: Equipment tracking, monitoring, and maintenance
-- Dependencies: 01-core-setup.sql, 06-user-management.sql
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- ADDITIONAL ENUMS FOR EQUIPMENT
-- =====================================================

-- Equipment types
CREATE TYPE equipment_type AS ENUM (
    'Excavator',
    'Dump Truck',
    'Loader',
    'Bulldozer',
    'Grader',
    'Drill',
    'Crusher',
    'Conveyor',
    'Generator',
    'Pump',
    'Other'
);

-- Equipment status
CREATE TYPE equipment_status AS ENUM (
    'Active',
    'Maintenance',
    'Repair',
    'Standby',
    'Decommissioned'
);

-- Maintenance types
CREATE TYPE maintenance_type AS ENUM (
    'Preventive',
    'Corrective',
    'Emergency',
    'Inspection',
    'Overhaul'
);

-- =====================================================
-- EQUIPMENT TABLE
-- =====================================================
CREATE TABLE equipment (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Equipment Identification
    equipment_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    type equipment_type NOT NULL,
    
    -- Manufacturer Information
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100) UNIQUE,
    year_manufactured INTEGER,
    
    -- Purchase Information
    purchase_date DATE,
    purchase_cost DECIMAL(12, 2),
    supplier VARCHAR(200),
    
    -- Technical Specifications
    engine_model VARCHAR(100),
    engine_power DECIMAL(8, 2), -- Power in kW
    fuel_capacity DECIMAL(8, 2), -- Fuel capacity in liters
    max_load_capacity DECIMAL(10, 2), -- Maximum load in tons
    operating_weight DECIMAL(10, 2), -- Operating weight in tons
    
    -- Location and Assignment
    current_location VARCHAR(200),
    home_location VARCHAR(200), -- Primary assigned location
    
    -- Operational Information
    status equipment_status DEFAULT 'Active',
    operational_hours DECIMAL(10, 2) DEFAULT 0,
    last_service_date DATE,
    next_service_date DATE,
    service_interval_hours INTEGER DEFAULT 250,
    
    -- Operator Assignment
    primary_operator_id UUID REFERENCES user_profiles(id),
    secondary_operator_id UUID REFERENCES user_profiles(id),
    
    -- Identification
    qr_code VARCHAR(200),
    photo_url TEXT,
    
    -- Status and Lifecycle
    is_active BOOLEAN DEFAULT true,
    commissioned_date DATE,
    decommissioned_date DATE,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),
    
    -- Constraints
    CHECK (year_manufactured > 1900 AND year_manufactured <= EXTRACT(YEAR FROM CURRENT_DATE) + 1),
    CHECK (purchase_cost >= 0),
    CHECK (fuel_capacity >= 0),
    CHECK (max_load_capacity >= 0),
    CHECK (operating_weight >= 0),
    CHECK (operational_hours >= 0),
    CHECK (service_interval_hours > 0),
    CHECK (decommissioned_date IS NULL OR decommissioned_date >= commissioned_date)
);

-- =====================================================
-- EQUIPMENT METRICS TABLE
-- =====================================================
CREATE TABLE equipment_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    equipment_id UUID NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    
    -- Metric Period
    recorded_date DATE NOT NULL,
    shift_number INTEGER CHECK (shift_number IN (1, 2)),
    
    -- Operational Hours
    operating_hours DECIMAL(5, 2) DEFAULT 0,
    planned_hours DECIMAL(5, 2) DEFAULT 0,
    maintenance_hours DECIMAL(5, 2) DEFAULT 0,
    downtime_hours DECIMAL(5, 2) DEFAULT 0,
    
    -- Utilization Metrics
    utilization_rate DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN planned_hours > 0 THEN (operating_hours / planned_hours * 100)
            ELSE 0 
        END
    ) STORED,
    availability_rate DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN (operating_hours + maintenance_hours + downtime_hours) > 0 THEN 
                (operating_hours / (operating_hours + maintenance_hours + downtime_hours) * 100)
            ELSE 0 
        END
    ) STORED,
    
    -- Fuel and Consumption
    fuel_consumed DECIMAL(8, 2) DEFAULT 0, -- Liters
    fuel_cost DECIMAL(8, 2) DEFAULT 0,
    fuel_efficiency DECIMAL(6, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN fuel_consumed > 0 AND operating_hours > 0 THEN fuel_consumed / operating_hours
            ELSE 0 
        END
    ) STORED, -- Liters per hour
    
    -- Performance Metrics
    distance_traveled DECIMAL(8, 2) DEFAULT 0, -- Kilometers
    load_cycles INTEGER DEFAULT 0,
    material_moved DECIMAL(10, 2) DEFAULT 0, -- Tons
    productivity_rate DECIMAL(8, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN operating_hours > 0 THEN material_moved / operating_hours
            ELSE 0 
        END
    ) STORED, -- Tons per hour
    
    -- Health and Condition
    health_score DECIMAL(5, 2) DEFAULT 100, -- Overall health score (0-100)
    engine_temperature DECIMAL(5, 2),
    oil_pressure DECIMAL(6, 2),
    battery_voltage DECIMAL(4, 2),
    
    -- Location and Assignment
    location VARCHAR(200),
    operator_id UUID REFERENCES user_profiles(id),
    
    -- Data Collection
    recorded_by UUID REFERENCES user_profiles(id),
    data_source VARCHAR(50) DEFAULT 'Manual', -- 'Manual', 'Automatic', 'GPS', 'Sensor'
    
    -- Notes
    notes TEXT,
    issues_reported TEXT,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(equipment_id, recorded_date, shift_number),
    CHECK (operating_hours >= 0),
    CHECK (planned_hours >= 0),
    CHECK (maintenance_hours >= 0),
    CHECK (downtime_hours >= 0),
    CHECK (fuel_consumed >= 0),
    CHECK (distance_traveled >= 0),
    CHECK (load_cycles >= 0),
    CHECK (material_moved >= 0),
    CHECK (health_score BETWEEN 0 AND 100)
);

-- =====================================================
-- MAINTENANCE SCHEDULES TABLE
-- =====================================================
CREATE TABLE maintenance_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Schedule Identification
    schedule_name VARCHAR(200) NOT NULL,
    equipment_id UUID NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    
    -- Maintenance Details
    maintenance_type maintenance_type NOT NULL,
    description TEXT,
    
    -- Scheduling Rules
    frequency_type VARCHAR(50) NOT NULL, -- 'Hours', 'Days', 'Weeks', 'Months'
    frequency_value INTEGER NOT NULL,
    
    -- Time-based scheduling
    start_date DATE,
    next_due_date DATE,
    
    -- Usage-based scheduling
    last_service_hours DECIMAL(10, 2),
    service_interval_hours INTEGER,
    next_service_hours DECIMAL(10, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN last_service_hours IS NOT NULL AND service_interval_hours IS NOT NULL 
            THEN last_service_hours + service_interval_hours
            ELSE NULL 
        END
    ) STORED,
    
    -- Work Details
    estimated_duration_hours DECIMAL(4, 2),
    required_skills TEXT[],
    estimated_cost DECIMAL(10, 2),
    
    -- Assignment
    assigned_technician_id UUID REFERENCES user_profiles(id),
    supervisor_id UUID REFERENCES user_profiles(id),
    
    -- Schedule Status
    is_active BOOLEAN DEFAULT true,
    is_critical BOOLEAN DEFAULT false,
    
    -- Notifications
    advance_notice_days INTEGER DEFAULT 7,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),
    
    -- Constraints
    CHECK (frequency_value > 0),
    CHECK (estimated_duration_hours > 0),
    CHECK (estimated_cost >= 0),
    CHECK (advance_notice_days >= 0),
    CHECK (service_interval_hours IS NULL OR service_interval_hours > 0)
);

-- =====================================================
-- WORK ORDERS TABLE
-- =====================================================
CREATE TABLE work_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Work Order Identification
    work_order_number VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    
    -- Work Order Type
    work_order_type VARCHAR(50) DEFAULT 'Maintenance',
    maintenance_type maintenance_type,
    priority priority_level DEFAULT 'Medium',
    
    -- Equipment and Location
    equipment_id UUID REFERENCES equipment(id),
    location VARCHAR(200),
    
    -- Related Records
    maintenance_schedule_id UUID REFERENCES maintenance_schedules(id),
    
    -- Status and Workflow
    status VARCHAR(50) DEFAULT 'Created', -- 'Created', 'Assigned', 'In Progress', 'Completed', 'Cancelled'
    
    -- Scheduling
    requested_date DATE,
    scheduled_start_date DATE,
    scheduled_end_date DATE,
    actual_start_date DATE,
    actual_end_date DATE,
    estimated_duration_hours DECIMAL(6, 2),
    actual_duration_hours DECIMAL(6, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN actual_start_date IS NOT NULL AND actual_end_date IS NOT NULL THEN
                EXTRACT(EPOCH FROM (actual_end_date::TIMESTAMP - actual_start_date::TIMESTAMP)) / 3600
            ELSE NULL
        END
    ) STORED,
    
    -- Personnel Assignment
    requested_by UUID REFERENCES user_profiles(id),
    assigned_to UUID REFERENCES user_profiles(id),
    supervisor_id UUID REFERENCES user_profiles(id),
    
    -- Work Details
    work_instructions TEXT,
    safety_requirements TEXT,
    required_tools TEXT[],
    
    -- Cost Information
    labor_cost DECIMAL(10, 2) DEFAULT 0,
    parts_cost DECIMAL(10, 2) DEFAULT 0,
    other_costs DECIMAL(10, 2) DEFAULT 0,
    total_cost DECIMAL(10, 2) GENERATED ALWAYS AS (
        COALESCE(labor_cost, 0) + COALESCE(parts_cost, 0) + COALESCE(other_costs, 0)
    ) STORED,
    
    -- Completion Details
    work_performed TEXT,
    issues_encountered TEXT,
    recommendations TEXT,
    
    -- Quality and Approval
    quality_check_required BOOLEAN DEFAULT false,
    quality_checked_by UUID REFERENCES user_profiles(id),
    quality_check_date DATE,
    quality_check_passed BOOLEAN,
    
    approved_by UUID REFERENCES user_profiles(id),
    approved_at TIMESTAMPTZ,
    
    -- Follow-up
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    follow_up_notes TEXT,
    
    -- Documentation
    photos_before TEXT[],
    photos_after TEXT[],
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),
    
    -- Constraints
    CHECK (estimated_duration_hours > 0),
    CHECK (labor_cost >= 0),
    CHECK (parts_cost >= 0),
    CHECK (other_costs >= 0),
    CHECK (scheduled_end_date IS NULL OR scheduled_end_date >= scheduled_start_date),
    CHECK (actual_end_date IS NULL OR actual_end_date >= actual_start_date)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Equipment Indexes
CREATE INDEX idx_equipment_number ON equipment(equipment_number);
CREATE INDEX idx_equipment_type ON equipment(type);
CREATE INDEX idx_equipment_status ON equipment(status, is_active);
CREATE INDEX idx_equipment_location ON equipment(current_location);
CREATE INDEX idx_equipment_operator ON equipment(primary_operator_id);
CREATE INDEX idx_equipment_service_date ON equipment(next_service_date);

-- Equipment Metrics Indexes
CREATE INDEX idx_equipment_metrics_equipment ON equipment_metrics(equipment_id);
CREATE INDEX idx_equipment_metrics_date ON equipment_metrics(recorded_date);
CREATE INDEX idx_equipment_metrics_shift ON equipment_metrics(shift_number);
CREATE INDEX idx_equipment_metrics_location ON equipment_metrics(location);
CREATE INDEX idx_equipment_metrics_operator ON equipment_metrics(operator_id);
CREATE INDEX idx_equipment_metrics_equipment_date ON equipment_metrics(equipment_id, recorded_date);

-- Maintenance Schedules Indexes
CREATE INDEX idx_maintenance_schedules_equipment ON maintenance_schedules(equipment_id);
CREATE INDEX idx_maintenance_schedules_type ON maintenance_schedules(maintenance_type);
CREATE INDEX idx_maintenance_schedules_due_date ON maintenance_schedules(next_due_date);
CREATE INDEX idx_maintenance_schedules_technician ON maintenance_schedules(assigned_technician_id);
CREATE INDEX idx_maintenance_schedules_active ON maintenance_schedules(is_active, is_critical);

-- Work Orders Indexes
CREATE INDEX idx_work_orders_number ON work_orders(work_order_number);
CREATE INDEX idx_work_orders_equipment ON work_orders(equipment_id);
CREATE INDEX idx_work_orders_location ON work_orders(location);
CREATE INDEX idx_work_orders_status ON work_orders(status);
CREATE INDEX idx_work_orders_priority ON work_orders(priority);
CREATE INDEX idx_work_orders_assigned ON work_orders(assigned_to);
CREATE INDEX idx_work_orders_scheduled ON work_orders(scheduled_start_date, scheduled_end_date);
CREATE INDEX idx_work_orders_type ON work_orders(work_order_type, maintenance_type);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update updated_at timestamp
CREATE TRIGGER update_equipment_updated_at 
    BEFORE UPDATE ON equipment 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_equipment_metrics_updated_at 
    BEFORE UPDATE ON equipment_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_schedules_updated_at 
    BEFORE UPDATE ON maintenance_schedules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_work_orders_updated_at 
    BEFORE UPDATE ON work_orders 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Sample Equipment
INSERT INTO equipment (
    equipment_number, name, type, manufacturer, model, year_manufactured, 
    current_location, status, operational_hours, service_interval_hours,
    primary_operator_id, created_by
) VALUES
('EX001', 'Excavator CAT 320D', 'Excavator', 'Caterpillar', '320D', 2020, 'Pit A', 'Active', 2450.5, 250, 
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'), 
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('DT001', 'Dump Truck Komatsu HD785', 'Dump Truck', 'Komatsu', 'HD785-7', 2019, 'Pit A', 'Active', 3200.0, 300,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('LD001', 'Wheel Loader CAT 980K', 'Loader', 'Caterpillar', '980K', 2021, 'Pit B', 'Active', 1800.5, 250,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('BD001', 'Bulldozer CAT D8T', 'Bulldozer', 'Caterpillar', 'D8T', 2018, 'Pit C', 'Active', 4100.0, 300,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Sample Maintenance Schedules
INSERT INTO maintenance_schedules (
    schedule_name, equipment_id, maintenance_type, frequency_type, frequency_value,
    next_due_date, service_interval_hours, estimated_duration_hours, is_critical,
    assigned_technician_id, created_by
) VALUES
('EX001 - 250 Hour Service', 
 (SELECT id FROM equipment WHERE equipment_number = 'EX001'), 
 'Preventive', 'Hours', 250, CURRENT_DATE + 7, 250, 4.0, true,
 (SELECT id FROM user_profiles WHERE role = 'Equipment Operator' LIMIT 1),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('DT001 - Monthly Inspection', 
 (SELECT id FROM equipment WHERE equipment_number = 'DT001'), 
 'Inspection', 'Days', 30, CURRENT_DATE + 15, NULL, 2.0, false,
 (SELECT id FROM user_profiles WHERE role = 'Equipment Operator' LIMIT 1),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Sample Work Orders
INSERT INTO work_orders (
    work_order_number, title, description, equipment_id, maintenance_type, priority, status,
    scheduled_start_date, estimated_duration_hours, requested_by, assigned_to, created_by
) VALUES
('WO-2024-001', 'Replace hydraulic filter on EX001', 
 'Scheduled maintenance - replace hydraulic filter and check fluid levels',
 (SELECT id FROM equipment WHERE equipment_number = 'EX001'),
 'Preventive', 'Medium', 'Assigned', CURRENT_DATE + 1, 4.0,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM user_profiles WHERE role = 'Equipment Operator' LIMIT 1),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('WO-2024-002', 'Repair tire on DT001',
 'Emergency repair - replace damaged tire on rear axle',
 (SELECT id FROM equipment WHERE equipment_number = 'DT001'),
 'Emergency', 'High', 'In Progress', CURRENT_DATE, 6.0,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM user_profiles WHERE role = 'Equipment Operator' LIMIT 1),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE equipment IS 'Equipment registry with specifications and tracking';
COMMENT ON TABLE equipment_metrics IS 'Daily equipment performance metrics and KPIs';
COMMENT ON TABLE maintenance_schedules IS 'Preventive maintenance schedules for equipment';
COMMENT ON TABLE work_orders IS 'Maintenance work orders and job tracking';

COMMENT ON COLUMN equipment.equipment_number IS 'Unique equipment identifier';
COMMENT ON COLUMN equipment_metrics.utilization_rate IS 'Auto-calculated utilization percentage';
COMMENT ON COLUMN equipment_metrics.availability_rate IS 'Auto-calculated availability percentage';
COMMENT ON COLUMN equipment_metrics.fuel_efficiency IS 'Auto-calculated fuel efficiency (L/hr)';
COMMENT ON COLUMN equipment_metrics.productivity_rate IS 'Auto-calculated productivity (tons/hr)';
COMMENT ON COLUMN work_orders.actual_duration_hours IS 'Auto-calculated actual work duration';
COMMENT ON COLUMN work_orders.total_cost IS 'Auto-calculated total cost from labor, parts, and other costs';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('007', 'Equipment management with maintenance tracking')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    equipment_count INTEGER;
    schedule_count INTEGER;
    work_order_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO equipment_count FROM equipment;
    SELECT COUNT(*) INTO schedule_count FROM maintenance_schedules;
    SELECT COUNT(*) INTO work_order_count FROM work_orders;
    
    RAISE NOTICE 'Equipment management setup completed successfully';
    RAISE NOTICE 'Sample equipment created: %', equipment_count;
    RAISE NOTICE 'Maintenance schedules: %', schedule_count;
    RAISE NOTICE 'Work orders: %', work_order_count;
    RAISE NOTICE 'Equipment tracking and maintenance management ready';
END $$;
