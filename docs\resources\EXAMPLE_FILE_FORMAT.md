# 📋 EXAMPLE FILE FORMAT - Template dan <PERSON><PERSON><PERSON>

> **📝 File**: `EXAMPLE_FILE_FORMAT.md`  
> **📅 Created**: 15 January 2025  
> **🔄 Last Updated**: 15 January 2025  
> **👤 Author**: Augment AI Agent  
> **📋 Version**: v1.0  
> **✅ Status**: Production Ready  
> **🎯 Purpose**: Template dan contoh format penulisan file .md yang konsisten

---

## 📋 Table of Contents
- [Format Nama File](#format-nama-file)
- [Template Header](#template-header)
- [Contoh File yang Benar](#contoh-file-yang-benar)
- [Contoh File yang Salah](#contoh-file-yang-salah)
- [Validasi Format](#validasi-format)

---

## 🔢 FORMAT NAMA FILE

### **✅ Format yang BENAR:**
```bash
# Untuk file dalam folder (dengan nomor)
01-overview.md
02-database-design.md
03-navigation-system.md
04-flow-diagrams.md
05-implementation-roadmap.md

# Untuk file root (tanpa nomor, hanya file khusus)
README.md
index.md
implementation-progress.md
AI_AGENT_DOCUMENTATION_RULES.md
```

### **❌ Format yang SALAH:**
```bash
# Tanpa nomor di folder
overview.md
database-design.md

# Format salah
Database-Design.md
navigation_system.md
01_overview.md
1-overview.md (tanpa leading zero)
01-Very-Long-File-Name-That-Is-Too-Descriptive.md
```

---

## 📄 TEMPLATE HEADER

### **📋 Template untuk File dengan Nomor:**
```markdown
# [Nomor]. [Judul Lengkap]

> **📝 File**: `[nomor]-[nama-file].md`  
> **📅 Created**: [DD Month YYYY]  
> **🔄 Last Updated**: [DD Month YYYY]  
> **👤 Author**: [AI Agent/Developer Name]  
> **📋 Version**: [v1.0/v2.0/etc]  
> **✅ Status**: [Draft/In Progress/Complete/Production Ready]  
> **🎯 Purpose**: [Brief description of file purpose]

---

## Table of Contents
- [Section 1](#section-1)
- [Section 2](#section-2)

---

[Content starts here]
```

### **📋 Template untuk File Root (Tanpa Nomor):**
```markdown
# 📊 [TITLE]

> **📝 File**: `[nama-file].md`  
> **📅 Created**: [DD Month YYYY]  
> **🔄 Last Updated**: [DD Month YYYY]  
> **👤 Author**: [AI Agent/Developer Name]  
> **📋 Version**: [v1.0/v2.0/etc]  
> **✅ Status**: [Draft/In Progress/Complete/Production Ready]  
> **🎯 Purpose**: [Brief description of file purpose]

---

[Content starts here]
```

---

## ✅ CONTOH FILE YANG BENAR

### **File: `architecture/01-overview.md`**
```markdown
# 01. System Architecture Overview

> **📝 File**: `01-overview.md`  
> **📅 Created**: 15 January 2025  
> **🔄 Last Updated**: 15 January 2025  
> **👤 Author**: Augment AI Agent  
> **📋 Version**: v2.0  
> **✅ Status**: Production Ready  
> **🎯 Purpose**: Comprehensive system architecture documentation for Mining Operations App

---

## Table of Contents
- [High-Level Architecture](#high-level-architecture)
- [Technology Stack](#technology-stack)
- [Component Overview](#component-overview)

---

## High-Level Architecture

The Mining Operations App follows a modern React Native architecture...

[Content continues...]
```

### **File: `features/02-production-overview.md`**
```markdown
# 02. Production Overview Feature

> **📝 File**: `02-production-overview.md`  
> **📅 Created**: 10 January 2025  
> **🔄 Last Updated**: 15 January 2025  
> **👤 Author**: Augment AI Agent  
> **📋 Version**: v1.2  
> **✅ Status**: Complete  
> **🎯 Purpose**: Documentation for production analytics and monitoring feature

---

## Table of Contents
- [Feature Overview](#feature-overview)
- [Mining Calculations](#mining-calculations)
- [Data Visualization](#data-visualization)

---

## Feature Overview

The Production Overview screen provides real-time analytics...

[Content continues...]
```

---

## ❌ CONTOH FILE YANG SALAH

### **❌ Nama File Salah:**
```markdown
# File: architecture/overview.md (SALAH - tidak ada nomor)
# File: features/Production-Overview.md (SALAH - PascalCase)
# File: development/setup_guide.md (SALAH - underscore)
```

### **❌ Header Tidak Lengkap:**
```markdown
# System Overview

> **Updated**: January 2025  
> **Status**: Complete

[Content] - SALAH! Missing required fields
```

### **❌ Format Tanggal Salah:**
```markdown
> **📅 Created**: Jan 15 2025  (SALAH)
> **📅 Created**: 2025-01-15   (SALAH)
> **📅 Created**: 15/01/2025   (SALAH)
> **📅 Created**: January 15th 2025  (SALAH)

# Yang BENAR:
> **📅 Created**: 15 January 2025
```

---

## 🔍 VALIDASI FORMAT

### **🛠️ Script Validasi:**
```bash
# Jalankan validasi struktur dan format
npm run docs:validate

# Jalankan pengecekan konten
npm run docs:check
```

### **✅ Checklist Validasi:**
```bash
□ Nama file menggunakan format [number]-[name].md
□ Header lengkap dengan semua field required
□ Format tanggal: DD Month YYYY
□ Version number konsisten
□ Status field diisi dengan benar
□ Purpose field menjelaskan tujuan file
□ Table of Contents ada (untuk file panjang)
□ Content terstruktur dengan baik
```

### **🎯 Field Status yang Valid:**
```bash
✅ VALID:
- Draft
- In Progress  
- Complete
- Production Ready

❌ INVALID:
- WIP
- Done
- Finished
- Ready
```

### **🎯 Field Version yang Valid:**
```bash
✅ VALID:
- v1.0, v1.1, v1.2
- v2.0, v2.1
- v3.0

❌ INVALID:
- 1.0, 2.0 (tanpa 'v')
- version 1.0
- V1.0 (uppercase V)
```

---

## 🎯 MANFAAT FORMAT KONSISTEN

### **✅ Keuntungan:**
- **Easy Navigation** - Nomor urut memudahkan navigasi
- **Consistent Structure** - Semua file memiliki format yang sama
- **Better Tracking** - Version dan date tracking yang jelas
- **Professional Look** - Dokumentasi terlihat profesional
- **Easy Maintenance** - Mudah untuk maintain dan update
- **Automated Validation** - Bisa divalidasi dengan script

### **🔍 Searchability:**
- File mudah dicari berdasarkan nomor
- Header fields memudahkan filtering
- Consistent naming memudahkan grep/search
- Version tracking memudahkan change management

---

**🎯 INGAT: Konsistensi adalah kunci untuk dokumentasi yang maintainable!**  
**📋 Selalu gunakan template ini untuk semua file .md baru**  
**🔄 Update "Last Updated" setiap kali mengedit file**
