import { CacheStrategy } from '../services/SmartCacheManager';

// Cache strategies optimized for mining operations
export const CACHE_STRATEGIES = {
  // Production Data Strategies
  HISTORICAL_PRODUCTION: {
    key: 'historical_production',
    ttl: 24 * 60 * 60 * 1000, // 24 hours - historical data doesn't change
    priority: 'high',
    refreshOnExpiry: false,
    networkDependent: true
  } as CacheStrategy,

  TODAY_PRODUCTION: {
    key: 'today_production',
    ttl: 5 * 60 * 1000, // 5 minutes - current data changes frequently
    priority: 'high',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  WEEKLY_PRODUCTION: {
    key: 'weekly_production',
    ttl: 30 * 60 * 1000, // 30 minutes - weekly aggregates
    priority: 'high',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  MONTHLY_PRODUCTION: {
    key: 'monthly_production',
    ttl: 2 * 60 * 60 * 1000, // 2 hours - monthly aggregates
    priority: 'medium',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  // Dashboard Strategies
  DASHBOARD_STATS: {
    key: 'dashboard_stats',
    ttl: 10 * 60 * 1000, // 10 minutes - dashboard overview
    priority: 'high',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  PRODUCTION_OVERVIEW: {
    key: 'production_overview',
    ttl: 10 * 60 * 1000, // 10 minutes - production overview
    priority: 'high',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  // Equipment Strategies
  EQUIPMENT_STATUS: {
    key: 'equipment_status',
    ttl: 30 * 60 * 1000, // 30 minutes - equipment status
    priority: 'medium',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  EQUIPMENT_LIST: {
    key: 'equipment_list',
    ttl: 4 * 60 * 60 * 1000, // 4 hours - equipment list rarely changes
    priority: 'medium',
    refreshOnExpiry: false,
    networkDependent: true
  } as CacheStrategy,

  // Safety Strategies
  SAFETY_INCIDENTS: {
    key: 'safety_incidents',
    ttl: 15 * 60 * 1000, // 15 minutes - safety data is critical
    priority: 'high',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  SAFETY_CHECKLISTS: {
    key: 'safety_checklists',
    ttl: 24 * 60 * 60 * 1000, // 24 hours - checklists don't change often
    priority: 'high',
    refreshOnExpiry: false,
    networkDependent: false // Critical for offline work
  } as CacheStrategy,

  // User & Settings Strategies
  USER_PROFILE: {
    key: 'user_profile',
    ttl: 24 * 60 * 60 * 1000, // 24 hours - user profile
    priority: 'medium',
    refreshOnExpiry: false,
    networkDependent: true
  } as CacheStrategy,

  APP_SETTINGS: {
    key: 'app_settings',
    ttl: 7 * 24 * 60 * 60 * 1000, // 7 days - app settings
    priority: 'low',
    refreshOnExpiry: false,
    networkDependent: false
  } as CacheStrategy,

  // Location & Master Data
  LOCATIONS: {
    key: 'locations',
    ttl: 24 * 60 * 60 * 1000, // 24 hours - locations rarely change
    priority: 'medium',
    refreshOnExpiry: false,
    networkDependent: true
  } as CacheStrategy,

  SHIFTS: {
    key: 'shifts',
    ttl: 4 * 60 * 60 * 1000, // 4 hours - shift schedules
    priority: 'medium',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  // Reports & Analytics
  PRODUCTION_REPORTS: {
    key: 'production_reports',
    ttl: 60 * 60 * 1000, // 1 hour - production reports
    priority: 'medium',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  MAINTENANCE_RECORDS: {
    key: 'maintenance_records',
    ttl: 30 * 60 * 1000, // 30 minutes - maintenance records
    priority: 'medium',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  // Chart Data Strategies
  CHART_DATA_DAILY: {
    key: 'chart_data_daily',
    ttl: 15 * 60 * 1000, // 15 minutes - daily charts
    priority: 'high',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  CHART_DATA_WEEKLY: {
    key: 'chart_data_weekly',
    ttl: 60 * 60 * 1000, // 1 hour - weekly charts
    priority: 'medium',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  CHART_DATA_MONTHLY: {
    key: 'chart_data_monthly',
    ttl: 4 * 60 * 60 * 1000, // 4 hours - monthly charts
    priority: 'medium',
    refreshOnExpiry: true,
    networkDependent: true
  } as CacheStrategy,

  // Image & Media Strategies
  ACTIVITY_IMAGES: {
    key: 'activity_images',
    ttl: 7 * 24 * 60 * 60 * 1000, // 7 days - activity images
    priority: 'low',
    refreshOnExpiry: false,
    networkDependent: true
  } as CacheStrategy,

  EQUIPMENT_PHOTOS: {
    key: 'equipment_photos',
    ttl: 7 * 24 * 60 * 60 * 1000, // 7 days - equipment photos
    priority: 'low',
    refreshOnExpiry: false,
    networkDependent: true
  } as CacheStrategy
};

// Cache configuration for different environments
export const CACHE_CONFIG = {
  development: {
    maxCacheSize: 100 * 1024 * 1024, // 100MB for development
    cleanupThreshold: 0.8,
    enableMetrics: true,
    logLevel: 'verbose'
  },
  production: {
    maxCacheSize: 50 * 1024 * 1024, // 50MB for production
    cleanupThreshold: 0.7,
    enableMetrics: false,
    logLevel: 'error'
  }
};

// Preload strategies for different scenarios
export const PRELOAD_STRATEGIES = {
  // Critical data that should be preloaded on app start
  APP_STARTUP: [
    CACHE_STRATEGIES.USER_PROFILE,
    CACHE_STRATEGIES.LOCATIONS,
    CACHE_STRATEGIES.SAFETY_CHECKLISTS,
    CACHE_STRATEGIES.TODAY_PRODUCTION
  ],

  // Data to preload when going offline
  OFFLINE_PREPARATION: [
    CACHE_STRATEGIES.SAFETY_CHECKLISTS,
    CACHE_STRATEGIES.EQUIPMENT_LIST,
    CACHE_STRATEGIES.USER_PROFILE,
    CACHE_STRATEGIES.LOCATIONS,
    CACHE_STRATEGIES.WEEKLY_PRODUCTION
  ],

  // Data to preload for dashboard
  DASHBOARD_PRELOAD: [
    CACHE_STRATEGIES.DASHBOARD_STATS,
    CACHE_STRATEGIES.PRODUCTION_OVERVIEW,
    CACHE_STRATEGIES.EQUIPMENT_STATUS,
    CACHE_STRATEGIES.TODAY_PRODUCTION
  ],

  // Data to preload for production screens
  PRODUCTION_PRELOAD: [
    CACHE_STRATEGIES.CHART_DATA_DAILY,
    CACHE_STRATEGIES.CHART_DATA_WEEKLY,
    CACHE_STRATEGIES.PRODUCTION_REPORTS,
    CACHE_STRATEGIES.WEEKLY_PRODUCTION
  ]
};

// Helper functions
export const getCacheStrategy = (key: string): CacheStrategy | undefined => {
  return Object.values(CACHE_STRATEGIES).find(strategy => strategy.key === key);
};

export const getCacheConfig = () => {
  return __DEV__ ? CACHE_CONFIG.development : CACHE_CONFIG.production;
};

export const getPreloadStrategy = (scenario: keyof typeof PRELOAD_STRATEGIES): CacheStrategy[] => {
  return PRELOAD_STRATEGIES[scenario] || [];
};

// Mining-specific cache utilities
export const MINING_CACHE_UTILS = {
  // Generate cache key for date-based data
  generateDateCacheKey: (baseKey: string, startDate?: string, endDate?: string): string => {
    if (!startDate && !endDate) return baseKey;
    return `${baseKey}_${startDate || 'no-start'}_${endDate || 'no-end'}`;
  },

  // Determine if data is historical (can be cached longer)
  isHistoricalData: (date: string): boolean => {
    const today = new Date().toISOString().split('T')[0];
    return date < today;
  },

  // Get appropriate TTL based on data age
  getDataAgeTTL: (date: string): number => {
    const today = new Date().toISOString().split('T')[0];
    if (date === today) {
      return CACHE_STRATEGIES.TODAY_PRODUCTION.ttl;
    } else if (date >= today) {
      return CACHE_STRATEGIES.WEEKLY_PRODUCTION.ttl;
    } else {
      return CACHE_STRATEGIES.HISTORICAL_PRODUCTION.ttl;
    }
  }
};

export default CACHE_STRATEGIES;
