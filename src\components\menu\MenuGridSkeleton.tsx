import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { Colors } from '../../constants/colors';
import { Layout } from '../../constants/layout';

interface MenuGridSkeletonProps {
  itemCount?: number;
}

const MenuGridSkeleton: React.FC<MenuGridSkeletonProps> = ({ itemCount = 11 }) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1200,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1200,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
      ])
    );

    // Pulse animation for additional loading feedback
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 0.8,
          duration: 800,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
      ])
    );

    shimmerAnimation.start();
    pulseAnimation.start();

    return () => {
      shimmerAnimation.stop();
      pulseAnimation.stop();
    };
  }, []);

  const shimmerStyle = {
    opacity: shimmerAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.7],
    }),
  };

  const pulseStyle = {
    transform: [{ scale: pulseAnim }],
  };

  const renderSkeletonItem = (index: number) => (
    <Animated.View key={index} style={[styles.skeletonItem, pulseStyle]}>
      <Animated.View style={[styles.skeletonIcon, shimmerStyle]} />
      <Animated.View style={[styles.skeletonText, shimmerStyle]} />
      <Animated.View style={[styles.skeletonTextShort, shimmerStyle]} />
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      {Array.from({ length: itemCount }, (_, index) => renderSkeletonItem(index))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: Layout.spacing.lg,
    justifyContent: 'space-between',
  },
  skeletonItem: {
    width: '22%',
    alignItems: 'center',
    marginBottom: Layout.spacing.xl,
  },
  skeletonIcon: {
    width: 50,
    height: 50,
    borderRadius: Layout.borderRadius.md,
    backgroundColor: Colors.border,
    marginBottom: Layout.spacing.sm,
  },
  skeletonText: {
    width: '80%',
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.border,
    marginBottom: 4,
  },
  skeletonTextShort: {
    width: '60%',
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.border,
  },
});

export default MenuGridSkeleton;
