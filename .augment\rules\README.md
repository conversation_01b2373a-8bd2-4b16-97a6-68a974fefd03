# 🤖 Augment Agent Rules

This folder contains rules and guidelines for Augment Agent to ensure consistent behavior across all interactions with this project.

## 📋 Rules Files

### **documentation.md**
- **Purpose**: Mandatory documentation structure rules
- **Scope**: All documentation work
- **Status**: Must follow - no exceptions
- **Key Points**: 
  - Folder structure is immutable
  - Always update existing files instead of creating new ones
  - Follow established naming conventions
  - Update navigation files when making changes

## 🎯 How It Works

When Augment Agent works on this project, it should:

1. **Read the rules first** - Always check documentation.md before making documentation changes
2. **Follow the structure** - Use established folder structure
3. **Update existing files** - Don't create redundant files
4. **Maintain navigation** - Keep index.md and other navigation files updated

## 🔄 Consistency Benefits

- **Predictable structure** - Always know where to find documentation
- **No ambiguity** - Clear rules for file placement
- **Easy maintenance** - Consistent structure across all AI interactions
- **Long-term sustainability** - Rules ensure project remains organized

## 📞 For Developers

If you're working with AI agents on this project:
1. Point them to these rules first
2. Ensure they follow the documentation structure
3. Review their work against the established patterns
4. Update rules if needed (but maintain consistency)

---

**Created**: January 2025  
**Purpose**: Ensure consistent AI agent behavior  
**Status**: Active and enforced
