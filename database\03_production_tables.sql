-- =====================================================
-- File: 03_production_tables.sql
-- Description: Production and mining operation tables
-- =====================================================

-- =====================================================
-- PRODUCTION TABLES
-- =====================================================

-- Production sites/locations
CREATE TABLE production_sites (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    site_code VARCHAR(20) UNIQUE NOT NULL,
    site_name VARCHAR(100) NOT NULL,
    location_coordinates POINT,
    area_hectares DECIMAL(10,2),
    site_type VARCHAR(50) NOT NULL, -- pit, dump, stockpile, etc
    status equipment_status DEFAULT 'operational' NOT NULL,
    supervisor_id UUID REFERENCES profiles(id),
    description TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES profiles(id),
    updated_by UUID REFERENCES profiles(id)
);

-- Production targets/plans
CREATE TABLE production_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    plan_code VARCHAR(20) UNIQUE NOT NULL,
    site_id UUID REFERENCES production_sites(id) NOT NULL,
    plan_date DATE NOT NULL,
    plan_period VARCHAR(20) NOT NULL, -- daily, weekly, monthly, yearly
    ob_target DECIMAL(12,2) DEFAULT 0 NOT NULL,
    ob_unit production_unit DEFAULT 'js' NOT NULL,
    ore_target DECIMAL(12,2) DEFAULT 0 NOT NULL,
    ore_unit production_unit DEFAULT 'tc' NOT NULL,
    fuel_target DECIMAL(12,2) DEFAULT 0 NOT NULL,
    fuel_unit production_unit DEFAULT 'liter' NOT NULL,
    stripping_ratio_target DECIMAL(8,4), -- ob_plan / ore_plan
    fuel_ratio_target DECIMAL(8,4), -- ob_plan + (ore_plan / 3.39)
    notes TEXT,
    approved_by UUID REFERENCES profiles(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    status report_status DEFAULT 'draft' NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES profiles(id),
    updated_by UUID REFERENCES profiles(id),
    
    UNIQUE(site_id, plan_date, plan_period)
);

-- Daily production records
CREATE TABLE production_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    record_code VARCHAR(20) UNIQUE NOT NULL,
    site_id UUID REFERENCES production_sites(id) NOT NULL,
    plan_id UUID REFERENCES production_plans(id),
    production_date DATE NOT NULL,
    shift_type shift_type NOT NULL,
    
    -- Overburden (OB) data
    ob_actual DECIMAL(12,2) DEFAULT 0 NOT NULL,
    ob_unit production_unit DEFAULT 'js' NOT NULL,
    ob_plan DECIMAL(12,2) DEFAULT 0 NOT NULL,
    
    -- Ore data
    ore_actual DECIMAL(12,2) DEFAULT 0 NOT NULL,
    ore_unit production_unit DEFAULT 'tc' NOT NULL,
    ore_plan DECIMAL(12,2) DEFAULT 0 NOT NULL,
    
    -- Fuel consumption
    fuel_actual DECIMAL(12,2) DEFAULT 0 NOT NULL,
    fuel_unit production_unit DEFAULT 'liter' NOT NULL,
    fuel_plan DECIMAL(12,2) DEFAULT 0 NOT NULL,
    

    
    -- Calculated ratios
    stripping_ratio_actual DECIMAL(8,4), -- ob_actual / ore_actual
    stripping_ratio_target DECIMAL(8,4), -- ob_plan / ore_plan
    fuel_ratio_actual DECIMAL(8,4), -- fuel_actual / (ob_actual + (ore_actual / 3.39))
    fuel_ratio_plan DECIMAL(8,4), -- fuel_target / (ob_plan + (ore_plan / 3.39))
    
    -- Weather and conditions
    weather_condition VARCHAR(50),
    temperature_celsius DECIMAL(4,1),
    rainfall_mm DECIMAL(6,2),
    
    -- Operational data
    working_hours DECIMAL(4,2) DEFAULT 0,
    downtime_hours DECIMAL(4,2) DEFAULT 0,
    downtime_reason TEXT,
    
    -- Quality metrics
    quality_grade VARCHAR(20),
    quality_notes TEXT,
    
    -- Personnel
    supervisor_id UUID REFERENCES profiles(id) NOT NULL,
    operator_ids UUID[] DEFAULT '{}',
    
    -- Status and approval
    status report_status DEFAULT 'draft' NOT NULL,
    submitted_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES profiles(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES profiles(id),
    updated_by UUID REFERENCES profiles(id),
    
    UNIQUE(site_id, production_date, shift_type)
);

-- Production summary views (materialized for performance)
CREATE MATERIALIZED VIEW production_daily_summary AS
SELECT 
    production_date,
    site_id,
    SUM(ob_actual) as total_ob_actual,
    SUM(ob_plan) as total_ob_plan,
    SUM(ore_actual) as total_ore_actual,
    SUM(ore_plan) as total_ore_plan,
    SUM(fuel_actual) as total_fuel_actual,
    SUM(fuel_plan) as total_fuel_plan,
    ROUND(AVG(stripping_ratio_actual), 4) as avg_stripping_ratio_actual,
    ROUND(AVG(stripping_ratio_target), 4) as avg_stripping_ratio_target,
    ROUND(AVG(fuel_ratio_actual), 4) as avg_fuel_ratio_actual,
    ROUND(AVG(fuel_ratio_plan), 4) as avg_fuel_ratio_plan,
    CASE 
        WHEN SUM(ob_plan) > 0 THEN (SUM(ob_actual) / SUM(ob_plan)) * 100 
        ELSE 0 
    END as ob_achievement_percent,
    CASE 
        WHEN SUM(ore_plan) > 0 THEN (SUM(ore_actual) / SUM(ore_plan)) * 100 
        ELSE 0 
    END as ore_achievement_percent,
    COUNT(*) as shift_count,
    MAX(updated_at) as last_updated
FROM production_records 
WHERE status = 'approved'
GROUP BY production_date, site_id;

-- Create indexes for materialized view
CREATE UNIQUE INDEX idx_production_daily_summary_unique 
ON production_daily_summary (production_date, site_id);

CREATE INDEX idx_production_daily_summary_date 
ON production_daily_summary (production_date);
