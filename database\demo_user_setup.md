# Demo User Setup for Mining Operations App

## Overview
This guide explains how to create a demo user for testing the Mining Operations App authentication and database integration.

## Method 1: Using Supabase Dashboard (Recommended)

### Step 1: Create Auth User
1. Go to Supabase Dashboard: https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv
2. Navigate to **Authentication** > **Users**
3. Click **Add User**
4. Fill in the details:
   - **Email**: `<EMAIL>`
   - **Password**: `demo123`
   - **Auto Confirm User**: ✅ (checked)
5. Click **Create User**

### Step 2: Create User Profile
After creating the auth user, copy the generated UUID and run this SQL in the SQL Editor:

```sql
-- Replace 'USER_UUID_HERE' with the actual UUID from the auth user created above
INSERT INTO users (
    id, 
    email, 
    full_name, 
    phone, 
    role, 
    location_id, 
    employee_id, 
    hire_date, 
    is_active
) VALUES (
    'USER_UUID_HERE',  -- Replace with actual UUID
    '<EMAIL>',
    'Demo Supervisor',
    '******-0199',
    'supervisor',
    '550e8400-e29b-41d4-a716-************',  -- North Mine Site
    'DEMO001',
    '2024-01-01',
    true
);
```

## Method 2: Using React Native App

### Step 1: Temporarily Enable Public Registration
If you want to test the signup flow, you can use the app's signup functionality:

```typescript
// In your React Native app, use the signup function
const userData = {
  full_name: 'Demo Supervisor',
  phone: '******-0199',
  role: 'supervisor' as const,
  location_id: '550e8400-e29b-41d4-a716-************',
  employee_id: 'DEMO001'
};

await DatabaseService.signUp('<EMAIL>', 'demo123', userData);
```

## Demo User Details

Once created, the demo user will have:

- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Name**: Demo Supervisor
- **Role**: Supervisor
- **Location**: North Mine Site
- **Employee ID**: DEMO001
- **Phone**: ******-0199

## Testing the Demo User

### Login Test
1. Open the Mining Operations App
2. Enter credentials:
   - Email: `<EMAIL>`
   - Password: `demo123`
3. Tap "Sign In" or "Try Demo Account"

### Expected Behavior
- User should be authenticated successfully
- Dashboard should load with real data from the database
- User profile should show supervisor role and assigned location
- All database operations should work with proper RLS permissions

## Permissions and Access

The demo supervisor user will have access to:

✅ **Dashboard**: View production metrics and equipment status
✅ **Equipment**: View and manage equipment at North Mine Site
✅ **Safety**: View and manage safety incidents
✅ **Reports**: View and create production reports
✅ **Profile**: View and edit own profile
✅ **Database**: Test database operations (via Database tab)

## Additional Test Users

You can create additional test users with different roles:

### Operator User
```sql
INSERT INTO users (id, email, full_name, role, location_id, employee_id) VALUES 
('OPERATOR_UUID', '<EMAIL>', 'Demo Operator', 'operator', '550e8400-e29b-41d4-a716-************', 'OP001');
```

### Safety Officer User
```sql
INSERT INTO users (id, email, full_name, role, location_id, employee_id) VALUES 
('SAFETY_UUID', '<EMAIL>', 'Demo Safety Officer', 'safety_officer', '550e8400-e29b-41d4-a716-************', 'SF001');
```

### Maintenance Tech User
```sql
INSERT INTO users (id, email, full_name, role, location_id, employee_id) VALUES 
('MAINT_UUID', '<EMAIL>', 'Demo Maintenance Tech', 'maintenance_tech', '550e8400-e29b-41d4-a716-************', 'MT001');
```

## Troubleshooting

### Issue: "User not found" error
- Ensure the auth user was created in Supabase Auth
- Verify the UUID matches between auth.users and public.users tables

### Issue: "Permission denied" errors
- Check that RLS policies are properly configured
- Verify the user's role and location_id are set correctly

### Issue: "Profile not found"
- Ensure the user profile was created in the public.users table
- Check that the user ID matches the auth user ID

### Issue: Login fails
- Verify email and password are correct
- Check that the user is confirmed in Supabase Auth
- Ensure the user is marked as active (is_active = true)

## Security Notes

⚠️ **Important**: This demo user is for testing purposes only. In production:

1. Remove or disable demo accounts
2. Use strong passwords
3. Implement proper user registration workflows
4. Set up email confirmation
5. Configure proper RLS policies for your use case

## Next Steps

After setting up the demo user:

1. Test all authentication flows
2. Verify database operations work correctly
3. Test different user roles and permissions
4. Implement additional features as needed
5. Set up proper production user management
