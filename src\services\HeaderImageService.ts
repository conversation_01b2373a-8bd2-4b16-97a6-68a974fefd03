import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { supabase } from '../config/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface HeaderImageResult {
  success: boolean;
  imageUri?: string;
  publicUrl?: string;
  error?: string;
}

interface ImagePickerResult {
  success: boolean;
  imageUri?: string;
  error?: string;
}

class HeaderImageService {
  private static instance: HeaderImageService;

  static getInstance(): HeaderImageService {
    if (!HeaderImageService.instance) {
      HeaderImageService.instance = new HeaderImageService();
    }
    return HeaderImageService.instance;
  }

  /**
   * Request camera and media library permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      return cameraPermission.status === 'granted' && mediaLibraryPermission.status === 'granted';
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Open camera to take a new header image
   */
  async openCamera(): Promise<ImagePickerResult> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        return { success: false, error: 'Camera permissions required' };
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9], // Landscape aspect ratio for header
        quality: 0.8,
        exif: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return { success: false, error: 'Image capture cancelled' };
      }

      return {
        success: true,
        imageUri: result.assets[0].uri,
      };
    } catch (error) {
      console.error('Error opening camera:', error);
      return { success: false, error: 'Failed to open camera' };
    }
  }

  /**
   * Open gallery to select header image
   */
  async openGallery(): Promise<ImagePickerResult> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        return { success: false, error: 'Gallery permissions required' };
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9], // Landscape aspect ratio for header
        quality: 0.8,
        exif: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return { success: false, error: 'Image selection cancelled' };
      }

      return {
        success: true,
        imageUri: result.assets[0].uri,
      };
    } catch (error) {
      console.error('Error opening gallery:', error);
      return { success: false, error: 'Failed to open gallery' };
    }
  }

  /**
   * Show image picker options (Camera or Gallery)
   */
  async showImagePickerOptions(): Promise<ImagePickerResult> {
    return new Promise((resolve) => {
      // This would typically show an ActionSheet or Alert
      // For now, we'll default to gallery
      this.openGallery().then(resolve);
    });
  }

  /**
   * Upload header image to Supabase Storage
   */
  async uploadHeaderImage(imageUri: string, userId: string, userName?: string): Promise<HeaderImageResult> {
    try {
      // Read the image file
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!fileInfo.exists) {
        return { success: false, error: 'Image file not found' };
      }

      // Create folder name from user name (sanitized) or fallback to userId
      const folderName = userName 
        ? userName.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 50)
        : userId;

      // Create filename for header image
      const fileExtension = imageUri.split('.').pop() || 'jpg';
      const fileName = `header_${Date.now()}.${fileExtension}`;
      const filePath = `${folderName}/headers/${fileName}`;

      console.log('📸 Starting header image upload:', { 
        fileName, 
        filePath, 
        imageUri, 
        folderName,
        originalUserName: userName 
      });

      // Read file as base64
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Convert base64 to binary
      const binaryString = atob(base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from('profile-photos')
        .upload(filePath, bytes, {
          contentType: `image/${fileExtension}`,
          upsert: true,
        });

      if (uploadError) {
        console.error('❌ Header image upload failed:', uploadError);
        return { success: false, error: uploadError.message };
      }

      console.log('✅ Header image uploaded successfully:', data);

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from('profile-photos')
        .getPublicUrl(filePath);

      if (!publicUrlData.publicUrl) {
        return { success: false, error: 'Failed to get public URL' };
      }

      console.log('🔗 Header image public URL:', publicUrlData.publicUrl);

      return {
        success: true,
        publicUrl: publicUrlData.publicUrl,
      };

    } catch (error) {
      console.error('❌ Error uploading header image:', error);
      return { success: false, error: 'Failed to upload header image' };
    }
  }

  /**
   * Save header image URL to local storage
   */
  async saveHeaderImageUrl(userId: string, imageUrl: string): Promise<boolean> {
    try {
      await AsyncStorage.setItem(`header_image_${userId}`, imageUrl);
      return true;
    } catch (error) {
      console.error('Error saving header image URL:', error);
      return false;
    }
  }

  /**
   * Get saved header image URL from local storage
   */
  async getHeaderImageUrl(userId: string): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(`header_image_${userId}`);
    } catch (error) {
      console.error('Error getting header image URL:', error);
      return null;
    }
  }

  /**
   * Delete old header image from storage
   */
  async deleteOldHeaderImage(imageUrl: string): Promise<void> {
    try {
      if (!imageUrl || !imageUrl.includes('supabase')) {
        return; // Not a Supabase storage URL
      }

      // Extract file path from URL
      const urlParts = imageUrl.split('/storage/v1/object/public/profile-photos/');
      if (urlParts.length !== 2) {
        console.warn('⚠️ Invalid header image URL format:', imageUrl);
        return;
      }

      const filePath = urlParts[1];
      console.log('🗑️ Deleting old header image:', filePath);

      const { error } = await supabase.storage
        .from('profile-photos')
        .remove([filePath]);

      if (error) {
        console.warn('⚠️ Failed to delete old header image:', error.message);
      } else {
        console.log('✅ Old header image deleted successfully');
      }
    } catch (error) {
      console.warn('⚠️ Error deleting old header image:', error);
    }
  }

  /**
   * Cleanup old header images for a user
   */
  async cleanupOldHeaderImages(userId: string, userName?: string): Promise<void> {
    try {
      // Create folder name same as in upload method
      const folderName = userName 
        ? userName.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 50)
        : userId;

      console.log('🧹 Cleaning up old header images for folder:', folderName);

      // List all files in user's headers folder
      const { data: files, error } = await supabase.storage
        .from('profile-photos')
        .list(`${folderName}/headers`, {
          limit: 100,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        console.warn('⚠️ Failed to list header files for cleanup:', error.message);
        return;
      }

      if (!files || files.length <= 1) {
        console.log('ℹ️ No old header files to cleanup');
        return;
      }

      // Keep the most recent file, delete the rest
      const filesToDelete = files.slice(1).map(file => `${folderName}/headers/${file.name}`);
      
      if (filesToDelete.length > 0) {
        console.log('🗑️ Deleting old header images:', filesToDelete);
        
        const { error: deleteError } = await supabase.storage
          .from('profile-photos')
          .remove(filesToDelete);

        if (deleteError) {
          console.warn('⚠️ Failed to delete some old header files:', deleteError.message);
        } else {
          console.log(`✅ Cleaned up ${filesToDelete.length} old header images`);
        }
      }
    } catch (error) {
      console.warn('⚠️ Error during header cleanup:', error);
    }
  }

  /**
   * Complete header image update process
   */
  async updateHeaderImage(userId: string, userName?: string, currentHeaderUrl?: string): Promise<HeaderImageResult> {
    try {
      // Show image picker options
      const pickerResult = await this.showImagePickerOptions();
      
      if (!pickerResult.success || !pickerResult.imageUri) {
        return { success: false, error: pickerResult.error || 'No image selected' };
      }

      // Delete old header image BEFORE uploading new one
      if (currentHeaderUrl) {
        console.log('🗑️ Deleting old header image before upload...');
        await this.deleteOldHeaderImage(currentHeaderUrl);
        
        // Also cleanup any other old header images for this user
        await this.cleanupOldHeaderImages(userId, userName);
      }

      // Upload new header image
      const uploadResult = await this.uploadHeaderImage(pickerResult.imageUri, userId, userName);
      
      if (!uploadResult.success || !uploadResult.publicUrl) {
        return { success: false, error: uploadResult.error || 'Upload failed' };
      }

      // Save header image URL to local storage
      const saveSuccess = await this.saveHeaderImageUrl(userId, uploadResult.publicUrl);
      
      if (!saveSuccess) {
        console.warn('⚠️ Failed to save header image URL to local storage');
      }

      console.log('✅ Header image update complete:', uploadResult.publicUrl);

      return {
        success: true,
        publicUrl: uploadResult.publicUrl,
      };
    } catch (error) {
      console.error('Error in complete header image update:', error);
      return { success: false, error: 'Failed to update header image' };
    }
  }

  /**
   * Get predefined header images (optional)
   */
  getPredefinedHeaders(): Array<{ id: string; name: string; url: string; preview: string }> {
    return [
      {
        id: 'mining_1',
        name: 'Mining Site Sunset',
        url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=450&fit=crop',
        preview: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=113&fit=crop'
      },
      {
        id: 'mining_2',
        name: 'Heavy Equipment',
        url: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=800&h=450&fit=crop',
        preview: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=200&h=113&fit=crop'
      },
      {
        id: 'mining_3',
        name: 'Open Pit Mine',
        url: 'https://images.unsplash.com/photo-1611273426858-450d8e3c9fce?w=800&h=450&fit=crop',
        preview: 'https://images.unsplash.com/photo-1611273426858-450d8e3c9fce?w=200&h=113&fit=crop'
      },
      {
        id: 'industrial_1',
        name: 'Industrial Complex',
        url: 'https://images.unsplash.com/photo-1565793298595-6a879b1d9492?w=800&h=450&fit=crop',
        preview: 'https://images.unsplash.com/photo-1565793298595-6a879b1d9492?w=200&h=113&fit=crop'
      },
      {
        id: 'nature_1',
        name: 'Mountain Landscape',
        url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=450&fit=crop',
        preview: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=200&h=113&fit=crop'
      },
      {
        id: 'abstract_1',
        name: 'Abstract Gradient',
        url: 'https://images.unsplash.com/photo-1557672172-298e090bd0f1?w=800&h=450&fit=crop',
        preview: 'https://images.unsplash.com/photo-1557672172-298e090bd0f1?w=200&h=113&fit=crop'
      }
    ];
  }
}

export default HeaderImageService;
