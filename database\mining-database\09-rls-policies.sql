-- =====================================================
-- Mining Operations Database - Row Level Security Policies
-- =====================================================
-- File: 09-rls-policies.sql
-- Description: Row Level Security policies for data access control
-- Dependencies: All previous schema files
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all main tables
ALTER TABLE daily_mining_report ENABLE ROW LEVEL SECURITY;
ALTER TABLE production_calendar ENABLE ROW LEVEL SECURITY;
ALTER TABLE production_targets_calendar ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE work_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_inspections ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_training ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_training_attendees ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE mining_certifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificate_renewals ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_activity_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_activity_actuals ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- HELPER FUNCTIONS FOR RLS
-- =====================================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS user_role AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM user_profiles 
        WHERE id = auth.uid()
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's primary location
CREATE OR REPLACE FUNCTION get_current_user_location()
RETURNS VARCHAR(200) AS $$
BEGIN
    RETURN (
        SELECT primary_location 
        FROM user_profiles 
        WHERE id = auth.uid()
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's allowed locations
CREATE OR REPLACE FUNCTION get_current_user_allowed_locations()
RETURNS TEXT[] AS $$
BEGIN
    RETURN (
        SELECT allowed_locations 
        FROM user_profiles 
        WHERE id = auth.uid()
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can access location
CREATE OR REPLACE FUNCTION user_can_access_location(location_name VARCHAR(200))
RETURNS BOOLEAN AS $$
DECLARE
    user_role_val user_role;
    user_locations TEXT[];
BEGIN
    user_role_val := get_current_user_role();
    user_locations := get_current_user_allowed_locations();
    
    -- Super Admin and Site Manager can access all locations
    IF user_role_val IN ('Super Admin', 'Site Manager') THEN
        RETURN TRUE;
    END IF;
    
    -- Check if location is in user's allowed locations
    IF location_name = ANY(user_locations) THEN
        RETURN TRUE;
    END IF;
    
    -- Check for 'All Sites' permission
    IF 'All Sites' = ANY(user_locations) THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is equipment operator for specific equipment
CREATE OR REPLACE FUNCTION user_is_equipment_operator(equipment_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM equipment 
        WHERE id = equipment_uuid 
        AND (primary_operator_id = auth.uid() OR secondary_operator_id = auth.uid())
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- DAILY MINING REPORT RLS POLICIES
-- =====================================================

-- Users can view reports for their allowed locations
CREATE POLICY daily_mining_report_select ON daily_mining_report FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_can_access_location(location) OR
    reported_by = auth.uid()
);

-- Users can insert reports for their allowed locations
CREATE POLICY daily_mining_report_insert ON daily_mining_report FOR INSERT WITH CHECK (
    user_has_permission(auth.uid(), 'reports.create', location) AND
    user_can_access_location(location)
);

-- Users can update their own reports or if they have manage permission
CREATE POLICY daily_mining_report_update ON daily_mining_report FOR UPDATE USING (
    (reported_by = auth.uid() AND status IN ('Draft', 'Submitted')) OR
    (user_has_permission(auth.uid(), 'reports.manage', location) AND user_can_access_location(location)) OR
    get_current_user_role() IN ('Super Admin', 'Site Manager')
);

-- =====================================================
-- PRODUCTION CALENDAR RLS POLICIES
-- =====================================================

-- All authenticated users can view calendar
CREATE POLICY production_calendar_select ON production_calendar FOR SELECT USING (
    auth.uid() IS NOT NULL
);

-- Only admins and managers can modify calendar
CREATE POLICY production_calendar_insert ON production_calendar FOR INSERT WITH CHECK (
    get_current_user_role() IN ('Super Admin', 'Site Manager')
);

CREATE POLICY production_calendar_update ON production_calendar FOR UPDATE USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager')
);

-- =====================================================
-- PRODUCTION TARGETS RLS POLICIES
-- =====================================================

-- Users can view targets for their allowed locations
CREATE POLICY production_targets_select ON production_targets_calendar FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_can_access_location(location)
);

-- Only managers can create/update targets
CREATE POLICY production_targets_insert ON production_targets_calendar FOR INSERT WITH CHECK (
    user_has_permission(auth.uid(), 'targets.manage', location) AND
    user_can_access_location(location)
);

CREATE POLICY production_targets_update ON production_targets_calendar FOR UPDATE USING (
    user_has_permission(auth.uid(), 'targets.manage', location) AND
    user_can_access_location(location)
);

-- =====================================================
-- USER MANAGEMENT RLS POLICIES
-- =====================================================

-- Users can see their own profile and others based on role
CREATE POLICY user_profiles_select ON user_profiles FOR SELECT USING (
    id = auth.uid() OR 
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    (get_current_user_role() = 'Shift Supervisor' AND 
     primary_location = get_current_user_location())
);

-- Only admins can create/update user profiles
CREATE POLICY user_profiles_insert ON user_profiles FOR INSERT WITH CHECK (
    get_current_user_role() IN ('Super Admin', 'Site Manager')
);

CREATE POLICY user_profiles_update ON user_profiles FOR UPDATE USING (
    id = auth.uid() OR 
    get_current_user_role() IN ('Super Admin', 'Site Manager')
);

-- User permissions: restricted to admins and self
CREATE POLICY user_permissions_select ON user_permissions FOR SELECT USING (
    user_id = auth.uid() OR 
    get_current_user_role() IN ('Super Admin', 'Site Manager')
);

-- User sessions: users can only see their own sessions
CREATE POLICY user_sessions_select ON user_sessions FOR SELECT USING (
    user_id = auth.uid() OR 
    get_current_user_role() = 'Super Admin'
);

-- =====================================================
-- EQUIPMENT MANAGEMENT RLS POLICIES
-- =====================================================

-- Users can view equipment in their allowed locations or assigned to them
CREATE POLICY equipment_select ON equipment FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_can_access_location(current_location) OR
    primary_operator_id = auth.uid() OR
    secondary_operator_id = auth.uid()
);

-- Only managers can create/update equipment
CREATE POLICY equipment_insert ON equipment FOR INSERT WITH CHECK (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_has_permission(auth.uid(), 'equipment.manage', current_location)
);

CREATE POLICY equipment_update ON equipment FOR UPDATE USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_has_permission(auth.uid(), 'equipment.manage', current_location)
);

-- Equipment metrics: operators can insert their own metrics
CREATE POLICY equipment_metrics_select ON equipment_metrics FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_can_access_location(location) OR
    operator_id = auth.uid() OR
    user_is_equipment_operator(equipment_id)
);

CREATE POLICY equipment_metrics_insert ON equipment_metrics FOR INSERT WITH CHECK (
    user_has_permission(auth.uid(), 'equipment.operate', location) OR
    user_has_permission(auth.uid(), 'equipment.manage', location) OR
    operator_id = auth.uid()
);

-- =====================================================
-- MAINTENANCE RLS POLICIES
-- =====================================================

-- Maintenance schedules: visible to relevant personnel
CREATE POLICY maintenance_schedules_select ON maintenance_schedules FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    assigned_technician_id = auth.uid() OR
    supervisor_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM equipment e 
        WHERE e.id = equipment_id 
        AND user_can_access_location(e.current_location)
    )
);

-- Work orders: access based on assignment and location
CREATE POLICY work_orders_select ON work_orders FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_can_access_location(location) OR
    assigned_to = auth.uid() OR
    requested_by = auth.uid() OR
    supervisor_id = auth.uid()
);

CREATE POLICY work_orders_insert ON work_orders FOR INSERT WITH CHECK (
    user_has_permission(auth.uid(), 'maintenance.create', location) OR
    user_has_permission(auth.uid(), 'maintenance.manage', location)
);

CREATE POLICY work_orders_update ON work_orders FOR UPDATE USING (
    user_has_permission(auth.uid(), 'maintenance.manage', location) OR
    (assigned_to = auth.uid() AND user_has_permission(auth.uid(), 'maintenance.execute', location))
);

-- =====================================================
-- SAFETY MANAGEMENT RLS POLICIES
-- =====================================================

-- Safety incidents: access based on location and involvement
CREATE POLICY safety_incidents_select ON safety_incidents FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    user_can_access_location(location) OR
    reported_by = auth.uid() OR
    assigned_investigator = auth.uid() OR
    safety_officer_id = auth.uid()
);

CREATE POLICY safety_incidents_insert ON safety_incidents FOR INSERT WITH CHECK (
    user_has_permission(auth.uid(), 'safety.report', location) OR
    user_can_access_location(location)
);

CREATE POLICY safety_incidents_update ON safety_incidents FOR UPDATE USING (
    user_has_permission(auth.uid(), 'safety.manage', location) OR
    (assigned_investigator = auth.uid() AND user_has_permission(auth.uid(), 'safety.investigate', location)) OR
    get_current_user_role() = 'Safety Officer'
);

-- Safety training: access based on role and attendance
CREATE POLICY safety_training_select ON safety_training FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    user_can_access_location(location) OR
    trainer_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM safety_training_attendees sta 
        WHERE sta.training_id = id AND sta.attendee_id = auth.uid()
    )
);

-- Training attendees: users can see their own attendance
CREATE POLICY safety_training_attendees_select ON safety_training_attendees FOR SELECT USING (
    attendee_id = auth.uid() OR
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    EXISTS (
        SELECT 1 FROM safety_training st 
        WHERE st.id = training_id 
        AND (st.trainer_id = auth.uid() OR user_can_access_location(st.location))
    )
);

-- Safety metrics: location-based access
CREATE POLICY safety_metrics_select ON safety_metrics FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    user_can_access_location(location)
);

-- =====================================================
-- MINING CERTIFICATIONS RLS POLICIES
-- =====================================================

-- Mining certifications: users can see their own certificates and managers can see all
CREATE POLICY mining_certifications_select ON mining_certifications FOR SELECT USING (
    holder_id = auth.uid() OR
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    (get_current_user_role() = 'Shift Supervisor' AND
     EXISTS (
         SELECT 1 FROM user_profiles up
         WHERE up.id = holder_id
         AND up.primary_location = get_current_user_location()
     ))
);

-- Only safety officers and admins can create/update certificates
CREATE POLICY mining_certifications_insert ON mining_certifications FOR INSERT WITH CHECK (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer')
);

CREATE POLICY mining_certifications_update ON mining_certifications FOR UPDATE USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    (holder_id = auth.uid() AND get_current_user_role() IN ('Shift Supervisor', 'Equipment Operator'))
);

-- Certificate renewals: similar access pattern
CREATE POLICY certificate_renewals_select ON certificate_renewals FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM mining_certifications mc
        WHERE mc.id = certificate_id
        AND (mc.holder_id = auth.uid() OR
             get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer'))
    )
);

CREATE POLICY certificate_renewals_insert ON certificate_renewals FOR INSERT WITH CHECK (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    EXISTS (
        SELECT 1 FROM mining_certifications mc
        WHERE mc.id = certificate_id
        AND mc.holder_id = auth.uid()
    )
);

CREATE POLICY certificate_renewals_update ON certificate_renewals FOR UPDATE USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    EXISTS (
        SELECT 1 FROM mining_certifications mc
        WHERE mc.id = certificate_id
        AND mc.holder_id = auth.uid()
    )
);

-- =====================================================
-- SAFETY ACTIVITY PLANS RLS POLICIES
-- =====================================================

-- Safety activity plans: location-based access
CREATE POLICY safety_activity_plans_select ON safety_activity_plans FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    user_can_access_location(location)
);

-- Only safety officers and managers can create/update plans
CREATE POLICY safety_activity_plans_insert ON safety_activity_plans FOR INSERT WITH CHECK (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer')
);

CREATE POLICY safety_activity_plans_update ON safety_activity_plans FOR UPDATE USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer')
);

-- =====================================================
-- SAFETY ACTIVITY ACTUALS RLS POLICIES
-- =====================================================

-- Safety activity actuals: location-based access
CREATE POLICY safety_activity_actuals_select ON safety_activity_actuals FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    user_can_access_location(location) OR
    recorded_by = auth.uid()
);

-- Users can insert actuals for their locations
CREATE POLICY safety_activity_actuals_insert ON safety_activity_actuals FOR INSERT WITH CHECK (
    user_has_permission(auth.uid(), 'safety.record', location) OR
    user_can_access_location(location) OR
    get_current_user_role() IN ('Safety Officer', 'Shift Supervisor')
);

-- Users can update their own records or if they have manage permission
CREATE POLICY safety_activity_actuals_update ON safety_activity_actuals FOR UPDATE USING (
    recorded_by = auth.uid() OR
    get_current_user_role() IN ('Super Admin', 'Site Manager', 'Safety Officer') OR
    (user_has_permission(auth.uid(), 'safety.manage', location) AND user_can_access_location(location))
);

-- =====================================================
-- AUDIT LOGS RLS POLICIES
-- =====================================================

-- Audit logs: restricted access
CREATE POLICY audit_logs_select ON audit_logs FOR SELECT USING (
    get_current_user_role() IN ('Super Admin', 'Site Manager') OR
    user_id = auth.uid()
);

-- Only system can insert audit logs
CREATE POLICY audit_logs_insert ON audit_logs FOR INSERT WITH CHECK (
    get_current_user_role() = 'Super Admin' OR
    current_user = 'postgres' -- Allow system inserts
);

-- =====================================================
-- APP SETTINGS RLS POLICIES
-- =====================================================

-- App settings: read access for all, write for admins
CREATE POLICY app_settings_select ON app_settings FOR SELECT USING (
    auth.uid() IS NOT NULL
);

CREATE POLICY app_settings_insert ON app_settings FOR INSERT WITH CHECK (
    get_current_user_role() = 'Super Admin'
);

CREATE POLICY app_settings_update ON app_settings FOR UPDATE USING (
    get_current_user_role() = 'Super Admin' OR
    (get_current_user_role() = 'Site Manager' AND NOT is_system_setting)
);

-- =====================================================
-- BYPASS POLICIES FOR SERVICE ACCOUNTS
-- =====================================================

-- Create policies that allow service accounts to bypass RLS
-- Service accounts should be identified by specific roles or settings

-- Allow service account full access (for system operations)
DO $$
BEGIN
    -- Create bypass policies for each table if service account is detected
    -- This would typically be done based on a specific user or role
    
    -- Example: Allow postgres user full access for maintenance
    IF current_user = 'postgres' THEN
        -- Service account has full access
        NULL; -- Placeholder - in production, implement proper service account logic
    END IF;
END $$;

-- =====================================================
-- POLICY MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to temporarily disable RLS for maintenance
CREATE OR REPLACE FUNCTION disable_rls_for_maintenance()
RETURNS void AS $$
DECLARE
    table_name text;
BEGIN
    -- Only Super Admin can disable RLS
    IF get_current_user_role() != 'Super Admin' THEN
        RAISE EXCEPTION 'Only Super Admin can disable RLS';
    END IF;
    
    -- Disable RLS on all tables
    ALTER TABLE daily_mining_report DISABLE ROW LEVEL SECURITY;
    ALTER TABLE production_calendar DISABLE ROW LEVEL SECURITY;
    ALTER TABLE production_targets_calendar DISABLE ROW LEVEL SECURITY;
    ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
    ALTER TABLE user_permissions DISABLE ROW LEVEL SECURITY;
    ALTER TABLE user_sessions DISABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment DISABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment_metrics DISABLE ROW LEVEL SECURITY;
    ALTER TABLE maintenance_schedules DISABLE ROW LEVEL SECURITY;
    ALTER TABLE work_orders DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_incidents DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_inspections DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training_attendees DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_metrics DISABLE ROW LEVEL SECURITY;
    ALTER TABLE mining_certifications DISABLE ROW LEVEL SECURITY;
    ALTER TABLE certificate_renewals DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_activity_plans DISABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_activity_actuals DISABLE ROW LEVEL SECURITY;
    ALTER TABLE audit_logs DISABLE ROW LEVEL SECURITY;
    ALTER TABLE app_settings DISABLE ROW LEVEL SECURITY;
    
    RAISE NOTICE 'RLS disabled for maintenance';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to re-enable RLS after maintenance
CREATE OR REPLACE FUNCTION enable_rls_after_maintenance()
RETURNS void AS $$
BEGIN
    -- Only Super Admin can enable RLS
    IF get_current_user_role() != 'Super Admin' THEN
        RAISE EXCEPTION 'Only Super Admin can enable RLS';
    END IF;
    
    -- Re-enable RLS on all tables
    ALTER TABLE daily_mining_report ENABLE ROW LEVEL SECURITY;
    ALTER TABLE production_calendar ENABLE ROW LEVEL SECURITY;
    ALTER TABLE production_targets_calendar ENABLE ROW LEVEL SECURITY;
    ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
    ALTER TABLE equipment_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE maintenance_schedules ENABLE ROW LEVEL SECURITY;
    ALTER TABLE work_orders ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_incidents ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_inspections ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_training_attendees ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_metrics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE mining_certifications ENABLE ROW LEVEL SECURITY;
    ALTER TABLE certificate_renewals ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_activity_plans ENABLE ROW LEVEL SECURITY;
    ALTER TABLE safety_activity_actuals ENABLE ROW LEVEL SECURITY;
    ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
    ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;
    
    RAISE NOTICE 'RLS re-enabled after maintenance';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- RLS MONITORING VIEW
-- =====================================================

-- View to monitor RLS policy effectiveness
CREATE VIEW v_rls_monitoring AS
SELECT 
    t.tablename,
    c.relrowsecurity as rls_enabled,
    COUNT(p.policyname) as policy_count,
    array_agg(p.policyname ORDER BY p.policyname) as policies
FROM pg_tables t
LEFT JOIN pg_class c ON c.relname = t.tablename
LEFT JOIN pg_policies p ON p.tablename = t.tablename AND p.schemaname = t.schemaname
WHERE t.schemaname = 'public'
GROUP BY t.tablename, c.relrowsecurity
ORDER BY t.tablename;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON FUNCTION get_current_user_role() IS 'Get the role of the currently authenticated user';
COMMENT ON FUNCTION get_current_user_location() IS 'Get the primary location of the currently authenticated user';
COMMENT ON FUNCTION get_current_user_allowed_locations() IS 'Get allowed locations for the currently authenticated user';
COMMENT ON FUNCTION user_can_access_location(VARCHAR) IS 'Check if current user can access specific location';
COMMENT ON FUNCTION user_is_equipment_operator(UUID) IS 'Check if current user is operator for specific equipment';
COMMENT ON FUNCTION disable_rls_for_maintenance() IS 'Temporarily disable RLS for maintenance (Super Admin only)';
COMMENT ON FUNCTION enable_rls_after_maintenance() IS 'Re-enable RLS after maintenance (Super Admin only)';

COMMENT ON VIEW v_rls_monitoring IS 'Monitor RLS status and policies for all tables';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('009', 'Row Level Security policies for data access control')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    rls_enabled_count INTEGER;
    policy_count INTEGER;
    critical_tables TEXT[] := ARRAY[
        'daily_mining_report', 'user_profiles', 'equipment', 
        'safety_incidents', 'work_orders', 'audit_logs'
    ];
    table_name TEXT;
    tables_without_rls TEXT[] := '{}';
BEGIN
    -- Check if RLS is enabled on critical tables
    FOREACH table_name IN ARRAY critical_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_tables t
            JOIN pg_class c ON c.relname = t.tablename
            WHERE t.schemaname = 'public' 
            AND t.tablename = table_name
            AND c.relrowsecurity = true
        ) THEN
            tables_without_rls := array_append(tables_without_rls, table_name);
        END IF;
    END LOOP;
    
    -- Count total tables with RLS enabled
    SELECT COUNT(*) INTO rls_enabled_count
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' 
    AND c.relrowsecurity = true;
    
    -- Count total policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE schemaname = 'public';
    
    RAISE NOTICE 'RLS policies setup completed successfully';
    RAISE NOTICE 'Tables with RLS enabled: %', rls_enabled_count;
    RAISE NOTICE 'Total RLS policies created: %', policy_count;
    
    IF array_length(tables_without_rls, 1) > 0 THEN
        RAISE WARNING 'Critical tables without RLS: %', array_to_string(tables_without_rls, ', ');
    ELSE
        RAISE NOTICE 'All critical tables have RLS enabled';
    END IF;
    
    RAISE NOTICE 'Use v_rls_monitoring view to check RLS status';
    RAISE NOTICE 'Data access is now controlled by user roles and locations';
END $$;
