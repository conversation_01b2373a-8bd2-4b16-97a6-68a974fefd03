/**
 * Supabase Database Connection Checker (JavaScript)
 * 
 * This script checks the connection to Supabase database and verifies
 * that all tables and functions are working properly.
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ohqbaimnhwvdfrmxvhxv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ocWJhaW1uaHd2ZGZybXh2aHh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4ODA3NzEsImV4cCI6MjA2ODQ1Njc3MX0.Qq-2pKIvW2SSJlgQqTW6I_gXdxt81oWv2wViadb9b-Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

class SupabaseConnectionChecker {
  constructor() {
    this.results = [];
  }

  addResult(test, status, message, details = null) {
    this.results.push({ test, status, message, details });
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${test}: ${message}`);
    if (details) {
      console.log(`   Details:`, details);
    }
  }

  async checkBasicConnection() {
    try {
      console.log('\n🔍 Testing basic Supabase connection...');
      
      // Test basic connection by trying to access a table
      const { data, error } = await supabase.from('users').select('count').limit(1);
      
      if (error) {
        this.addResult('Basic Connection', 'FAIL', `Connection failed: ${error.message}`, error);
      } else {
        this.addResult('Basic Connection', 'PASS', 'Successfully connected to Supabase');
      }
    } catch (error) {
      this.addResult('Basic Connection', 'FAIL', `Connection error: ${error.message}`, error);
    }
  }

  async checkAuthentication() {
    try {
      console.log('\n🔐 Testing authentication system...');
      
      // Check current session
      const { data: session, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        this.addResult('Session Check', 'WARNING', `Session error: ${sessionError.message}`);
      } else if (session.session) {
        this.addResult('Session Check', 'PASS', 'Active session found');
        
        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError) {
          this.addResult('User Check', 'WARNING', `User error: ${userError.message}`);
        } else if (user) {
          this.addResult('User Check', 'PASS', `User authenticated: ${user.email}`);
        }
      } else {
        this.addResult('Session Check', 'WARNING', 'No active session (not logged in)');
      }
    } catch (error) {
      this.addResult('Authentication', 'FAIL', `Auth error: ${error.message}`, error);
    }
  }

  async checkDatabaseTables() {
    try {
      console.log('\n📊 Testing database tables...');
      
      const tables = [
        'users',
        'locations',
        'equipment',
        'production_reports',
        'safety_incidents',
        'maintenance_records',
        'shifts',
        'user_shifts',
        'activity_documentation'
      ];

      for (const table of tables) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);
            
          if (error) {
            this.addResult(`Table: ${table}`, 'FAIL', `Table access failed: ${error.message}`);
          } else {
            this.addResult(`Table: ${table}`, 'PASS', `Table accessible (${data?.length || 0} sample records)`);
          }
        } catch (tableError) {
          this.addResult(`Table: ${table}`, 'FAIL', `Table error: ${tableError.message}`);
        }
      }
    } catch (error) {
      this.addResult('Database Tables', 'FAIL', `Tables check failed: ${error.message}`, error);
    }
  }

  async checkDataIntegrity() {
    try {
      console.log('\n🔍 Testing data integrity...');
      
      // Check locations
      const { data: locations, error: locError } = await supabase
        .from('locations')
        .select('*');
        
      if (locError) {
        this.addResult('Locations Data', 'FAIL', `Locations query failed: ${locError.message}`);
      } else {
        this.addResult('Locations Data', 'PASS', `Found ${locations?.length || 0} locations`);
      }

      // Check equipment
      const { data: equipment, error: eqError } = await supabase
        .from('equipment')
        .select('*')
        .limit(5);
        
      if (eqError) {
        this.addResult('Equipment Data', 'FAIL', `Equipment query failed: ${eqError.message}`);
      } else {
        this.addResult('Equipment Data', 'PASS', `Found ${equipment?.length || 0} equipment records`);
      }

      // Check recent production data
      const { data: production, error: prodError } = await supabase
        .from('production_reports')
        .select('*')
        .order('report_date', { ascending: false })
        .limit(5);
        
      if (prodError) {
        this.addResult('Production Data', 'FAIL', `Production query failed: ${prodError.message}`);
      } else {
        this.addResult('Production Data', 'PASS', `Found ${production?.length || 0} recent production records`);
      }

    } catch (error) {
      this.addResult('Data Integrity', 'FAIL', `Data integrity check failed: ${error.message}`, error);
    }
  }

  async checkRealTimeFeatures() {
    try {
      console.log('\n⚡ Testing real-time features...');
      
      // Test real-time subscription
      const channel = supabase
        .channel('test-channel')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'equipment' }, 
          (payload) => {
            console.log('Real-time event received:', payload);
          }
        );

      const subscribeResult = await channel.subscribe();
      
      if (subscribeResult) {
        this.addResult('Real-time Subscription', 'PASS', 'Real-time subscription successful');
        
        // Clean up
        setTimeout(() => {
          supabase.removeChannel(channel);
        }, 1000);
      } else {
        this.addResult('Real-time Subscription', 'WARNING', 'Subscription failed');
      }
      
    } catch (error) {
      this.addResult('Real-time Features', 'FAIL', `Real-time test failed: ${error.message}`, error);
    }
  }

  async checkDatabaseHealth() {
    try {
      console.log('\n🏥 Testing database health...');
      
      // Test multiple queries to ensure database is responsive
      const startTime = Date.now();
      
      const promises = [
        supabase.from('users').select('count').limit(1),
        supabase.from('locations').select('count').limit(1),
        supabase.from('equipment').select('count').limit(1)
      ];
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      const hasErrors = results.some(result => result.error);
      
      if (hasErrors) {
        this.addResult('Database Health', 'WARNING', 'Some queries failed during health check');
      } else {
        this.addResult('Database Health', 'PASS', `Database responsive (${responseTime}ms response time)`);
      }
      
    } catch (error) {
      this.addResult('Database Health', 'FAIL', `Health check failed: ${error.message}`, error);
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 SUPABASE CONNECTION CHECK SUMMARY');
    console.log('='.repeat(60));
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️  Warnings: ${warnings}`);
    console.log(`📊 Total Tests: ${this.results.length}`);
    
    if (failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }
    
    if (warnings > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.results
        .filter(r => r.status === 'WARNING')
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }
    
    console.log('\n' + '='.repeat(60));
    
    if (failed === 0) {
      console.log('🎉 All critical tests passed! Supabase connection is healthy.');
    } else {
      console.log('🚨 Some tests failed. Please check the issues above.');
    }
    
    console.log('\n📊 Database Information:');
    console.log(`   URL: ${supabaseUrl}`);
    console.log(`   Project ID: ohqbaimnhwvdfrmxvhxv`);
    console.log(`   Region: us-east-1`);
  }

  async runAllChecks() {
    console.log('🚀 Starting Supabase Database Connection Check...');
    console.log('Database URL:', supabaseUrl);
    
    await this.checkBasicConnection();
    await this.checkAuthentication();
    await this.checkDatabaseTables();
    await this.checkDataIntegrity();
    await this.checkRealTimeFeatures();
    await this.checkDatabaseHealth();
    
    this.printSummary();
  }
}

// Run the checker
async function main() {
  const checker = new SupabaseConnectionChecker();
  await checker.runAllChecks();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { SupabaseConnectionChecker };
