-- =====================================================
-- Complete Database Setup for Dashboard Header Management
-- Run this script in Supabase SQL Editor
-- =====================================================

-- 1. Create dashboard_header_images table
CREATE TABLE IF NOT EXISTS dashboard_header_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    thumbnail_url TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dashboard_header_images_active_order 
ON dashboard_header_images (is_active, display_order);

CREATE INDEX IF NOT EXISTS idx_dashboard_header_images_created_by 
ON dashboard_header_images (created_by);

-- 3. Enable RLS (Row Level Security)
ALTER TABLE dashboard_header_images ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies
DROP POLICY IF EXISTS "Anyone can view active dashboard header images" ON dashboard_header_images;
DROP POLICY IF EXISTS "Admins can manage dashboard header images" ON dashboard_header_images;

CREATE POLICY "Anyone can view active dashboard header images" 
ON dashboard_header_images FOR SELECT 
USING (is_active = true);

CREATE POLICY "Admins can manage dashboard header images" 
ON dashboard_header_images FOR ALL 
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.is_active = true
        AND (
            users.departemen IN ('Administration', 'Management') 
            OR LOWER(users.jabatan) LIKE '%admin%'
            OR LOWER(users.jabatan) LIKE '%manager%'
        )
    )
);

-- 5. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_dashboard_header_images_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Create trigger for updated_at
DROP TRIGGER IF EXISTS trigger_update_dashboard_header_images_updated_at ON dashboard_header_images;
CREATE TRIGGER trigger_update_dashboard_header_images_updated_at
    BEFORE UPDATE ON dashboard_header_images
    FOR EACH ROW
    EXECUTE FUNCTION update_dashboard_header_images_updated_at();

-- 7. Create function to check if user is admin
CREATE OR REPLACE FUNCTION is_user_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id 
        AND is_active = true
        AND (
            departemen IN ('Administration', 'Management') 
            OR LOWER(jabatan) LIKE '%admin%'
            OR LOWER(jabatan) LIKE '%manager%'
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Create function to get active dashboard header images
CREATE OR REPLACE FUNCTION get_dashboard_header_images()
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    description TEXT,
    image_url TEXT,
    thumbnail_url TEXT,
    display_order INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dhi.id,
        dhi.title,
        dhi.description,
        dhi.image_url,
        dhi.thumbnail_url,
        dhi.display_order
    FROM dashboard_header_images dhi
    WHERE dhi.is_active = true
    ORDER BY dhi.display_order ASC, dhi.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Create function to add new dashboard header image
CREATE OR REPLACE FUNCTION add_dashboard_header_image(
    p_title VARCHAR(255),
    p_description TEXT,
    p_image_url TEXT,
    p_thumbnail_url TEXT DEFAULT NULL,
    p_display_order INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_id UUID;
    max_order INTEGER;
BEGIN
    -- Check if user is admin
    IF NOT is_user_admin() THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    -- Get max display order if not provided
    IF p_display_order IS NULL THEN
        SELECT COALESCE(MAX(display_order), 0) + 1 
        INTO max_order 
        FROM dashboard_header_images;
    ELSE
        max_order := p_display_order;
    END IF;

    -- Insert new header image
    INSERT INTO dashboard_header_images (
        title, 
        description, 
        image_url, 
        thumbnail_url, 
        display_order, 
        created_by
    ) VALUES (
        p_title, 
        p_description, 
        p_image_url, 
        p_thumbnail_url, 
        max_order, 
        auth.uid()
    ) RETURNING id INTO new_id;

    RETURN new_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Create function to update dashboard header image order
CREATE OR REPLACE FUNCTION update_dashboard_header_image_order(
    p_id UUID,
    p_new_order INTEGER
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user is admin
    IF NOT is_user_admin() THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    UPDATE dashboard_header_images 
    SET display_order = p_new_order
    WHERE id = p_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Create function to toggle dashboard header image status
CREATE OR REPLACE FUNCTION toggle_dashboard_header_image_status(
    p_id UUID,
    p_is_active BOOLEAN
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user is admin
    IF NOT is_user_admin() THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    UPDATE dashboard_header_images 
    SET is_active = p_is_active
    WHERE id = p_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. Create function to delete dashboard header image
CREATE OR REPLACE FUNCTION delete_dashboard_header_image(p_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user is admin
    IF NOT is_user_admin() THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    DELETE FROM dashboard_header_images WHERE id = p_id;
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Insert default header images
INSERT INTO dashboard_header_images (title, description, image_url, display_order, is_active) VALUES
(
    'Mining Site Operations', 
    'Heavy machinery and mining operations in action',
    'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    1,
    true
),
(
    'Open Pit Mining', 
    'Large scale open pit mining operations with sunset view',
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    2,
    true
),
(
    'Heavy Equipment', 
    'Mining trucks and heavy equipment in industrial setting',
    'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    3,
    true
) ON CONFLICT DO NOTHING;

-- 14. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON dashboard_header_images TO authenticated;
GRANT EXECUTE ON FUNCTION get_dashboard_header_images() TO authenticated;
GRANT EXECUTE ON FUNCTION add_dashboard_header_image(VARCHAR(255), TEXT, TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION update_dashboard_header_image_order(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION toggle_dashboard_header_image_status(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_dashboard_header_image(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION is_user_admin(UUID) TO authenticated;

-- 15. Create function to get admin users list
CREATE OR REPLACE FUNCTION get_admin_users()
RETURNS TABLE (
    id UUID,
    email VARCHAR(255),
    full_name VARCHAR(255),
    departemen VARCHAR(100),
    jabatan VARCHAR(100)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.email,
        u.full_name,
        u.departemen,
        u.jabatan
    FROM users u
    WHERE u.is_active = true
    AND (
        u.departemen IN ('Administration', 'Management') 
        OR LOWER(u.jabatan) LIKE '%admin%'
        OR LOWER(u.jabatan) LIKE '%manager%'
    )
    ORDER BY u.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION get_admin_users() TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Dashboard Header Images table and functions created successfully!';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Create admin users via Supabase Auth';
    RAISE NOTICE '2. Update their profiles with departemen = Administration/Management';
    RAISE NOTICE '3. Test the admin functionality in the app';
END $$;
