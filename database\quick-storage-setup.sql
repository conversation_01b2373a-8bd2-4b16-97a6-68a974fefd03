-- =====================================================
-- 🚀 QUICK STORAGE SETUP FOR PROFILE IMAGES
-- =====================================================
-- File: quick-storage-setup.sql
-- Purpose: Minimal setup to fix "Bucket not found" error
-- Usage: Copy and paste this entire script into Supabase SQL Editor
-- =====================================================

-- 📁 CREATE AVATARS STORAGE BUCKET
-- =====================================================
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars', 
  'avatars', 
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 🔐 STORAGE POLICIES (REQUIRED FOR UPLOAD)
-- =====================================================

-- Policy 1: Allow authenticated users to upload their own avatars
CREATE POLICY "Users can upload their own avatar" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- Policy 2: Allow authenticated users to update their own avatars  
CREATE POLICY "Users can update their own avatar" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- Policy 3: Allow authenticated users to delete their own avatars
CREATE POLICY "Users can delete their own avatar" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- Policy 4: Allow public access to view avatars
CREATE POLICY "Public can view avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- 🗃️ USERS TABLE SETUP
-- =====================================================

-- Add avatar_url column if not exists
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS avatar_url TEXT;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_users_avatar_url ON users(avatar_url);

-- ✅ VERIFICATION QUERIES
-- =====================================================

-- Check if bucket was created successfully
SELECT 'Bucket Check' as test, 
       CASE 
         WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'avatars') 
         THEN '✅ Avatars bucket exists' 
         ELSE '❌ Avatars bucket NOT found' 
       END as result;

-- Check if policies were created
SELECT 'Policy Check' as test,
       COUNT(*) || ' policies created for avatars bucket' as result
FROM pg_policies 
WHERE tablename = 'objects' 
AND policyname LIKE '%avatar%';

-- Check if users table has avatar_url column
SELECT 'Column Check' as test,
       CASE 
         WHEN EXISTS (
           SELECT 1 FROM information_schema.columns 
           WHERE table_name = 'users' AND column_name = 'avatar_url'
         ) 
         THEN '✅ avatar_url column exists in users table' 
         ELSE '❌ avatar_url column NOT found in users table' 
       END as result;

-- 🎯 SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 STORAGE SETUP COMPLETED!';
    RAISE NOTICE '✅ Avatars bucket created';
    RAISE NOTICE '✅ Storage policies configured';
    RAISE NOTICE '✅ Users table updated';
    RAISE NOTICE '';
    RAISE NOTICE '📱 You can now test profile image upload in the app!';
    RAISE NOTICE '🔧 If upload still fails, check:';
    RAISE NOTICE '   1. User is logged in';
    RAISE NOTICE '   2. Camera/Gallery permissions granted';
    RAISE NOTICE '   3. Network connection active';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- 🧪 OPTIONAL: TEST DATA
-- =====================================================

-- Uncomment below to add test avatar URLs to existing users
-- UPDATE users 
-- SET avatar_url = 'https://ui-avatars.com/api/?name=' || 
--                  replace(COALESCE(full_name, email), ' ', '+') || 
--                  '&size=200&background=2563eb&color=ffffff&bold=true&format=png'
-- WHERE avatar_url IS NULL 
-- AND (full_name IS NOT NULL OR email IS NOT NULL);

-- =====================================================
-- 📋 MANUAL VERIFICATION STEPS:
-- =====================================================
-- 1. Run this entire script in Supabase SQL Editor
-- 2. Check that all verification queries show ✅ results
-- 3. Go to Storage tab in Supabase Dashboard
-- 4. Verify 'avatars' bucket exists and is public
-- 5. Test upload in the Mining Operations App
-- =====================================================
