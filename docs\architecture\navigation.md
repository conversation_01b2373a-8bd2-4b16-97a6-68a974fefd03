# Navigation System Documentation

## Overview

The Mining Operations App uses React Navigation v6 with a hybrid navigation structure combining Tab Navigation and Stack Navigation to provide intuitive user experience and proper screen hierarchy.

## Navigation Architecture

### Navigation Flow
```
App
├── AuthContext (Authentication wrapper)
├── NavigationContainer
└── TabNavigator (Bottom tabs)
    ├── Dashboard Stack
    │   ├── DashboardScreen (Main landing page)
    │   └── ProductionOverviewScreen (Detailed analytics)
    ├── Equipment Stack
    │   └── EquipmentScreen
    ├── Safety Stack
    │   └── SafetyScreen
    ├── Reports Stack
    │   └── ReportsScreen
    └── Settings Stack
        └── SettingsScreen
```

## Core Components

### 1. TabNavigator (`src/navigation/TabNavigator.tsx`)

Main navigation container with bottom tab bar.

#### Configuration
```typescript
const Tab = createBottomTabNavigator();

export default function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: styles.tabBar,
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textLight,
      }}
    >
      <Tab.Screen name="Dashboard" component={DashboardStackNavigator} />
      <Tab.Screen name="Equipment" component={EquipmentStackNavigator} />
      <Tab.Screen name="Safety" component={SafetyStackNavigator} />
      <Tab.Screen name="Reports" component={ReportsStackNavigator} />
      <Tab.Screen name="Settings" component={SettingsStackNavigator} />
    </Tab.Navigator>
  );
}
```

#### Tab Configuration
- **Dashboard**: Home icon, main landing page
- **Equipment**: Construct icon, equipment management
- **Safety**: Shield icon, safety reports
- **Reports**: Bar chart icon, analytics and reports
- **Settings**: Settings icon, app configuration

### 2. DashboardStackNavigator (`src/navigation/DashboardStackNavigator.tsx`)

Stack navigator for dashboard-related screens.

#### Screen Hierarchy
```typescript
const Stack = createStackNavigator();

export default function DashboardStackNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: Colors.background },
      }}
    >
      <Stack.Screen 
        name="DashboardMain" 
        component={DashboardScreen} 
      />
      <Stack.Screen 
        name="ProductionOverview" 
        component={ProductionOverviewScreen} 
      />
    </Stack.Navigator>
  );
}
```

#### Navigation Flow
1. **DashboardMain**: Entry point with modern layered design
2. **ProductionOverview**: Detailed production analytics (accessible from dashboard menu)

### 3. Other Stack Navigators

Each feature area has its own stack navigator:
- `EquipmentStackNavigator`
- `SafetyStackNavigator`
- `ReportsStackNavigator`
- `SettingsStackNavigator`

## Screen Definitions

### DashboardScreen
- **Route**: `Dashboard → DashboardMain`
- **Purpose**: Main landing page with overview and quick actions
- **Features**: 
  - Modern layered design
  - Quick action menu grid
  - Tab navigation for content filtering
  - Global search functionality

### ProductionOverviewScreen
- **Route**: `Dashboard → ProductionOverview`
- **Purpose**: Detailed production metrics and analytics
- **Navigation**: Accessible from DashboardScreen menu items

### Other Screens
- **EquipmentScreen**: Equipment status and management
- **SafetyScreen**: Safety reports and incident tracking
- **ReportsScreen**: Data visualization and reporting
- **SettingsScreen**: App configuration and preferences

## Navigation Patterns

### 1. Tab-Based Navigation
Primary navigation method using bottom tabs for main feature areas.

```typescript
// Navigate to different tabs
navigation.navigate('Equipment');
navigation.navigate('Safety');
navigation.navigate('Reports');
```

### 2. Stack-Based Navigation
Secondary navigation within feature areas using stack navigation.

```typescript
// Navigate within dashboard stack
navigation.navigate('ProductionOverview');

// Navigate with parameters
navigation.navigate('ProductionOverview', {
  dateRange: 'thisWeek',
  metric: 'stripRatio'
});
```

### 3. Cross-Stack Navigation
Navigation between different feature stacks.

```typescript
// Navigate to specific screen in different stack
navigation.navigate('Equipment', {
  screen: 'EquipmentDetail',
  params: { equipmentId: 'EX-001' }
});
```

## Navigation Hooks

### useNavigation
Primary hook for navigation actions.

```typescript
import { useNavigation } from '@react-navigation/native';

const navigation = useNavigation();

// Navigate to screen
navigation.navigate('ProductionOverview');

// Go back
navigation.goBack();

// Reset navigation stack
navigation.reset({
  index: 0,
  routes: [{ name: 'DashboardMain' }],
});
```

### useRoute
Hook for accessing route parameters and information.

```typescript
import { useRoute } from '@react-navigation/native';

const route = useRoute();
const { equipmentId } = route.params;
```

### useFocusEffect
Hook for handling screen focus events.

```typescript
import { useFocusEffect } from '@react-navigation/native';

useFocusEffect(
  useCallback(() => {
    // Screen focused - refresh data
    loadData();
    
    return () => {
      // Screen unfocused - cleanup
      cleanup();
    };
  }, [])
);
```

## Type Safety

### Navigation Types
```typescript
// Define navigation parameter types
type RootStackParamList = {
  DashboardMain: undefined;
  ProductionOverview: {
    dateRange?: string;
    metric?: string;
  };
  EquipmentDetail: {
    equipmentId: string;
  };
};

// Use typed navigation
type NavigationProp = StackNavigationProp<RootStackParamList>;
const navigation = useNavigation<NavigationProp>();
```

### Route Types
```typescript
type RouteProp = StackRouteProp<RootStackParamList, 'ProductionOverview'>;
const route = useRoute<RouteProp>();
```

## Styling

### Tab Bar Styling
```typescript
const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    height: Layout.tabBarHeight,
    paddingBottom: Layout.spacing.sm,
    paddingTop: Layout.spacing.sm,
    ...Layout.shadow,
  },
  
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
  },
  
  iconContainerFocused: {
    backgroundColor: Colors.primaryLight,
    borderRadius: Layout.borderRadius.sm,
  },
});
```

### Screen Styling
```typescript
const screenOptions = {
  headerShown: false,
  cardStyle: { 
    backgroundColor: Colors.background 
  },
  cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
};
```

## Deep Linking

### URL Structure
```
myapp://dashboard
myapp://equipment/detail/EX-001
myapp://production/overview?metric=stripRatio
myapp://safety/reports
```

### Configuration
```typescript
const linking = {
  prefixes: ['myapp://'],
  config: {
    screens: {
      Dashboard: {
        screens: {
          DashboardMain: 'dashboard',
          ProductionOverview: 'production/overview',
        },
      },
      Equipment: {
        screens: {
          EquipmentMain: 'equipment',
          EquipmentDetail: 'equipment/detail/:equipmentId',
        },
      },
    },
  },
};
```

## Best Practices

### 1. Navigation Structure
- Keep navigation hierarchy shallow (max 3 levels)
- Use tabs for primary navigation
- Use stacks for feature-specific flows
- Implement proper back button handling

### 2. Performance
- Use lazy loading for screens when possible
- Implement proper cleanup in useFocusEffect
- Avoid unnecessary re-renders during navigation

### 3. User Experience
- Provide clear visual feedback for navigation actions
- Implement proper loading states during navigation
- Handle navigation errors gracefully
- Maintain navigation state across app restarts

### 4. Accessibility
- Provide proper screen reader labels
- Implement keyboard navigation support
- Use semantic navigation patterns
- Test with accessibility tools

## Troubleshooting

### Common Issues
1. **Navigation not working**: Check screen registration and route names
2. **Type errors**: Ensure proper TypeScript navigation types
3. **Back button issues**: Implement proper navigation stack management
4. **Performance issues**: Check for memory leaks in navigation listeners

### Debug Tips
- Use React Navigation DevTools
- Enable navigation state logging
- Check navigation stack state
- Monitor performance during navigation

## Related Files
- `src/navigation/TabNavigator.tsx` - Main tab navigation
- `src/navigation/DashboardStackNavigator.tsx` - Dashboard stack
- `src/screens/DashboardScreen.tsx` - Main dashboard implementation
- `App.tsx` - Navigation container setup

## Related Documentation
- [Dashboard Screen](../features/dashboard-screen.md)
- [Architecture Overview](overview.md)
- [Design System](../design/design-system.md)
