-- Mining Operations App - Indexes, Triggers, and RLS Policies

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Users table indexes
CREATE INDEX idx_users_location_id ON users(location_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_employee_id ON users(employee_id);
CREATE INDEX idx_users_is_active ON users(is_active);

-- Equipment table indexes
CREATE INDEX idx_equipment_location_id ON equipment(location_id);
CREATE INDEX idx_equipment_type ON equipment(equipment_type);
CREATE INDEX idx_equipment_status ON equipment(status);
CREATE INDEX idx_equipment_serial_number ON equipment(serial_number);
CREATE INDEX idx_equipment_next_maintenance_due ON equipment(next_maintenance_due);

-- Safety incidents indexes
CREATE INDEX idx_safety_incidents_reported_by ON safety_incidents(reported_by);
CREATE INDEX idx_safety_incidents_location_id ON safety_incidents(location_id);
CREATE INDEX idx_safety_incidents_incident_date ON safety_incidents(incident_date);
CREATE INDEX idx_safety_incidents_severity ON safety_incidents(severity);
CREATE INDEX idx_safety_incidents_status ON safety_incidents(status);
CREATE INDEX idx_safety_incidents_equipment_id ON safety_incidents(equipment_id);

-- Production reports indexes
CREATE INDEX idx_production_reports_created_by ON production_reports(created_by);
CREATE INDEX idx_production_reports_location_id ON production_reports(location_id);
CREATE INDEX idx_production_reports_report_date ON production_reports(report_date);
CREATE INDEX idx_production_reports_shift ON production_reports(shift);

-- Maintenance records indexes
CREATE INDEX idx_maintenance_records_equipment_id ON maintenance_records(equipment_id);
CREATE INDEX idx_maintenance_records_performed_by ON maintenance_records(performed_by);
CREATE INDEX idx_maintenance_records_scheduled_date ON maintenance_records(scheduled_date);
CREATE INDEX idx_maintenance_records_status ON maintenance_records(status);

-- Shifts indexes
CREATE INDEX idx_shifts_location_id ON shifts(location_id);
CREATE INDEX idx_shifts_shift_date ON shifts(shift_date);
CREATE INDEX idx_shifts_supervisor_id ON shifts(supervisor_id);

-- User shifts indexes
CREATE INDEX idx_user_shifts_user_id ON user_shifts(user_id);
CREATE INDEX idx_user_shifts_shift_id ON user_shifts(shift_id);
CREATE INDEX idx_user_shifts_status ON user_shifts(status);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_equipment_updated_at BEFORE UPDATE ON equipment FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_safety_incidents_updated_at BEFORE UPDATE ON safety_incidents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_production_reports_updated_at BEFORE UPDATE ON production_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_records_updated_at BEFORE UPDATE ON maintenance_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shifts_updated_at BEFORE UPDATE ON shifts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_shifts_updated_at BEFORE UPDATE ON user_shifts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate incident numbers
CREATE OR REPLACE FUNCTION generate_incident_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.incident_number = 'INC-' || TO_CHAR(NEW.incident_date, 'YYYY') || '-' || LPAD(nextval('incident_number_seq')::text, 6, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for incident numbers
CREATE SEQUENCE incident_number_seq START 1;

-- Apply incident number trigger
CREATE TRIGGER generate_incident_number_trigger BEFORE INSERT ON safety_incidents FOR EACH ROW EXECUTE FUNCTION generate_incident_number();

-- Function to generate maintenance numbers
CREATE OR REPLACE FUNCTION generate_maintenance_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.maintenance_number = 'MAINT-' || TO_CHAR(NEW.scheduled_date, 'YYYY') || '-' || LPAD(nextval('maintenance_number_seq')::text, 6, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for maintenance numbers
CREATE SEQUENCE maintenance_number_seq START 1;

-- Apply maintenance number trigger
CREATE TRIGGER generate_maintenance_number_trigger BEFORE INSERT ON maintenance_records FOR EACH ROW EXECUTE FUNCTION generate_maintenance_number();

-- Function to generate report numbers
CREATE OR REPLACE FUNCTION generate_report_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.report_number = 'RPT-' || TO_CHAR(NEW.report_date, 'YYYY-MM-DD') || '-' || UPPER(LEFT(NEW.shift::text, 1)) || '-' || LPAD(nextval('report_number_seq')::text, 4, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create sequence for report numbers
CREATE SEQUENCE report_number_seq START 1;

-- Apply report number trigger
CREATE TRIGGER generate_report_number_trigger BEFORE INSERT ON production_reports FOR EACH ROW EXECUTE FUNCTION generate_report_number();

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE safety_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE production_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE shifts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_shifts ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS user_role AS $$
BEGIN
    RETURN (SELECT role FROM users WHERE id = auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get current user's location
CREATE OR REPLACE FUNCTION get_user_location()
RETURNS UUID AS $$
BEGIN
    RETURN (SELECT location_id FROM users WHERE id = auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Supervisors and admins can view all users in their location" ON users FOR SELECT USING (
    get_user_role() IN ('supervisor', 'admin') AND location_id = get_user_location()
);
CREATE POLICY "Admins can manage all users" ON users FOR ALL USING (get_user_role() = 'admin');

-- Locations table policies
CREATE POLICY "Users can view their assigned location" ON locations FOR SELECT USING (
    id = get_user_location() OR get_user_role() IN ('admin', 'supervisor')
);
CREATE POLICY "Admins can manage locations" ON locations FOR ALL USING (get_user_role() = 'admin');

-- Equipment table policies
CREATE POLICY "Users can view equipment at their location" ON equipment FOR SELECT USING (
    location_id = get_user_location() OR get_user_role() IN ('admin', 'maintenance_tech')
);
CREATE POLICY "Maintenance techs can update equipment" ON equipment FOR UPDATE USING (
    get_user_role() IN ('maintenance_tech', 'admin')
);
CREATE POLICY "Supervisors and admins can manage equipment" ON equipment FOR ALL USING (
    get_user_role() IN ('supervisor', 'admin')
);

-- Safety incidents policies
CREATE POLICY "Users can view incidents at their location" ON safety_incidents FOR SELECT USING (
    location_id = get_user_location() OR get_user_role() IN ('admin', 'safety_officer')
);
CREATE POLICY "Users can create incidents" ON safety_incidents FOR INSERT WITH CHECK (
    location_id = get_user_location()
);
CREATE POLICY "Safety officers and admins can manage incidents" ON safety_incidents FOR ALL USING (
    get_user_role() IN ('safety_officer', 'admin')
);

-- Production reports policies
CREATE POLICY "Users can view reports from their location" ON production_reports FOR SELECT USING (
    location_id = get_user_location() OR get_user_role() IN ('admin', 'supervisor')
);
CREATE POLICY "Users can create reports for their location" ON production_reports FOR INSERT WITH CHECK (
    location_id = get_user_location()
);
CREATE POLICY "Supervisors can manage reports" ON production_reports FOR ALL USING (
    get_user_role() IN ('supervisor', 'admin')
);
