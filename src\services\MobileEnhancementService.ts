import { Platform, Dimensions, StatusBar } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Conditional imports for Expo modules (may not be available in all environments)
let Device: any = null;
let Battery: any = null;
let Location: any = null;
let ImagePicker: any = null;
let FileSystem: any = null;
let Sharing: any = null;

try {
  Device = require('expo-device');
} catch (e) {
  console.warn('expo-device not available');
}

try {
  Battery = require('expo-battery');
} catch (e) {
  console.warn('expo-battery not available');
}

try {
  Location = require('expo-location');
} catch (e) {
  console.warn('expo-location not available');
}

try {
  ImagePicker = require('expo-image-picker');
} catch (e) {
  console.warn('expo-image-picker not available');
}

try {
  FileSystem = require('expo-file-system');
} catch (e) {
  console.warn('expo-file-system not available');
}

try {
  Sharing = require('expo-sharing');
} catch (e) {
  console.warn('expo-sharing not available');
}

export interface DeviceCapabilities {
  hasCamera: boolean;
  hasGPS: boolean;
  hasBiometrics: boolean;
  hasNFC: boolean;
  batteryLevel: number;
  isTablet: boolean;
  screenSize: { width: number; height: number };
  orientation: 'portrait' | 'landscape';
}

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  address?: string;
}

export interface PhotoCapture {
  uri: string;
  width: number;
  height: number;
  fileSize: number;
  timestamp: number;
  location?: LocationData;
}

export class MobileEnhancementService {
  private static instance: MobileEnhancementService;
  private deviceCapabilities: DeviceCapabilities | null = null;
  private locationWatcher: any = null; // Location.LocationSubscription when available

  private constructor() {}

  static getInstance(): MobileEnhancementService {
    if (!MobileEnhancementService.instance) {
      MobileEnhancementService.instance = new MobileEnhancementService();
    }
    return MobileEnhancementService.instance;
  }

  // Initialize mobile enhancements
  async initialize(): Promise<void> {
    try {
      await this.detectDeviceCapabilities();
      await this.requestPermissions();
      console.log('📱 Mobile enhancements initialized');
    } catch (error) {
      console.error('❌ Failed to initialize mobile enhancements:', error);
    }
  }

  // Device capabilities detection
  private async detectDeviceCapabilities(): Promise<void> {
    const { width, height } = Dimensions.get('window');
    const batteryLevel = await Battery.getBatteryLevelAsync();
    
    this.deviceCapabilities = {
      hasCamera: Device.deviceType !== Device.DeviceType.DESKTOP,
      hasGPS: Platform.OS !== 'web',
      hasBiometrics: Platform.OS !== 'web',
      hasNFC: Platform.OS === 'android',
      batteryLevel: batteryLevel * 100,
      isTablet: Device.deviceType === Device.DeviceType.TABLET,
      screenSize: { width, height },
      orientation: width > height ? 'landscape' : 'portrait'
    };

    console.log('📱 Device capabilities detected:', this.deviceCapabilities);
  }

  getDeviceCapabilities(): DeviceCapabilities | null {
    return this.deviceCapabilities;
  }

  // Permission management
  private async requestPermissions(): Promise<void> {
    try {
      // Camera permissions
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      if (cameraPermission.status !== 'granted') {
        console.warn('⚠️ Camera permission not granted');
      }

      // Location permissions
      const locationPermission = await Location.requestForegroundPermissionsAsync();
      if (locationPermission.status !== 'granted') {
        console.warn('⚠️ Location permission not granted');
      }

      console.log('✅ Permissions requested');
    } catch (error) {
      console.error('❌ Failed to request permissions:', error);
    }
  }

  // Photo capture for mining operations
  async capturePhoto(options: {
    includeLocation?: boolean;
    quality?: number;
    allowsEditing?: boolean;
  } = {}): Promise<PhotoCapture | null> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Camera permission not granted');
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing || false,
        aspect: [4, 3],
        quality: options.quality || 0.8,
        exif: true
      });

      if (result.canceled) {
        return null;
      }

      const asset = result.assets[0];
      let location: LocationData | undefined;

      // Get location if requested
      if (options.includeLocation) {
        location = await this.getCurrentLocation();
      }

      // Get file size
      const fileInfo = await FileSystem.getInfoAsync(asset.uri);
      const fileSize = fileInfo.exists ? fileInfo.size || 0 : 0;

      const photoCapture: PhotoCapture = {
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        fileSize,
        timestamp: Date.now(),
        location
      };

      console.log('📸 Photo captured:', photoCapture);
      return photoCapture;
    } catch (error) {
      console.error('❌ Failed to capture photo:', error);
      return null;
    }
  }

  // Select photo from gallery
  async selectPhoto(): Promise<PhotoCapture | null> {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (result.canceled) {
        return null;
      }

      const asset = result.assets[0];
      const fileInfo = await FileSystem.getInfoAsync(asset.uri);
      const fileSize = fileInfo.exists ? fileInfo.size || 0 : 0;

      return {
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        fileSize,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('❌ Failed to select photo:', error);
      return null;
    }
  }

  // Location services for mining sites
  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Location permission not granted');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 5000,
        distanceInterval: 10
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || 0,
        timestamp: location.timestamp
      };

      // Get address if possible
      try {
        const addresses = await Location.reverseGeocodeAsync({
          latitude: locationData.latitude,
          longitude: locationData.longitude
        });

        if (addresses.length > 0) {
          const address = addresses[0];
          locationData.address = `${address.street || ''} ${address.city || ''} ${address.region || ''}`.trim();
        }
      } catch (error) {
        console.warn('⚠️ Failed to get address:', error);
      }

      console.log('📍 Location obtained:', locationData);
      return locationData;
    } catch (error) {
      console.error('❌ Failed to get location:', error);
      return null;
    }
  }

  // Start location tracking for mining operations
  async startLocationTracking(callback: (location: LocationData) => void): Promise<boolean> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Location permission not granted');
      }

      this.locationWatcher = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 30000, // 30 seconds
          distanceInterval: 50  // 50 meters
        },
        (location) => {
          const locationData: LocationData = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy || 0,
            timestamp: location.timestamp
          };
          callback(locationData);
        }
      );

      console.log('📍 Location tracking started');
      return true;
    } catch (error) {
      console.error('❌ Failed to start location tracking:', error);
      return false;
    }
  }

  // Stop location tracking
  stopLocationTracking(): void {
    if (this.locationWatcher) {
      this.locationWatcher.remove();
      this.locationWatcher = null;
      console.log('📍 Location tracking stopped');
    }
  }

  // File management for mining documents
  async saveFile(content: string, filename: string, mimeType: string = 'text/plain'): Promise<string | null> {
    try {
      const fileUri = `${FileSystem.documentDirectory}${filename}`;
      await FileSystem.writeAsStringAsync(fileUri, content);
      
      console.log('💾 File saved:', fileUri);
      return fileUri;
    } catch (error) {
      console.error('❌ Failed to save file:', error);
      return null;
    }
  }

  // Share files (reports, photos, etc.)
  async shareFile(uri: string, title: string = 'Mining Operations File'): Promise<boolean> {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        console.warn('⚠️ Sharing not available on this platform');
        return false;
      }

      await Sharing.shareAsync(uri, {
        mimeType: 'application/octet-stream',
        dialogTitle: title
      });

      console.log('📤 File shared successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to share file:', error);
      return false;
    }
  }

  // Battery optimization for mining field work
  async getBatteryInfo(): Promise<{
    level: number;
    state: string;
    lowPowerMode: boolean;
  }> {
    try {
      const level = await Battery.getBatteryLevelAsync();
      const state = await Battery.getBatteryStateAsync();
      const lowPowerMode = await Battery.isLowPowerModeEnabledAsync();

      return {
        level: level * 100,
        state: Battery.BatteryState[state],
        lowPowerMode
      };
    } catch (error) {
      console.error('❌ Failed to get battery info:', error);
      return { level: 100, state: 'Unknown', lowPowerMode: false };
    }
  }

  // Optimize app for battery saving
  async enableBatterySavingMode(): Promise<void> {
    try {
      // Store current settings
      await AsyncStorage.setItem('battery_saving_mode', 'true');
      
      // Reduce background activities
      this.stopLocationTracking();
      
      // Reduce screen brightness (if possible)
      if (Platform.OS === 'android') {
        // Android-specific battery optimizations
        console.log('🔋 Battery saving mode enabled (Android)');
      } else if (Platform.OS === 'ios') {
        // iOS-specific battery optimizations
        console.log('🔋 Battery saving mode enabled (iOS)');
      }
      
      console.log('🔋 Battery saving mode enabled');
    } catch (error) {
      console.error('❌ Failed to enable battery saving mode:', error);
    }
  }

  // Disable battery saving mode
  async disableBatterySavingMode(): Promise<void> {
    try {
      await AsyncStorage.removeItem('battery_saving_mode');
      console.log('🔋 Battery saving mode disabled');
    } catch (error) {
      console.error('❌ Failed to disable battery saving mode:', error);
    }
  }

  // Check if battery saving mode is enabled
  async isBatterySavingModeEnabled(): Promise<boolean> {
    try {
      const enabled = await AsyncStorage.getItem('battery_saving_mode');
      return enabled === 'true';
    } catch (error) {
      return false;
    }
  }

  // Haptic feedback for mining operations
  async provideFeedback(type: 'success' | 'warning' | 'error' | 'light' | 'medium' | 'heavy'): Promise<void> {
    try {
      if (Platform.OS === 'ios') {
        const { Haptics } = require('expo-haptics');
        
        switch (type) {
          case 'success':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            break;
          case 'warning':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          case 'error':
            await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
          case 'light':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            break;
          case 'medium':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            break;
          case 'heavy':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            break;
        }
      } else if (Platform.OS === 'android') {
        // Android haptic feedback
        const { Vibration } = require('react-native');
        
        switch (type) {
          case 'success':
            Vibration.vibrate([0, 100, 50, 100]);
            break;
          case 'warning':
            Vibration.vibrate([0, 200, 100, 200]);
            break;
          case 'error':
            Vibration.vibrate([0, 300, 150, 300, 150, 300]);
            break;
          default:
            Vibration.vibrate(100);
        }
      }
    } catch (error) {
      console.error('❌ Failed to provide haptic feedback:', error);
    }
  }

  // Screen orientation management
  async lockOrientation(orientation: 'portrait' | 'landscape'): Promise<void> {
    try {
      const { ScreenOrientation } = require('expo-screen-orientation');
      
      if (orientation === 'portrait') {
        await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT);
      } else {
        await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE);
      }
      
      console.log(`📱 Screen locked to ${orientation}`);
    } catch (error) {
      console.error('❌ Failed to lock screen orientation:', error);
    }
  }

  async unlockOrientation(): Promise<void> {
    try {
      const { ScreenOrientation } = require('expo-screen-orientation');
      await ScreenOrientation.unlockAsync();
      console.log('📱 Screen orientation unlocked');
    } catch (error) {
      console.error('❌ Failed to unlock screen orientation:', error);
    }
  }

  // Cleanup
  cleanup(): void {
    this.stopLocationTracking();
    console.log('📱 Mobile enhancements cleaned up');
  }
}

export default MobileEnhancementService;
