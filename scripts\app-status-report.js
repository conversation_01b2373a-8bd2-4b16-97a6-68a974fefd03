/**
 * Mining Operations App - Status Report Generator
 * 
 * This script generates a comprehensive status report for the
 * Mining Operations App including database connectivity,
 * application health, and system information.
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase configuration
const supabaseUrl = 'https://ohqbaimnhwvdfrmxvhxv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Qq-2pKIvW2SSJlgQqTW6I_gXdxt81oWv2wViadb9b-Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

class AppStatusReporter {
  constructor() {
    this.report = {
      timestamp: new Date().toISOString(),
      application: {
        name: 'Mining Operations App',
        version: '1.0.0',
        status: 'unknown'
      },
      database: {
        provider: 'Supabase',
        url: supabaseUrl,
        connection: 'unknown',
        tables: {},
        dataCount: {}
      },
      system: {
        platform: process.platform,
        nodeVersion: process.version,
        architecture: process.arch
      },
      tests: [],
      recommendations: []
    };
  }

  log(message, type = 'info') {
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  addTest(name, status, message, details = null) {
    this.report.tests.push({ name, status, message, details });
  }

  async checkPackageJson() {
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      this.report.application.name = packageData.name || 'Mining Operations App';
      this.report.application.version = packageData.version || '1.0.0';
      this.report.application.dependencies = Object.keys(packageData.dependencies || {}).length;
      this.report.application.devDependencies = Object.keys(packageData.devDependencies || {}).length;
      
      this.log(`Application: ${this.report.application.name} v${this.report.application.version}`, 'success');
      return true;
    } catch (error) {
      this.log(`Failed to read package.json: ${error.message}`, 'error');
      return false;
    }
  }

  async testDatabaseConnection() {
    try {
      this.log('Testing database connection...');
      
      const startTime = Date.now();
      const { data, error } = await supabase.from('users').select('count').limit(1);
      const responseTime = Date.now() - startTime;
      
      if (error) {
        this.report.database.connection = 'failed';
        this.report.database.error = error.message;
        this.addTest('Database Connection', 'FAIL', `Connection failed: ${error.message}`);
        return false;
      } else {
        this.report.database.connection = 'success';
        this.report.database.responseTime = responseTime;
        this.addTest('Database Connection', 'PASS', `Connected successfully (${responseTime}ms)`);
        return true;
      }
    } catch (error) {
      this.report.database.connection = 'error';
      this.report.database.error = error.message;
      this.addTest('Database Connection', 'FAIL', `Connection error: ${error.message}`);
      return false;
    }
  }

  async checkDatabaseTables() {
    const tables = [
      'users', 'locations', 'equipment', 'production_reports',
      'safety_incidents', 'maintenance_records', 'shifts', 
      'user_shifts', 'activity_documentation'
    ];

    this.log('Checking database tables...');
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1);
        
        if (error) {
          this.report.database.tables[table] = { status: 'error', error: error.message };
          this.addTest(`Table: ${table}`, 'FAIL', `Access failed: ${error.message}`);
        } else {
          this.report.database.tables[table] = { status: 'accessible', sampleRecords: data?.length || 0 };
          this.addTest(`Table: ${table}`, 'PASS', `Accessible (${data?.length || 0} sample records)`);
        }
      } catch (tableError) {
        this.report.database.tables[table] = { status: 'error', error: tableError.message };
        this.addTest(`Table: ${table}`, 'FAIL', `Error: ${tableError.message}`);
      }
    }
  }

  async getDataCounts() {
    this.log('Getting data counts...');
    
    const tables = ['users', 'locations', 'equipment', 'production_reports', 'safety_incidents'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('*');
        
        if (!error && data) {
          this.report.database.dataCount[table] = data.length;
        } else {
          this.report.database.dataCount[table] = 0;
        }
      } catch (error) {
        this.report.database.dataCount[table] = 0;
      }
    }
  }

  async checkAuthentication() {
    try {
      this.log('Checking authentication system...');
      
      const { data: session, error } = await supabase.auth.getSession();
      
      if (error) {
        this.report.authentication = { status: 'error', error: error.message };
        this.addTest('Authentication', 'FAIL', `Auth error: ${error.message}`);
      } else if (session.session) {
        this.report.authentication = { status: 'authenticated', user: session.session.user.email };
        this.addTest('Authentication', 'PASS', `User authenticated: ${session.session.user.email}`);
      } else {
        this.report.authentication = { status: 'not_authenticated' };
        this.addTest('Authentication', 'WARNING', 'No active session (not logged in)');
      }
    } catch (error) {
      this.report.authentication = { status: 'error', error: error.message };
      this.addTest('Authentication', 'FAIL', `Auth check failed: ${error.message}`);
    }
  }

  generateRecommendations() {
    this.log('Generating recommendations...');
    
    // Check if database has data
    const totalRecords = Object.values(this.report.database.dataCount).reduce((sum, count) => sum + count, 0);
    
    if (totalRecords === 0) {
      this.report.recommendations.push({
        type: 'data',
        priority: 'medium',
        title: 'No Sample Data Found',
        description: 'Database tables are empty. Consider running the sample data population script.',
        action: 'Run: node scripts/populate-sample-data.js'
      });
    }
    
    if (this.report.authentication?.status === 'not_authenticated') {
      this.report.recommendations.push({
        type: 'auth',
        priority: 'low',
        title: 'No Active Session',
        description: 'No user is currently logged in. This is normal for initial setup.',
        action: 'Create a user account through the app or admin panel'
      });
    }
    
    if (this.report.database.connection === 'success' && this.report.database.responseTime > 2000) {
      this.report.recommendations.push({
        type: 'performance',
        priority: 'medium',
        title: 'Slow Database Response',
        description: `Database response time is ${this.report.database.responseTime}ms. Consider optimizing queries.`,
        action: 'Check network connection and database performance'
      });
    }
    
    // Check for failed table access
    const failedTables = Object.entries(this.report.database.tables)
      .filter(([table, info]) => info.status === 'error')
      .map(([table]) => table);
      
    if (failedTables.length > 0) {
      this.report.recommendations.push({
        type: 'database',
        priority: 'high',
        title: 'Table Access Issues',
        description: `Cannot access tables: ${failedTables.join(', ')}`,
        action: 'Check database schema and permissions'
      });
    }
  }

  printReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📊 MINING OPERATIONS APP - STATUS REPORT');
    console.log('='.repeat(80));
    
    console.log(`\n📱 APPLICATION INFO:`);
    console.log(`   Name: ${this.report.application.name}`);
    console.log(`   Version: ${this.report.application.version}`);
    console.log(`   Dependencies: ${this.report.application.dependencies || 'N/A'}`);
    console.log(`   Dev Dependencies: ${this.report.application.devDependencies || 'N/A'}`);
    
    console.log(`\n🗄️  DATABASE INFO:`);
    console.log(`   Provider: ${this.report.database.provider}`);
    console.log(`   URL: ${this.report.database.url}`);
    console.log(`   Connection: ${this.report.database.connection}`);
    if (this.report.database.responseTime) {
      console.log(`   Response Time: ${this.report.database.responseTime}ms`);
    }
    
    console.log(`\n📊 DATA COUNTS:`);
    Object.entries(this.report.database.dataCount).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`);
    });
    
    console.log(`\n🔐 AUTHENTICATION:`);
    console.log(`   Status: ${this.report.authentication?.status || 'unknown'}`);
    if (this.report.authentication?.user) {
      console.log(`   User: ${this.report.authentication.user}`);
    }
    
    console.log(`\n🧪 TEST RESULTS:`);
    const passed = this.report.tests.filter(t => t.status === 'PASS').length;
    const failed = this.report.tests.filter(t => t.status === 'FAIL').length;
    const warnings = this.report.tests.filter(t => t.status === 'WARNING').length;
    
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   ⚠️  Warnings: ${warnings}`);
    console.log(`   📊 Total: ${this.report.tests.length}`);
    
    if (this.report.recommendations.length > 0) {
      console.log(`\n💡 RECOMMENDATIONS:`);
      this.report.recommendations.forEach((rec, index) => {
        const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢';
        console.log(`   ${priority} ${rec.title}`);
        console.log(`      ${rec.description}`);
        console.log(`      Action: ${rec.action}`);
        if (index < this.report.recommendations.length - 1) console.log('');
      });
    }
    
    console.log('\n' + '='.repeat(80));
    console.log(`Report generated at: ${this.report.timestamp}`);
    console.log('='.repeat(80));
  }

  async saveReport() {
    try {
      const reportPath = path.join(process.cwd(), 'app-status-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));
      this.log(`Report saved to: ${reportPath}`, 'success');
    } catch (error) {
      this.log(`Failed to save report: ${error.message}`, 'error');
    }
  }

  async generateReport() {
    console.log('🚀 Generating Mining Operations App Status Report...\n');
    
    await this.checkPackageJson();
    await this.testDatabaseConnection();
    await this.checkDatabaseTables();
    await this.getDataCounts();
    await this.checkAuthentication();
    this.generateRecommendations();
    
    this.printReport();
    await this.saveReport();
    
    console.log('\n✅ Status report generation completed!');
  }
}

// Run the reporter
async function main() {
  const reporter = new AppStatusReporter();
  await reporter.generateReport();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { AppStatusReporter };
