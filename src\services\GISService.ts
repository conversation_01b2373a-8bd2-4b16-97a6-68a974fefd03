import { supabase } from '../config/supabase';

export interface LocationPoint {
  latitude: number;
  longitude: number;
  altitude?: number;
}

export interface GISLocation {
  id: string;
  name: string;
  location_type: 'mine_site' | 'equipment_location' | 'safety_zone' | 'office' | 'storage';
  coordinates: LocationPoint;
  radius?: number; // in meters
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProximityAlert {
  location_id: string;
  location_name: string;
  distance: number; // in meters
  alert_type: 'entering' | 'leaving' | 'nearby';
}

export class GISService {
  private static instance: GISService;

  private constructor() {}

  static getInstance(): GISService {
    if (!GISService.instance) {
      GISService.instance = new GISService();
    }
    return GISService.instance;
  }

  // Create location with PostGIS point
  async createLocation(location: Omit<GISLocation, 'id' | 'created_at' | 'updated_at'>): Promise<GISLocation | null> {
    try {
      const { data, error } = await supabase
        .from('locations')
        .insert({
          name: location.name,
          location_type: location.location_type,
          // Use PostGIS ST_Point function for proper spatial data
          coordinates: `POINT(${location.coordinates.longitude} ${location.coordinates.latitude})`,
          radius: location.radius || 100,
          description: location.description,
          is_active: location.is_active
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Failed to create location:', error);
        return null;
      }

      console.log('📍 Location created with GIS coordinates:', data);
      return data;
    } catch (error) {
      console.error('❌ GIS location creation error:', error);
      return null;
    }
  }

  // Get locations within radius of a point
  async getLocationsNearby(
    centerPoint: LocationPoint, 
    radiusMeters: number = 1000
  ): Promise<GISLocation[]> {
    try {
      // Use PostGIS ST_DWithin for spatial query
      const { data, error } = await supabase
        .rpc('get_locations_within_radius', {
          center_lat: centerPoint.latitude,
          center_lng: centerPoint.longitude,
          radius_meters: radiusMeters
        });

      if (error) {
        console.error('❌ Failed to get nearby locations:', error);
        return [];
      }

      console.log(`📍 Found ${data?.length || 0} locations within ${radiusMeters}m`);
      return data || [];
    } catch (error) {
      console.error('❌ GIS proximity query error:', error);
      return [];
    }
  }

  // Calculate distance between two points
  async calculateDistance(point1: LocationPoint, point2: LocationPoint): Promise<number> {
    try {
      // Use PostGIS ST_Distance for accurate distance calculation
      const { data, error } = await supabase
        .rpc('calculate_distance', {
          lat1: point1.latitude,
          lng1: point1.longitude,
          lat2: point2.latitude,
          lng2: point2.longitude
        });

      if (error) {
        console.error('❌ Failed to calculate distance:', error);
        return 0;
      }

      return data || 0;
    } catch (error) {
      console.error('❌ Distance calculation error:', error);
      return 0;
    }
  }

  // Track equipment location
  async updateEquipmentLocation(equipmentId: string, location: LocationPoint): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('equipment')
        .update({
          current_location: `POINT(${location.longitude} ${location.latitude})`,
          last_location_update: new Date().toISOString()
        })
        .eq('id', equipmentId);

      if (error) {
        console.error('❌ Failed to update equipment location:', error);
        return false;
      }

      console.log(`📍 Equipment ${equipmentId} location updated`);
      return true;
    } catch (error) {
      console.error('❌ Equipment location update error:', error);
      return false;
    }
  }

  // Check proximity alerts for safety zones
  async checkProximityAlerts(currentLocation: LocationPoint): Promise<ProximityAlert[]> {
    try {
      const { data, error } = await supabase
        .rpc('check_proximity_alerts', {
          current_lat: currentLocation.latitude,
          current_lng: currentLocation.longitude
        });

      if (error) {
        console.error('❌ Failed to check proximity alerts:', error);
        return [];
      }

      const alerts: ProximityAlert[] = data || [];
      
      if (alerts.length > 0) {
        console.log(`⚠️ Found ${alerts.length} proximity alerts`);
        alerts.forEach(alert => {
          console.log(`📍 ${alert.alert_type.toUpperCase()}: ${alert.location_name} (${alert.distance}m)`);
        });
      }

      return alerts;
    } catch (error) {
      console.error('❌ Proximity alerts check error:', error);
      return [];
    }
  }

  // Get mining area boundaries
  async getMiningAreaBoundaries(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .eq('location_type', 'mine_site')
        .eq('is_active', true);

      if (error) {
        console.error('❌ Failed to get mining boundaries:', error);
        return [];
      }

      console.log(`🗺️ Found ${data?.length || 0} active mining areas`);
      return data || [];
    } catch (error) {
      console.error('❌ Mining boundaries query error:', error);
      return [];
    }
  }

  // Create geofence for safety monitoring
  async createGeofence(
    name: string,
    center: LocationPoint,
    radius: number,
    alertType: 'entry' | 'exit' | 'both' = 'both'
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('locations')
        .insert({
          name: `Geofence: ${name}`,
          location_type: 'safety_zone',
          coordinates: `POINT(${center.longitude} ${center.latitude})`,
          radius: radius,
          description: `Geofence alert zone - ${alertType} monitoring`,
          is_active: true,
          metadata: { alert_type: alertType }
        });

      if (error) {
        console.error('❌ Failed to create geofence:', error);
        return false;
      }

      console.log(`🚧 Geofence created: ${name} (${radius}m radius)`);
      return true;
    } catch (error) {
      console.error('❌ Geofence creation error:', error);
      return false;
    }
  }

  // Get equipment locations for fleet tracking
  async getEquipmentLocations(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('equipment')
        .select(`
          id, name, equipment_type, status,
          current_location, last_location_update
        `)
        .not('current_location', 'is', null);

      if (error) {
        console.error('❌ Failed to get equipment locations:', error);
        return [];
      }

      console.log(`🚛 Found ${data?.length || 0} equipment with location data`);
      return data || [];
    } catch (error) {
      console.error('❌ Equipment locations query error:', error);
      return [];
    }
  }

  // Generate location-based reports
  async generateLocationReport(locationId: string, startDate: string, endDate: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .rpc('generate_location_report', {
          location_id: locationId,
          start_date: startDate,
          end_date: endDate
        });

      if (error) {
        console.error('❌ Failed to generate location report:', error);
        return null;
      }

      console.log(`📊 Location report generated for ${locationId}`);
      return data;
    } catch (error) {
      console.error('❌ Location report generation error:', error);
      return null;
    }
  }
}

export default GISService;
