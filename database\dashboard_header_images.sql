-- =====================================================
-- Dashboard Header Images Table
-- =====================================================

-- Create table for dashboard header images
CREATE TABLE IF NOT EXISTS dashboard_header_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    thumbnail_url TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_dashboard_header_images_active_order 
ON dashboard_header_images (is_active, display_order);

CREATE INDEX IF NOT EXISTS idx_dashboard_header_images_created_by 
ON dashboard_header_images (created_by);

-- Enable RLS (Row Level Security)
ALTER TABLE dashboard_header_images ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Anyone can view active dashboard header images"
ON dashboard_header_images FOR SELECT
USING (is_active = true);

CREATE POLICY "Admins can manage dashboard header images"
ON dashboard_header_images FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM users
        WHERE users.id = auth.uid()
        AND users.is_active = true
        AND (
            users.departemen IN ('Administration', 'Management')
            OR LOWER(users.jabatan) LIKE '%admin%'
            OR LOWER(users.jabatan) LIKE '%manager%'
        )
    )
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_dashboard_header_images_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER trigger_update_dashboard_header_images_updated_at
    BEFORE UPDATE ON dashboard_header_images
    FOR EACH ROW
    EXECUTE FUNCTION update_dashboard_header_images_updated_at();

-- Insert default header images
INSERT INTO dashboard_header_images (title, description, image_url, display_order, is_active) VALUES
(
    'Mining Site Operations', 
    'Heavy machinery and mining operations in action',
    'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    1,
    true
),
(
    'Open Pit Mining', 
    'Large scale open pit mining operations with sunset view',
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    2,
    true
),
(
    'Heavy Equipment', 
    'Mining trucks and heavy equipment in industrial setting',
    'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    3,
    true
);

-- Create function to get active dashboard header images
CREATE OR REPLACE FUNCTION get_dashboard_header_images()
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    description TEXT,
    image_url TEXT,
    thumbnail_url TEXT,
    display_order INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dhi.id,
        dhi.title,
        dhi.description,
        dhi.image_url,
        dhi.thumbnail_url,
        dhi.display_order
    FROM dashboard_header_images dhi
    WHERE dhi.is_active = true
    ORDER BY dhi.display_order ASC, dhi.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to add new dashboard header image
CREATE OR REPLACE FUNCTION add_dashboard_header_image(
    p_title VARCHAR(255),
    p_description TEXT,
    p_image_url TEXT,
    p_thumbnail_url TEXT DEFAULT NULL,
    p_display_order INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_id UUID;
    max_order INTEGER;
BEGIN
    -- Get max display order if not provided
    IF p_display_order IS NULL THEN
        SELECT COALESCE(MAX(display_order), 0) + 1 
        INTO max_order 
        FROM dashboard_header_images;
    ELSE
        max_order := p_display_order;
    END IF;

    -- Insert new header image
    INSERT INTO dashboard_header_images (
        title, 
        description, 
        image_url, 
        thumbnail_url, 
        display_order, 
        created_by
    ) VALUES (
        p_title, 
        p_description, 
        p_image_url, 
        p_thumbnail_url, 
        max_order, 
        auth.uid()
    ) RETURNING id INTO new_id;

    RETURN new_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update dashboard header image order
CREATE OR REPLACE FUNCTION update_dashboard_header_image_order(
    p_id UUID,
    p_new_order INTEGER
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE dashboard_header_images 
    SET display_order = p_new_order
    WHERE id = p_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to toggle dashboard header image status
CREATE OR REPLACE FUNCTION toggle_dashboard_header_image_status(
    p_id UUID,
    p_is_active BOOLEAN
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE dashboard_header_images 
    SET is_active = p_is_active
    WHERE id = p_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to delete dashboard header image
CREATE OR REPLACE FUNCTION delete_dashboard_header_image(p_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    DELETE FROM dashboard_header_images WHERE id = p_id;
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON dashboard_header_images TO authenticated;
GRANT EXECUTE ON FUNCTION get_dashboard_header_images() TO authenticated;
GRANT EXECUTE ON FUNCTION add_dashboard_header_image(VARCHAR(255), TEXT, TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION update_dashboard_header_image_order(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION toggle_dashboard_header_image_status(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_dashboard_header_image(UUID) TO authenticated;
