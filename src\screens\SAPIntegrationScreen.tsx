import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { Layout } from '../constants/layout';

interface SAPIntegrationScreenProps {
  navigation: any;
}

interface SAPConnection {
  status: 'connected' | 'disconnected' | 'error';
  lastSync: string;
  server: string;
  version: string;
}

interface SAPModule {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'error';
  icon: keyof typeof Ionicons.glyphMap;
  lastUpdate: string;
}

const SAPIntegrationScreen: React.FC<SAPIntegrationScreenProps> = ({ navigation }) => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<SAPConnection>({
    status: 'connected',
    lastSync: '2024-01-19 14:30:00',
    server: 'SAP-MINING-PROD',
    version: 'SAP ECC 6.0'
  });

  const [sapModules] = useState<SAPModule[]>([
    {
      id: 'mm',
      name: 'Material Management',
      description: 'Mining equipment & supplies management',
      status: 'active',
      icon: 'cube-outline',
      lastUpdate: '2024-01-19 14:25:00'
    },
    {
      id: 'pm',
      name: 'Plant Maintenance',
      description: 'Equipment maintenance & work orders',
      status: 'active',
      icon: 'construct-outline',
      lastUpdate: '2024-01-19 14:20:00'
    },
    {
      id: 'pp',
      name: 'Production Planning',
      description: 'Mining production schedules & planning',
      status: 'active',
      icon: 'calendar-outline',
      lastUpdate: '2024-01-19 14:15:00'
    },
    {
      id: 'fi',
      name: 'Financial Accounting',
      description: 'Cost centers & financial reporting',
      status: 'active',
      icon: 'card-outline',
      lastUpdate: '2024-01-19 14:10:00'
    },
    {
      id: 'hr',
      name: 'Human Resources',
      description: 'Employee data & payroll integration',
      status: 'inactive',
      icon: 'people-outline',
      lastUpdate: '2024-01-19 13:45:00'
    },
    {
      id: 'qm',
      name: 'Quality Management',
      description: 'Mining quality control & testing',
      status: 'error',
      icon: 'checkmark-circle-outline',
      lastUpdate: '2024-01-19 12:30:00'
    }
  ]);

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Layout.spacing.lg,
      paddingVertical: Layout.spacing.md,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: Layout.fontSize.xl,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginLeft: Layout.spacing.md,
    },
    content: {
      flex: 1,
      paddingHorizontal: Layout.spacing.lg,
      paddingTop: Layout.spacing.lg,
    },
    connectionCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.lg,
      marginBottom: Layout.spacing.lg,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    statusRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Layout.spacing.sm,
    },
    statusText: {
      fontSize: Layout.fontSize.lg,
      fontWeight: '600',
      marginLeft: Layout.spacing.sm,
    },
    infoText: {
      fontSize: Layout.fontSize.sm,
      color: colors.textSecondary,
      marginBottom: Layout.spacing.xs,
    },
    sectionTitle: {
      fontSize: Layout.fontSize.lg,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.md,
      marginTop: Layout.spacing.sm,
    },
    moduleCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.md,
      padding: Layout.spacing.md,
      marginBottom: Layout.spacing.md,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: isDarkMode ? 0.2 : 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    moduleHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Layout.spacing.sm,
    },
    moduleName: {
      fontSize: Layout.fontSize.md,
      fontWeight: '600',
      color: colors.textPrimary,
      marginLeft: Layout.spacing.sm,
      flex: 1,
    },
    moduleDescription: {
      fontSize: Layout.fontSize.sm,
      color: colors.textSecondary,
      marginBottom: Layout.spacing.xs,
    },
    moduleFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    lastUpdate: {
      fontSize: Layout.fontSize.xs,
      color: colors.textLight,
    },
    syncButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: Layout.spacing.lg,
      paddingVertical: Layout.spacing.sm,
      borderRadius: Layout.borderRadius.md,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: Layout.spacing.lg,
    },
    syncButtonText: {
      color: colors.textInverse,
      fontSize: Layout.fontSize.md,
      fontWeight: '600',
      marginLeft: Layout.spacing.xs,
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return colors.success;
      case 'disconnected':
      case 'inactive':
        return colors.warning;
      case 'error':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
        return 'checkmark-circle';
      case 'disconnected':
      case 'inactive':
        return 'pause-circle';
      case 'error':
        return 'alert-circle';
      default:
        return 'help-circle';
    }
  };

  const handleSync = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      Alert.alert('Success', 'SAP data synchronized successfully');
      setConnectionStatus(prev => ({
        ...prev,
        lastSync: new Date().toLocaleString()
      }));
    } catch (error) {
      Alert.alert('Error', 'Failed to sync SAP data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  return (
    <SafeAreaView style={dynamicStyles.container}>
      <View style={dynamicStyles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.textPrimary} />
        </TouchableOpacity>
        <Text style={dynamicStyles.headerTitle}>SAP Integration</Text>
      </View>

      <ScrollView 
        style={dynamicStyles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Connection Status */}
        <View style={dynamicStyles.connectionCard}>
          <View style={dynamicStyles.statusRow}>
            <Ionicons 
              name={getStatusIcon(connectionStatus.status)} 
              size={24} 
              color={getStatusColor(connectionStatus.status)} 
            />
            <Text style={[dynamicStyles.statusText, { color: getStatusColor(connectionStatus.status) }]}>
              {connectionStatus.status.toUpperCase()}
            </Text>
          </View>
          <Text style={dynamicStyles.infoText}>Server: {connectionStatus.server}</Text>
          <Text style={dynamicStyles.infoText}>Version: {connectionStatus.version}</Text>
          <Text style={dynamicStyles.infoText}>Last Sync: {connectionStatus.lastSync}</Text>
        </View>

        {/* SAP Modules */}
        <Text style={dynamicStyles.sectionTitle}>SAP Modules</Text>
        {sapModules.map((module) => (
          <TouchableOpacity key={module.id} style={dynamicStyles.moduleCard}>
            <View style={dynamicStyles.moduleHeader}>
              <Ionicons name={module.icon} size={20} color={colors.primary} />
              <Text style={dynamicStyles.moduleName}>{module.name}</Text>
              <Ionicons 
                name={getStatusIcon(module.status)} 
                size={16} 
                color={getStatusColor(module.status)} 
              />
            </View>
            <Text style={dynamicStyles.moduleDescription}>{module.description}</Text>
            <View style={dynamicStyles.moduleFooter}>
              <Text style={dynamicStyles.lastUpdate}>Updated: {module.lastUpdate}</Text>
            </View>
          </TouchableOpacity>
        ))}

        {/* Sync Button */}
        <TouchableOpacity 
          style={dynamicStyles.syncButton} 
          onPress={handleSync}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.textInverse} />
          ) : (
            <Ionicons name="sync" size={20} color={colors.textInverse} />
          )}
          <Text style={dynamicStyles.syncButtonText}>
            {loading ? 'Syncing...' : 'Sync All Modules'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SAPIntegrationScreen;
