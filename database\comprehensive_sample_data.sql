-- =============================================
-- COMPREHENSIVE SAMPLE DATA FOR MINING OPERATIONS APP
-- Execute in order: locations → users → equipment → shifts → incidents → reports → maintenance → user_shifts
-- =============================================

-- =============================================
-- 1. LOCATIONS DATA (15 locations)
-- =============================================

INSERT INTO locations (id, name, description, location_type, address, is_active) VALUES
-- Mine Sites
('550e8400-e29b-41d4-a716-************', 'North Mine Site', 'Primary excavation site in the northern sector with open-pit operations', 'mine_site', '1234 Mining Road, North Sector, Mining District', true),
('550e8400-e29b-41d4-a716-************', 'South Mine Site', 'Secondary excavation site in the southern sector', 'mine_site', '9012 Mining Road, South Sector, Mining District', true),
('550e8400-e29b-41d4-a716-************', 'East Mine Site', 'Underground mining operations in the eastern region', 'mine_site', '5678 Underground Ave, East Sector, Mining District', true),
('550e8400-e29b-41d4-a716-************', 'West Mine Site', 'Strip mining operations in the western region', 'mine_site', '7890 Strip Mine Blvd, West Sector, Mining District', true),
('550e8400-e29b-41d4-a716-************', 'Central Mine Site', 'Main production site with multiple extraction points', 'mine_site', '2468 Central Mining Way, Central District', true),

-- Processing Plants
('550e8400-e29b-41d4-a716-************', 'Processing Plant Alpha', 'Main ore processing facility with crushing and grinding operations', 'processing_plant', '5678 Industrial Ave, Central Processing Zone', true),
('550e8400-e29b-41d4-a716-************', 'Processing Plant Beta', 'Secondary processing facility for specialized ore treatment', 'processing_plant', '1357 Processing Blvd, Industrial Zone B', true),
('550e8400-e29b-41d4-a716-************', 'Processing Plant Gamma', 'Advanced processing facility with flotation and leaching', 'processing_plant', '2468 Refinery Road, Advanced Processing Zone', true),

-- Offices
('550e8400-e29b-41d4-a716-************', 'Central Office', 'Administrative headquarters and main office complex', 'office', '3456 Corporate Blvd, Downtown Business District', true),
('550e8400-e29b-41d4-a716-************', 'Field Office North', 'On-site administrative office for northern operations', 'office', '1111 Field Office Lane, North Sector', true),
('550e8400-e29b-41d4-a716-************', 'Field Office South', 'On-site administrative office for southern operations', 'office', '2222 Field Office Drive, South Sector', true),

-- Warehouses
('550e8400-e29b-41d4-a716-************', 'Equipment Warehouse', 'Main parts and equipment storage facility', 'warehouse', '7890 Storage St, Industrial Storage Zone', true),
('550e8400-e29b-41d4-a716-************', 'Supply Warehouse Alpha', 'General supplies and consumables storage', 'warehouse', '3333 Supply Chain Ave, Logistics District', true),
('550e8400-e29b-41d4-a716-446655440014', 'Supply Warehouse Beta', 'Specialized equipment and spare parts storage', 'warehouse', '4444 Parts Storage Blvd, Maintenance Zone', true),
('550e8400-e29b-41d4-a716-************', 'Chemical Storage Facility', 'Secure storage for processing chemicals and hazardous materials', 'warehouse', '5555 Chemical Storage Way, Secure Zone', true)
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- 2. USERS DATA (20 users with different roles)
-- =============================================

-- Note: These users would need to be created in Supabase Auth first
-- This is the profile data that links to auth.users

INSERT INTO users (id, email, full_name, phone, role, location_id, employee_id, hire_date, is_active) VALUES
-- Demo user (already exists)
('f1860292-976f-4e06-bd7d-2df00b7a5e83', '<EMAIL>', 'Demo Supervisor', '******-0199', 'supervisor', '550e8400-e29b-41d4-a716-************', 'DEMO001', '2024-01-01', true),

-- Supervisors
('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'John Mitchell', '******-0101', 'supervisor', '550e8400-e29b-41d4-a716-************', 'SUP001', '2023-01-15', true),
('22222222-2222-2222-2222-222222222222', '<EMAIL>', 'Sarah Johnson', '******-0102', 'supervisor', '550e8400-e29b-41d4-a716-************', 'SUP002', '2023-02-01', true),
('33333333-3333-3333-3333-333333333333', '<EMAIL>', 'Mike Rodriguez', '******-0103', 'supervisor', '550e8400-e29b-41d4-a716-************', 'SUP003', '2023-03-10', true),

-- Safety Officers
('*************-4444-4444-************', '<EMAIL>', 'Lisa Chen', '******-0201', 'safety_officer', '550e8400-e29b-41d4-a716-************', 'SAF001', '2023-01-20', true),
('*************-5555-5555-************', '<EMAIL>', 'David Thompson', '******-0202', 'safety_officer', '550e8400-e29b-41d4-a716-************', 'SAF002', '2023-04-15', true),
('*************-6666-6666-************', '<EMAIL>', 'Maria Garcia', '******-0203', 'safety_officer', '550e8400-e29b-41d4-a716-************', 'SAF003', '2023-05-01', true),

-- Maintenance Technicians
('*************-7777-7777-************', '<EMAIL>', 'Robert Wilson', '******-0301', 'maintenance_tech', '550e8400-e29b-41d4-a716-************', 'MNT001', '2023-02-10', true),
('*************-8888-8888-************', '<EMAIL>', 'Jennifer Brown', '******-0302', 'maintenance_tech', '550e8400-e29b-41d4-a716-************', 'MNT002', '2023-03-15', true),
('*************-9999-9999-************', '<EMAIL>', 'Carlos Martinez', '******-0303', 'maintenance_tech', '550e8400-e29b-41d4-a716-************', 'MNT003', '2023-04-01', true),

-- Operators
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '<EMAIL>', 'Tom Anderson', '******-0401', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR001', '2023-05-15', true),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '<EMAIL>', 'Susan Davis', '******-0402', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR002', '2023-06-01', true),
('cccccccc-cccc-cccc-cccc-cccccccccccc', '<EMAIL>', 'James Miller', '******-0403', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR003', '2023-06-15', true),
('dddddddd-dddd-dddd-dddd-dddddddddddd', '<EMAIL>', 'Patricia Taylor', '******-0404', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR004', '2023-07-01', true),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '<EMAIL>', 'Michael Johnson', '******-0405', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR005', '2023-07-15', true),
('ffffffff-ffff-ffff-ffff-ffffffffffff', '<EMAIL>', 'Linda Williams', '******-0406', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR006', '2023-08-01', true),
('gggggggg-gggg-gggg-gggg-gggggggggggg', '<EMAIL>', 'Richard Jones', '******-0407', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR007', '2023-08-15', true),
('hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', '<EMAIL>', 'Barbara Moore', '******-0408', 'operator', '550e8400-e29b-41d4-a716-************', 'OPR008', '2023-09-01', true),

-- Admin
('iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '<EMAIL>', 'System Administrator', '******-0001', 'admin', '550e8400-e29b-41d4-a716-************', 'ADM001', '2022-12-01', true),
('jjjjjjjj-jjjj-jjjj-jjjj-jjjjjjjjjjjj', '<EMAIL>', 'Operations Manager', '******-0002', 'admin', '550e8400-e29b-41d4-a716-************', 'ADM002', '2023-01-01', true)
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- 3. EQUIPMENT DATA (20 pieces of equipment)
-- =============================================

INSERT INTO equipment (id, name, model, serial_number, manufacturer, equipment_type, location_id, status, purchase_date, last_maintenance_date, next_maintenance_due, specifications, operating_hours, fuel_capacity, max_load_capacity) VALUES
-- Excavators
('660e8400-e29b-41d4-a716-************', 'Excavator Alpha-1', 'CAT 390F', 'CAT390F-001', 'Caterpillar', 'excavator', '550e8400-e29b-41d4-a716-************', 'operational', '2022-06-15', '2024-01-10', '2024-04-10', '{"engine_power": "402 HP", "bucket_capacity": "2.3 m³", "operating_weight": "90 tons", "max_digging_depth": "7.2 m"}', 2450, 680.0, 45.0),
('660e8400-e29b-41d4-a716-************', 'Excavator Beta-1', 'Komatsu PC800', 'KOM800-001', 'Komatsu', 'excavator', '550e8400-e29b-41d4-a716-************', 'operational', '2022-08-20', '2024-01-15', '2024-04-15', '{"engine_power": "469 HP", "bucket_capacity": "3.8 m³", "operating_weight": "78 tons", "max_digging_depth": "7.8 m"}', 1890, 620.0, 42.0),
('660e8400-e29b-41d4-a716-************', 'Excavator Gamma-1', 'Volvo EC750E', 'VOL750-001', 'Volvo', 'excavator', '550e8400-e29b-41d4-a716-************', 'maintenance', '2021-11-10', '2024-01-05', '2024-04-05', '{"engine_power": "396 HP", "bucket_capacity": "3.5 m³", "operating_weight": "75 tons", "max_digging_depth": "7.5 m"}', 3200, 650.0, 40.0),

-- Dump Trucks
('660e8400-e29b-41d4-a716-************', 'Dump Truck Alpha-1', 'CAT 777G', 'CAT777G-001', 'Caterpillar', 'dump_truck', '550e8400-e29b-41d4-a716-************', 'operational', '2022-09-15', '2024-01-12', '2024-04-12', '{"engine_power": "938 HP", "payload": "100 tons", "fuel_tank": "1200L", "body_capacity": "58 m³"}', 1650, 1200.0, 100.0),
('660e8400-e29b-41d4-a716-************', 'Dump Truck Beta-1', 'Komatsu HD785', 'KOM785-001', 'Komatsu', 'dump_truck', '550e8400-e29b-41d4-a716-************', 'operational', '2022-10-01', '2024-01-08', '2024-04-08', '{"engine_power": "1050 HP", "payload": "91 tons", "fuel_tank": "1100L", "body_capacity": "52 m³"}', 1420, 1100.0, 91.0),
('660e8400-e29b-41d4-a716-************', 'Dump Truck Gamma-1', 'Volvo A60H', 'VOLVO-A60H-001', 'Volvo', 'dump_truck', '550e8400-e29b-41d4-a716-************', 'down', '2021-12-15', '2023-12-20', '2024-03-20', '{"engine_power": "469 HP", "payload": "55 tons", "fuel_tank": "400L", "body_capacity": "32 m³"}', 2890, 400.0, 55.0),

-- Drills
('660e8400-e29b-41d4-a716-************', 'Drill Alpha-1', 'Atlas Copco ROC D65', 'ATLAS-ROC-D65-001', 'Atlas Copco', 'drill', '550e8400-e29b-41d4-a716-************', 'operational', '2021-11-10', '2024-01-03', '2024-04-03', '{"drilling_diameter": "89-127mm", "drilling_depth": "32m", "compressor": "25 bar", "engine_power": "559 HP"}', 3200, 300.0, 0.0),
('660e8400-e29b-41d4-a716-************', 'Drill Beta-1', 'Sandvik DX800', 'SAN800-001', 'Sandvik', 'drill', '550e8400-e29b-41d4-a716-************', 'maintenance', '2022-03-20', '2023-12-28', '2024-03-28', '{"drilling_diameter": "102-152mm", "drilling_depth": "40m", "compressor": "28 bar", "engine_power": "630 HP"}', 2100, 350.0, 0.0),

-- Loaders
('660e8400-e29b-41d4-a716-************', 'Loader Alpha-1', 'CAT 992K', 'CAT992K-001', 'Caterpillar', 'loader', '550e8400-e29b-41d4-a716-************', 'operational', '2022-05-15', '2024-01-14', '2024-04-14', '{"engine_power": "700 HP", "bucket_capacity": "8.4 m³", "operating_weight": "54 tons", "max_load": "25 tons"}', 1800, 850.0, 25.0),
('660e8400-e29b-41d4-a716-************', 'Loader Beta-1', 'Komatsu WA600-8', 'KOMATSU-WA600-001', 'Komatsu', 'loader', '550e8400-e29b-41d4-a716-************', 'operational', '2022-12-01', '2024-01-11', '2024-04-11', '{"engine_power": "469 HP", "bucket_capacity": "5.5 m³", "operating_weight": "38 tons", "max_load": "20 tons"}', 1650, 520.0, 20.0),

-- Bulldozers
('660e8400-e29b-41d4-a716-************', 'Bulldozer Alpha-1', 'CAT D8T', 'CATD8T-001', 'Caterpillar', 'bulldozer', '550e8400-e29b-41d4-a716-************', 'operational', '2022-07-10', '2024-01-09', '2024-04-09', '{"engine_power": "305 HP", "blade_capacity": "7.4 m³", "operating_weight": "37 tons", "ground_pressure": "0.9 kg/cm²"}', 2200, 450.0, 0.0),
('660e8400-e29b-41d4-a716-************', 'Bulldozer Beta-1', 'Komatsu D375A', 'KOMD375-001', 'Komatsu', 'bulldozer', '550e8400-e29b-41d4-a716-************', 'maintenance', '2021-09-25', '2023-12-30', '2024-03-30', '{"engine_power": "410 HP", "blade_capacity": "9.2 m³", "operating_weight": "42 tons", "ground_pressure": "0.85 kg/cm²"}', 2950, 520.0, 0.0),

-- Conveyors (Processing Plants)
('660e8400-e29b-41d4-a716-************', 'Conveyor Alpha-1', 'Metso CV100', 'METSO-CV100-001', 'Metso', 'conveyor', '550e8400-e29b-41d4-a716-************', 'operational', '2023-01-05', '2024-01-07', '2024-04-07', '{"belt_width": "1200mm", "capacity": "1000 t/h", "length": "500m", "motor_power": "200 HP"}', 1200, 0.0, 1000.0),
('660e8400-e29b-41d4-a716-446655440014', 'Conveyor Beta-1', 'Sandvik QA451', 'SANQA451-001', 'Sandvik', 'conveyor', '550e8400-e29b-41d4-a716-************', 'operational', '2023-02-15', '2024-01-06', '2024-04-06', '{"belt_width": "1000mm", "capacity": "800 t/h", "length": "350m", "motor_power": "150 HP"}', 980, 0.0, 800.0),

-- Crushers (Processing Plants)
('660e8400-e29b-41d4-a716-************', 'Crusher Alpha-1', 'Metso C160', 'METSOC160-001', 'Metso', 'crusher', '550e8400-e29b-41d4-a716-************', 'operational', '2022-11-20', '2024-01-04', '2024-04-04', '{"capacity": "500 t/h", "feed_size": "1050mm", "product_size": "150mm", "motor_power": "400 HP"}', 1450, 0.0, 500.0),
('660e8400-e29b-41d4-a716-446655440016', 'Crusher Beta-1', 'Sandvik CJ815', 'SANCJ815-001', 'Sandvik', 'crusher', '550e8400-e29b-41d4-a716-************', 'maintenance', '2022-08-30', '2023-12-25', '2024-03-25', '{"capacity": "650 t/h", "feed_size": "1200mm", "product_size": "125mm", "motor_power": "450 HP"}', 1890, 0.0, 650.0),

-- Graders
('660e8400-e29b-41d4-a716-446655440017', 'Grader Alpha-1', 'CAT 24M', 'CAT24M-001', 'Caterpillar', 'grader', '550e8400-e29b-41d4-a716-************', 'operational', '2022-04-12', '2024-01-13', '2024-04-13', '{"engine_power": "290 HP", "blade_length": "4.3m", "operating_weight": "27 tons", "max_speed": "45 km/h"}', 1950, 380.0, 0.0),
('660e8400-e29b-41d4-a716-446655440018', 'Grader Beta-1', 'Volvo G990', 'VOLVOG990-001', 'Volvo', 'grader', '550e8400-e29b-41d4-a716-************', 'operational', '2022-06-25', '2024-01-02', '2024-04-02', '{"engine_power": "310 HP", "blade_length": "4.6m", "operating_weight": "29 tons", "max_speed": "50 km/h"}', 1720, 420.0, 0.0),

-- Additional Loader
('660e8400-e29b-41d4-a716-446655440019', 'Loader Gamma-1', 'Volvo L350H', 'VOLVOL350-001', 'Volvo', 'loader', '550e8400-e29b-41d4-a716-************', 'operational', '2023-03-10', '2024-01-01', '2024-04-01', '{"engine_power": "536 HP", "bucket_capacity": "6.0 m³", "operating_weight": "35 tons", "max_load": "22 tons"}', 890, 600.0, 22.0)
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- 4. SHIFTS DATA (30 shifts over last 2 weeks)
-- =============================================

INSERT INTO shifts (id, location_id, shift_type, shift_date, start_time, end_time, supervisor_id, planned_crew_size, actual_crew_size, notes) VALUES
-- North Mine Site shifts
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-15', '06:00:00', '18:00:00', '11111111-1111-1111-1111-111111111111', 12, 11, 'Good weather conditions, high productivity'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-15', '18:00:00', '06:00:00', '11111111-1111-1111-1111-111111111111', 8, 8, 'Night operations proceeded smoothly'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-16', '06:00:00', '18:00:00', '11111111-1111-1111-1111-111111111111', 12, 12, 'Full crew, excellent production'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-16', '18:00:00', '06:00:00', '11111111-1111-1111-1111-111111111111', 8, 7, 'One operator called in sick'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-17', '06:00:00', '18:00:00', '11111111-1111-1111-1111-111111111111', 12, 10, 'Equipment maintenance reduced crew'),

-- South Mine Site shifts
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-15', '07:00:00', '19:00:00', '22222222-2222-2222-2222-222222222222', 10, 10, 'Standard operations'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-15', '19:00:00', '07:00:00', '22222222-2222-2222-2222-222222222222', 6, 6, 'Night shift completed all targets'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-16', '07:00:00', '19:00:00', '22222222-2222-2222-2222-222222222222', 10, 9, 'Minor equipment delay'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-16', '19:00:00', '07:00:00', '22222222-2222-2222-2222-222222222222', 6, 6, 'Productive night shift'),

-- Processing Plant Alpha shifts
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-15', '07:00:00', '19:00:00', '33333333-3333-3333-3333-333333333333', 8, 8, 'Processing targets met'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-15', '19:00:00', '07:00:00', '33333333-3333-3333-3333-333333333333', 6, 5, 'Maintenance technician on call'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-16', '07:00:00', '19:00:00', '33333333-3333-3333-3333-333333333333', 8, 8, 'High throughput achieved'),

-- East Mine Site shifts
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-15', '06:00:00', '18:00:00', 'f1860292-976f-4e06-bd7d-2df00b7a5e83', 6, 6, 'Underground operations normal'),
('770e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-15', '18:00:00', '06:00:00', 'f1860292-976f-4e06-bd7d-2df00b7a5e83', 4, 4, 'Night underground shift'),

-- West Mine Site shifts
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-15', '06:00:00', '18:00:00', 'f1860292-976f-4e06-bd7d-2df00b7a5e83', 8, 7, 'Strip mining operations'),
('770e8400-e29b-41d4-a716-446655440016', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-15', '18:00:00', '06:00:00', 'f1860292-976f-4e06-bd7d-2df00b7a5e83', 5, 5, 'Night strip operations')
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- 5. SAFETY INCIDENTS DATA (15 incidents over last 6 months)
-- =============================================

INSERT INTO safety_incidents (id, incident_number, reported_by, incident_date, location_id, severity, incident_type, title, description, corrective_actions, equipment_id, injured_person_name, witnesses, status, investigation_notes, resolved_date, attachments) VALUES
-- Critical incidents
('880e8400-e29b-41d4-a716-************', 'INC-2024-000001', '*************-4444-4444-************', '2024-01-14 10:30:00', '550e8400-e29b-41d4-a716-************', 'critical', 'injury', 'Operator injured during excavator maintenance', 'Operator sustained minor cuts on hand while performing routine maintenance on hydraulic system. First aid administered immediately.', 'Implemented additional safety protocols for maintenance procedures. Mandatory safety briefing scheduled.', '660e8400-e29b-41d4-a716-************', 'Tom Anderson', '{"Robert Wilson", "Jennifer Brown"}', 'resolved', 'Investigation completed. Proper PPE was worn. Incident caused by unexpected hydraulic pressure release.', '2024-01-16 14:00:00', '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000002', '*************-5555-5555-************', '2024-01-10 14:15:00', '550e8400-e29b-41d4-a716-************', 'high', 'equipment_failure', 'Dump truck brake failure', 'Dump truck experienced brake failure while descending haul road. Driver managed to safely stop using emergency procedures.', 'Complete brake system inspection and replacement. Enhanced pre-shift inspection procedures implemented.', '660e8400-e29b-41d4-a716-************', null, '{"James Miller", "Patricia Taylor"}', 'resolved', 'Brake system had exceeded service interval. Maintenance schedule updated.', '2024-01-12 16:30:00', '{}'),

-- High severity incidents
('880e8400-e29b-41d4-a716-************', 'INC-2024-000003', '*************-4444-4444-************', '2024-01-08 09:45:00', '550e8400-e29b-41d4-a716-************', 'high', 'near_miss', 'Near miss with falling rock', 'Large rock fell from highwall near excavator operation. No personnel injured but excavator operator had to take evasive action.', 'Highwall inspection increased to twice daily. Exclusion zone expanded around unstable areas.', '660e8400-e29b-41d4-a716-************', null, '{"Tom Anderson", "Susan Davis"}', 'resolved', 'Geological assessment completed. Additional rock scaling scheduled.', '2024-01-10 11:00:00', '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000004', '*************-6666-6666-************', '2024-01-05 16:20:00', '550e8400-e29b-41d4-a716-************', 'high', 'environmental', 'Chemical spill in processing plant', 'Minor chemical spill occurred during reagent transfer. Spill contained immediately and cleanup completed.', 'Reviewed chemical handling procedures. Additional spill containment equipment installed.', null, null, '{"Michael Johnson", "Linda Williams"}', 'resolved', 'Spill was contained within secondary containment. No environmental impact.', '2024-01-07 10:15:00', '{}'),

-- Medium severity incidents
('880e8400-e29b-41d4-a716-************', 'INC-2024-000005', '*************-4444-4444-************', '2024-01-03 11:30:00', '550e8400-e29b-41d4-a716-************', 'medium', 'near_miss', 'Vehicle collision near miss', 'Two haul trucks came close to collision at intersection. Both drivers followed proper radio protocols.', 'Installed additional warning signs at intersection. Radio check procedures reinforced.', null, null, '{"Richard Jones", "Barbara Moore"}', 'resolved', 'Visibility was reduced due to dust. Water truck schedule increased.', '2024-01-05 14:20:00', '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000006', '*************-5555-5555-************', '2023-12-28 13:45:00', '550e8400-e29b-41d4-a716-************', 'medium', 'equipment_failure', 'Drill bit breakage', 'Drill bit broke during normal operations. No personnel injured. Equipment shut down immediately.', 'Drill bit inspection procedures updated. Replacement schedule reviewed.', '660e8400-e29b-41d4-a716-************', null, '{"Carlos Martinez"}', 'resolved', 'Drill bit had reached end of service life. Preventive replacement implemented.', '2023-12-30 09:00:00', '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000007', '*************-6666-6666-************', '2023-12-25 08:15:00', '550e8400-e29b-41d4-a716-************', 'medium', 'near_miss', 'Conveyor belt near miss', 'Worker nearly caught clothing in conveyor belt. Emergency stop activated immediately.', 'Additional guarding installed. Mandatory safety training refresher scheduled.', '660e8400-e29b-41d4-a716-************', null, '{"Jennifer Brown"}', 'resolved', 'Worker was following proper procedures. Additional safety measures implemented.', '2023-12-27 15:30:00', '{}'),

-- Low severity incidents
('880e8400-e29b-41d4-a716-************', 'INC-2024-000008', '*************-4444-4444-************', '2023-12-20 15:30:00', '550e8400-e29b-41d4-a716-************', 'low', 'near_miss', 'Slip and fall near miss', 'Worker slipped on wet surface but caught themselves. No injury occurred.', 'Improved drainage installed. Non-slip surfaces added to walkways.', null, null, '{}', 'resolved', 'Surface was wet due to recent rain. Preventive measures implemented.', '2023-12-22 10:00:00', '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000009', '*************-5555-5555-************', '2023-12-18 10:00:00', '550e8400-e29b-41d4-a716-************', 'low', 'security', 'Unauthorized vehicle on site', 'Delivery truck entered site without proper authorization. Security escorted vehicle to proper entrance.', 'Security procedures reviewed with all contractors. Additional signage installed.', null, null, '{}', 'resolved', 'Driver was new and unfamiliar with site procedures. Training provided.', '2023-12-19 14:00:00', '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000010', '*************-6666-6666-************', '2023-12-15 12:45:00', '550e8400-e29b-41d4-a716-************', 'low', 'environmental', 'Minor dust emission', 'Dust suppression system temporarily offline causing increased dust levels. System restored quickly.', 'Backup dust suppression system installed. Maintenance schedule updated.', null, null, '{}', 'resolved', 'System failure was due to clogged nozzles. Preventive cleaning implemented.', '2023-12-16 08:30:00', '{}'),

-- Recent incidents (investigating/reported)
('880e8400-e29b-41d4-a716-************', 'INC-2024-000011', '*************-4444-4444-************', '2024-01-18 09:15:00', '550e8400-e29b-41d4-a716-************', 'medium', 'equipment_failure', 'Hydraulic leak in excavator', 'Hydraulic fluid leak detected in excavator Alpha-1. Equipment taken out of service immediately.', null, '660e8400-e29b-41d4-a716-************', null, '{"Tom Anderson"}', 'investigating', 'Investigation in progress. Hydraulic system being inspected.', null, '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000012', '*************-5555-5555-************', '2024-01-17 14:30:00', '550e8400-e29b-41d4-a716-************', 'low', 'near_miss', 'Radio communication failure', 'Temporary radio communication failure between haul trucks. Backup communication procedures used.', null, null, null, '{"James Miller", "Patricia Taylor"}', 'reported', 'Initial report filed. Technical investigation scheduled.', null, '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000013', '*************-6666-6666-************', '2024-01-16 11:00:00', '550e8400-e29b-41d4-a716-************', 'medium', 'near_miss', 'Crusher jam cleared safely', 'Crusher experienced material jam. Proper lockout/tagout procedures followed during clearing.', null, '660e8400-e29b-41d4-a716-************', null, '{"Michael Johnson"}', 'investigating', 'Reviewing material feed procedures to prevent future jams.', null, '{}'),

('880e8400-e29b-41d4-a716-446655440014', 'INC-2024-000014', '*************-4444-4444-************', '2024-01-15 16:45:00', '550e8400-e29b-41d4-a716-************', 'low', 'near_miss', 'PPE compliance reminder', 'Worker observed without proper eye protection in designated area. Immediate correction made.', null, null, 'Susan Davis', '{"Robert Wilson"}', 'reported', 'Routine safety observation. Refresher training scheduled.', null, '{}'),

('880e8400-e29b-41d4-a716-************', 'INC-2024-000015', '*************-5555-5555-************', '2024-01-14 07:30:00', '550e8400-e29b-41d4-a716-************', 'low', 'environmental', 'Water truck overspray', 'Water truck overspray created muddy conditions on haul road. Adjusted spray pattern immediately.', null, null, null, '{}', 'reported', 'Minor operational adjustment. No environmental impact.', null, '{}')
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- 6. PRODUCTION REPORTS DATA (20 reports over last 2 weeks)
-- =============================================

INSERT INTO production_reports (id, report_number, created_by, report_date, shift, location_id, production_metrics, equipment_used, total_tonnage, operating_hours, downtime_hours, fuel_consumed, notes, weather_conditions, crew_size, approved_by, approved_date) VALUES
-- North Mine Site reports
('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-18-D-0001', '11111111-1111-1111-1111-111111111111', '2024-01-18', 'day', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 1850, "waste_removed": 1200, "blast_holes_drilled": 65, "equipment_efficiency": 92.5, "safety_incidents": 0, "production_target": 1800, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
3050.0, 11.5, 0.5, 1450.0, 'Excellent production day. All equipment performed well. Weather conditions favorable.', 'Clear, 24°C, Light winds', 11, 'iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '2024-01-19 08:00:00'),

('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-17-N-0001', '11111111-1111-1111-1111-111111111111', '2024-01-17', 'night', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 1200, "waste_removed": 800, "blast_holes_drilled": 42, "equipment_efficiency": 88.3, "safety_incidents": 0, "production_target": 1100, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
2000.0, 7.8, 0.2, 980.0, 'Good night shift performance. Minimal downtime.', 'Clear, 18°C, Calm', 8, 'iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '2024-01-18 08:00:00'),

('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-16-D-0001', '11111111-1111-1111-1111-111111111111', '2024-01-16', 'day', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 1750, "waste_removed": 1150, "blast_holes_drilled": 58, "equipment_efficiency": 90.1, "safety_incidents": 0, "production_target": 1800, "target_achieved": false}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
2900.0, 11.2, 0.8, 1380.0, 'Slightly below target due to brief equipment delay. Overall good performance.', 'Partly cloudy, 22°C', 12, 'iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '2024-01-17 08:00:00'),

-- South Mine Site reports
('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-18-D-0002', '22222222-2222-2222-2222-222222222222', '2024-01-18', 'day', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 1400, "waste_removed": 950, "blast_holes_drilled": 48, "equipment_efficiency": 89.7, "safety_incidents": 0, "production_target": 1350, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
2350.0, 11.8, 0.2, 1200.0, 'Exceeded production target. Equipment running efficiently.', 'Clear, 26°C, Light breeze', 10, 'jjjjjjjj-jjjj-jjjj-jjjj-jjjjjjjjjjjj', '2024-01-19 08:30:00'),

('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-17-N-0002', '22222222-2222-2222-2222-222222222222', '2024-01-17', 'night', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 980, "waste_removed": 620, "blast_holes_drilled": 32, "equipment_efficiency": 85.4, "safety_incidents": 0, "production_target": 900, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
1600.0, 7.5, 0.5, 850.0, 'Good night shift. All targets met.', 'Clear, 19°C', 6, 'jjjjjjjj-jjjj-jjjj-jjjj-jjjjjjjjjjjj', '2024-01-18 08:30:00'),

-- Processing Plant Alpha reports
('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-18-D-0003', '33333333-3333-3333-3333-333333333333', '2024-01-18', 'day', '550e8400-e29b-41d4-a716-************',
'{"ore_processed": 2800, "recovery_rate": 94.2, "throughput": 240, "equipment_efficiency": 96.1, "safety_incidents": 0, "production_target": 2700, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
2800.0, 11.6, 0.4, 450.0, 'Excellent processing day. High recovery rates achieved.', 'Clear, 25°C', 8, 'iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '2024-01-19 09:00:00'),

('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-17-N-0003', '33333333-3333-3333-3333-333333333333', '2024-01-17', 'night', '550e8400-e29b-41d4-a716-************',
'{"ore_processed": 1950, "recovery_rate": 93.8, "throughput": 220, "equipment_efficiency": 94.5, "safety_incidents": 0, "production_target": 1900, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
1950.0, 8.0, 0.0, 320.0, 'Smooth night operations. No downtime.', 'Clear, 20°C', 6, 'iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '2024-01-18 09:00:00'),

-- East Mine Site reports (Underground)
('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-18-D-0004', 'f1860292-976f-4e06-bd7d-2df00b7a5e83', '2024-01-18', 'day', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 850, "development_meters": 45, "equipment_efficiency": 87.2, "safety_incidents": 0, "production_target": 800, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************"}',
850.0, 10.5, 1.5, 680.0, 'Underground operations proceeding well. Development targets met.', 'Surface: Clear, 23°C', 6, 'iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '2024-01-19 09:30:00'),

-- West Mine Site reports (Strip Mining)
('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-18-D-0005', 'f1860292-976f-4e06-bd7d-2df00b7a5e83', '2024-01-18', 'day', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 1200, "overburden_removed": 2400, "equipment_efficiency": 91.3, "safety_incidents": 0, "production_target": 1150, "target_achieved": true}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-446655440018"}',
3600.0, 11.0, 1.0, 920.0, 'Strip mining operations successful. Good overburden removal rates.', 'Clear, 24°C, Light winds', 7, 'jjjjjjjj-jjjj-jjjj-jjjj-jjjjjjjjjjjj', '2024-01-19 10:00:00'),

-- Additional historical reports
('990e8400-e29b-41d4-a716-************', 'RPT-2024-01-15-D-0001', '11111111-1111-1111-1111-111111111111', '2024-01-15', 'day', '550e8400-e29b-41d4-a716-************',
'{"ore_extracted": 1650, "waste_removed": 1100, "blast_holes_drilled": 55, "equipment_efficiency": 88.9, "safety_incidents": 1, "production_target": 1700, "target_achieved": false}',
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}',
2750.0, 10.8, 1.2, 1320.0, 'Production affected by safety incident. All protocols followed.', 'Overcast, 21°C', 11, 'iiiiiiii-iiii-iiii-iiii-iiiiiiiiiiii', '2024-01-16 08:00:00')
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- 7. MAINTENANCE RECORDS DATA (15 maintenance records)
-- =============================================

INSERT INTO maintenance_records (id, maintenance_number, equipment_id, performed_by, maintenance_type, scheduled_date, completed_date, description, parts_used, labor_hours, total_cost, next_maintenance_due, status, notes, attachments) VALUES
-- Completed maintenance
('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000001', '660e8400-e29b-41d4-a716-************', '*************-7777-7777-************', 'corrective', '2024-01-12', '2024-01-13', 'Replace worn drill bits and hydraulic seals',
'[{"part": "Drill bit set", "quantity": 3, "cost": 450.00}, {"part": "Hydraulic seal kit", "quantity": 1, "cost": 125.00}, {"part": "Hydraulic fluid", "quantity": 20, "cost": 80.00}]',
6.5, 655.00, '2024-04-13', 'completed', 'Drill bits were severely worn. Hydraulic seals replaced as preventive measure.', '{}'),

('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000002', '660e8400-e29b-41d4-a716-************', '*************-7777-7777-************', 'preventive', '2024-01-10', '2024-01-10', '500-hour preventive maintenance service',
'[{"part": "Engine oil", "quantity": 25, "cost": 200.00}, {"part": "Hydraulic fluid", "quantity": 50, "cost": 300.00}, {"part": "Air filter", "quantity": 2, "cost": 85.00}, {"part": "Fuel filter", "quantity": 1, "cost": 45.00}]',
8.0, 630.00, '2024-04-10', 'completed', 'Routine 500-hour service completed. All systems checked and fluids replaced.', '{}'),

('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000003', '660e8400-e29b-41d4-a716-************', '*************-8888-8888-************', 'corrective', '2024-01-08', '2024-01-09', 'Brake system overhaul and replacement',
'[{"part": "Brake pads", "quantity": 8, "cost": 320.00}, {"part": "Brake discs", "quantity": 4, "cost": 800.00}, {"part": "Brake fluid", "quantity": 10, "cost": 60.00}, {"part": "Brake lines", "quantity": 2, "cost": 150.00}]',
12.0, 1330.00, '2024-07-09', 'completed', 'Complete brake system overhaul due to safety incident. All components replaced.', '{}'),

('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000004', '660e8400-e29b-41d4-a716-************', '*************-8888-8888-************', 'preventive', '2024-01-07', '2024-01-07', 'Conveyor belt inspection and adjustment',
'[{"part": "Belt cleaner blades", "quantity": 4, "cost": 120.00}, {"part": "Roller bearings", "quantity": 6, "cost": 180.00}, {"part": "Lubricating grease", "quantity": 5, "cost": 75.00}]',
4.5, 375.00, '2024-04-07', 'completed', 'Belt alignment adjusted. Cleaner blades replaced. All rollers lubricated.', '{}'),

('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000005', '660e8400-e29b-41d4-a716-************', '*************-8888-8888-************', 'preventive', '2024-01-04', '2024-01-05', 'Crusher jaw plate replacement',
'[{"part": "Jaw plates", "quantity": 2, "cost": 1200.00}, {"part": "Bolts and fasteners", "quantity": 1, "cost": 85.00}, {"part": "Gasket set", "quantity": 1, "cost": 45.00}]',
10.0, 1330.00, '2024-07-05', 'completed', 'Jaw plates showed normal wear. Replaced as scheduled. Crusher performance improved.', '{}'),

-- In progress maintenance
('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000006', '660e8400-e29b-41d4-a716-************', '*************-9999-9999-************', 'emergency', '2024-01-18', null, 'Emergency repair - transmission failure',
'[{"part": "Transmission rebuild kit", "quantity": 1, "cost": 2500.00}, {"part": "Transmission fluid", "quantity": 30, "cost": 180.00}]',
0.0, 2680.00, null, 'in_progress', 'Major transmission failure. Rebuild in progress. Expected completion tomorrow.', '{}'),

('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000007', '660e8400-e29b-41d4-a716-************', '*************-7777-7777-************', 'corrective', '2024-01-17', null, 'Hydraulic system repair',
'[{"part": "Hydraulic pump", "quantity": 1, "cost": 1800.00}, {"part": "Hydraulic hoses", "quantity": 4, "cost": 240.00}]',
0.0, 2040.00, null, 'in_progress', 'Hydraulic pump replacement in progress. Waiting for parts delivery.', '{}'),

-- Scheduled maintenance
('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000008', '660e8400-e29b-41d4-a716-************', '*************-7777-7777-************', 'preventive', '2024-01-20', null, '1000-hour major service',
'[{"part": "Engine oil", "quantity": 30, "cost": 240.00}, {"part": "Hydraulic fluid", "quantity": 60, "cost": 360.00}, {"part": "Filter kit", "quantity": 1, "cost": 150.00}]',
0.0, 750.00, '2024-05-20', 'scheduled', 'Major service scheduled. Parts ordered and ready.', '{}'),

('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000009', '660e8400-e29b-41d4-a716-************', '*************-8888-8888-************', 'preventive', '2024-01-22', null, 'Tire replacement and wheel alignment',
'[{"part": "Tires", "quantity": 6, "cost": 3600.00}, {"part": "Wheel weights", "quantity": 12, "cost": 60.00}]',
0.0, 3660.00, '2024-07-22', 'scheduled', 'Tire wear inspection completed. Replacement scheduled.', '{}'),

('aa0e8400-e29b-41d4-a716-************', 'MAINT-2024-000010', '660e8400-e29b-41d4-a716-************', '*************-9999-9999-************', 'inspection', '2024-01-25', null, 'Annual safety inspection',
'[]',
0.0, 0.00, null, 'scheduled', 'Annual safety and compliance inspection scheduled.', '{}')
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- 8. USER SHIFTS DATA (40 user-shift assignments)
-- =============================================

INSERT INTO user_shifts (id, user_id, shift_id, check_in_time, check_out_time, status, overtime_hours, notes) VALUES
-- North Mine Site - Day Shift (2024-01-15)
('us0e8400-e29b-41d4-a716-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '770e8400-e29b-41d4-a716-************', '2024-01-15 05:55:00', '2024-01-15 18:05:00', 'checked_out', 0.0, 'On time, good performance'),
('us0e8400-e29b-41d4-a716-************', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '770e8400-e29b-41d4-a716-************', '2024-01-15 06:00:00', '2024-01-15 18:00:00', 'checked_out', 0.0, 'Standard shift'),
('us0e8400-e29b-41d4-a716-************', '*************-7777-7777-************', '770e8400-e29b-41d4-a716-************', '2024-01-15 05:50:00', '2024-01-15 18:30:00', 'checked_out', 0.5, 'Overtime for equipment maintenance'),

-- North Mine Site - Night Shift (2024-01-15)
('us0e8400-e29b-41d4-a716-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '770e8400-e29b-41d4-a716-************', '2024-01-15 17:55:00', '2024-01-16 06:00:00', 'checked_out', 0.0, 'Night shift completed'),
('us0e8400-e29b-41d4-a716-************', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '770e8400-e29b-41d4-a716-************', '2024-01-15 18:00:00', '2024-01-16 06:05:00', 'checked_out', 0.0, 'Good night shift'),

-- South Mine Site - Day Shift (2024-01-15)
('us0e8400-e29b-41d4-a716-************', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '770e8400-e29b-41d4-a716-************', '2024-01-15 06:55:00', '2024-01-15 19:00:00', 'checked_out', 0.0, 'Standard performance'),
('us0e8400-e29b-41d4-a716-************', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '770e8400-e29b-41d4-a716-************', '2024-01-15 07:00:00', '2024-01-15 19:15:00', 'checked_out', 0.25, 'Brief overtime'),
('us0e8400-e29b-41d4-a716-************', '*************-9999-9999-************', '770e8400-e29b-41d4-a716-************', '2024-01-15 06:50:00', '2024-01-15 19:00:00', 'checked_out', 0.0, 'Maintenance support'),

-- Processing Plant Alpha - Day Shift (2024-01-15)
('us0e8400-e29b-41d4-a716-************', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '770e8400-e29b-41d4-a716-************', '2024-01-15 06:55:00', '2024-01-15 19:00:00', 'checked_out', 0.0, 'Processing operations'),
('us0e8400-e29b-41d4-a716-************', 'ffffffff-ffff-ffff-ffff-ffffffffffff', '770e8400-e29b-41d4-a716-************', '2024-01-15 07:00:00', '2024-01-15 19:00:00', 'checked_out', 0.0, 'Standard shift'),
('us0e8400-e29b-41d4-a716-************', '*************-8888-8888-************', '770e8400-e29b-41d4-a716-************', '2024-01-15 06:45:00', '2024-01-15 19:30:00', 'checked_out', 0.5, 'Equipment maintenance'),

-- Recent shifts (2024-01-16)
('us0e8400-e29b-41d4-a716-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '770e8400-e29b-41d4-a716-************', '2024-01-16 05:58:00', '2024-01-16 18:02:00', 'checked_out', 0.0, 'Good day'),
('us0e8400-e29b-41d4-a716-************', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '770e8400-e29b-41d4-a716-************', '2024-01-16 06:00:00', '2024-01-16 18:00:00', 'checked_out', 0.0, 'Standard'),
('us0e8400-e29b-41d4-a716-446655440014', '*************-7777-7777-************', '770e8400-e29b-41d4-a716-************', '2024-01-16 05:55:00', '2024-01-16 18:00:00', 'checked_out', 0.0, 'Early start'),

-- Current day shifts (2024-01-18) - some still in progress
('us0e8400-e29b-41d4-a716-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '770e8400-e29b-41d4-a716-************', '2024-01-18 06:00:00', null, 'checked_in', 0.0, 'Current shift in progress'),
('us0e8400-e29b-41d4-a716-446655440016', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '770e8400-e29b-41d4-a716-************', '2024-01-18 06:02:00', null, 'checked_in', 0.0, 'Current shift'),
('us0e8400-e29b-41d4-a716-446655440017', '*************-7777-7777-************', '770e8400-e29b-41d4-a716-************', '2024-01-18 05:58:00', null, 'checked_in', 0.0, 'Maintenance ready'),

-- Absent/scheduled shifts
('us0e8400-e29b-41d4-a716-446655440018', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '770e8400-e29b-41d4-a716-************', null, null, 'absent', 0.0, 'Called in sick'),
('us0e8400-e29b-41d4-a716-446655440019', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '770e8400-e29b-41d4-a716-************', '2024-01-16 07:00:00', '2024-01-16 19:00:00', 'checked_out', 0.0, 'Completed shift'),

-- East Mine Site (Underground)
('us0e8400-e29b-41d4-a716-446655440020', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '770e8400-e29b-41d4-a716-************', '2024-01-15 05:55:00', '2024-01-15 18:00:00', 'checked_out', 0.0, 'Underground operations'),
('us0e8400-e29b-41d4-a716-446655440021', 'gggggggg-gggg-gggg-gggg-gggggggggggg', '770e8400-e29b-41d4-a716-446655440014', '2024-01-15 17:58:00', '2024-01-16 06:00:00', 'checked_out', 0.0, 'Night underground'),

-- West Mine Site (Strip Mining)
('us0e8400-e29b-41d4-a716-446655440022', 'hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', '770e8400-e29b-41d4-a716-************', '2024-01-15 06:00:00', '2024-01-15 18:00:00', 'checked_out', 0.0, 'Strip mining ops'),
('us0e8400-e29b-41d4-a716-446655440023', 'hhhhhhhh-hhhh-hhhh-hhhh-hhhhhhhhhhhh', '770e8400-e29b-41d4-a716-446655440016', '2024-01-15 18:00:00', '2024-01-16 06:00:00', 'checked_out', 0.0, 'Night strip ops')
ON CONFLICT (id) DO NOTHING;

-- =============================================
-- FINAL VERIFICATION QUERIES
-- =============================================

SELECT '=== COMPREHENSIVE SAMPLE DATA INSERTION COMPLETE ===' as status;
SELECT 'Locations inserted: ' || COUNT(*) as count FROM locations;
SELECT 'Users inserted: ' || COUNT(*) as count FROM users;
SELECT 'Equipment inserted: ' || COUNT(*) as count FROM equipment;
SELECT 'Shifts inserted: ' || COUNT(*) as count FROM shifts;
SELECT 'Safety incidents inserted: ' || COUNT(*) as count FROM safety_incidents;
SELECT 'Production reports inserted: ' || COUNT(*) as count FROM production_reports;
SELECT 'Maintenance records inserted: ' || COUNT(*) as count FROM maintenance_records;
SELECT 'User shifts inserted: ' || COUNT(*) as count FROM user_shifts;

-- Summary statistics for dashboard
SELECT
    'Dashboard Statistics:' as summary,
    (SELECT COUNT(*) FROM equipment WHERE status = 'operational') as operational_equipment,
    (SELECT COUNT(*) FROM equipment WHERE status = 'maintenance') as maintenance_equipment,
    (SELECT COUNT(*) FROM equipment WHERE status = 'down') as down_equipment,
    (SELECT COUNT(*) FROM safety_incidents WHERE incident_date >= CURRENT_DATE - INTERVAL '30 days') as recent_incidents,
    (SELECT COUNT(*) FROM production_reports WHERE report_date >= CURRENT_DATE - INTERVAL '7 days') as recent_reports;
