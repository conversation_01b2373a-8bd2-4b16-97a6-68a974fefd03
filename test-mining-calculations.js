const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
);

// Mining calculation functions (JavaScript version)
class MiningCalculationService {
  static calculateStripRatio(overburden, ore) {
    if (ore <= 0) return 0;
    return Number((overburden / ore).toFixed(4));
  }

  static calculateFuelRatio(fuel, overburden, ore) {
    const totalMaterial = overburden + (ore / 3.39);
    if (totalMaterial <= 0) return 0;
    return Number((fuel / totalMaterial).toFixed(4));
  }

  static calculateTotalMaterial(overburden, ore) {
    return Number((overburden + (ore / 3.39)).toFixed(2));
  }
}

async function testMiningCalculations() {
  console.log('🧪 TESTING MINING CALCULATIONS - FR & SR FORMULAS');
  console.log('================================================');
  console.log('');

  // Test data samples
  const testCases = [
    {
      name: 'Normal Operations',
      actual_ob: 5000,    // Bcm
      actual_ore: 1500,   // tons
      actual_fuel: 7200,  // liters
      plan_ob: 4800,
      plan_ore: 1600,
      plan_fuel: 7000
    },
    {
      name: 'High Strip Ratio',
      actual_ob: 8000,
      actual_ore: 1000,
      actual_fuel: 9500,
      plan_ob: 7500,
      plan_ore: 1200,
      plan_fuel: 9000
    },
    {
      name: 'Low Strip Ratio',
      actual_ob: 2000,
      actual_ore: 2500,
      actual_fuel: 5500,
      plan_ob: 2200,
      plan_ore: 2400,
      plan_fuel: 5800
    }
  ];

  console.log('📊 TESTING STANDARDIZED FORMULAS:');
  console.log('');

  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}:`);
    console.log(`   Input: OB=${testCase.actual_ob} Bcm, Ore=${testCase.actual_ore} tons, Fuel=${testCase.actual_fuel}L`);
    
    // Test Strip Ratio
    const actualSR = testCase.actual_ore > 0 ? testCase.actual_ob / testCase.actual_ore : 0;
    const planSR = testCase.plan_ore > 0 ? testCase.plan_ob / testCase.plan_ore : 0;
    
    console.log(`   Strip Ratio: Actual=${actualSR.toFixed(2)}, Plan=${planSR.toFixed(2)}`);
    
    // Test Fuel Ratio (corrected formula)
    const totalActualMaterial = testCase.actual_ob + (testCase.actual_ore / 3.39);
    const totalPlanMaterial = testCase.plan_ob + (testCase.plan_ore / 3.39);
    const actualFR = totalActualMaterial > 0 ? testCase.actual_fuel / totalActualMaterial : 0;
    const planFR = totalPlanMaterial > 0 ? testCase.plan_fuel / totalPlanMaterial : 0;
    
    console.log(`   Total Material: Actual=${totalActualMaterial.toFixed(2)} Bcm, Plan=${totalPlanMaterial.toFixed(2)} Bcm`);
    console.log(`   Fuel Ratio: Actual=${actualFR.toFixed(4)} L/Bcm, Plan=${planFR.toFixed(4)} L/Bcm`);
    
    // Variance calculations
    const srVariance = actualSR - planSR;
    const frVariance = actualFR - planFR;
    
    console.log(`   Variances: SR=${srVariance.toFixed(2)}, FR=${frVariance.toFixed(4)} L/Bcm`);
    console.log('');
  });

  // Test with real database data
  console.log('🗄️ TESTING WITH REAL DATABASE DATA:');
  console.log('');

  try {
    const { data: sampleData, error } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .not('actual_ob', 'is', null)
      .not('actual_ore', 'is', null)
      .not('actual_fuel', 'is', null)
      .limit(5)
      .order('date', { ascending: false });

    if (error) {
      console.error('❌ Database query error:', error);
      return;
    }

    if (!sampleData || sampleData.length === 0) {
      console.log('⚠️ No sample data found in database');
      return;
    }

    console.log(`📊 Testing with ${sampleData.length} real records:`);
    console.log('');

    sampleData.forEach((record, index) => {
      console.log(`${index + 1}. Date: ${record.date}`);
      console.log(`   Raw Data: OB=${record.actual_ob}, Ore=${record.actual_ore}, Fuel=${record.actual_fuel}`);
      
      // Calculate using corrected formulas
      const correctSR = record.actual_ore > 0 ? record.actual_ob / record.actual_ore : 0;
      const totalMaterial = record.actual_ob + (record.actual_ore / 3.39);
      const correctFR = totalMaterial > 0 ? record.actual_fuel / totalMaterial : 0;
      
      console.log(`   Corrected SR: ${correctSR.toFixed(4)}`);
      console.log(`   Corrected FR: ${correctFR.toFixed(4)} L/Bcm`);
      console.log(`   Total Material: ${totalMaterial.toFixed(2)} Bcm`);
      
      // Compare with stored values if they exist
      if (record.stripping_ratio !== null) {
        const srDiff = Math.abs(correctSR - record.stripping_ratio);
        console.log(`   Stored SR: ${record.stripping_ratio}, Difference: ${srDiff.toFixed(4)}`);
      }
      
      if (record.fuel_ratio !== null) {
        const frDiff = Math.abs(correctFR - record.fuel_ratio);
        console.log(`   Stored FR: ${record.fuel_ratio}, Difference: ${frDiff.toFixed(4)}`);
      }
      
      console.log('');
    });

  } catch (error) {
    console.error('❌ Database test error:', error);
  }

  // Test formula consistency across different services
  console.log('🔄 TESTING FORMULA CONSISTENCY:');
  console.log('');

  const testData = {
    actual_ob: 5000,
    actual_ore: 1500,
    actual_fuel: 7200,
    plan_ob: 4800,
    plan_ore: 1600,
    plan_fuel: 7000
  };

  console.log('Test Data:', testData);
  console.log('');

  // Manual calculation
  const manualSR = testData.actual_ore > 0 ? testData.actual_ob / testData.actual_ore : 0;
  const manualTotalMaterial = testData.actual_ob + (testData.actual_ore / 3.39);
  const manualFR = manualTotalMaterial > 0 ? testData.actual_fuel / manualTotalMaterial : 0;

  console.log('Manual Calculations:');
  console.log(`   SR: ${manualSR.toFixed(4)}`);
  console.log(`   FR: ${manualFR.toFixed(4)} L/Bcm`);
  console.log(`   Total Material: ${manualTotalMaterial.toFixed(2)} Bcm`);
  console.log('');

  // Validation tests
  console.log('✅ VALIDATION TESTS:');
  console.log('');

  const validationTests = [
    {
      name: 'Normal Range Test',
      data: { actual_ob: 5000, actual_ore: 1500, actual_fuel: 7200 },
      expectedSRRange: [2.0, 4.0],
      expectedFRRange: [1.0, 3.0]
    },
    {
      name: 'High Strip Ratio Test',
      data: { actual_ob: 10000, actual_ore: 1000, actual_fuel: 12000 },
      expectedSRRange: [8.0, 12.0],
      expectedFRRange: [1.0, 4.0]
    },
    {
      name: 'Edge Case - Zero Ore',
      data: { actual_ob: 5000, actual_ore: 0, actual_fuel: 7200 },
      expectedSRRange: [0, 0],
      expectedFRRange: [1.0, 2.0]
    }
  ];

  validationTests.forEach((test, index) => {
    console.log(`${index + 1}. ${test.name}:`);
    
    const sr = test.data.actual_ore > 0 ? test.data.actual_ob / test.data.actual_ore : 0;
    const totalMat = test.data.actual_ob + (test.data.actual_ore / 3.39);
    const fr = totalMat > 0 ? test.data.actual_fuel / totalMat : 0;
    
    console.log(`   Calculated SR: ${sr.toFixed(4)}`);
    console.log(`   Calculated FR: ${fr.toFixed(4)} L/Bcm`);
    
    const srInRange = sr >= test.expectedSRRange[0] && sr <= test.expectedSRRange[1];
    const frInRange = fr >= test.expectedFRRange[0] && fr <= test.expectedFRRange[1];
    
    console.log(`   SR Range Check: ${srInRange ? '✅' : '❌'} (Expected: ${test.expectedSRRange[0]}-${test.expectedSRRange[1]})`);
    console.log(`   FR Range Check: ${frInRange ? '✅' : '❌'} (Expected: ${test.expectedFRRange[0]}-${test.expectedFRRange[1]})`);
    console.log('');
  });

  console.log('🎯 FORMULA DOCUMENTATION:');
  console.log('========================');
  console.log('Strip Ratio (SR): Overburden (Bcm) / Ore (tons)');
  console.log('Fuel Ratio (FR): Fuel (L) / (OB (Bcm) + (Ore (tons) / 3.39))');
  console.log('Total Material: OB (Bcm) + (Ore (tons) / 3.39)');
  console.log('Ore Density Factor: 3.39 (conversion from tons to Bcm)');
  console.log('Units: SR (ratio), FR (L/Bcm), Total Material (Bcm)');
  console.log('');

  console.log('✅ MINING CALCULATIONS TEST COMPLETED');
  console.log('=====================================');
}

// Run the tests
testMiningCalculations().catch(console.error);
