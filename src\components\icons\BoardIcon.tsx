import React from 'react';
import Svg, { G, Path, Defs, LinearGradient, Stop } from 'react-native-svg';

interface BoardIconProps {
  width?: number;
  height?: number;
  opacity?: number;
}

const BoardIcon: React.FC<BoardIconProps> = ({ 
  width = 16, 
  height = 16, 
  opacity = 1 
}) => {
  return (
    <Svg 
      width={width} 
      height={height} 
      viewBox="0 0 16 16"
      style={{ opacity }}
    >
      <Defs>
        <LinearGradient 
          id="fluentColorBoard160" 
          x1="4.667" 
          x2="8.327" 
          y1="7.182" 
          y2="15.777" 
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#b0f098" />
          <Stop offset="1" stopColor="#52d17c" />
        </LinearGradient>
        
        <LinearGradient 
          id="fluentColorBoard161" 
          x1="8.4" 
          x2="13.077" 
          y1="2.889" 
          y2="9.103" 
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#52d17c" />
          <Stop offset="1" stopColor="#309c61" />
        </LinearGradient>
        
        <LinearGradient 
          id="fluentColorBoard162" 
          x1="2.857" 
          x2="8.01" 
          y1="6.421" 
          y2="11.174" 
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#42b870" />
          <Stop offset="1" stopColor="#1a7f7c" />
        </LinearGradient>
        
        <LinearGradient 
          id="fluentColorBoard163" 
          x1="2.857" 
          x2="4.433" 
          y1="2.8" 
          y2="6.881" 
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#b0f098" />
          <Stop offset="1" stopColor="#64de89" />
        </LinearGradient>
      </Defs>
      
      <G fill="none">
        <Path 
          fill="url(#fluentColorBoard160)" 
          d="m14 10l-3-1l-3 1l-1 2l1 2h3.5a2.5 2.5 0 0 0 2.5-2.5z"
        />
        <Path 
          fill="url(#fluentColorBoard161)" 
          d="M8 2L7 6l1 4h6V4.5A2.5 2.5 0 0 0 11.5 2z"
        />
        <Path 
          fill="url(#fluentColorBoard162)" 
          d="M8 14V6L5 5L2 6v5.5A2.5 2.5 0 0 0 4.5 14z"
        />
        <Path 
          fill="url(#fluentColorBoard163)" 
          d="M8 2v4H2V4.5A2.5 2.5 0 0 1 4.5 2z"
        />
      </G>
    </Svg>
  );
};

export default BoardIcon;
