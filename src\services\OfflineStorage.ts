import AsyncStorage from '@react-native-async-storage/async-storage';
import { ProductionDataItem } from '../components/charts/ChartDataProcessor';
import PlatformService from './PlatformService';

export interface OfflineQueueItem {
  id: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE';
  table: string;
  data: any;
  timestamp: number;
  retryCount: number;
}

export interface OfflineStorageKeys {
  PRODUCTION_DATA: 'production_data';
  LOCATIONS_DATA: 'locations_data';
  EQUIPMENT_DATA: 'equipment_data';
  OFFLINE_QUEUE: 'offline_queue';
  LAST_SYNC: 'last_sync';
  APP_SETTINGS: 'app_settings';
}

export class OfflineStorage {
  private static readonly KEYS: OfflineStorageKeys = {
    PRODUCTION_DATA: 'production_data',
    LOCATIONS_DATA: 'locations_data',
    EQUIPMENT_DATA: 'equipment_data',
    OFFLINE_QUEUE: 'offline_queue',
    LAST_SYNC: 'last_sync',
    APP_SETTINGS: 'app_settings',
  };

  // Platform-aware storage methods
  private static async setItem(key: string, value: string): Promise<void> {
    if (PlatformService.isWeb()) {
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.error('❌ localStorage setItem failed:', error);
        throw error;
      }
    } else {
      await AsyncStorage.setItem(key, value);
    }
  }

  private static async getItem(key: string): Promise<string | null> {
    if (PlatformService.isWeb()) {
      try {
        return localStorage.getItem(key);
      } catch (error) {
        console.error('❌ localStorage getItem failed:', error);
        return null;
      }
    } else {
      return await AsyncStorage.getItem(key);
    }
  }

  private static async removeItem(key: string): Promise<void> {
    if (PlatformService.isWeb()) {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error('❌ localStorage removeItem failed:', error);
      }
    } else {
      await AsyncStorage.removeItem(key);
    }
  }

  private static async multiRemove(keys: string[]): Promise<void> {
    if (PlatformService.isWeb()) {
      try {
        keys.forEach(key => localStorage.removeItem(key));
      } catch (error) {
        console.error('❌ localStorage multiRemove failed:', error);
      }
    } else {
      await AsyncStorage.multiRemove(keys);
    }
  }

  private static async multiGet(keys: string[]): Promise<[string, string | null][]> {
    if (PlatformService.isWeb()) {
      try {
        return keys.map(key => [key, localStorage.getItem(key)] as [string, string | null]);
      } catch (error) {
        console.error('❌ localStorage multiGet failed:', error);
        return keys.map(key => [key, null] as [string, string | null]);
      }
    } else {
      const result = await AsyncStorage.multiGet(keys);
      return result.map(([key, value]) => [key, value] as [string, string | null]);
    }
  }

  // Production Data Management
  static async saveProductionData(data: ProductionDataItem[]): Promise<void> {
    try {
      const jsonData = JSON.stringify(data);
      await this.setItem(this.KEYS.PRODUCTION_DATA, jsonData);
      console.log(`💾 Saved ${data.length} production records to offline storage`);
    } catch (error) {
      console.error('❌ Failed to save production data:', error);
      throw error;
    }
  }

  static async getProductionData(): Promise<ProductionDataItem[]> {
    try {
      const jsonData = await this.getItem(this.KEYS.PRODUCTION_DATA);
      if (!jsonData) return [];

      const data = JSON.parse(jsonData);
      console.log(`📱 Loaded ${data.length} production records from offline storage`);
      return data;
    } catch (error) {
      console.error('❌ Failed to load production data:', error);
      return [];
    }
  }

  // Offline Queue Management
  static async addToQueue(item: Omit<OfflineQueueItem, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    try {
      const queue = await this.getOfflineQueue();
      const newItem: OfflineQueueItem = {
        ...item,
        id: `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: Date.now(),
        retryCount: 0,
      };
      
      queue.push(newItem);
      await this.setItem(this.KEYS.OFFLINE_QUEUE, JSON.stringify(queue));
      console.log(`📝 Added item to offline queue: ${newItem.action} ${newItem.table}`);
    } catch (error) {
      console.error('❌ Failed to add to offline queue:', error);
      throw error;
    }
  }

  static async getOfflineQueue(): Promise<OfflineQueueItem[]> {
    try {
      const jsonData = await this.getItem(this.KEYS.OFFLINE_QUEUE);
      return jsonData ? JSON.parse(jsonData) : [];
    } catch (error) {
      console.error('❌ Failed to get offline queue:', error);
      return [];
    }
  }

  static async removeFromQueue(itemId: string): Promise<void> {
    try {
      const queue = await this.getOfflineQueue();
      const filteredQueue = queue.filter(item => item.id !== itemId);
      await AsyncStorage.setItem(this.KEYS.OFFLINE_QUEUE, JSON.stringify(filteredQueue));
      console.log(`🗑️ Removed item from offline queue: ${itemId}`);
    } catch (error) {
      console.error('❌ Failed to remove from offline queue:', error);
      throw error;
    }
  }

  static async updateQueueItemRetryCount(itemId: string): Promise<void> {
    try {
      const queue = await this.getOfflineQueue();
      const item = queue.find(q => q.id === itemId);
      if (item) {
        item.retryCount += 1;
        await AsyncStorage.setItem(this.KEYS.OFFLINE_QUEUE, JSON.stringify(queue));
      }
    } catch (error) {
      console.error('❌ Failed to update retry count:', error);
    }
  }

  // Sync Management
  static async setLastSyncTime(timestamp: number): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.LAST_SYNC, timestamp.toString());
    } catch (error) {
      console.error('❌ Failed to set last sync time:', error);
    }
  }

  static async getLastSyncTime(): Promise<number> {
    try {
      const timestamp = await AsyncStorage.getItem(this.KEYS.LAST_SYNC);
      return timestamp ? parseInt(timestamp, 10) : 0;
    } catch (error) {
      console.error('❌ Failed to get last sync time:', error);
      return 0;
    }
  }

  // Locations Data
  static async saveLocationsData(data: any[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.LOCATIONS_DATA, JSON.stringify(data));
      console.log(`📍 Saved ${data.length} locations to offline storage`);
    } catch (error) {
      console.error('❌ Failed to save locations data:', error);
    }
  }

  static async getLocationsData(): Promise<any[]> {
    try {
      const jsonData = await AsyncStorage.getItem(this.KEYS.LOCATIONS_DATA);
      return jsonData ? JSON.parse(jsonData) : [];
    } catch (error) {
      console.error('❌ Failed to load locations data:', error);
      return [];
    }
  }

  // Equipment Data
  static async saveEquipmentData(data: any[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.EQUIPMENT_DATA, JSON.stringify(data));
      console.log(`🚜 Saved ${data.length} equipment records to offline storage`);
    } catch (error) {
      console.error('❌ Failed to save equipment data:', error);
    }
  }

  static async getEquipmentData(): Promise<any[]> {
    try {
      const jsonData = await AsyncStorage.getItem(this.KEYS.EQUIPMENT_DATA);
      return jsonData ? JSON.parse(jsonData) : [];
    } catch (error) {
      console.error('❌ Failed to load equipment data:', error);
      return [];
    }
  }

  // App Settings
  static async saveAppSettings(settings: any): Promise<void> {
    try {
      await AsyncStorage.setItem(this.KEYS.APP_SETTINGS, JSON.stringify(settings));
    } catch (error) {
      console.error('❌ Failed to save app settings:', error);
    }
  }

  static async getAppSettings(): Promise<any> {
    try {
      const jsonData = await AsyncStorage.getItem(this.KEYS.APP_SETTINGS);
      return jsonData ? JSON.parse(jsonData) : {};
    } catch (error) {
      console.error('❌ Failed to load app settings:', error);
      return {};
    }
  }

  // Storage Management
  static async clearAllData(): Promise<void> {
    try {
      const keys = Object.values(this.KEYS);
      await AsyncStorage.multiRemove(keys);
      console.log('🧹 Cleared all offline data');
    } catch (error) {
      console.error('❌ Failed to clear offline data:', error);
    }
  }

  static async getStorageInfo(): Promise<{
    totalSize: number;
    itemCount: number;
    lastSync: number;
    queueSize: number;
  }> {
    try {
      const keys = Object.values(this.KEYS);
      const items = await AsyncStorage.multiGet(keys);
      
      let totalSize = 0;
      let itemCount = 0;
      
      items.forEach(([key, value]) => {
        if (value) {
          totalSize += value.length;
          if (key === this.KEYS.PRODUCTION_DATA) {
            const data = JSON.parse(value);
            itemCount += Array.isArray(data) ? data.length : 0;
          }
        }
      });

      const lastSync = await this.getLastSyncTime();
      const queue = await this.getOfflineQueue();

      return {
        totalSize,
        itemCount,
        lastSync,
        queueSize: queue.length,
      };
    } catch (error) {
      console.error('❌ Failed to get storage info:', error);
      return { totalSize: 0, itemCount: 0, lastSync: 0, queueSize: 0 };
    }
  }
}

export default OfflineStorage;
