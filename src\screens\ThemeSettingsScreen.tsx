import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Layout } from '../constants';
import { useTheme, useThemeColors, ThemeMode } from '../contexts/ThemeContext';

interface ThemeSettingsScreenProps {
  navigation: any;
}

interface ThemeSettings {
  theme: 'light' | 'dark' | 'auto';
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
  compactMode: boolean;
  highContrast: boolean;
  reduceMotion: boolean;
}

const ThemeSettingsScreen: React.FC<ThemeSettingsScreenProps> = ({ navigation }) => {
  const { themeMode, isDarkMode, setThemeMode } = useTheme();
  const colors = useThemeColors();

  const [settings, setSettings] = useState<ThemeSettings>({
    theme: themeMode === 'system' ? 'auto' : themeMode,
    accentColor: Colors.primary,
    fontSize: 'medium',
    compactMode: false,
    highContrast: false,
    reduceMotion: false,
  });
  const [loading, setLoading] = useState(true);

  const themeOptions = [
    { key: 'light', label: 'Light', icon: 'sunny', description: 'Light theme for better visibility in bright environments' },
    { key: 'dark', label: 'Dark', icon: 'moon', description: 'Dark theme for reduced eye strain in low light' },
    { key: 'auto', label: 'Auto', icon: 'phone-portrait', description: 'Automatically switch based on system settings' },
  ];

  const accentColors = [
    { key: '#007AFF', label: 'Blue', color: '#007AFF' },
    { key: '#34C759', label: 'Green', color: '#34C759' },
    { key: '#FF9500', label: 'Orange', color: '#FF9500' },
    { key: '#FF3B30', label: 'Red', color: '#FF3B30' },
    { key: '#5856D6', label: 'Purple', color: '#5856D6' },
    { key: '#FF2D92', label: 'Pink', color: '#FF2D92' },
  ];

  const fontSizeOptions = [
    { key: 'small', label: 'Small', description: 'Compact text for more content' },
    { key: 'medium', label: 'Medium', description: 'Standard text size' },
    { key: 'large', label: 'Large', description: 'Larger text for better readability' },
  ];

  useEffect(() => {
    loadSettings();
  }, []);

  // Sync with ThemeContext changes
  useEffect(() => {
    setSettings(prev => ({
      ...prev,
      theme: themeMode === 'system' ? 'auto' : themeMode
    }));
  }, [themeMode]);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('themeSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading theme settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (newSettings: ThemeSettings) => {
    try {
      await AsyncStorage.setItem('themeSettings', JSON.stringify(newSettings));
      setSettings(newSettings);
      
      // Show restart notice for theme changes
      if (newSettings.theme !== settings.theme || newSettings.accentColor !== settings.accentColor) {
        Alert.alert(
          'Theme Changed',
          'Some theme changes may require restarting the app to take full effect.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error saving theme settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
    // Map 'auto' to 'system' for ThemeContext
    const themeMode: ThemeMode = theme === 'auto' ? 'system' : theme;
    setThemeMode(themeMode);

    const newSettings = { ...settings, theme };
    saveSettings(newSettings);
  };

  const handleAccentColorChange = (accentColor: string) => {
    const newSettings = { ...settings, accentColor };
    saveSettings(newSettings);
  };

  const handleFontSizeChange = (fontSize: 'small' | 'medium' | 'large') => {
    const newSettings = { ...settings, fontSize };
    saveSettings(newSettings);
  };

  const handleToggle = (key: keyof ThemeSettings) => {
    const newSettings = {
      ...settings,
      [key]: !settings[key],
    };
    saveSettings(newSettings);
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Reset Theme Settings',
      'Are you sure you want to reset all theme settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            const defaultSettings: ThemeSettings = {
              theme: 'light',
              accentColor: Colors.primary,
              fontSize: 'medium',
              compactMode: false,
              highContrast: false,
              reduceMotion: false,
            };
            saveSettings(defaultSettings);
          },
        },
      ]
    );
  };

  const ThemeOption = ({ option }: { option: typeof themeOptions[0] }) => (
    <TouchableOpacity
      style={[
        styles.optionItem,
        settings.theme === option.key && styles.selectedOption
      ]}
      onPress={() => handleThemeChange(option.key as any)}
    >
      <View style={styles.optionIcon}>
        <Ionicons 
          name={option.icon as any} 
          size={20} 
          color={settings.theme === option.key ? Colors.primary : Colors.textLight} 
        />
      </View>
      <View style={styles.optionContent}>
        <Text style={[
          styles.optionTitle,
          settings.theme === option.key && styles.selectedText
        ]}>
          {option.label}
        </Text>
        <Text style={styles.optionDescription}>{option.description}</Text>
      </View>
      {settings.theme === option.key && (
        <Ionicons name="checkmark-circle" size={20} color={Colors.primary} />
      )}
    </TouchableOpacity>
  );

  const ColorOption = ({ color }: { color: typeof accentColors[0] }) => (
    <TouchableOpacity
      style={[
        styles.colorOption,
        { backgroundColor: color.color },
        settings.accentColor === color.key && styles.selectedColorOption
      ]}
      onPress={() => handleAccentColorChange(color.key)}
    >
      {settings.accentColor === color.key && (
        <Ionicons name="checkmark" size={16} color="white" />
      )}
    </TouchableOpacity>
  );

  const FontSizeOption = ({ option }: { option: typeof fontSizeOptions[0] }) => (
    <TouchableOpacity
      style={[
        styles.optionItem,
        settings.fontSize === option.key && styles.selectedOption
      ]}
      onPress={() => handleFontSizeChange(option.key as any)}
    >
      <View style={styles.optionContent}>
        <Text style={[
          styles.optionTitle,
          settings.fontSize === option.key && styles.selectedText
        ]}>
          {option.label}
        </Text>
        <Text style={styles.optionDescription}>{option.description}</Text>
      </View>
      {settings.fontSize === option.key && (
        <Ionicons name="checkmark-circle" size={20} color={Colors.primary} />
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading theme settings...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDarkMode ? "light-content" : "dark-content"}
        backgroundColor={colors.surface}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Theme Settings</Text>
        <TouchableOpacity
          style={styles.resetButton}
          onPress={resetToDefaults}
        >
          <Ionicons name="refresh" size={20} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Theme Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          {themeOptions.map((option) => (
            <ThemeOption key={option.key} option={option} />
          ))}
        </View>

        {/* Accent Color */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accent Color</Text>
          <View style={styles.colorGrid}>
            {accentColors.map((color) => (
              <ColorOption key={color.key} color={color} />
            ))}
          </View>
        </View>

        {/* Font Size */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Text Size</Text>
          {fontSizeOptions.map((option) => (
            <FontSizeOption key={option.key} option={option} />
          ))}
        </View>

        {/* Accessibility */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accessibility</Text>
          
          <TouchableOpacity
            style={styles.toggleItem}
            onPress={() => handleToggle('compactMode')}
          >
            <View style={styles.toggleContent}>
              <Text style={styles.toggleTitle}>Compact Mode</Text>
              <Text style={styles.toggleDescription}>Reduce spacing for more content</Text>
            </View>
            <View style={[
              styles.toggle,
              settings.compactMode && styles.toggleActive
            ]}>
              {settings.compactMode && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.toggleItem}
            onPress={() => handleToggle('highContrast')}
          >
            <View style={styles.toggleContent}>
              <Text style={styles.toggleTitle}>High Contrast</Text>
              <Text style={styles.toggleDescription}>Increase contrast for better visibility</Text>
            </View>
            <View style={[
              styles.toggle,
              settings.highContrast && styles.toggleActive
            ]}>
              {settings.highContrast && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.toggleItem}
            onPress={() => handleToggle('reduceMotion')}
          >
            <View style={styles.toggleContent}>
              <Text style={styles.toggleTitle}>Reduce Motion</Text>
              <Text style={styles.toggleDescription}>Minimize animations and transitions</Text>
            </View>
            <View style={[
              styles.toggle,
              settings.reduceMotion && styles.toggleActive
            ]}>
              {settings.reduceMotion && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
          </TouchableOpacity>
        </View>

        {/* Preview */}
        <View style={styles.previewSection}>
          <Text style={styles.sectionTitle}>Preview</Text>
          <View style={[
            styles.previewCard,
            { borderColor: settings.accentColor }
          ]}>
            <View style={styles.previewHeader}>
              <View style={[
                styles.previewIcon,
                { backgroundColor: settings.accentColor }
              ]}>
                <Ionicons name="business" size={20} color="white" />
              </View>
              <Text style={styles.previewTitle}>Mining Operations</Text>
            </View>
            <Text style={styles.previewText}>
              This is how your app will look with the selected theme settings.
            </Text>
          </View>
        </View>

        {/* Info */}
        <View style={styles.infoSection}>
          <Ionicons name="information-circle" size={20} color={Colors.info} />
          <Text style={styles.infoText}>
            Theme changes may require restarting the app to take full effect. 
            Some settings help improve accessibility and readability.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// Static styles that don't depend on theme
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  resetButton: {
    padding: Layout.spacing.xs,
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  section: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.md,
    marginTop: Layout.spacing.lg,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    paddingHorizontal: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    marginBottom: Layout.spacing.sm,
  },
  selectedOption: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary + '10',
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.xs,
  },
  selectedText: {
    color: Colors.primary,
  },
  optionDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Layout.spacing.md,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: Colors.background,
    shadowColor: Colors.textPrimary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  toggleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  toggleContent: {
    flex: 1,
  },
  toggleTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.xs,
  },
  toggleDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  toggle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleActive: {
    backgroundColor: Colors.primary,
  },
  previewSection: {
    marginBottom: Layout.spacing.xl,
  },
  previewCard: {
    backgroundColor: Colors.surface,
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 2,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  previewIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.sm,
  },
  previewTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  previewText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    lineHeight: 20,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.info + '20',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.xl,
  },
  infoText: {
    flex: 1,
    fontSize: Layout.fontSize.sm,
    color: Colors.info,
    marginLeft: Layout.spacing.sm,
    lineHeight: 20,
  },
  loadingText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
  },
});

export default ThemeSettingsScreen;
