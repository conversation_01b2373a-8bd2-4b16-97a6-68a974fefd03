-- Sample SQL queries for production metrics analysis
-- These queries demonstrate common operations and reporting needs

-- =====================================================
-- BASIC QUERIES
-- =====================================================

-- Get all production metrics for a specific month
SELECT 
    date,
    monthly,
    week,
    actual_ob,
    plan_ob,
    (actual_ob / NULLIF(plan_ob, 0) * 100) as ob_achievement_pct,
    actual_ore,
    plan_ore,
    (actual_ore / NULLIF(plan_ore, 0) * 100) as ore_achievement_pct,
    actual_fuel,
    plan_fuel
FROM daily_production_metrics 
WHERE monthly = 'January 2024'
ORDER BY date;

-- Get production summary by week
SELECT 
    week,
    COUNT(*) as days_reported,
    SUM(actual_ob) as total_actual_ob,
    SUM(plan_ob) as total_plan_ob,
    ROUND(SUM(actual_ob) / NULLIF(SUM(plan_ob), 0) * 100, 2) as ob_achievement_pct,
    SUM(actual_ore) as total_actual_ore,
    SUM(plan_ore) as total_plan_ore,
    ROUND(SUM(actual_ore) / NULLIF(SUM(plan_ore), 0) * 100, 2) as ore_achievement_pct
FROM daily_production_metrics 
WHERE date >= '2024-01-01' AND date <= '2024-01-31'
GROUP BY week
ORDER BY week;

-- =====================================================
-- PERFORMANCE ANALYSIS
-- =====================================================

-- Daily achievement percentages with variance analysis
SELECT 
    date,
    monthly,
    ROUND(actual_ob / NULLIF(plan_ob, 0) * 100, 2) as ob_achievement,
    ROUND(actual_ore / NULLIF(plan_ore, 0) * 100, 2) as ore_achievement,
    CASE 
        WHEN actual_ob >= plan_ob THEN 'Target Met'
        WHEN actual_ob >= plan_ob * 0.9 THEN 'Near Target'
        ELSE 'Below Target'
    END as ob_performance,
    CASE 
        WHEN actual_ore >= plan_ore THEN 'Target Met'
        WHEN actual_ore >= plan_ore * 0.9 THEN 'Near Target'
        ELSE 'Below Target'
    END as ore_performance,
    actual_rain + actual_slippery as total_impact_hours
FROM daily_production_metrics 
WHERE date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY date DESC;

-- Best and worst performing days
WITH daily_performance AS (
    SELECT 
        date,
        monthly,
        ROUND((actual_ob + actual_ore) / NULLIF(plan_ob + plan_ore, 0) * 100, 2) as overall_achievement,
        actual_rain + actual_slippery as impact_hours
    FROM daily_production_metrics 
    WHERE date >= CURRENT_DATE - INTERVAL '90 days'
)
SELECT 
    'Best Performance' as category,
    date,
    monthly,
    overall_achievement,
    impact_hours
FROM daily_performance 
WHERE overall_achievement = (SELECT MAX(overall_achievement) FROM daily_performance)

UNION ALL

SELECT 
    'Worst Performance' as category,
    date,
    monthly,
    overall_achievement,
    impact_hours
FROM daily_performance 
WHERE overall_achievement = (SELECT MIN(overall_achievement) FROM daily_performance)
ORDER BY category, overall_achievement DESC;

-- =====================================================
-- TREND ANALYSIS
-- =====================================================

-- Monthly trends with moving averages
SELECT 
    monthly,
    COUNT(*) as reporting_days,
    ROUND(AVG(actual_ob), 2) as avg_daily_ob,
    ROUND(AVG(plan_ob), 2) as avg_planned_ob,
    ROUND(AVG(actual_ore), 2) as avg_daily_ore,
    ROUND(AVG(plan_ore), 2) as avg_planned_ore,
    ROUND(AVG(actual_fuel), 2) as avg_daily_fuel,
    ROUND(AVG(actual_rain + actual_slippery), 2) as avg_impact_hours,
    ROUND(AVG(actual_ob / NULLIF(plan_ob, 0) * 100), 2) as avg_ob_achievement,
    ROUND(AVG(actual_ore / NULLIF(plan_ore, 0) * 100), 2) as avg_ore_achievement
FROM daily_production_metrics 
GROUP BY monthly
ORDER BY 
    CASE 
        WHEN monthly LIKE '%2023' THEN 1
        WHEN monthly LIKE '%2024' THEN 2
        ELSE 3
    END,
    CASE 
        WHEN monthly LIKE 'January%' THEN 1
        WHEN monthly LIKE 'February%' THEN 2
        WHEN monthly LIKE 'March%' THEN 3
        WHEN monthly LIKE 'April%' THEN 4
        WHEN monthly LIKE 'May%' THEN 5
        WHEN monthly LIKE 'June%' THEN 6
        WHEN monthly LIKE 'July%' THEN 7
        WHEN monthly LIKE 'August%' THEN 8
        WHEN monthly LIKE 'September%' THEN 9
        WHEN monthly LIKE 'October%' THEN 10
        WHEN monthly LIKE 'November%' THEN 11
        WHEN monthly LIKE 'December%' THEN 12
    END;

-- Weekly performance with 4-week rolling average
WITH weekly_stats AS (
    SELECT 
        week,
        monthly,
        SUM(actual_ob) as week_actual_ob,
        SUM(plan_ob) as week_plan_ob,
        SUM(actual_ore) as week_actual_ore,
        SUM(plan_ore) as week_plan_ore,
        AVG(actual_rain + actual_slippery) as avg_impact_hours
    FROM daily_production_metrics 
    GROUP BY week, monthly
),
rolling_averages AS (
    SELECT 
        week,
        monthly,
        week_actual_ob,
        week_plan_ob,
        ROUND(week_actual_ob / NULLIF(week_plan_ob, 0) * 100, 2) as ob_achievement,
        AVG(week_actual_ob) OVER (ORDER BY week ROWS BETWEEN 3 PRECEDING AND CURRENT ROW) as rolling_avg_ob,
        AVG(week_actual_ore) OVER (ORDER BY week ROWS BETWEEN 3 PRECEDING AND CURRENT ROW) as rolling_avg_ore
    FROM weekly_stats
)
SELECT * FROM rolling_averages ORDER BY week;

-- =====================================================
-- IMPACT ANALYSIS
-- =====================================================

-- Weather impact correlation
SELECT 
    CASE 
        WHEN actual_rain + actual_slippery = 0 THEN 'No Impact'
        WHEN actual_rain + actual_slippery <= 2 THEN 'Low Impact'
        WHEN actual_rain + actual_slippery <= 4 THEN 'Medium Impact'
        ELSE 'High Impact'
    END as impact_category,
    COUNT(*) as days,
    ROUND(AVG(actual_ob / NULLIF(plan_ob, 0) * 100), 2) as avg_ob_achievement,
    ROUND(AVG(actual_ore / NULLIF(plan_ore, 0) * 100), 2) as avg_ore_achievement,
    ROUND(AVG(actual_fuel), 2) as avg_fuel_consumption
FROM daily_production_metrics 
WHERE date >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY 
    CASE 
        WHEN actual_rain + actual_slippery = 0 THEN 'No Impact'
        WHEN actual_rain + actual_slippery <= 2 THEN 'Low Impact'
        WHEN actual_rain + actual_slippery <= 4 THEN 'Medium Impact'
        ELSE 'High Impact'
    END
ORDER BY 
    CASE 
        WHEN impact_category = 'No Impact' THEN 1
        WHEN impact_category = 'Low Impact' THEN 2
        WHEN impact_category = 'Medium Impact' THEN 3
        ELSE 4
    END;

-- Fuel efficiency analysis
SELECT 
    monthly,
    ROUND(SUM(actual_ob + actual_ore) / NULLIF(SUM(actual_fuel), 0), 3) as tons_per_liter,
    ROUND(SUM(actual_fuel) / NULLIF(SUM(actual_ob + actual_ore), 0), 3) as liters_per_ton,
    SUM(actual_fuel) as total_fuel_consumed,
    SUM(actual_ob + actual_ore) as total_production
FROM daily_production_metrics 
GROUP BY monthly
ORDER BY tons_per_liter DESC;

-- =====================================================
-- DATA QUALITY CHECKS
-- =====================================================

-- Check for missing or unusual data
SELECT 
    'Missing OB Data' as issue_type,
    COUNT(*) as count
FROM daily_production_metrics 
WHERE actual_ob = 0 AND plan_ob > 0

UNION ALL

SELECT 
    'Missing ORE Data' as issue_type,
    COUNT(*) as count
FROM daily_production_metrics 
WHERE actual_ore = 0 AND plan_ore > 0

UNION ALL

SELECT 
    'Extreme OB Variance' as issue_type,
    COUNT(*) as count
FROM daily_production_metrics 
WHERE ABS(actual_ob - plan_ob) > plan_ob * 0.5 AND plan_ob > 0

UNION ALL

SELECT 
    'High Impact Hours' as issue_type,
    COUNT(*) as count
FROM daily_production_metrics 
WHERE actual_rain + actual_slippery > 8;

-- Recent data completeness
SELECT 
    date,
    CASE WHEN actual_ob > 0 THEN '✓' ELSE '✗' END as has_ob,
    CASE WHEN actual_ore > 0 THEN '✓' ELSE '✗' END as has_ore,
    CASE WHEN actual_fuel > 0 THEN '✓' ELSE '✗' END as has_fuel,
    CASE WHEN plan_ob > 0 THEN '✓' ELSE '✗' END as has_ob_plan,
    CASE WHEN plan_ore > 0 THEN '✓' ELSE '✗' END as has_ore_plan
FROM daily_production_metrics 
WHERE date >= CURRENT_DATE - INTERVAL '7 days'
ORDER BY date DESC;

-- =====================================================
-- REPORTING QUERIES
-- =====================================================

-- Executive summary for current month
WITH current_month AS (
    SELECT * FROM daily_production_metrics 
    WHERE monthly = TO_CHAR(CURRENT_DATE, 'Month YYYY')
),
summary_stats AS (
    SELECT 
        COUNT(*) as reporting_days,
        SUM(actual_ob) as total_ob,
        SUM(plan_ob) as planned_ob,
        SUM(actual_ore) as total_ore,
        SUM(plan_ore) as planned_ore,
        SUM(actual_fuel) as total_fuel,
        AVG(actual_rain + actual_slippery) as avg_impact_hours
    FROM current_month
)
SELECT 
    'Current Month Production Summary' as report_title,
    reporting_days,
    ROUND(total_ob, 0) as "Total OB (Bcm)",
    ROUND(planned_ob, 0) as "Planned OB (Bcm)",
    ROUND(total_ob / NULLIF(planned_ob, 0) * 100, 1) as "OB Achievement %",
    ROUND(total_ore, 0) as "Total Ore (tons)",
    ROUND(planned_ore, 0) as "Planned Ore (tons)",
    ROUND(total_ore / NULLIF(planned_ore, 0) * 100, 1) as "Ore Achievement %",
    ROUND(total_fuel, 0) as "Total Fuel (liters)",
    ROUND(avg_impact_hours, 1) as "Avg Impact Hours/Day"
FROM summary_stats;
