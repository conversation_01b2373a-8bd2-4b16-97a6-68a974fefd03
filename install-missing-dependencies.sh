#!/bin/bash

echo "🔧 Installing missing dependencies for Mining Operations App..."

# Core Expo dependencies
echo "📱 Installing Expo mobile dependencies..."
npm install expo-device expo-battery expo-location expo-sharing
npm install expo-image-picker expo-file-system
npm install expo-haptics expo-screen-orientation

# Additional dependencies for services
echo "🛠️ Installing service dependencies..."
npm install buffer react-native-get-random-values

# Development dependencies
echo "🧪 Installing development dependencies..."
npm install --save-dev @types/react-native

# Polyfills for React Native
echo "🔧 Installing polyfills..."
npm install react-native-polyfill-globals

echo "✅ All dependencies installed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Add polyfills to your App.tsx"
echo "2. Update metro.config.js if needed"
echo "3. Run 'npx expo install --fix' to ensure compatibility"
