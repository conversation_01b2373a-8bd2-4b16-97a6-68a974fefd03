/**
 * Production Domain Models
 * Pure data models without any business logic or external dependencies
 */

export interface ProductionMetric {
  id: string;
  date: string;
  monthly: string;
  week: number;
  locationId: string;
  actualOB: number;
  planOB: number;
  actualOre: number;
  planOre: number;
  actualFuel: number;
  planFuel: number;
  rainImpactHours: number;
  slipperyConditionsHours: number;
  achievementPercentage: number;
  createdAt: string;
  updatedAt: string;
}

export interface AggregatedMetrics {
  period: TimePeriod;
  totalActualOB: number;
  totalPlanOB: number;
  totalActualOre: number;
  totalPlanOre: number;
  totalActualFuel: number;
  totalPlanFuel: number;
  averageAchievement: number;
  dataPoints: number;
}

export interface ChartDataPoint {
  date: string;
  label: string;
  actualOB: number;
  planOB: number;
  actualOre: number;
  planOre: number;
  stripRatio: number;
  targetStripRatio: number;
  fuelConsumption: number;
  fuelPlan: number;
}

export interface ProductionSummary {
  overburdenVolume: MetricSummary;
  oreVolume: MetricSummary;
  stripRatio: MetricSummary;
  fuelEfficiency: MetricSummary;
  rainImpact: MetricSummary;
  slipperyConditions: MetricSummary;
}

export interface MetricSummary {
  actual: number;
  target: number;
  unit: string;
  achievementPercentage: number;
  trend: 'up' | 'down' | 'stable';
  isHighlighted?: boolean;
}

export interface ChartConfiguration {
  type: AnalyticsTab;
  datasets: ChartDataset[];
  colors: string[];
  yAxisLabel: string;
  formatValue: (value: number) => string;
}

export interface ChartDataset {
  data: number[];
  color: (opacity?: number) => string;
  strokeWidth: number;
  label: string;
}

export interface ProductionTargets {
  overburden: number;
  ore: number;
  fuel: number;
  rainHours: number;
  stripRatio: number;
}

// Enums and Types
export type TimePeriod = 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
export type AnalyticsTab = 'Production' | 'Impact' | 'Fuel' | 'Strip Ratio';

// DTOs (Data Transfer Objects)
export interface GetProductionMetricsRequest {
  startDate: string;
  endDate: string;
  locationId: string;
  period: TimePeriod;
}

export interface GetProductionMetricsResponse {
  metrics: ProductionMetric[];
  aggregated: AggregatedMetrics;
  chartData: ChartDataPoint[];
  summary: ProductionSummary;
}

export interface CreateProductionMetricDto {
  date: string;
  monthly: string;
  week: number;
  locationId: string;
  actualOB: number;
  planOB: number;
  actualOre: number;
  planOre: number;
  actualFuel: number;
  planFuel: number;
  rainImpactHours?: number;
  slipperyConditionsHours?: number;
}

export interface UpdateProductionMetricDto {
  id: string;
  actualOB?: number;
  planOB?: number;
  actualOre?: number;
  planOre?: number;
  actualFuel?: number;
  planFuel?: number;
  rainImpactHours?: number;
  slipperyConditionsHours?: number;
}

// Error Types
export class ProductionError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ProductionError';
  }
}

export class ValidationError extends ProductionError {
  constructor(message: string, public field: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class DataAccessError extends ProductionError {
  constructor(message: string, public operation: string, details?: any) {
    super(message, 'DATA_ACCESS_ERROR', details);
    this.name = 'DataAccessError';
  }
}

// Utility Types
export interface DateRange {
  startDate: string;
  endDate: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface SortOptions {
  field: keyof ProductionMetric;
  direction: 'asc' | 'desc';
}

export interface FilterOptions {
  locationId?: string;
  dateRange?: DateRange;
  minAchievement?: number;
  maxAchievement?: number;
}

// Constants
export const PRODUCTION_CONSTANTS = {
  DEFAULT_STRIP_RATIO: 2.5,
  MAX_RAIN_IMPACT_HOURS: 24,
  MIN_ACHIEVEMENT_PERCENTAGE: 0,
  MAX_ACHIEVEMENT_PERCENTAGE: 200,
  CHART_COLORS: {
    OVERBURDEN: '#5196F4',
    ORE: '#4CAF50',
    FUEL: '#9C27B0',
    STRIP_RATIO: '#FF9800',
    TARGET: '#757575',
  },
} as const;

// Type Guards
export const isProductionMetric = (obj: any): obj is ProductionMetric => {
  return (
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.date === 'string' &&
    typeof obj.actualOB === 'number' &&
    typeof obj.planOB === 'number'
  );
};

export const isValidTimePeriod = (period: string): period is TimePeriod => {
  return ['Daily', 'Weekly', 'Monthly', 'Yearly'].includes(period);
};

export const isValidAnalyticsTab = (tab: string): tab is AnalyticsTab => {
  return ['Production', 'Impact', 'Fuel', 'Strip Ratio'].includes(tab);
};
