-- =====================================================
-- Mining Operations Database - Migration 004
-- =====================================================
-- Migration: 004_sample_data.sql
-- Description: Sample data for testing and demonstration
-- Version: 1.0
-- Date: 2024-01-20
-- Dependencies: 003_security_performance.sql
-- =====================================================

-- Check if previous migration was applied
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM schema_migrations WHERE version = '003') THEN
        RAISE EXCEPTION 'Migration 003 must be applied before running migration 004';
    END IF;
END $$;

-- Record this migration start
INSERT INTO schema_migrations (version, description) 
VALUES ('004', 'Sample data for testing and demonstration')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- LOAD SAMPLE DATA
-- =====================================================

DO $$
DECLARE
    start_time TIMESTAMPTZ := NOW();
    end_time TIMESTAMPTZ;
    execution_time INTEGER;
BEGIN
    RAISE NOTICE 'Starting Migration 004: Sample Data Loading';
    RAISE NOTICE 'Start time: %', start_time;
    
    -- Load sample data
    RAISE NOTICE 'Executing 05-sample-data.sql...';
    \i ../05-sample-data.sql
    
    -- Calculate execution time
    end_time := NOW();
    execution_time := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    -- Update migration record
    UPDATE schema_migrations 
    SET 
        applied_at = end_time,
        checksum = md5(random()::text),
        execution_time_ms = execution_time
    WHERE version = '004';
    
    RAISE NOTICE 'Migration 004 completed successfully';
    RAISE NOTICE 'End time: %', end_time;
    RAISE NOTICE 'Execution time: % ms', execution_time;
END $$;

-- =====================================================
-- ADDITIONAL SAMPLE DATA FOR IUT/OTT
-- =====================================================

-- Sample IUT Inspections
INSERT INTO iut_inspections (
    inspector_id, iut_certificate_id, inspection_date, inspection_time,
    location, equipment_id, inspection_type, inspection_areas,
    total_items_checked, compliant_items, non_compliant_items, critical_findings,
    findings_summary, recommendations, status
) VALUES
-- IUT Inspection 1
((SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM mining_certifications WHERE certificate_type = 'IUT' LIMIT 1),
 CURRENT_DATE - 1, '09:00:00', 'Pit A',
 (SELECT id FROM equipment WHERE equipment_number = 'EX001'),
 'Routine', ARRAY['Equipment Condition', 'Safety Procedures', 'Work Area'],
 25, 22, 3, 1,
 'Overall good condition with minor safety procedure violations',
 'Improve PPE compliance and update safety signage',
 'Completed'),

-- IUT Inspection 2
((SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM mining_certifications WHERE certificate_type = 'IUT' LIMIT 1),
 CURRENT_DATE - 3, '14:30:00', 'Pit B',
 (SELECT id FROM equipment WHERE equipment_number = 'DT001'),
 'Follow-up', ARRAY['Equipment Maintenance', 'Operator Compliance'],
 15, 15, 0, 0,
 'All previous issues have been resolved satisfactorily',
 'Continue current maintenance schedule',
 'Completed');

-- Sample OTT Observations
INSERT INTO ott_observations (
    observer_id, ott_certificate_id, observation_date, observation_time,
    location, work_area, observed_person_id, observed_task,
    task_duration_minutes, behavioral_categories,
    safe_behaviors_count, unsafe_behaviors_count, total_behaviors_observed,
    positive_observations, areas_for_improvement, feedback_provided,
    feedback_method, feedback_summary, status
) VALUES
-- OTT Observation 1
((SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM mining_certifications WHERE certificate_type = 'OTT' LIMIT 1),
 CURRENT_DATE - 1, '10:15:00', 'Pit A', 'Loading Area',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 'Loading material into dump truck', 45,
 ARRAY['PPE Usage', 'Equipment Operation', 'Communication'],
 8, 2, 10,
 'Excellent equipment operation skills and good communication with spotter',
 'Need to improve hard hat positioning and use of high-visibility vest',
 true, 'Verbal',
 'Provided immediate feedback on PPE usage. Worker was receptive and made corrections.',
 'Completed'),

-- OTT Observation 2
((SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 (SELECT id FROM mining_certifications WHERE certificate_type = 'OTT' LIMIT 1),
 CURRENT_DATE - 2, '15:45:00', 'Pit B', 'Maintenance Area',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 'Routine equipment maintenance', 60,
 ARRAY['Tool Usage', 'Safety Procedures', 'Lockout/Tagout'],
 12, 0, 12,
 'Perfect adherence to lockout/tagout procedures and proper tool usage',
 'No areas for improvement identified',
 true, 'Written',
 'Commended worker for excellent safety practices. Used as positive example for team.',
 'Completed');

-- =====================================================
-- UPDATE SAFETY METRICS WITH IUT/OTT DATA
-- =====================================================

-- Update safety metrics to include IUT/OTT activities
UPDATE safety_metrics SET
    iut_inspections_completed = (
        SELECT COUNT(*) FROM iut_inspections 
        WHERE location = safety_metrics.location 
        AND inspection_date = safety_metrics.metric_date
    ),
    iut_average_compliance_rate = (
        SELECT COALESCE(AVG(overall_compliance_rate), 0) FROM iut_inspections 
        WHERE location = safety_metrics.location 
        AND inspection_date = safety_metrics.metric_date
    ),
    iut_critical_findings = (
        SELECT COALESCE(SUM(critical_findings), 0) FROM iut_inspections 
        WHERE location = safety_metrics.location 
        AND inspection_date = safety_metrics.metric_date
    ),
    ott_observations_completed = (
        SELECT COUNT(*) FROM ott_observations 
        WHERE location = safety_metrics.location 
        AND observation_date = safety_metrics.metric_date
    ),
    ott_average_safety_rate = (
        SELECT COALESCE(AVG(safety_compliance_rate), 0) FROM ott_observations 
        WHERE location = safety_metrics.location 
        AND observation_date = safety_metrics.metric_date
    ),
    ott_unsafe_behaviors_identified = (
        SELECT COALESCE(SUM(unsafe_behaviors_count), 0) FROM ott_observations 
        WHERE location = safety_metrics.location 
        AND observation_date = safety_metrics.metric_date
    );

-- =====================================================
-- CREATE SAMPLE TRAINING RECORDS FOR IUT/OTT
-- =====================================================

-- Sample IUT Training
INSERT INTO safety_training (
    training_name, training_type, duration_hours, scheduled_date,
    location, max_attendees, trainer_id, certification_required,
    description, created_by
) VALUES
('IUT - Inspeksi Umum Terencana Training', 'IUT Training', 8.0, CURRENT_DATE + 7,
 'Training Center', 15, 
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'), true,
 'Comprehensive training on planned general inspection procedures, checklist usage, and compliance assessment',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Sample OTT Training
INSERT INTO safety_training (
    training_name, training_type, duration_hours, scheduled_date,
    location, max_attendees, trainer_id, certification_required,
    description, created_by
) VALUES
('OTT - Observasi Tugas Terencana Training', 'OTT Training', 6.0, CURRENT_DATE + 14,
 'Training Center', 12,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'), true,
 'Training on planned task observation techniques, behavioral assessment, and effective feedback delivery',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Register attendees for IUT training
INSERT INTO safety_training_attendees (
    training_id, attendee_id, registered_at, status
) VALUES
((SELECT id FROM safety_training WHERE training_name LIKE 'IUT -%'),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 NOW(), 'Registered'),
((SELECT id FROM safety_training WHERE training_name LIKE 'IUT -%'),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 NOW(), 'Registered');

-- Register attendees for OTT training
INSERT INTO safety_training_attendees (
    training_id, attendee_id, registered_at, status
) VALUES
((SELECT id FROM safety_training WHERE training_name LIKE 'OTT -%'),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 NOW(), 'Registered'),
((SELECT id FROM safety_training WHERE training_name LIKE 'OTT -%'),
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 NOW(), 'Registered');

-- =====================================================
-- CREATE SAMPLE CERTIFICATE RENEWALS FOR IUT/OTT
-- =====================================================

-- Sample IUT certificate renewal
INSERT INTO certificate_renewals (
    certificate_id, renewal_type, renewal_initiated_date, renewal_due_date,
    training_required, training_completed, competency_test_required,
    status, notes, created_by
) VALUES
((SELECT id FROM mining_certifications WHERE certificate_type = 'IUT' LIMIT 1),
 'Regular', CURRENT_DATE - 30, CURRENT_DATE + 60,
 true, false, true,
 'In Progress', 
 'Annual IUT certification renewal. Training scheduled for next month.',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- =====================================================
-- POPULATE PRODUCTION CALENDAR FOR CURRENT YEAR
-- =====================================================

-- Ensure production calendar is populated
SELECT populate_production_calendar(EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER);

-- Add some holidays
UPDATE production_calendar SET 
    is_holiday = true,
    holiday_name = 'New Year Day'
WHERE EXTRACT(MONTH FROM calendar_date) = 1 
AND EXTRACT(DAY FROM calendar_date) = 1;

UPDATE production_calendar SET 
    is_holiday = true,
    holiday_name = 'Independence Day'
WHERE EXTRACT(MONTH FROM calendar_date) = 8 
AND EXTRACT(DAY FROM calendar_date) = 17;

-- =====================================================
-- REFRESH MATERIALIZED VIEWS
-- =====================================================

-- Refresh all materialized views with new data
SELECT refresh_all_materialized_views();

-- Update table statistics
SELECT analyze_all_tables();

-- =====================================================
-- VERIFICATION AND SUMMARY
-- =====================================================

DO $$
DECLARE
    daily_reports_count INTEGER;
    equipment_count INTEGER;
    safety_incidents_count INTEGER;
    certifications_count INTEGER;
    iut_inspections_count INTEGER;
    ott_observations_count INTEGER;
    training_count INTEGER;
    users_count INTEGER;
BEGIN
    -- Count all sample data
    SELECT COUNT(*) INTO daily_reports_count FROM daily_mining_report;
    SELECT COUNT(*) INTO equipment_count FROM equipment;
    SELECT COUNT(*) INTO safety_incidents_count FROM safety_incidents;
    SELECT COUNT(*) INTO certifications_count FROM mining_certifications;
    SELECT COUNT(*) INTO iut_inspections_count FROM iut_inspections;
    SELECT COUNT(*) INTO ott_observations_count FROM ott_observations;
    SELECT COUNT(*) INTO training_count FROM safety_training;
    SELECT COUNT(*) INTO users_count FROM user_profiles;
    
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MIGRATION 004 VERIFICATION - SAMPLE DATA LOADED';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Daily mining reports: %', daily_reports_count;
    RAISE NOTICE 'Equipment records: %', equipment_count;
    RAISE NOTICE 'Safety incidents: %', safety_incidents_count;
    RAISE NOTICE 'Mining certifications: %', certifications_count;
    RAISE NOTICE 'IUT inspections: %', iut_inspections_count;
    RAISE NOTICE 'OTT observations: %', ott_observations_count;
    RAISE NOTICE 'Training sessions: %', training_count;
    RAISE NOTICE 'User profiles: %', users_count;
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Sample data includes:';
    RAISE NOTICE '- Production data for multiple locations';
    RAISE NOTICE '- Equipment with maintenance records';
    RAISE NOTICE '- Safety incidents and training programs';
    RAISE NOTICE '- Greencard, IUT, and OTT certifications';
    RAISE NOTICE '- IUT inspections and OTT observations';
    RAISE NOTICE '- User accounts with different roles';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Database is ready for testing and demonstration!';
    RAISE NOTICE '================================================';
END $$;

-- =====================================================
-- SAMPLE QUERIES FOR TESTING
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'SAMPLE QUERIES TO TEST THE DATABASE';
    RAISE NOTICE '================================================';
    RAISE NOTICE '';
    RAISE NOTICE '1. View recent daily reports:';
    RAISE NOTICE '   SELECT location, report_date, total_actual_material, total_achievement_percent';
    RAISE NOTICE '   FROM daily_mining_report ORDER BY report_date DESC LIMIT 5;';
    RAISE NOTICE '';
    RAISE NOTICE '2. View IUT inspection summary:';
    RAISE NOTICE '   SELECT * FROM v_iut_inspection_summary LIMIT 5;';
    RAISE NOTICE '';
    RAISE NOTICE '3. View OTT observation summary:';
    RAISE NOTICE '   SELECT * FROM v_ott_observation_summary LIMIT 5;';
    RAISE NOTICE '';
    RAISE NOTICE '4. View certificate dashboard:';
    RAISE NOTICE '   SELECT * FROM v_certificate_dashboard WHERE days_remaining <= 90;';
    RAISE NOTICE '';
    RAISE NOTICE '5. View IUT/OTT combined dashboard:';
    RAISE NOTICE '   SELECT * FROM v_iut_ott_dashboard LIMIT 10;';
    RAISE NOTICE '';
    RAISE NOTICE '6. Get IUT compliance trend:';
    RAISE NOTICE '   SELECT * FROM get_iut_compliance_trend(''Pit A'', 30);';
    RAISE NOTICE '';
    RAISE NOTICE '7. Get OTT safety trend:';
    RAISE NOTICE '   SELECT * FROM get_ott_safety_trend(''Pit A'', 30);';
    RAISE NOTICE '';
END $$;
