import { Platform } from 'react-native';

interface ShadowConfig {
  shadowColor?: string;
  shadowOffset?: {
    width: number;
    height: number;
  };
  shadowOpacity?: number;
  shadowRadius?: number;
  elevation?: number;
}

interface WebShadowConfig {
  boxShadow?: string;
  elevation?: number;
}

/**
 * Creates platform-specific shadow styles
 * For iOS/Android: uses shadowColor, shadowOffset, shadowOpacity, shadowRadius, elevation
 * For Web: uses boxShadow and elevation
 */
export const createShadow = (config: ShadowConfig): ShadowConfig | WebShadowConfig => {
  if (Platform.OS === 'web') {
    const {
      shadowColor = '#000',
      shadowOffset = { width: 0, height: 2 },
      shadowOpacity = 0.1,
      shadowRadius = 3.84,
      elevation = 5,
    } = config;

    // Convert React Native shadow to CSS box-shadow
    const offsetX = shadowOffset.width;
    const offsetY = shadowOffset.height;
    const blur = shadowRadius;
    const color = `rgba(0, 0, 0, ${shadowOpacity})`;
    
    return {
      boxShadow: `${offsetX}px ${offsetY}px ${blur}px ${color}`,
      elevation,
    };
  }

  // Return original config for iOS/Android
  return config;
};

/**
 * Predefined shadow presets
 */
export const ShadowPresets = {
  small: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  }),
  
  medium: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  }),
  
  large: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8,
  }),
  
  card: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 4,
  }),
  
  button: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  }),
};
