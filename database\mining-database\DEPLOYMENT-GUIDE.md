# 🚀 Mining Operations Database - Deployment Guide

Panduan lengkap untuk deployment database Mining Operations dengan semua modul lengkap.

## 📋 **Overview Modul**

Database ini terdiri dari 10 modul utama:

1. **Core Setup** - Extensions, types, functions
2. **Production Calendar** - Calendar dan targets
3. **Daily Mining Report** - Tabel utama produksi
4. **Analytical Views** - Views untuk analisis
5. **User Management** - Authentication & authorization
6. **Equipment Management** - Equipment tracking & maintenance
7. **Safety Management** - Safety incidents, training, compliance
8. **Row Level Security** - Data access control
9. **Performance Indexes** - Database optimization
10. **Sample Data** - Data untuk testing

## 🎯 **Prerequisites**

### **Software Requirements**
- PostgreSQL 14+ atau Supabase account
- psql client atau database management tool
- Git (untuk clone repository)

### **Database Requirements**
- Database dengan privileges untuk CREATE TABLE, INDEX, FUNCTION
- Extensions: uuid-ossp, pg_stat_statements
- Minimum 1GB storage untuk production data

## 🚀 **Deployment Methods**

### **Method 1: Complete Deployment (Recommended)**

```bash
# 1. Connect to database
psql "postgresql://username:password@host:port/database"

# 2. Run complete deployment
\i deploy.sql
```

### **Method 2: Step-by-Step Deployment**

```bash
# Execute in exact order
\i 01-core-setup.sql
\i 02-production-calendar.sql
\i 03-daily-mining-report.sql
\i 04-analytical-views.sql
\i 06-user-management.sql
\i 07-equipment-management.sql
\i 08-safety-management.sql
\i 09-rls-policies.sql
\i 10-indexes-performance.sql
\i 05-sample-data.sql  # Optional
```

### **Method 3: Supabase Dashboard**

1. Login ke Supabase Dashboard
2. Go to SQL Editor
3. Copy-paste content dari `deploy.sql`
4. Execute query

## ✅ **Post-Deployment Verification**

### **1. Check Migration Status**
```sql
SELECT * FROM schema_migrations ORDER BY version;
```

### **2. Verify Tables**
```sql
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'public';
-- Expected: 17+ tables
```

### **3. Check RLS Status**
```sql
SELECT * FROM v_rls_monitoring;
-- All critical tables should have RLS enabled
```

### **4. Test Sample Data**
```sql
-- Check daily reports
SELECT COUNT(*) FROM daily_mining_report;

-- Check users
SELECT COUNT(*) FROM user_profiles;

-- Check equipment
SELECT COUNT(*) FROM equipment;

-- Check safety incidents
SELECT COUNT(*) FROM safety_incidents;
```

### **5. Performance Check**
```sql
-- Check indexes
SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public';
-- Expected: 50+ indexes

-- Check materialized views
SELECT COUNT(*) FROM pg_matviews WHERE schemaname = 'public';
-- Expected: 3+ materialized views
```

## 🔧 **Configuration**

### **1. Populate Calendar**
```sql
-- Populate calendar for current year
SELECT populate_production_calendar(2024);

-- Add holidays
UPDATE production_calendar SET 
    is_holiday = true, 
    holiday_name = 'New Year Day'
WHERE calendar_date = '2024-01-01';
```

### **2. Create Production Targets**
```sql
INSERT INTO production_targets_calendar (
    location, period_type, period_start_date, period_end_date,
    year_number, month_number, target_ob, target_ore, 
    target_sr, target_fr, created_by
) VALUES (
    'Your Location', 'Monthly', '2024-01-01', '2024-01-31',
    2024, 1, 45000, 18000, 2.50, 0.85,
    (SELECT id FROM user_profiles WHERE email = '<EMAIL>')
);
```

### **3. Setup Users**
```sql
-- Update sample users with your data
UPDATE user_profiles SET 
    email = '<EMAIL>',
    first_name = 'Your',
    last_name = 'Name'
WHERE email = '<EMAIL>';
```

### **4. Configure App Settings**
```sql
UPDATE app_settings SET setting_value = '"Your Mining Company"' 
WHERE setting_key = 'app.name';

UPDATE app_settings SET setting_value = '1.35' 
WHERE setting_key = 'fuel.default_price_per_liter';
```

## 📊 **Testing & Validation**

### **1. Insert Test Daily Report**
```sql
INSERT INTO daily_mining_report (
    location, report_date, 
    shift_1_ob, shift_2_ob, plan_ob,
    shift_1_ore, shift_2_ore, plan_ore,
    fuel_actual, fuel_plan, plan_fr, plan_sr,
    reported_by, status
) VALUES (
    'Test Location', CURRENT_DATE,
    850.5, 920.3, 1800.0,
    450.2, 380.7, 800.0,
    1250.5, 1360.0, 0.85, 2.50,
    (SELECT id FROM user_profiles LIMIT 1),
    'Draft'
);
```

### **2. Test Analytical Views**
```sql
-- Test weekly summary
SELECT * FROM v_weekly_production_summary 
WHERE year_number = 2024 LIMIT 5;

-- Test monthly summary
SELECT * FROM v_monthly_production_summary 
WHERE year_number = 2024 LIMIT 5;

-- Test dashboard
SELECT * FROM v_production_dashboard 
WHERE report_date >= CURRENT_DATE - 7;
```

### **3. Test Equipment Management**
```sql
-- Insert test equipment
INSERT INTO equipment (
    equipment_number, name, type, current_location, 
    status, created_by
) VALUES (
    'TEST001', 'Test Excavator', 'Excavator', 'Test Location',
    'Active', (SELECT id FROM user_profiles LIMIT 1)
);

-- Test equipment metrics
SELECT * FROM mv_equipment_utilization LIMIT 5;
```

### **4. Test Safety Management**
```sql
-- Insert test safety incident
INSERT INTO safety_incidents (
    incident_number, title, description, incident_type, 
    severity, location, occurred_at, reported_by
) VALUES (
    'TEST-001', 'Test Incident', 'Test safety incident for validation',
    'Near Miss', 'Low', 'Test Location', NOW(),
    (SELECT id FROM user_profiles LIMIT 1)
);

-- Test safety views
SELECT * FROM mv_safety_summary LIMIT 5;
```

## 🔒 **Security Setup**

### **1. Test RLS Policies**
```sql
-- Test as different user roles
SET ROLE authenticated;

-- Test data access
SELECT COUNT(*) FROM daily_mining_report;
SELECT COUNT(*) FROM equipment;
SELECT COUNT(*) FROM safety_incidents;
```

### **2. User Permissions**
```sql
-- Grant permissions to users
INSERT INTO user_permissions (user_id, permission, location) VALUES
((SELECT id FROM user_profiles WHERE email = '<EMAIL>'), 
 'reports.create', 'Your Location');
```

## 🛠️ **Maintenance Setup**

### **1. Schedule Regular Tasks**
```sql
-- Update scheduled jobs
UPDATE scheduled_jobs SET 
    next_run_at = NOW() + INTERVAL '1 day'
WHERE job_name = 'Refresh Materialized Views';
```

### **2. Performance Monitoring**
```sql
-- Check table sizes
SELECT * FROM v_table_sizes LIMIT 10;

-- Check index usage
SELECT * FROM v_index_usage WHERE usage_category = 'Unused';

-- Check slow queries
SELECT * FROM v_slow_queries LIMIT 10;
```

## 📱 **Mobile App Integration**

### **Supabase Configuration**
```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'your-supabase-url'
const supabaseKey = 'your-supabase-anon-key'

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})
```

### **Sample React Native Usage**
```javascript
// Insert daily report
const insertDailyReport = async (reportData) => {
  const { data, error } = await supabase
    .from('daily_mining_report')
    .insert([reportData])
  
  return { data, error }
}

// Get weekly summary
const getWeeklySummary = async (location, year, week) => {
  const { data, error } = await supabase
    .from('v_weekly_production_summary')
    .select('*')
    .eq('location', location)
    .eq('year_number', year)
    .eq('week_number', week)
  
  return { data, error }
}
```

## 🚨 **Troubleshooting**

### **Common Issues**

#### **1. Extension Not Found**
```sql
-- Install required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
```

#### **2. Permission Denied**
```sql
-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE your_database TO your_user;
GRANT ALL ON SCHEMA public TO your_user;
```

#### **3. RLS Blocking Queries**
```sql
-- Temporarily disable RLS for debugging
SELECT disable_rls_for_maintenance();
-- Remember to re-enable: SELECT enable_rls_after_maintenance();
```

#### **4. Slow Queries**
```sql
-- Refresh materialized views
SELECT refresh_all_materialized_views();

-- Update table statistics
SELECT analyze_all_tables();
```

## 📞 **Support**

### **Deployment Checklist**
- [ ] All 10 SQL files executed successfully
- [ ] 17+ tables created
- [ ] 50+ indexes created
- [ ] RLS enabled on all tables
- [ ] Sample data loaded (optional)
- [ ] Calendar populated for current year
- [ ] Production targets configured
- [ ] User accounts setup
- [ ] App settings configured
- [ ] Mobile app connected (if applicable)

### **Production Readiness**
- [ ] Backup strategy implemented
- [ ] Monitoring setup
- [ ] Performance baseline established
- [ ] Security policies tested
- [ ] User training completed
- [ ] Documentation updated

---

**🏭 Your Mining Operations Database is Ready for Production!**

This comprehensive database provides everything needed for modern mining operations management with production tracking, equipment monitoring, safety compliance, and advanced analytics.
