# Offline Support & Data Synchronization - Mining Operations App

## 🔄 **Offline Architecture Overview**

### **Why Offline Support is Critical**
Mining operations often occur in remote locations with poor or intermittent internet connectivity. The app must function fully offline and sync data when connectivity is restored.

### **Key Requirements**
- **Full offline functionality** for all core features
- **Automatic data synchronization** when online
- **Conflict resolution** for concurrent edits
- **Data integrity** during sync operations
- **User feedback** for sync status
- **Efficient storage** management
- **Background sync** capabilities

### **Architecture Components**
1. **Local SQLite Database** - Primary offline storage
2. **Sync Engine** - Handles data synchronization
3. **Conflict Resolution** - Manages data conflicts
4. **Network Monitor** - Tracks connectivity status
5. **Queue Manager** - Manages pending operations
6. **Cache Manager** - Optimizes data storage

## 📁 **File Structure**

```
src/services/offline/
├── database/
│   ├── DatabaseService.ts
│   ├── migrations/
│   │   ├── 001_initial_schema.sql
│   │   ├── 002_add_sync_tables.sql
│   │   └── 003_add_indexes.sql
│   └── schemas/
│       ├── production.schema.ts
│       ├── equipment.schema.ts
│       └── safety.schema.ts
├── sync/
│   ├── SyncEngine.ts
│   ├── ConflictResolver.ts
│   ├── SyncQueue.ts
│   └── SyncStrategies.ts
├── network/
│   ├── NetworkMonitor.ts
│   ├── NetworkService.ts
│   └── RetryManager.ts
├── cache/
│   ├── CacheManager.ts
│   ├── ImageCache.ts
│   └── DataCache.ts
└── storage/
    ├── SecureStorage.ts
    ├── FileStorage.ts
    └── BackupService.ts
```

## 🗄️ **Local Database Implementation**

### **Database Service**
```typescript
// src/services/offline/database/DatabaseService.ts
import * as SQLite from 'expo-sqlite';
import { Asset } from 'expo-asset';
import * as FileSystem from 'expo-file-system';

export interface DatabaseConfig {
  name: string;
  version: number;
  migrations: string[];
}

export class DatabaseService {
  private db: SQLite.WebSQLDatabase | null = null;
  private config: DatabaseConfig;
  private isInitialized = false;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing database...');
      
      // Open database
      this.db = SQLite.openDatabase(this.config.name);
      
      // Run migrations
      await this.runMigrations();
      
      // Verify database integrity
      await this.verifyDatabase();
      
      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }

  private async runMigrations(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not opened'));
        return;
      }

      // Get current version
      this.db.transaction(tx => {
        tx.executeSql(
          'CREATE TABLE IF NOT EXISTS schema_version (version INTEGER)',
          [],
          () => {
            tx.executeSql(
              'SELECT version FROM schema_version ORDER BY version DESC LIMIT 1',
              [],
              async (_, result) => {
                const currentVersion = result.rows.length > 0 ? result.rows.item(0).version : 0;
                
                try {
                  await this.applyMigrations(currentVersion);
                  resolve();
                } catch (error) {
                  reject(error);
                }
              },
              (_, error) => {
                // Table doesn't exist, start from version 0
                this.applyMigrations(0).then(resolve).catch(reject);
                return false;
              }
            );
          }
        );
      });
    });
  }

  private async applyMigrations(currentVersion: number): Promise<void> {
    const migrationsToRun = this.config.migrations.slice(currentVersion);
    
    for (let i = 0; i < migrationsToRun.length; i++) {
      const migrationPath = migrationsToRun[i];
      const migrationVersion = currentVersion + i + 1;
      
      console.log(`Applying migration ${migrationVersion}: ${migrationPath}`);
      
      try {
        // Load migration file
        const asset = Asset.fromModule(require(`./migrations/${migrationPath}`));
        await asset.downloadAsync();
        const migrationSQL = await FileSystem.readAsStringAsync(asset.localUri!);
        
        // Execute migration
        await this.executeMigration(migrationSQL, migrationVersion);
        
        console.log(`Migration ${migrationVersion} applied successfully`);
      } catch (error) {
        console.error(`Migration ${migrationVersion} failed:`, error);
        throw error;
      }
    }
  }

  private async executeMigration(sql: string, version: number): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not opened'));
        return;
      }

      this.db.transaction(
        tx => {
          // Split SQL into individual statements
          const statements = sql.split(';').filter(stmt => stmt.trim());
          
          statements.forEach(statement => {
            if (statement.trim()) {
              tx.executeSql(statement.trim());
            }
          });
          
          // Update schema version
          tx.executeSql(
            'INSERT INTO schema_version (version) VALUES (?)',
            [version]
          );
        },
        error => reject(error),
        () => resolve()
      );
    });
  }

  private async verifyDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not opened'));
        return;
      }

      // Verify critical tables exist
      const criticalTables = [
        'production_records',
        'equipment',
        'safety_incidents',
        'sync_queue',
        'sync_status'
      ];

      this.db.transaction(tx => {
        criticalTables.forEach(tableName => {
          tx.executeSql(
            `SELECT name FROM sqlite_master WHERE type='table' AND name=?`,
            [tableName],
            (_, result) => {
              if (result.rows.length === 0) {
                reject(new Error(`Critical table ${tableName} not found`));
              }
            },
            (_, error) => {
              reject(error);
              return false;
            }
          );
        });
      }, reject, resolve);
    });
  }

  // Generic query methods
  async query(sql: string, params: any[] = []): Promise<SQLite.SQLResultSet> {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      this.db!.transaction(tx => {
        tx.executeSql(
          sql,
          params,
          (_, result) => resolve(result),
          (_, error) => {
            reject(error);
            return false;
          }
        );
      });
    });
  }

  async insert(table: string, data: Record<string, any>): Promise<number> {
    const columns = Object.keys(data);
    const values = Object.values(data);
    const placeholders = columns.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;
    const result = await this.query(sql, values);
    
    return result.insertId!;
  }

  async update(
    table: string, 
    data: Record<string, any>, 
    where: string, 
    whereParams: any[] = []
  ): Promise<number> {
    const columns = Object.keys(data);
    const values = Object.values(data);
    const setClause = columns.map(col => `${col} = ?`).join(', ');
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;
    const result = await this.query(sql, [...values, ...whereParams]);
    
    return result.rowsAffected;
  }

  async delete(table: string, where: string, whereParams: any[] = []): Promise<number> {
    const sql = `DELETE FROM ${table} WHERE ${where}`;
    const result = await this.query(sql, whereParams);
    
    return result.rowsAffected;
  }

  async select(
    table: string,
    columns: string[] = ['*'],
    where?: string,
    whereParams: any[] = [],
    orderBy?: string,
    limit?: number
  ): Promise<any[]> {
    let sql = `SELECT ${columns.join(', ')} FROM ${table}`;
    
    if (where) {
      sql += ` WHERE ${where}`;
    }
    
    if (orderBy) {
      sql += ` ORDER BY ${orderBy}`;
    }
    
    if (limit) {
      sql += ` LIMIT ${limit}`;
    }
    
    const result = await this.query(sql, whereParams);
    
    const rows: any[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      rows.push(result.rows.item(i));
    }
    
    return rows;
  }

  // Transaction support
  async transaction(callback: (tx: SQLite.SQLTransaction) => void): Promise<void> {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      this.db!.transaction(
        callback,
        error => reject(error),
        () => resolve()
      );
    });
  }

  // Database maintenance
  async vacuum(): Promise<void> {
    await this.query('VACUUM');
  }

  async analyze(): Promise<void> {
    await this.query('ANALYZE');
  }

  async getSize(): Promise<number> {
    const result = await this.query('PRAGMA page_count');
    const pageCount = result.rows.item(0).page_count;
    
    const pageSizeResult = await this.query('PRAGMA page_size');
    const pageSize = pageSizeResult.rows.item(0).page_size;
    
    return pageCount * pageSize;
  }

  // Close database
  async close(): Promise<void> {
    if (this.db) {
      // SQLite in React Native doesn't have explicit close
      this.db = null;
      this.isInitialized = false;
    }
  }
}

// Database configuration
export const databaseConfig: DatabaseConfig = {
  name: 'mining_operations.db',
  version: 3,
  migrations: [
    '001_initial_schema.sql',
    '002_add_sync_tables.sql',
    '003_add_indexes.sql'
  ]
};

// Singleton instance
export const database = new DatabaseService(databaseConfig);
```

### **Migration Files**

#### **001_initial_schema.sql**
```sql
-- Initial database schema for Mining Operations App

-- Users and authentication
CREATE TABLE user_profiles (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  role TEXT NOT NULL,
  department TEXT,
  location_id TEXT,
  phone_number TEXT,
  profile_photo_url TEXT,
  employee_id TEXT UNIQUE,
  is_active INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);

-- Locations
CREATE TABLE locations (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  code TEXT UNIQUE NOT NULL,
  type TEXT NOT NULL,
  parent_id TEXT,
  latitude REAL,
  longitude REAL,
  is_active INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (parent_id) REFERENCES locations(id)
);

-- Shifts
CREATE TABLE shifts (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  location_id TEXT,
  start_time TEXT NOT NULL,
  end_time TEXT NOT NULL,
  is_active INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  FOREIGN KEY (location_id) REFERENCES locations(id)
);

-- Production records
CREATE TABLE production_records (
  id TEXT PRIMARY KEY,
  location_id TEXT NOT NULL,
  shift_id TEXT,
  material_type TEXT NOT NULL,
  quantity REAL NOT NULL,
  unit TEXT DEFAULT 'tons',
  quality_grade TEXT,
  recorded_at TEXT NOT NULL,
  recorded_by TEXT NOT NULL,
  equipment_id TEXT,
  notes TEXT,
  weather_conditions TEXT,
  latitude REAL,
  longitude REAL,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (location_id) REFERENCES locations(id),
  FOREIGN KEY (shift_id) REFERENCES shifts(id),
  FOREIGN KEY (recorded_by) REFERENCES user_profiles(id),
  FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

-- Equipment
CREATE TABLE equipment (
  id TEXT PRIMARY KEY,
  equipment_number TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  category TEXT NOT NULL,
  manufacturer TEXT,
  model TEXT,
  year_manufactured INTEGER,
  serial_number TEXT,
  current_location_id TEXT,
  status TEXT DEFAULT 'Active',
  fuel_type TEXT,
  fuel_capacity REAL,
  max_load_capacity REAL,
  operating_weight REAL,
  qr_code TEXT,
  photo_url TEXT,
  is_active INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (current_location_id) REFERENCES locations(id)
);

-- Equipment metrics
CREATE TABLE equipment_metrics (
  id TEXT PRIMARY KEY,
  equipment_id TEXT NOT NULL,
  recorded_date TEXT NOT NULL,
  shift_id TEXT,
  operating_hours REAL DEFAULT 0,
  planned_hours REAL DEFAULT 0,
  maintenance_hours REAL DEFAULT 0,
  downtime_hours REAL DEFAULT 0,
  fuel_consumed REAL DEFAULT 0,
  distance_traveled REAL DEFAULT 0,
  load_cycles INTEGER DEFAULT 0,
  operator_id TEXT,
  location_id TEXT,
  efficiency_score REAL,
  utilization_rate REAL,
  health_score REAL,
  recorded_by TEXT,
  created_at TEXT NOT NULL,
  FOREIGN KEY (equipment_id) REFERENCES equipment(id),
  FOREIGN KEY (shift_id) REFERENCES shifts(id),
  FOREIGN KEY (operator_id) REFERENCES user_profiles(id),
  FOREIGN KEY (location_id) REFERENCES locations(id),
  FOREIGN KEY (recorded_by) REFERENCES user_profiles(id),
  UNIQUE(equipment_id, recorded_date, shift_id)
);

-- Safety incidents
CREATE TABLE safety_incidents (
  id TEXT PRIMARY KEY,
  incident_number TEXT UNIQUE NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  incident_type TEXT NOT NULL,
  severity TEXT NOT NULL,
  location_id TEXT,
  exact_location TEXT,
  latitude REAL,
  longitude REAL,
  occurred_at TEXT NOT NULL,
  reported_at TEXT NOT NULL,
  reported_by TEXT NOT NULL,
  people_involved INTEGER DEFAULT 0,
  injuries_count INTEGER DEFAULT 0,
  fatalities_count INTEGER DEFAULT 0,
  equipment_involved TEXT,
  weather_conditions TEXT,
  immediate_actions TEXT,
  root_cause TEXT,
  corrective_actions TEXT,
  status TEXT DEFAULT 'Reported',
  assigned_investigator TEXT,
  investigation_deadline TEXT,
  closed_at TEXT,
  closed_by TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (location_id) REFERENCES locations(id),
  FOREIGN KEY (reported_by) REFERENCES user_profiles(id),
  FOREIGN KEY (equipment_involved) REFERENCES equipment(id),
  FOREIGN KEY (assigned_investigator) REFERENCES user_profiles(id),
  FOREIGN KEY (closed_by) REFERENCES user_profiles(id)
);

-- Maintenance schedules
CREATE TABLE maintenance_schedules (
  id TEXT PRIMARY KEY,
  equipment_id TEXT NOT NULL,
  maintenance_type TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  frequency_type TEXT NOT NULL,
  frequency_value INTEGER NOT NULL,
  last_completed_at TEXT,
  next_due_date TEXT NOT NULL,
  estimated_duration_hours REAL,
  priority TEXT DEFAULT 'Medium',
  assigned_technician TEXT,
  is_active INTEGER DEFAULT 1,
  created_by TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (equipment_id) REFERENCES equipment(id),
  FOREIGN KEY (assigned_technician) REFERENCES user_profiles(id),
  FOREIGN KEY (created_by) REFERENCES user_profiles(id)
);

-- Work orders
CREATE TABLE work_orders (
  id TEXT PRIMARY KEY,
  work_order_number TEXT UNIQUE NOT NULL,
  equipment_id TEXT NOT NULL,
  maintenance_schedule_id TEXT,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  work_type TEXT NOT NULL,
  priority TEXT DEFAULT 'Medium',
  status TEXT DEFAULT 'Open',
  requested_by TEXT NOT NULL,
  assigned_to TEXT,
  estimated_hours REAL,
  actual_hours REAL,
  estimated_cost REAL,
  actual_cost REAL,
  scheduled_start TEXT,
  scheduled_end TEXT,
  actual_start TEXT,
  actual_end TEXT,
  completion_notes TEXT,
  safety_requirements TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (equipment_id) REFERENCES equipment(id),
  FOREIGN KEY (maintenance_schedule_id) REFERENCES maintenance_schedules(id),
  FOREIGN KEY (requested_by) REFERENCES user_profiles(id),
  FOREIGN KEY (assigned_to) REFERENCES user_profiles(id)
);

-- Attachments
CREATE TABLE attachments (
  id TEXT PRIMARY KEY,
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  file_type TEXT,
  mime_type TEXT,
  uploaded_by TEXT,
  uploaded_at TEXT NOT NULL,
  is_active INTEGER DEFAULT 1,
  FOREIGN KEY (uploaded_by) REFERENCES user_profiles(id)
);
```

#### **002_add_sync_tables.sql**
```sql
-- Sync management tables

-- Sync queue for pending operations
CREATE TABLE sync_queue (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  operation TEXT NOT NULL, -- 'CREATE', 'UPDATE', 'DELETE'
  data TEXT NOT NULL, -- JSON data
  created_at TEXT NOT NULL,
  attempts INTEGER DEFAULT 0,
  last_attempt_at TEXT,
  last_error TEXT,
  is_synced INTEGER DEFAULT 0
);

-- Sync status tracking
CREATE TABLE sync_status (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  last_sync_at TEXT,
  last_sync_version INTEGER DEFAULT 0,
  sync_direction TEXT NOT NULL, -- 'UP', 'DOWN', 'BOTH'
  is_enabled INTEGER DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  UNIQUE(table_name, sync_direction)
);

-- Conflict resolution log
CREATE TABLE sync_conflicts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  local_data TEXT NOT NULL,
  remote_data TEXT NOT NULL,
  resolution_strategy TEXT, -- 'LOCAL_WINS', 'REMOTE_WINS', 'MERGE', 'MANUAL'
  resolved_data TEXT,
  resolved_at TEXT,
  resolved_by TEXT,
  created_at TEXT NOT NULL,
  FOREIGN KEY (resolved_by) REFERENCES user_profiles(id)
);

-- Sync metadata for change tracking
CREATE TABLE sync_metadata (
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  version INTEGER NOT NULL DEFAULT 1,
  last_modified_at TEXT NOT NULL,
  last_modified_by TEXT,
  is_deleted INTEGER DEFAULT 0,
  sync_hash TEXT,
  PRIMARY KEY (table_name, record_id),
  FOREIGN KEY (last_modified_by) REFERENCES user_profiles(id)
);
```

This completes the offline database implementation. The next section will cover the sync engine and conflict resolution mechanisms.
