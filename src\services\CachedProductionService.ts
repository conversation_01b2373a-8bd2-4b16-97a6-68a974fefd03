import AsyncStorage from '@react-native-async-storage/async-storage';
import { ProductionDataItem } from '../components/charts/ChartDataProcessor';
import { ProductionDataService, ProductionDataFilter, ProductionMetrics } from './ProductionDataService';
import { productionService, DashboardStats, ProductionOverview } from './productionService';
import { TimePeriod } from '../models/Production';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheConfig {
  historicalDataTTL: number; // 24 hours for historical data
  todayDataTTL: number; // 5 minutes for today's data
  dashboardStatsTTL: number; // 10 minutes for dashboard stats
  equipmentStatusTTL: number; // 30 minutes for equipment status
}

export class CachedProductionService {
  private static instance: CachedProductionService;
  private productionService: ProductionDataService;
  private cacheConfig: CacheConfig;

  private constructor() {
    this.productionService = ProductionDataService.getInstance();
    this.cacheConfig = {
      historicalDataTTL: 24 * 60 * 60 * 1000, // 24 hours
      todayDataTTL: 5 * 60 * 1000, // 5 minutes
      dashboardStatsTTL: 10 * 60 * 1000, // 10 minutes
      equipmentStatusTTL: 30 * 60 * 1000, // 30 minutes
    };
  }

  static getInstance(): CachedProductionService {
    if (!CachedProductionService.instance) {
      CachedProductionService.instance = new CachedProductionService();
    }
    return CachedProductionService.instance;
  }

  // Generic cache methods
  private async setCache<T>(key: string, data: T, ttl: number): Promise<void> {
    try {
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl
      };
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(cacheItem));
      console.log(`💾 Cached data for key: ${key} (TTL: ${ttl/1000/60}min)`);
    } catch (error) {
      console.error(`❌ Failed to cache data for key ${key}:`, error);
    }
  }

  private async getCache<T>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(`cache_${key}`);
      if (!cached) return null;

      const cacheItem: CacheItem<T> = JSON.parse(cached);
      const now = Date.now();
      
      // Check if cache is still valid
      if (now - cacheItem.timestamp > cacheItem.ttl) {
        console.log(`⏰ Cache expired for key: ${key}`);
        await AsyncStorage.removeItem(`cache_${key}`);
        return null;
      }

      console.log(`✅ Cache hit for key: ${key}`);
      return cacheItem.data;
    } catch (error) {
      console.error(`❌ Failed to get cache for key ${key}:`, error);
      return null;
    }
  }

  private async clearCache(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`cache_${key}`);
      console.log(`🗑️ Cleared cache for key: ${key}`);
    } catch (error) {
      console.error(`❌ Failed to clear cache for key ${key}:`, error);
    }
  }

  // Smart cache key generation
  private generateCacheKey(prefix: string, params: any): string {
    const paramString = JSON.stringify(params);
    // Use simple hash instead of Buffer for React Native compatibility
    const hash = this.simpleHash(paramString);
    return `${prefix}_${hash}`;
  }

  // Simple hash function for React Native compatibility
  private simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Determine TTL based on data characteristics
  private getTTLForDateRange(startDate?: string, endDate?: string): number {
    const today = new Date().toISOString().split('T')[0];
    
    // If requesting today's data, use short TTL
    if (!startDate || !endDate || endDate >= today) {
      return this.cacheConfig.todayDataTTL;
    }
    
    // Historical data can be cached longer
    return this.cacheConfig.historicalDataTTL;
  }

  // Cached production data methods
  async getProductionData(filter?: ProductionDataFilter): Promise<ProductionDataItem[]> {
    const cacheKey = this.generateCacheKey('production_data', filter || {});
    
    // Try to get from cache first
    const cached = await this.getCache<ProductionDataItem[]>(cacheKey);
    if (cached) {
      return cached;
    }

    // Cache miss - fetch from service
    console.log('🔄 Cache miss - fetching production data from database...');
    const data = await this.productionService.getProductionData(filter);
    
    // Cache the result with appropriate TTL
    const ttl = this.getTTLForDateRange(filter?.startDate, filter?.endDate);
    await this.setCache(cacheKey, data, ttl);
    
    return data;
  }

  async getProductionDataByDateRange(startDate: string, endDate: string): Promise<ProductionDataItem[]> {
    const cacheKey = this.generateCacheKey('production_date_range', { startDate, endDate });
    
    const cached = await this.getCache<ProductionDataItem[]>(cacheKey);
    if (cached) {
      return cached;
    }

    console.log(`🔄 Fetching production data for range: ${startDate} to ${endDate}`);
    const data = await this.productionService.getProductionDataByDateRange(startDate, endDate);
    
    const ttl = this.getTTLForDateRange(startDate, endDate);
    await this.setCache(cacheKey, data, ttl);
    
    return data;
  }

  async getProductionDataByPeriod(timePeriod: TimePeriod): Promise<ProductionDataItem[]> {
    const cacheKey = this.generateCacheKey('production_period', { timePeriod });
    
    const cached = await this.getCache<ProductionDataItem[]>(cacheKey);
    if (cached) {
      return cached;
    }

    console.log(`🔄 Fetching production data for period: ${timePeriod}`);
    const data = await this.productionService.getProductionDataByPeriod(timePeriod);
    
    // Use shorter TTL for current periods
    const ttl = timePeriod.includes('current') || timePeriod.includes('today') 
      ? this.cacheConfig.todayDataTTL 
      : this.cacheConfig.historicalDataTTL;
    
    await this.setCache(cacheKey, data, ttl);
    
    return data;
  }

  async getProductionMetrics(filter?: ProductionDataFilter): Promise<ProductionMetrics> {
    const cacheKey = this.generateCacheKey('production_metrics', filter || {});
    
    const cached = await this.getCache<ProductionMetrics>(cacheKey);
    if (cached) {
      return cached;
    }

    console.log('🔄 Calculating production metrics...');
    const metrics = await this.productionService.getProductionMetrics(filter);
    
    const ttl = this.getTTLForDateRange(filter?.startDate, filter?.endDate);
    await this.setCache(cacheKey, metrics, ttl);
    
    return metrics;
  }

  // Dashboard specific caching
  async getDashboardStats(): Promise<DashboardStats> {
    const cacheKey = 'dashboard_stats';

    const cached = await this.getCache<DashboardStats>(cacheKey);
    if (cached) {
      return cached;
    }

    console.log('🔄 Fetching dashboard stats...');
    const stats = await productionService.getDashboardStats();

    await this.setCache(cacheKey, stats, this.cacheConfig.dashboardStatsTTL);

    return stats;
  }

  async getProductionOverview(): Promise<ProductionOverview> {
    const cacheKey = 'production_overview';

    const cached = await this.getCache<ProductionOverview>(cacheKey);
    if (cached) {
      return cached;
    }

    console.log('🔄 Fetching production overview...');
    const overview = await productionService.getProductionOverview();

    await this.setCache(cacheKey, overview, this.cacheConfig.dashboardStatsTTL);

    return overview;
  }

  // Cache management methods
  async invalidateCache(pattern?: string): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      if (pattern) {
        const filteredKeys = cacheKeys.filter(key => key.includes(pattern));
        await AsyncStorage.multiRemove(filteredKeys);
        console.log(`🗑️ Invalidated ${filteredKeys.length} cache entries matching pattern: ${pattern}`);
      } else {
        await AsyncStorage.multiRemove(cacheKeys);
        console.log(`🗑️ Invalidated all ${cacheKeys.length} cache entries`);
      }
    } catch (error) {
      console.error('❌ Failed to invalidate cache:', error);
    }
  }

  async getCacheStats(): Promise<{ totalKeys: number; totalSize: number; keys: string[] }> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      let totalSize = 0;
      for (const key of cacheKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      }

      return {
        totalKeys: cacheKeys.length,
        totalSize,
        keys: cacheKeys
      };
    } catch (error) {
      console.error('❌ Failed to get cache stats:', error);
      return { totalKeys: 0, totalSize: 0, keys: [] };
    }
  }

  // Preload critical data
  async preloadCriticalData(): Promise<void> {
    console.log('🚀 Preloading critical data...');
    
    try {
      // Preload today's data
      const today = new Date().toISOString().split('T')[0];
      await this.getProductionDataByDateRange(today, today);
      
      // Preload current week data
      await this.getProductionDataByPeriod('current_week' as TimePeriod);
      
      // Preload dashboard stats
      await this.getDashboardStats();
      await this.getProductionOverview();
      
      console.log('✅ Critical data preloaded successfully');
    } catch (error) {
      console.error('❌ Failed to preload critical data:', error);
    }
  }
}

export default CachedProductionService;
