import React from 'react';
import { View, StyleSheet, StatusBar, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';

interface ThemedScreenProps {
  children: React.ReactNode;
  style?: ViewStyle;
  statusBarStyle?: 'auto' | 'light' | 'dark';
  backgroundColor?: string;
  edges?: ('top' | 'bottom' | 'left' | 'right')[];
}

const ThemedScreen: React.FC<ThemedScreenProps> = ({
  children,
  style,
  statusBarStyle = 'auto',
  backgroundColor,
  edges = ['top', 'bottom', 'left', 'right'],
}) => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();

  const getStatusBarStyle = () => {
    if (statusBarStyle === 'auto') {
      return isDarkMode ? 'light-content' : 'dark-content';
    }
    return statusBarStyle === 'light' ? 'light-content' : 'dark-content';
  };

  const screenStyle = {
    flex: 1,
    backgroundColor: backgroundColor || colors.background,
  };

  return (
    <SafeAreaView style={[screenStyle, style]} edges={edges}>
      <StatusBar
        barStyle={getStatusBarStyle()}
        backgroundColor="transparent"
        translucent
      />
      {children}
    </SafeAreaView>
  );
};

export default ThemedScreen;
