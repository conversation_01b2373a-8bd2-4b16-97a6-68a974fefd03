const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ohqbaimnhwvdfrmxvhxv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ocWJhaW1uaHd2ZGZybXh2aHh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4ODA3NzEsImV4cCI6MjA2ODQ1Njc3MX0.Qq-2pKIvW2SSJlgQqTW6I_gXdxt81oWv2wViadb9b-Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkSupabaseConnection() {
  console.log('🔍 Checking Supabase connection...');
  console.log(`📡 URL: ${supabaseUrl}`);

  try {
    // Test basic connection by trying to access a known table
    const { data, error } = await supabase
      .from('locations')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }

    console.log('✅ Supabase connection successful!');
    console.log(`📊 Found ${data || 0} locations in database`);

    return true;
  } catch (error) {
    console.error('❌ Connection error:', error.message);
    return false;
  }
}

async function checkTables() {
  console.log('\n🏗️ Checking specific tables...');
  
  const tables = [
    'locations',
    'users', 
    'equipment',
    'safety_incidents',
    'production_reports',
    'maintenance_records',
    'shifts',
    'user_shifts',
    'daily_production_metrics'
  ];

  for (const tableName of tables) {
    try {
      const { count, error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.log(`❌ Table '${tableName}': ${error.message}`);
      } else {
        console.log(`✅ Table '${tableName}': ${count || 0} records`);
      }
    } catch (err) {
      console.log(`❌ Table '${tableName}': ${err.message}`);
    }
  }
}

async function getSampleData() {
  console.log('\n📊 Getting sample data...');
  
  try {
    // Get locations
    const { data: locations, error: locError } = await supabase
      .from('locations')
      .select('id, name, location_type')
      .limit(5);

    if (!locError && locations) {
      console.log('\n🏢 Sample Locations:');
      locations.forEach(loc => {
        console.log(`  - ${loc.name} (${loc.location_type})`);
      });
    }

    // Get users
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('full_name, role, email')
      .limit(5);

    if (!userError && users) {
      console.log('\n👥 Sample Users:');
      users.forEach(user => {
        console.log(`  - ${user.full_name} (${user.role}) - ${user.email}`);
      });
    }

    // Get equipment
    const { data: equipment, error: equipError } = await supabase
      .from('equipment')
      .select('name, equipment_type, status, manufacturer')
      .limit(5);

    if (!equipError && equipment) {
      console.log('\n🚛 Sample Equipment:');
      equipment.forEach(eq => {
        console.log(`  - ${eq.name} (${eq.equipment_type}) - ${eq.status} - ${eq.manufacturer}`);
      });
    }

  } catch (error) {
    console.error('❌ Error getting sample data:', error.message);
  }
}

async function checkProductionData() {
  console.log('\n📈 Checking production data...');
  
  try {
    // Check daily production metrics
    const { data: metrics, error: metricsError } = await supabase
      .from('daily_production_metrics')
      .select('date, actual_ob, actual_ore, actual_fuel')
      .order('date', { ascending: false })
      .limit(5);

    if (!metricsError && metrics && metrics.length > 0) {
      console.log('\n📊 Recent Production Metrics:');
      metrics.forEach(metric => {
        const stripRatio = metric.actual_ore > 0 ? (metric.actual_ob / metric.actual_ore).toFixed(2) : '0.00';
        console.log(`  ${metric.date}: OB=${metric.actual_ob} Bcm, Ore=${metric.actual_ore} tons, Strip Ratio=${stripRatio}, Fuel=${metric.actual_fuel}L`);
      });
    } else if (metricsError) {
      console.log(`❌ Production metrics error: ${metricsError.message}`);
    } else {
      console.log('ℹ️ No production metrics data found');
    }

    // Check production reports
    const { data: reports, error: reportsError } = await supabase
      .from('production_reports')
      .select('report_number, report_date, shift, total_tonnage')
      .order('report_date', { ascending: false })
      .limit(3);

    if (!reportsError && reports && reports.length > 0) {
      console.log('\n📋 Recent Production Reports:');
      reports.forEach(report => {
        console.log(`  ${report.report_number} - ${report.report_date} (${report.shift}) - ${report.total_tonnage} tons`);
      });
    } else if (reportsError) {
      console.log(`❌ Production reports error: ${reportsError.message}`);
    } else {
      console.log('ℹ️ No production reports data found');
    }

  } catch (error) {
    console.error('❌ Error checking production data:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting Supabase Database Check...\n');
  
  try {
    const isConnected = await checkSupabaseConnection();
    if (!isConnected) {
      console.log('❌ Cannot proceed without database connection');
      process.exit(1);
    }

    await checkTables();
    await getSampleData();
    await checkProductionData();

    console.log('\n✅ Database check completed successfully!');
    console.log('\n🌐 Access your Supabase dashboard at:');
    console.log(`   https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv`);

  } catch (error) {
    console.error('\n❌ Database check failed:', error.message);
    process.exit(1);
  }
}

main().catch(console.error);
