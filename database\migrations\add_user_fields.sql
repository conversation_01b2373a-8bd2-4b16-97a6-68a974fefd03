-- Migration: Add NIK, Departemen, and Jabatan to users table
-- Date: 2025-01-15
-- Description: Add additional user profile fields for Indonesian employee data

-- Add new columns to users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS nik character varying(16) NULL,
ADD COLUMN IF NOT EXISTS departemen character varying(100) NULL,
ADD COLUMN IF NOT EXISTS jabatan character varying(100) NULL;

-- Add unique constraint for NIK (Indonesian National ID)
ALTER TABLE public.users 
ADD CONSTRAINT users_nik_key UNIQUE (nik);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_nik ON public.users USING btree (nik) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_users_departemen ON public.users USING btree (departemen) TABLESPACE pg_default;
CREATE INDEX IF NOT EXISTS idx_users_jabatan ON public.users USING btree (jabatan) TABLESPACE pg_default;

-- Add comments for documentation
COMMENT ON COLUMN public.users.nik IS 'Nomor Induk <PERSON> (Indonesian National ID Number)';
COMMENT ON COLUMN public.users.departemen IS 'Department/Division where the employee works';
COMMENT ON COLUMN public.users.jabatan IS 'Job title/position of the employee';

-- Update the updated_at trigger to include new columns (already exists, just for reference)
-- The existing trigger update_users_updated_at will automatically handle the new columns

-- Optional: Create enum types for common departments and positions (uncomment if needed)
/*
-- Create enum for common departments
CREATE TYPE public.department_type AS ENUM (
  'production',
  'maintenance',
  'safety',
  'administration',
  'finance',
  'human_resources',
  'logistics',
  'quality_control',
  'engineering',
  'security'
);

-- Create enum for common job positions
CREATE TYPE public.position_type AS ENUM (
  'manager',
  'supervisor',
  'operator',
  'technician',
  'engineer',
  'analyst',
  'coordinator',
  'specialist',
  'assistant',
  'director'
);

-- If you want to use enums instead of varchar, run these:
-- ALTER TABLE public.users ALTER COLUMN departemen TYPE public.department_type USING departemen::public.department_type;
-- ALTER TABLE public.users ALTER COLUMN jabatan TYPE public.position_type USING jabatan::public.position_type;
*/

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
  AND table_schema = 'public'
  AND column_name IN ('nik', 'departemen', 'jabatan')
ORDER BY ordinal_position;
