import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { supabase } from '../config/supabase';
import { Alert } from 'react-native';
import StorageSetupService from './StorageSetupService';

export interface ImagePickerResult {
  success: boolean;
  imageUri?: string;
  error?: string;
}

export interface UploadResult {
  success: boolean;
  publicUrl?: string;
  error?: string;
}

class ProfileImageService {
  private static instance: ProfileImageService;

  static getInstance(): ProfileImageService {
    if (!ProfileImageService.instance) {
      ProfileImageService.instance = new ProfileImageService();
    }
    return ProfileImageService.instance;
  }

  /**
   * Request camera and media library permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      // Request camera permission
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      
      // Request media library permission
      const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (cameraPermission.status !== 'granted' || mediaLibraryPermission.status !== 'granted') {
        Alert.alert(
          'Permissions Required',
          'Camera and photo library access are required to change your profile picture.',
          [{ text: 'OK' }]
        );
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Show image picker options (Camera or Gallery)
   */
  async showImagePickerOptions(): Promise<ImagePickerResult> {
    return new Promise((resolve) => {
      Alert.alert(
        'Select Profile Picture',
        'Choose how you want to select your profile picture',
        [
          {
            text: 'Camera',
            onPress: async () => {
              const result = await this.openCamera();
              resolve(result);
            },
          },
          {
            text: 'Gallery',
            onPress: async () => {
              const result = await this.openGallery();
              resolve(result);
            },
          },
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => resolve({ success: false }),
          },
        ]
      );
    });
  }

  /**
   * Open camera to take a photo
   */
  async openCamera(): Promise<ImagePickerResult> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return { success: false, error: 'Camera permission denied' };
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio
        quality: 0.8,
        base64: false,
      });

      if (result.canceled) {
        return { success: false, error: 'User cancelled camera' };
      }

      return {
        success: true,
        imageUri: result.assets[0].uri,
      };
    } catch (error) {
      console.error('Error opening camera:', error);
      return { success: false, error: 'Failed to open camera' };
    }
  }

  /**
   * Open gallery to select a photo
   */
  async openGallery(): Promise<ImagePickerResult> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return { success: false, error: 'Gallery permission denied' };
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio
        quality: 0.8,
        base64: false,
      });

      if (result.canceled) {
        return { success: false, error: 'User cancelled gallery selection' };
      }

      return {
        success: true,
        imageUri: result.assets[0].uri,
      };
    } catch (error) {
      console.error('Error opening gallery:', error);
      return { success: false, error: 'Failed to open gallery' };
    }
  }

  /**
   * Upload image to Supabase Storage - React Native Compatible Version
   */
  async uploadProfileImage(imageUri: string, userId: string, userName?: string): Promise<UploadResult> {
    try {
      // Read the image file
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!fileInfo.exists) {
        return { success: false, error: 'Image file not found' };
      }

      // Create folder name from user name (sanitized) or fallback to userId
      const folderName = userName
        ? userName.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 50)
        : userId;

      // Create filename from user name (sanitized) with timestamp for uniqueness
      const fileExtension = imageUri.split('.').pop() || 'jpg';
      const sanitizedUserName = userName
        ? userName.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 30)
        : `user_${userId.substring(0, 8)}`;

      const fileName = `${sanitizedUserName}_${Date.now()}.${fileExtension}`;
      const filePath = `${folderName}/${fileName}`;

      console.log('📸 Starting upload:', {
        fileName,
        filePath,
        imageUri,
        folderName,
        sanitizedUserName,
        originalUserName: userName
      });

      // Quick check if storage is accessible (don't try to create bucket)
      const storageSetupService = StorageSetupService.getInstance();
      const checkResult = await storageSetupService.checkStorageConfiguration();

      if (!checkResult.success) {
        console.error('❌ Storage not accessible:', checkResult.message);
        console.log('ℹ️ Available buckets:', checkResult.details?.availableBuckets);

        return {
          success: false,
          error: `Storage issue: ${checkResult.message}. Please check Supabase configuration.`
        };
      }

      console.log('✅ Storage is accessible:', checkResult.message);

      // Use the proven method from our successful tests: base64 to Uint8Array
      try {
        console.log('📤 Reading image as base64...');
        const base64 = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        console.log('🔄 Converting base64 to Uint8Array...');
        // Convert base64 to binary for React Native compatibility (proven method)
        const binaryString = atob(base64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        console.log('📤 Uploading image...', {
          fileName,
          filePath,
          bytesLength: bytes.length,
          contentType: `image/${fileExtension}`
        });

        const { data, error } = await supabase.storage
          .from('profile-photos')
          .upload(filePath, bytes, {
            contentType: `image/${fileExtension}`,
            cacheControl: '3600',
            upsert: true,
          });

        if (error) {
          console.error('❌ Upload failed:', error);
          return { success: false, error: error.message };
        }

        console.log('✅ Upload successful:', data);

        // Get public URL
        const { data: publicUrlData } = supabase.storage
          .from('profile-photos')
          .getPublicUrl(filePath);

        console.log('🔗 Public URL generated:', publicUrlData.publicUrl);

        return {
          success: true,
          publicUrl: publicUrlData.publicUrl,
        };

      } catch (uploadError) {
        console.error('❌ Upload error:', uploadError);
        return {
          success: false,
          error: uploadError instanceof Error ? uploadError.message : 'Failed to upload image'
        };
      }

    } catch (error) {
      console.error('❌ Error uploading image:', error);
      return { success: false, error: 'Failed to upload image' };
    }
  }

  /**
   * Update user profile with new avatar URL
   */
  async updateUserAvatar(userId: string, avatarUrl: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })
        .eq('id', userId);

      if (error) {
        console.error('Error updating profile:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating user avatar:', error);
      return false;
    }
  }

  /**
   * Delete old avatar from storage
   */
  async deleteOldAvatar(avatarUrl: string): Promise<void> {
    try {
      if (!avatarUrl || !avatarUrl.includes('supabase')) {
        return; // Not a Supabase storage URL
      }

      // Extract file path from URL - handle both old and new bucket names
      let filePath = '';

      if (avatarUrl.includes('/storage/v1/object/public/profile-photos/')) {
        const urlParts = avatarUrl.split('/storage/v1/object/public/profile-photos/');
        filePath = urlParts[1];
      } else if (avatarUrl.includes('/storage/v1/object/public/avatars/')) {
        const urlParts = avatarUrl.split('/storage/v1/object/public/avatars/');
        filePath = urlParts[1];
      } else {
        // Fallback: extract filename and assume it's in avatars bucket
        const urlParts = avatarUrl.split('/');
        const fileName = urlParts[urlParts.length - 1];
        filePath = fileName;
      }

      console.log('🗑️ Deleting old avatar:', filePath);

      // Try profile-photos bucket first, then avatars bucket
      let { error } = await supabase.storage
        .from('profile-photos')
        .remove([filePath]);

      if (error && error.message.includes('not found')) {
        // Try avatars bucket as fallback
        ({ error } = await supabase.storage
          .from('avatars')
          .remove([filePath]));
      }

      if (error) {
        console.warn('⚠️ Failed to delete old avatar:', error.message);
      } else {
        console.log('✅ Old avatar deleted successfully');
      }
    } catch (error) {
      console.warn('⚠️ Error deleting old avatar:', error);
    }
  }

  /**
   * Cleanup old profile images for a user to save storage space
   */
  async cleanupOldProfileImages(userId: string, userName?: string): Promise<void> {
    try {
      // Create folder name same as in upload method
      const folderName = userName
        ? userName.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 50)
        : userId;

      // Create sanitized user name for filename matching
      const sanitizedUserName = userName
        ? userName.toLowerCase().replace(/[^a-z0-9]/g, '_').substring(0, 30)
        : `user_${userId.substring(0, 8)}`;

      console.log('🧹 Cleaning up old profile images for:', {
        folderName,
        sanitizedUserName
      });

      // List all files in user's folder
      const { data: files, error } = await supabase.storage
        .from('profile-photos')
        .list(folderName, {
          limit: 100,
          sortBy: { column: 'created_at', order: 'desc' }
        });

      if (error) {
        console.warn('⚠️ Failed to list files for cleanup:', error.message);
        return;
      }

      if (!files || files.length === 0) {
        console.log('ℹ️ No files found in folder');
        return;
      }

      // Filter files that belong to this user (by filename pattern)
      const userFiles = files.filter(file =>
        file.name.startsWith(sanitizedUserName) ||
        file.name.startsWith('profile_') || // Legacy files
        file.name.includes(userId.substring(0, 8)) // Fallback pattern
      );

      if (userFiles.length <= 1) {
        console.log('ℹ️ No old files to cleanup');
        return;
      }

      // Keep the most recent file, delete the rest
      const filesToDelete = userFiles.slice(1).map(file => `${folderName}/${file.name}`);

      if (filesToDelete.length > 0) {
        console.log('🗑️ Deleting old profile images:', filesToDelete);

        const { error: deleteError } = await supabase.storage
          .from('profile-photos')
          .remove(filesToDelete);

        if (deleteError) {
          console.warn('⚠️ Failed to delete some old files:', deleteError.message);
        } else {
          console.log(`✅ Cleaned up ${filesToDelete.length} old profile images`);
        }
      }
    } catch (error) {
      console.warn('⚠️ Error during cleanup:', error);
    }
  }

  /**
   * Complete profile image update process
   */
  async updateProfileImage(userId: string, userName?: string, currentAvatarUrl?: string): Promise<UploadResult> {
    try {
      // Show image picker options
      const pickerResult = await this.showImagePickerOptions();

      if (!pickerResult.success || !pickerResult.imageUri) {
        return { success: false, error: pickerResult.error || 'No image selected' };
      }

      // Delete old avatar BEFORE uploading new one to save storage
      if (currentAvatarUrl) {
        console.log('🗑️ Deleting old avatar before upload...');
        await this.deleteOldAvatar(currentAvatarUrl);

        // Also cleanup any other old profile images for this user
        await this.cleanupOldProfileImages(userId, userName);
      }

      // Upload new image
      const uploadResult = await this.uploadProfileImage(pickerResult.imageUri, userId, userName);

      if (!uploadResult.success || !uploadResult.publicUrl) {
        return { success: false, error: uploadResult.error || 'Upload failed' };
      }

      // Update user profile
      const updateSuccess = await this.updateUserAvatar(userId, uploadResult.publicUrl);

      if (!updateSuccess) {
        // If profile update fails, delete the uploaded image to avoid orphaned files
        console.log('⚠️ Profile update failed, cleaning up uploaded image...');
        await this.deleteOldAvatar(uploadResult.publicUrl);
        return { success: false, error: 'Failed to update profile' };
      }

      console.log('✅ Profile image update complete:', uploadResult.publicUrl);

      return {
        success: true,
        publicUrl: uploadResult.publicUrl,
      };
    } catch (error) {
      console.error('Error in complete profile image update:', error);
      return { success: false, error: 'Failed to update profile image' };
    }
  }

  /**
   * Get default avatar URL based on user info
   */
  getDefaultAvatarUrl(fullName?: string, role?: string): string {
    if (fullName) {
      // Use UI Avatars service for generated avatars
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(fullName)}&size=200&background=2563eb&color=ffffff&bold=true&format=png`;
    }

    // Default role-based avatars
    const roleAvatars = {
      supervisor: 'https://randomuser.me/api/portraits/men/32.jpg',
      operator: 'https://randomuser.me/api/portraits/men/45.jpg',
      safety_officer: 'https://randomuser.me/api/portraits/women/68.jpg',
      maintenance_tech: 'https://randomuser.me/api/portraits/men/22.jpg',
      admin: 'https://randomuser.me/api/portraits/women/44.jpg',
    };

    return role && roleAvatars[role as keyof typeof roleAvatars] 
      ? roleAvatars[role as keyof typeof roleAvatars]
      : 'https://randomuser.me/api/portraits/men/1.jpg';
  }
}

export default ProfileImageService;
