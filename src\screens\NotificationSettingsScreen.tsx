import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  StyleSheet,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Layout } from '../constants';

interface NotificationSettingsScreenProps {
  navigation: any;
}

interface NotificationSettings {
  pushNotifications: boolean;
  emailNotifications: boolean;
  safetyAlerts: boolean;
  maintenanceAlerts: boolean;
  shiftReminders: boolean;
  reportNotifications: boolean;
  emergencyAlerts: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

const NotificationSettingsScreen: React.FC<NotificationSettingsScreenProps> = ({ navigation }) => {
  const [settings, setSettings] = useState<NotificationSettings>({
    pushNotifications: true,
    emailNotifications: true,
    safetyAlerts: true,
    maintenanceAlerts: true,
    shiftReminders: true,
    reportNotifications: false,
    emergencyAlerts: true,
    soundEnabled: true,
    vibrationEnabled: true,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('notificationSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (newSettings: NotificationSettings) => {
    try {
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving notification settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const handleToggle = (key: keyof NotificationSettings) => {
    const newSettings = {
      ...settings,
      [key]: !settings[key],
    };
    saveSettings(newSettings);
  };

  const resetToDefaults = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all notification settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            const defaultSettings: NotificationSettings = {
              pushNotifications: true,
              emailNotifications: true,
              safetyAlerts: true,
              maintenanceAlerts: true,
              shiftReminders: true,
              reportNotifications: false,
              emergencyAlerts: true,
              soundEnabled: true,
              vibrationEnabled: true,
            };
            saveSettings(defaultSettings);
          },
        },
      ]
    );
  };

  const SettingItem = ({ 
    title, 
    description, 
    value, 
    onToggle, 
    icon 
  }: {
    title: string;
    description: string;
    value: boolean;
    onToggle: () => void;
    icon: string;
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingIcon}>
        <Ionicons name={icon as any} size={20} color={Colors.primary} />
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: Colors.border, true: Colors.primary + '40' }}
        thumbColor={value ? Colors.primary : Colors.textLight}
      />
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <TouchableOpacity
          style={styles.resetButton}
          onPress={resetToDefaults}
        >
          <Ionicons name="refresh" size={20} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* General Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>General</Text>
          
          <SettingItem
            title="Push Notifications"
            description="Receive push notifications on your device"
            value={settings.pushNotifications}
            onToggle={() => handleToggle('pushNotifications')}
            icon="notifications"
          />

          <SettingItem
            title="Email Notifications"
            description="Receive notifications via email"
            value={settings.emailNotifications}
            onToggle={() => handleToggle('emailNotifications')}
            icon="mail"
          />
        </View>

        {/* Work Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Work Alerts</Text>
          
          <SettingItem
            title="Safety Alerts"
            description="Critical safety notifications and warnings"
            value={settings.safetyAlerts}
            onToggle={() => handleToggle('safetyAlerts')}
            icon="shield-checkmark"
          />

          <SettingItem
            title="Maintenance Alerts"
            description="Equipment maintenance and repair notifications"
            value={settings.maintenanceAlerts}
            onToggle={() => handleToggle('maintenanceAlerts')}
            icon="build"
          />

          <SettingItem
            title="Shift Reminders"
            description="Reminders for upcoming shifts and schedules"
            value={settings.shiftReminders}
            onToggle={() => handleToggle('shiftReminders')}
            icon="time"
          />

          <SettingItem
            title="Report Notifications"
            description="Updates on reports and documentation"
            value={settings.reportNotifications}
            onToggle={() => handleToggle('reportNotifications')}
            icon="document-text"
          />

          <SettingItem
            title="Emergency Alerts"
            description="Critical emergency notifications (always recommended)"
            value={settings.emergencyAlerts}
            onToggle={() => handleToggle('emergencyAlerts')}
            icon="warning"
          />
        </View>

        {/* Sound & Vibration */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sound & Vibration</Text>
          
          <SettingItem
            title="Sound"
            description="Play sound for notifications"
            value={settings.soundEnabled}
            onToggle={() => handleToggle('soundEnabled')}
            icon="volume-high"
          />

          <SettingItem
            title="Vibration"
            description="Vibrate device for notifications"
            value={settings.vibrationEnabled}
            onToggle={() => handleToggle('vibrationEnabled')}
            icon="phone-portrait"
          />
        </View>

        {/* Info */}
        <View style={styles.infoSection}>
          <Ionicons name="information-circle" size={20} color={Colors.info} />
          <Text style={styles.infoText}>
            Emergency alerts are highly recommended for safety reasons. 
            You can customize other notifications based on your preferences.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  resetButton: {
    padding: Layout.spacing.xs,
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  section: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
    marginTop: Layout.spacing.lg,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  settingContent: {
    flex: 1,
    marginRight: Layout.spacing.md,
  },
  settingTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  settingDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.info + '20',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.xl,
  },
  infoText: {
    flex: 1,
    fontSize: Layout.fontSize.sm,
    color: Colors.info,
    marginLeft: Layout.spacing.sm,
    lineHeight: 20,
  },
  loadingText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
  },
});

export default NotificationSettingsScreen;
