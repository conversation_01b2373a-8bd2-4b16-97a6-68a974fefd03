# Cross-Platform Shadow System

## Overview

The Shadow Helper system provides a unified approach to handling shadows across different platforms (iOS, Android, Web) in React Native applications, eliminating deprecation warnings and ensuring consistent visual appearance.

## Problem Statement

React Native Web deprecated the use of `shadowColor`, `shadowOffset`, `shadowOpacity`, and `shadowRadius` properties, requiring the use of CSS `boxShadow` instead. This created inconsistencies between mobile and web platforms.

## Solution

### ShadowHelper Utility (`src/utils/shadowHelper.ts`)

A platform-aware utility that automatically converts React Native shadow properties to appropriate platform-specific styles.

#### Core Function
```typescript
export const createShadow = (config: ShadowConfig): ShadowConfig | WebShadowConfig => {
  if (Platform.OS === 'web') {
    // Convert to CSS boxShadow
    return {
      boxShadow: `${offsetX}px ${offsetY}px ${blur}px ${color}`,
      elevation,
    };
  }
  // Return original config for iOS/Android
  return config;
};
```

#### Predefined Presets
```typescript
export const ShadowPresets = {
  small: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  }),
  
  medium: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  }),
  
  large: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8,
  }),
  
  card: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 4,
  }),
  
  button: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  }),
};
```

## Usage

### Basic Implementation
```typescript
import { ShadowPresets } from '../utils/shadowHelper';

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    ...ShadowPresets.card, // Cross-platform shadow
  },
  
  button: {
    backgroundColor: 'blue',
    borderRadius: 4,
    padding: 12,
    ...ShadowPresets.button, // Cross-platform shadow
  },
});
```

### Custom Shadow
```typescript
import { createShadow } from '../utils/shadowHelper';

const customShadow = createShadow({
  shadowColor: '#FF0000',
  shadowOffset: { width: 2, height: 4 },
  shadowOpacity: 0.2,
  shadowRadius: 6,
  elevation: 10,
});

const styles = StyleSheet.create({
  customElement: {
    ...customShadow,
  },
});
```

## Integration with Layout Constants

### Updated Layout Constants
```typescript
// src/constants/layout.ts
import { ShadowPresets } from '../utils/shadowHelper';

export const Layout = {
  // ... other properties
  shadow: ShadowPresets.medium,
  shadowLight: ShadowPresets.small,
};
```

### Usage in Components
```typescript
const styles = StyleSheet.create({
  container: {
    ...Layout.shadow, // Uses cross-platform shadow
  },
});
```

## Platform-Specific Output

### Web (CSS boxShadow)
```css
.element {
  box-shadow: 0px 2px 3.84px rgba(0, 0, 0, 0.1);
  /* elevation is ignored on web */
}
```

### iOS/Android (Native Properties)
```typescript
{
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 3.84,
  elevation: 5, // Android only
}
```

## Benefits

### 1. Cross-Platform Compatibility
- Automatic platform detection
- Appropriate styling for each platform
- No manual platform checks needed

### 2. No Deprecation Warnings
- Uses modern CSS boxShadow for web
- Maintains native properties for mobile
- Future-proof implementation

### 3. Consistent Visual Appearance
- Same shadow appearance across platforms
- Predefined presets for consistency
- Easy to maintain and update

### 4. Developer Experience
- Simple API with preset options
- TypeScript support with proper typing
- Reusable across the entire application

## Migration Guide

### Before (Deprecated)
```typescript
const styles = StyleSheet.create({
  card: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
```

### After (Modern)
```typescript
import { ShadowPresets } from '../utils/shadowHelper';

const styles = StyleSheet.create({
  card: {
    ...ShadowPresets.card,
  },
});
```

## Best Practices

### 1. Use Predefined Presets
- Prefer `ShadowPresets` over custom shadows
- Ensures consistency across the app
- Easier to maintain and update

### 2. Choose Appropriate Preset
- `small`: For subtle elements (badges, chips)
- `medium`: For standard cards and containers
- `large`: For modals and overlays
- `card`: Specifically for card components
- `button`: For interactive elements

### 3. Performance Considerations
- Shadows can impact performance on lower-end devices
- Use sparingly and test on target devices
- Consider disabling shadows for performance-critical screens

### 4. Accessibility
- Ensure shadows don't interfere with text readability
- Test with high contrast modes
- Provide alternative visual cues if needed

## Testing

### Unit Tests
```typescript
import { createShadow, ShadowPresets } from '../shadowHelper';
import { Platform } from 'react-native';

describe('ShadowHelper', () => {
  it('should return boxShadow for web platform', () => {
    Platform.OS = 'web';
    const result = createShadow({
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 5,
    });
    
    expect(result).toHaveProperty('boxShadow');
    expect(result.boxShadow).toBe('0px 2px 4px rgba(0, 0, 0, 0.1)');
  });
});
```

## Troubleshooting

### Common Issues
1. **Shadows not appearing on web**: Check if boxShadow is being applied
2. **Performance issues**: Reduce shadow complexity or use fewer shadows
3. **Inconsistent appearance**: Ensure using the same preset across platforms

### Debug Tips
- Use browser dev tools to inspect CSS on web
- Check React Native debugger for mobile platforms
- Test on actual devices for accurate shadow rendering

## Related Files
- `src/utils/shadowHelper.ts` - Main implementation
- `src/constants/layout.ts` - Layout constants integration
- `src/screens/DashboardScreen.tsx` - Usage example
