// API-specific types for Mining Operations Platform

export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  path: string;
  description: string;
  requiresAuth: boolean;
  parameters?: ApiParameter[];
  requestBody?: ApiRequestBody;
  responses: ApiResponse[];
}

export interface ApiParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  description: string;
  example?: any;
  validation?: ValidationRule;
}

export interface ApiRequestBody {
  contentType: string;
  schema: any;
  example?: any;
}

export interface ApiResponse {
  statusCode: number;
  description: string;
  schema?: any;
  example?: any;
}

export interface ValidationRule {
  min?: number;
  max?: number;
  pattern?: string;
  enum?: any[];
}

// Authentication types
export interface AuthToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope?: string;
}

export interface AuthUser {
  id: string;
  email: string;
  user_metadata: {
    first_name?: string;
    last_name?: string;
    role?: string;
    [key: string]: any;
  };
  app_metadata: {
    provider?: string;
    providers?: string[];
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
}

// API Error types
export interface ApiErrorResponse {
  error: {
    message: string;
    details?: string;
    hint?: string;
    code?: string;
  };
  status: number;
  statusText: string;
}

// Pagination types
export interface PaginatedApiResponse<T> {
  data: T[];
  count: number;
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
}

// Query parameters
export interface QueryParams {
  select?: string;
  filter?: string;
  order?: string;
  limit?: number;
  offset?: number;
  page?: number;
}

// Supabase specific types
export interface SupabaseQueryBuilder {
  select(columns?: string): SupabaseQueryBuilder;
  insert(values: any): SupabaseQueryBuilder;
  update(values: any): SupabaseQueryBuilder;
  delete(): SupabaseQueryBuilder;
  eq(column: string, value: any): SupabaseQueryBuilder;
  neq(column: string, value: any): SupabaseQueryBuilder;
  gt(column: string, value: any): SupabaseQueryBuilder;
  gte(column: string, value: any): SupabaseQueryBuilder;
  lt(column: string, value: any): SupabaseQueryBuilder;
  lte(column: string, value: any): SupabaseQueryBuilder;
  like(column: string, pattern: string): SupabaseQueryBuilder;
  ilike(column: string, pattern: string): SupabaseQueryBuilder;
  in(column: string, values: any[]): SupabaseQueryBuilder;
  is(column: string, value: any): SupabaseQueryBuilder;
  order(column: string, options?: { ascending?: boolean }): SupabaseQueryBuilder;
  limit(count: number): SupabaseQueryBuilder;
  range(from: number, to: number): SupabaseQueryBuilder;
  single(): SupabaseQueryBuilder;
  maybeSingle(): SupabaseQueryBuilder;
}

// Real-time subscription types
export interface RealtimeChannel {
  subscribe(callback?: (status: string) => void): RealtimeChannel;
  unsubscribe(): Promise<string>;
  on(event: string, filter: any, callback: (payload: any) => void): RealtimeChannel;
}

export interface RealtimeClient {
  channel(name: string): RealtimeChannel;
  removeAllChannels(): Promise<string[]>;
  disconnect(): void;
}

// File upload types
export interface FileUploadOptions {
  cacheControl?: string;
  contentType?: string;
  duplex?: string;
  upsert?: boolean;
}

export interface FileObject {
  name: string;
  id: string;
  updated_at: string;
  created_at: string;
  last_accessed_at: string;
  metadata: {
    eTag: string;
    size: number;
    mimetype: string;
    cacheControl: string;
    lastModified: string;
    contentLength: number;
    httpStatusCode: number;
  };
}

// Batch operation types
export interface BatchOperation {
  operation: 'insert' | 'update' | 'delete';
  table: string;
  data: any;
  conditions?: any;
}

export interface BatchResult {
  success: boolean;
  operation: BatchOperation;
  result?: any;
  error?: string;
}

// API rate limiting
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// API versioning
export interface ApiVersion {
  version: string;
  deprecated: boolean;
  deprecationDate?: string;
  supportEndDate?: string;
  migrationGuide?: string;
}

// Health check
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    database: 'up' | 'down';
    storage: 'up' | 'down';
    auth: 'up' | 'down';
    realtime: 'up' | 'down';
  };
  metrics?: {
    responseTime: number;
    uptime: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}
