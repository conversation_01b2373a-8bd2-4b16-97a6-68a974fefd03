/**
 * Dependency Injection Container for Mining Operations Platform
 */
export class ServiceContainer {
  private static instance: ServiceContainer;
  private services = new Map<string, any>();
  private factories = new Map<string, () => any>();
  private singletons = new Set<string>();

  private constructor() {}

  public static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Register a service instance
   */
  register<T>(key: string, implementation: T): void {
    this.services.set(key, implementation);
  }

  /**
   * Register a service factory
   */
  registerFactory<T>(key: string, factory: () => T, singleton: boolean = false): void {
    this.factories.set(key, factory);
    if (singleton) {
      this.singletons.add(key);
    }
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(key: string, factory: () => T): void {
    this.registerFactory(key, factory, true);
  }

  /**
   * Resolve a service
   */
  resolve<T>(key: string): T {
    // Check if already instantiated
    if (this.services.has(key)) {
      return this.services.get(key);
    }

    // Check if factory exists
    if (this.factories.has(key)) {
      const factory = this.factories.get(key)!;
      const instance = factory();

      // Store singleton instances
      if (this.singletons.has(key)) {
        this.services.set(key, instance);
      }

      return instance;
    }

    throw new Error(`Service '${key}' not registered`);
  }

  /**
   * Check if service is registered
   */
  has(key: string): boolean {
    return this.services.has(key) || this.factories.has(key);
  }

  /**
   * Unregister a service
   */
  unregister(key: string): void {
    this.services.delete(key);
    this.factories.delete(key);
    this.singletons.delete(key);
  }

  /**
   * Clear all services
   */
  clear(): void {
    this.services.clear();
    this.factories.clear();
    this.singletons.clear();
  }

  /**
   * Get all registered service keys
   */
  getRegisteredKeys(): string[] {
    const keys = new Set<string>();
    
    for (const key of this.services.keys()) {
      keys.add(key);
    }
    
    for (const key of this.factories.keys()) {
      keys.add(key);
    }
    
    return Array.from(keys);
  }

  /**
   * Create a scoped container
   */
  createScope(): ServiceContainer {
    const scopedContainer = new ServiceContainer();
    
    // Copy parent services
    for (const [key, service] of this.services) {
      scopedContainer.services.set(key, service);
    }
    
    for (const [key, factory] of this.factories) {
      scopedContainer.factories.set(key, factory);
    }
    
    for (const key of this.singletons) {
      scopedContainer.singletons.add(key);
    }
    
    return scopedContainer;
  }
}
