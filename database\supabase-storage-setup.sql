-- =====================================================
-- 📸 SUPABASE STORAGE SETUP FOR PROFILE IMAGES
-- =====================================================
-- File: supabase-storage-setup.sql
-- Purpose: Setup storage bucket and policies for profile images
-- Created: 15 January 2025
-- Author: Augment AI Agent
-- =====================================================

-- 📁 CREATE AVATARS STORAGE BUCKET
-- =====================================================
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars', 
  'avatars', 
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 🔐 STORAGE POLICIES FOR AVATARS BUCKET
-- =====================================================

-- Policy 1: Allow authenticated users to upload their own avatars
CREATE POLICY "Users can upload their own avatar" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- Policy 2: Allow authenticated users to update their own avatars
CREATE POLICY "Users can update their own avatar" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- Policy 3: Allow authenticated users to delete their own avatars
CREATE POLICY "Users can delete their own avatar" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' 
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND auth.role() = 'authenticated'
);

-- Policy 4: Allow public access to view avatars
CREATE POLICY "Public can view avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- 🗃️ DATABASE TABLE UPDATES
-- =====================================================

-- Ensure users table has avatar_url column (if not exists)
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS avatar_url TEXT;

-- Add index for better performance on avatar_url queries
CREATE INDEX IF NOT EXISTS idx_users_avatar_url ON users(avatar_url);

-- Add updated_at trigger for users table (if not exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 📊 STORAGE ANALYTICS VIEWS (OPTIONAL)
-- =====================================================

-- View to track avatar storage usage
CREATE OR REPLACE VIEW avatar_storage_stats AS
SELECT 
    COUNT(*) as total_avatars,
    SUM(metadata->>'size')::bigint as total_size_bytes,
    ROUND(AVG((metadata->>'size')::bigint)) as avg_size_bytes,
    MIN(created_at) as first_upload,
    MAX(created_at) as last_upload
FROM storage.objects 
WHERE bucket_id = 'avatars';

-- View to track user avatar status
CREATE OR REPLACE VIEW user_avatar_status AS
SELECT 
    u.id,
    u.full_name,
    u.email,
    u.role,
    CASE 
        WHEN u.avatar_url IS NOT NULL THEN 'custom'
        ELSE 'default'
    END as avatar_type,
    u.avatar_url,
    u.created_at as user_created,
    u.updated_at as profile_updated
FROM users u;

-- 🔍 HELPER FUNCTIONS
-- =====================================================

-- Function to get user's avatar URL with fallback
CREATE OR REPLACE FUNCTION get_user_avatar_url(user_id UUID)
RETURNS TEXT AS $$
DECLARE
    avatar_url TEXT;
    user_name TEXT;
    user_role TEXT;
BEGIN
    -- Get user's avatar URL and info
    SELECT u.avatar_url, u.full_name, u.role
    INTO avatar_url, user_name, user_role
    FROM users u
    WHERE u.id = user_id;
    
    -- Return custom avatar if exists
    IF avatar_url IS NOT NULL AND avatar_url != '' THEN
        RETURN avatar_url;
    END IF;
    
    -- Generate fallback avatar URL based on name
    IF user_name IS NOT NULL AND user_name != '' THEN
        RETURN 'https://ui-avatars.com/api/?name=' || 
               replace(user_name, ' ', '+') || 
               '&size=200&background=2563eb&color=ffffff&bold=true&format=png';
    END IF;
    
    -- Return role-based default avatar
    RETURN CASE user_role
        WHEN 'supervisor' THEN 'https://randomuser.me/api/portraits/men/32.jpg'
        WHEN 'operator' THEN 'https://randomuser.me/api/portraits/men/45.jpg'
        WHEN 'safety_officer' THEN 'https://randomuser.me/api/portraits/women/68.jpg'
        WHEN 'maintenance_tech' THEN 'https://randomuser.me/api/portraits/men/22.jpg'
        WHEN 'admin' THEN 'https://randomuser.me/api/portraits/women/44.jpg'
        ELSE 'https://randomuser.me/api/portraits/men/1.jpg'
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up orphaned avatar files
CREATE OR REPLACE FUNCTION cleanup_orphaned_avatars()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    avatar_record RECORD;
BEGIN
    -- Find avatar files that don't have corresponding users
    FOR avatar_record IN
        SELECT name, id
        FROM storage.objects
        WHERE bucket_id = 'avatars'
        AND NOT EXISTS (
            SELECT 1 FROM users 
            WHERE avatar_url LIKE '%' || storage.objects.name || '%'
        )
        AND created_at < NOW() - INTERVAL '7 days' -- Only delete files older than 7 days
    LOOP
        -- Delete the orphaned file
        DELETE FROM storage.objects 
        WHERE id = avatar_record.id;
        
        deleted_count := deleted_count + 1;
    END LOOP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 📝 USAGE EXAMPLES
-- =====================================================

-- Example 1: Get user avatar with fallback
-- SELECT get_user_avatar_url('user-uuid-here');

-- Example 2: Check storage stats
-- SELECT * FROM avatar_storage_stats;

-- Example 3: Check user avatar status
-- SELECT * FROM user_avatar_status WHERE avatar_type = 'custom';

-- Example 4: Clean up orphaned files (run periodically)
-- SELECT cleanup_orphaned_avatars();

-- 🎯 VERIFICATION QUERIES
-- =====================================================

-- Verify bucket exists
SELECT * FROM storage.buckets WHERE id = 'avatars';

-- Verify policies exist
SELECT * FROM pg_policies WHERE tablename = 'objects' AND policyname LIKE '%avatar%';

-- Verify users table has avatar_url column
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'users' AND column_name = 'avatar_url';

-- Verify index exists
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'users' AND indexname = 'idx_users_avatar_url';

-- =====================================================
-- 🎉 SETUP COMPLETE!
-- =====================================================
-- 
-- Next Steps:
-- 1. Run this script in Supabase SQL Editor
-- 2. Verify all policies and functions are created
-- 3. Test avatar upload from the app
-- 4. Monitor storage usage with provided views
-- 
-- Storage Structure:
-- avatars/
-- ├── user-id-1/
-- │   └── profile_user-id-1_timestamp.jpg
-- ├── user-id-2/
-- │   └── profile_user-id-2_timestamp.png
-- └── ...
-- 
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '📸 Avatar storage setup completed successfully!';
    RAISE NOTICE '✅ Bucket: avatars (public, 5MB limit)';
    RAISE NOTICE '✅ Policies: Upload, Update, Delete, View';
    RAISE NOTICE '✅ Database: avatar_url column and index';
    RAISE NOTICE '✅ Functions: Fallback URLs and cleanup';
    RAISE NOTICE '✅ Views: Storage stats and user status';
    RAISE NOTICE '🚀 Ready for profile image uploads!';
END $$;
