# Data Models & Database Schema - Mining Operations App

## 🗄️ **Database Architecture**

### **Technology Stack**
- **Primary Database**: Supabase (PostgreSQL)
- **Local Database**: SQLite (for offline support)
- **Real-time**: Supabase Realtime subscriptions
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage

### **Database Design Principles**
1. **Normalization**: Reduce data redundancy
2. **Performance**: Optimized for read-heavy operations
3. **Scalability**: Support for multiple mining sites
4. **Audit Trail**: Track all data changes
5. **Security**: Row Level Security (RLS) policies

## 👤 **User Management Schema**

### **users table** (Supabase Auth)
```sql
-- Managed by Supabase Auth
-- Contains: id, email, created_at, updated_at, etc.
```

### **user_profiles table**
```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  full_name VARCHAR(200) GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
  role user_role NOT NULL DEFAULT 'Observer',
  department VARCHAR(100),
  location_id UUID REFERENCES locations(id),
  phone_number VARCHAR(20),
  profile_photo_url TEXT,
  employee_id VARCHAR(50) UNIQUE,
  hire_date DATE,
  is_active BOOLEAN DEFAULT true,
  last_login_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User roles enum
CREATE TYPE user_role AS ENUM (
  'Super Admin',
  'Site Manager',
  'Shift Supervisor', 
  'Equipment Operator',
  'Safety Officer',
  'Maintenance Technician',
  'Quality Controller',
  'Observer'
);
```

### **user_permissions table**
```sql
CREATE TABLE user_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
  permission VARCHAR(100) NOT NULL,
  granted_by UUID REFERENCES user_profiles(id),
  granted_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true,
  UNIQUE(user_id, permission)
);
```

## 🏢 **Location & Site Management**

### **locations table**
```sql
CREATE TABLE locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL,
  type location_type NOT NULL,
  parent_id UUID REFERENCES locations(id),
  address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  timezone VARCHAR(50) DEFAULT 'UTC',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE location_type AS ENUM (
  'Mine Site',
  'Processing Plant',
  'Warehouse',
  'Office',
  'Pit',
  'Dump Site',
  'Workshop'
);
```

### **shifts table**
```sql
CREATE TABLE shifts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  location_id UUID REFERENCES locations(id),
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 📊 **Production Management Schema**

### **production_targets table**
```sql
CREATE TABLE production_targets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id),
  material_type VARCHAR(100) NOT NULL,
  target_date DATE NOT NULL,
  target_quantity DECIMAL(12, 2) NOT NULL,
  target_unit VARCHAR(20) DEFAULT 'tons',
  shift_id UUID REFERENCES shifts(id),
  created_by UUID REFERENCES user_profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(location_id, material_type, target_date, shift_id)
);
```

### **production_records table**
```sql
CREATE TABLE production_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id) NOT NULL,
  shift_id UUID REFERENCES shifts(id),
  material_type VARCHAR(100) NOT NULL,
  quantity DECIMAL(12, 2) NOT NULL,
  unit VARCHAR(20) DEFAULT 'tons',
  quality_grade VARCHAR(50),
  recorded_at TIMESTAMPTZ NOT NULL,
  recorded_by UUID REFERENCES user_profiles(id),
  equipment_id UUID REFERENCES equipment(id),
  notes TEXT,
  weather_conditions VARCHAR(100),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **production_calendar table**
```sql
CREATE TABLE production_calendar (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id),
  production_month INTEGER NOT NULL, -- 1-12
  production_year INTEGER NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  working_days INTEGER,
  target_quantity DECIMAL(12, 2),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(location_id, production_month, production_year)
);
```

## 🚜 **Equipment Management Schema**

### **equipment table**
```sql
CREATE TABLE equipment (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  equipment_number VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(200) NOT NULL,
  type equipment_type NOT NULL,
  category equipment_category NOT NULL,
  manufacturer VARCHAR(100),
  model VARCHAR(100),
  year_manufactured INTEGER,
  serial_number VARCHAR(100),
  purchase_date DATE,
  purchase_cost DECIMAL(12, 2),
  current_location_id UUID REFERENCES locations(id),
  status equipment_status DEFAULT 'Active',
  fuel_type VARCHAR(50),
  fuel_capacity DECIMAL(8, 2),
  max_load_capacity DECIMAL(10, 2),
  operating_weight DECIMAL(10, 2),
  qr_code VARCHAR(200),
  photo_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE equipment_type AS ENUM (
  'Excavator',
  'Dump Truck',
  'Loader',
  'Bulldozer',
  'Drill',
  'Crusher',
  'Conveyor',
  'Generator',
  'Pump',
  'Compressor'
);

CREATE TYPE equipment_category AS ENUM (
  'Heavy Machinery',
  'Light Vehicle',
  'Processing Equipment',
  'Support Equipment',
  'Safety Equipment'
);

CREATE TYPE equipment_status AS ENUM (
  'Active',
  'Maintenance',
  'Inactive',
  'Repair',
  'Retired'
);
```

### **equipment_metrics table**
```sql
CREATE TABLE equipment_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  equipment_id UUID REFERENCES equipment(id) ON DELETE CASCADE,
  recorded_date DATE NOT NULL,
  shift_id UUID REFERENCES shifts(id),
  operating_hours DECIMAL(5, 2) DEFAULT 0,
  planned_hours DECIMAL(5, 2) DEFAULT 0,
  maintenance_hours DECIMAL(5, 2) DEFAULT 0,
  downtime_hours DECIMAL(5, 2) DEFAULT 0,
  fuel_consumed DECIMAL(8, 2) DEFAULT 0,
  distance_traveled DECIMAL(8, 2) DEFAULT 0,
  load_cycles INTEGER DEFAULT 0,
  operator_id UUID REFERENCES user_profiles(id),
  location_id UUID REFERENCES locations(id),
  efficiency_score DECIMAL(5, 2),
  utilization_rate DECIMAL(5, 2),
  health_score DECIMAL(5, 2),
  recorded_by UUID REFERENCES user_profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(equipment_id, recorded_date, shift_id)
);
```

### **equipment_alerts table**
```sql
CREATE TABLE equipment_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  equipment_id UUID REFERENCES equipment(id) ON DELETE CASCADE,
  alert_type alert_type NOT NULL,
  severity alert_severity NOT NULL,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  triggered_at TIMESTAMPTZ DEFAULT NOW(),
  acknowledged_at TIMESTAMPTZ,
  acknowledged_by UUID REFERENCES user_profiles(id),
  resolved_at TIMESTAMPTZ,
  resolved_by UUID REFERENCES user_profiles(id),
  resolution_notes TEXT,
  is_active BOOLEAN DEFAULT true
);

CREATE TYPE alert_type AS ENUM (
  'Maintenance Due',
  'Performance Issue',
  'Safety Concern',
  'Fuel Low',
  'Overheating',
  'Mechanical Failure',
  'Operational'
);

CREATE TYPE alert_severity AS ENUM (
  'Low',
  'Medium', 
  'High',
  'Critical'
);
```

## 🛡️ **Safety Management Schema**

### **safety_incidents table**
```sql
CREATE TABLE safety_incidents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  incident_number VARCHAR(50) UNIQUE NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  incident_type incident_type NOT NULL,
  severity incident_severity NOT NULL,
  location_id UUID REFERENCES locations(id),
  exact_location TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  occurred_at TIMESTAMPTZ NOT NULL,
  reported_at TIMESTAMPTZ DEFAULT NOW(),
  reported_by UUID REFERENCES user_profiles(id) NOT NULL,
  people_involved INTEGER DEFAULT 0,
  injuries_count INTEGER DEFAULT 0,
  fatalities_count INTEGER DEFAULT 0,
  equipment_involved UUID REFERENCES equipment(id),
  weather_conditions VARCHAR(100),
  immediate_actions TEXT,
  root_cause TEXT,
  corrective_actions TEXT,
  status incident_status DEFAULT 'Reported',
  assigned_investigator UUID REFERENCES user_profiles(id),
  investigation_deadline DATE,
  closed_at TIMESTAMPTZ,
  closed_by UUID REFERENCES user_profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE incident_type AS ENUM (
  'Near Miss',
  'First Aid',
  'Medical Treatment',
  'Lost Time Injury',
  'Property Damage',
  'Environmental',
  'Security',
  'Fire',
  'Chemical Spill'
);

CREATE TYPE incident_severity AS ENUM (
  'Low',
  'Medium',
  'High', 
  'Critical'
);

CREATE TYPE incident_status AS ENUM (
  'Reported',
  'Under Investigation',
  'Investigation Complete',
  'Closed'
);
```

### **safety_checklists table**
```sql
CREATE TABLE safety_checklists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  location_id UUID REFERENCES locations(id),
  frequency checklist_frequency NOT NULL,
  is_mandatory BOOLEAN DEFAULT false,
  created_by UUID REFERENCES user_profiles(id),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE checklist_frequency AS ENUM (
  'Daily',
  'Weekly', 
  'Monthly',
  'Quarterly',
  'As Needed'
);
```

### **safety_checklist_items table**
```sql
CREATE TABLE safety_checklist_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  checklist_id UUID REFERENCES safety_checklists(id) ON DELETE CASCADE,
  item_text TEXT NOT NULL,
  item_order INTEGER NOT NULL,
  is_critical BOOLEAN DEFAULT false,
  requires_photo BOOLEAN DEFAULT false,
  requires_comment BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### **safety_checklist_completions table**
```sql
CREATE TABLE safety_checklist_completions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  checklist_id UUID REFERENCES safety_checklists(id),
  completed_by UUID REFERENCES user_profiles(id) NOT NULL,
  location_id UUID REFERENCES locations(id),
  shift_id UUID REFERENCES shifts(id),
  completed_at TIMESTAMPTZ DEFAULT NOW(),
  overall_status completion_status NOT NULL,
  notes TEXT,
  supervisor_review_required BOOLEAN DEFAULT false,
  reviewed_by UUID REFERENCES user_profiles(id),
  reviewed_at TIMESTAMPTZ
);

CREATE TYPE completion_status AS ENUM (
  'Pass',
  'Fail',
  'Partial',
  'Needs Review'
);
```

## 🔧 **Maintenance Management Schema**

### **maintenance_schedules table**
```sql
CREATE TABLE maintenance_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  equipment_id UUID REFERENCES equipment(id) ON DELETE CASCADE,
  maintenance_type maintenance_type NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  frequency_type frequency_type NOT NULL,
  frequency_value INTEGER NOT NULL,
  last_completed_at TIMESTAMPTZ,
  next_due_date DATE NOT NULL,
  estimated_duration_hours DECIMAL(4, 2),
  priority maintenance_priority DEFAULT 'Medium',
  assigned_technician UUID REFERENCES user_profiles(id),
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES user_profiles(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE maintenance_type AS ENUM (
  'Preventive',
  'Predictive',
  'Corrective',
  'Emergency',
  'Inspection'
);

CREATE TYPE frequency_type AS ENUM (
  'Hours',
  'Days',
  'Weeks',
  'Months',
  'Kilometers',
  'Cycles'
);

CREATE TYPE maintenance_priority AS ENUM (
  'Low',
  'Medium',
  'High',
  'Emergency'
);
```

### **work_orders table**
```sql
CREATE TABLE work_orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  work_order_number VARCHAR(50) UNIQUE NOT NULL,
  equipment_id UUID REFERENCES equipment(id) NOT NULL,
  maintenance_schedule_id UUID REFERENCES maintenance_schedules(id),
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  work_type work_type NOT NULL,
  priority maintenance_priority DEFAULT 'Medium',
  status work_order_status DEFAULT 'Open',
  requested_by UUID REFERENCES user_profiles(id) NOT NULL,
  assigned_to UUID REFERENCES user_profiles(id),
  estimated_hours DECIMAL(5, 2),
  actual_hours DECIMAL(5, 2),
  estimated_cost DECIMAL(10, 2),
  actual_cost DECIMAL(10, 2),
  scheduled_start TIMESTAMPTZ,
  scheduled_end TIMESTAMPTZ,
  actual_start TIMESTAMPTZ,
  actual_end TIMESTAMPTZ,
  completion_notes TEXT,
  safety_requirements TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE work_type AS ENUM (
  'Preventive Maintenance',
  'Corrective Maintenance',
  'Emergency Repair',
  'Inspection',
  'Calibration',
  'Upgrade',
  'Installation'
);

CREATE TYPE work_order_status AS ENUM (
  'Open',
  'Assigned',
  'In Progress',
  'On Hold',
  'Completed',
  'Cancelled'
);
```

### **maintenance_parts table**
```sql
CREATE TABLE maintenance_parts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  part_number VARCHAR(100) UNIQUE NOT NULL,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  manufacturer VARCHAR(100),
  unit_cost DECIMAL(10, 2),
  stock_quantity INTEGER DEFAULT 0,
  minimum_stock INTEGER DEFAULT 0,
  location VARCHAR(100),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 📁 **File Management Schema**

### **attachments table**
```sql
CREATE TABLE attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_type VARCHAR(50) NOT NULL, -- 'incident', 'equipment', 'work_order', etc.
  entity_id UUID NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  file_type VARCHAR(100),
  mime_type VARCHAR(100),
  uploaded_by UUID REFERENCES user_profiles(id),
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);
```

## 🔄 **Audit & Sync Schema**

### **audit_logs table**
```sql
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name VARCHAR(100) NOT NULL,
  record_id UUID NOT NULL,
  action audit_action NOT NULL,
  old_values JSONB,
  new_values JSONB,
  changed_by UUID REFERENCES user_profiles(id),
  changed_at TIMESTAMPTZ DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT
);

CREATE TYPE audit_action AS ENUM (
  'INSERT',
  'UPDATE', 
  'DELETE'
);
```

### **sync_status table** (for offline support)
```sql
CREATE TABLE sync_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name VARCHAR(100) NOT NULL,
  record_id UUID NOT NULL,
  action sync_action NOT NULL,
  data JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  synced_at TIMESTAMPTZ,
  sync_attempts INTEGER DEFAULT 0,
  last_error TEXT,
  is_synced BOOLEAN DEFAULT false
);

CREATE TYPE sync_action AS ENUM (
  'CREATE',
  'UPDATE',
  'DELETE'
);
```

## 🔐 **Row Level Security (RLS) Policies**

### **User Profile Access**
```sql
-- Users can only see their own profile and profiles in their location
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Site managers can view all profiles in their location" ON user_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.id = auth.uid() 
      AND up.role IN ('Super Admin', 'Site Manager')
      AND (up.location_id = user_profiles.location_id OR up.role = 'Super Admin')
    )
  );
```

### **Production Data Access**
```sql
-- Users can only access production data for their location
CREATE POLICY "Location-based production access" ON production_records
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.id = auth.uid() 
      AND (up.location_id = production_records.location_id OR up.role = 'Super Admin')
    )
  );
```

### **Equipment Access**
```sql
-- Equipment operators can only see equipment they're assigned to
CREATE POLICY "Equipment access by role" ON equipment
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_profiles up 
      WHERE up.id = auth.uid() 
      AND (
        up.role IN ('Super Admin', 'Site Manager', 'Shift Supervisor') OR
        (up.role = 'Equipment Operator' AND up.location_id = equipment.current_location_id)
      )
    )
  );
```

## 📊 **Database Indexes for Performance**

```sql
-- User profiles
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_location ON user_profiles(location_id);
CREATE INDEX idx_user_profiles_active ON user_profiles(is_active);

-- Production records
CREATE INDEX idx_production_records_date ON production_records(recorded_at);
CREATE INDEX idx_production_records_location ON production_records(location_id);
CREATE INDEX idx_production_records_material ON production_records(material_type);

-- Equipment metrics
CREATE INDEX idx_equipment_metrics_date ON equipment_metrics(recorded_date);
CREATE INDEX idx_equipment_metrics_equipment ON equipment_metrics(equipment_id);
CREATE INDEX idx_equipment_metrics_location ON equipment_metrics(location_id);

-- Safety incidents
CREATE INDEX idx_safety_incidents_date ON safety_incidents(occurred_at);
CREATE INDEX idx_safety_incidents_status ON safety_incidents(status);
CREATE INDEX idx_safety_incidents_severity ON safety_incidents(severity);

-- Work orders
CREATE INDEX idx_work_orders_status ON work_orders(status);
CREATE INDEX idx_work_orders_priority ON work_orders(priority);
CREATE INDEX idx_work_orders_equipment ON work_orders(equipment_id);
```

## 🔄 **Database Functions & Triggers**

### **Auto-update timestamps**
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables with updated_at column
CREATE TRIGGER update_user_profiles_updated_at 
  BEFORE UPDATE ON user_profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **Generate incident numbers**
```sql
CREATE OR REPLACE FUNCTION generate_incident_number()
RETURNS TRIGGER AS $$
BEGIN
  NEW.incident_number = 'INC-' || TO_CHAR(NOW(), 'YYYY') || '-' || 
                        LPAD(NEXTVAL('incident_number_seq')::TEXT, 6, '0');
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE SEQUENCE incident_number_seq;
CREATE TRIGGER generate_incident_number_trigger
  BEFORE INSERT ON safety_incidents
  FOR EACH ROW EXECUTE FUNCTION generate_incident_number();
```

This completes the comprehensive database schema. The next section will cover the implementation details for each feature module.
