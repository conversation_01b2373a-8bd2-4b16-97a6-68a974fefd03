import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Modal,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { Layout } from '../constants/layout';

interface AttendanceScreenProps {
  navigation: any;
}

interface AttendanceRecord {
  id: string;
  date: string;
  checkIn: string;
  checkOut?: string;
  shift: 'morning' | 'afternoon' | 'night';
  location: string;
  status: 'present' | 'late' | 'absent' | 'overtime';
  workHours?: number;
}

interface ShiftSchedule {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  description: string;
  isActive: boolean;
}

const AttendanceScreen: React.FC<AttendanceScreenProps> = ({ navigation }) => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();
  const { profile } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [showShiftModal, setShowShiftModal] = useState(false);
  const [selectedShift, setSelectedShift] = useState<ShiftSchedule | null>(null);

  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord>({
    id: '1',
    date: new Date().toISOString().split('T')[0],
    checkIn: '',
    shift: 'morning',
    location: 'Main Mining Site',
    status: 'present'
  });

  const [recentAttendance] = useState<AttendanceRecord[]>([
    {
      id: '1',
      date: '2024-01-18',
      checkIn: '07:00',
      checkOut: '15:30',
      shift: 'morning',
      location: 'Main Mining Site',
      status: 'present',
      workHours: 8.5
    },
    {
      id: '2',
      date: '2024-01-17',
      checkIn: '07:15',
      checkOut: '15:45',
      shift: 'morning',
      location: 'Main Mining Site',
      status: 'late',
      workHours: 8.5
    },
    {
      id: '3',
      date: '2024-01-16',
      checkIn: '15:00',
      checkOut: '23:30',
      shift: 'afternoon',
      location: 'Processing Plant',
      status: 'present',
      workHours: 8.5
    }
  ]);

  const [shifts] = useState<ShiftSchedule[]>([
    {
      id: '1',
      name: 'Morning Shift',
      startTime: '07:00',
      endTime: '15:00',
      description: 'Main mining operations',
      isActive: true
    },
    {
      id: '2',
      name: 'Afternoon Shift',
      startTime: '15:00',
      endTime: '23:00',
      description: 'Processing and maintenance',
      isActive: true
    },
    {
      id: '3',
      name: 'Night Shift',
      startTime: '23:00',
      endTime: '07:00',
      description: 'Security and monitoring',
      isActive: false
    }
  ]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Layout.spacing.lg,
      paddingVertical: Layout.spacing.md,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: Layout.fontSize.xl,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginLeft: Layout.spacing.md,
    },
    content: {
      flex: 1,
      paddingHorizontal: Layout.spacing.lg,
      paddingTop: Layout.spacing.lg,
    },
    timeCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.xl,
      marginBottom: Layout.spacing.lg,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 8,
      elevation: 5,
    },
    currentTime: {
      fontSize: 32,
      fontWeight: 'bold',
      color: colors.primary,
      marginBottom: Layout.spacing.sm,
    },
    currentDate: {
      fontSize: Layout.fontSize.lg,
      color: colors.textSecondary,
      marginBottom: Layout.spacing.lg,
    },
    checkInButton: {
      backgroundColor: colors.success,
      paddingHorizontal: Layout.spacing.xl,
      paddingVertical: Layout.spacing.md,
      borderRadius: Layout.borderRadius.lg,
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Layout.spacing.sm,
    },
    checkOutButton: {
      backgroundColor: colors.error,
      paddingHorizontal: Layout.spacing.xl,
      paddingVertical: Layout.spacing.md,
      borderRadius: Layout.borderRadius.lg,
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Layout.spacing.sm,
    },
    buttonText: {
      color: colors.textInverse,
      fontSize: Layout.fontSize.lg,
      fontWeight: '600',
      marginLeft: Layout.spacing.sm,
    },
    statusText: {
      fontSize: Layout.fontSize.md,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    sectionTitle: {
      fontSize: Layout.fontSize.lg,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.md,
      marginTop: Layout.spacing.sm,
    },
    attendanceCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.md,
      padding: Layout.spacing.md,
      marginBottom: Layout.spacing.sm,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: isDarkMode ? 0.2 : 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    attendanceHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Layout.spacing.sm,
    },
    attendanceDate: {
      fontSize: Layout.fontSize.md,
      fontWeight: '600',
      color: colors.textPrimary,
    },
    statusBadge: {
      paddingHorizontal: Layout.spacing.sm,
      paddingVertical: Layout.spacing.xs,
      borderRadius: Layout.borderRadius.sm,
    },
    statusBadgeText: {
      fontSize: Layout.fontSize.xs,
      fontWeight: '600',
      color: colors.textInverse,
    },
    attendanceDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    detailItem: {
      alignItems: 'center',
    },
    detailLabel: {
      fontSize: Layout.fontSize.xs,
      color: colors.textSecondary,
      marginBottom: Layout.spacing.xs,
    },
    detailValue: {
      fontSize: Layout.fontSize.sm,
      fontWeight: '600',
      color: colors.textPrimary,
    },
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
      backgroundColor: colors.surface,
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.lg,
      width: '90%',
      maxHeight: '80%',
    },
    modalTitle: {
      fontSize: Layout.fontSize.xl,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.lg,
      textAlign: 'center',
    },
    shiftItem: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.md,
      padding: Layout.spacing.md,
      marginBottom: Layout.spacing.sm,
      borderWidth: 1,
      borderColor: colors.cardBorder,
    },
    shiftName: {
      fontSize: Layout.fontSize.md,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.xs,
    },
    shiftTime: {
      fontSize: Layout.fontSize.sm,
      color: colors.primary,
      marginBottom: Layout.spacing.xs,
    },
    shiftDescription: {
      fontSize: Layout.fontSize.sm,
      color: colors.textSecondary,
    },
    modalButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: Layout.spacing.lg,
    },
    modalButton: {
      flex: 1,
      paddingVertical: Layout.spacing.md,
      borderRadius: Layout.borderRadius.md,
      alignItems: 'center',
      marginHorizontal: Layout.spacing.xs,
    },
    cancelButton: {
      backgroundColor: colors.surfaceSecondary,
    },
    confirmButton: {
      backgroundColor: colors.primary,
    },
    modalButtonText: {
      fontSize: Layout.fontSize.md,
      fontWeight: '600',
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return colors.success;
      case 'late':
        return colors.warning;
      case 'absent':
        return colors.error;
      case 'overtime':
        return colors.primary;
      default:
        return colors.textSecondary;
    }
  };

  const handleCheckIn = () => {
    setShowShiftModal(true);
  };

  const confirmCheckIn = () => {
    const now = new Date();
    setTodayAttendance(prev => ({
      ...prev,
      checkIn: now.toTimeString().slice(0, 5),
    }));
    setIsCheckedIn(true);
    setShowShiftModal(false);
    Alert.alert('Success', 'Checked in successfully!');
  };

  const handleCheckOut = () => {
    const now = new Date();
    setTodayAttendance(prev => ({
      ...prev,
      checkOut: now.toTimeString().slice(0, 5),
    }));
    setIsCheckedIn(false);
    Alert.alert('Success', 'Checked out successfully!');
  };

  const renderShiftItem = ({ item }: { item: ShiftSchedule }) => (
    <TouchableOpacity
      style={[
        dynamicStyles.shiftItem,
        selectedShift?.id === item.id && { borderColor: colors.primary, borderWidth: 2 }
      ]}
      onPress={() => setSelectedShift(item)}
      disabled={!item.isActive}
    >
      <Text style={[dynamicStyles.shiftName, !item.isActive && { opacity: 0.5 }]}>
        {item.name}
      </Text>
      <Text style={[dynamicStyles.shiftTime, !item.isActive && { opacity: 0.5 }]}>
        {item.startTime} - {item.endTime}
      </Text>
      <Text style={[dynamicStyles.shiftDescription, !item.isActive && { opacity: 0.5 }]}>
        {item.description}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={dynamicStyles.container}>
      <View style={dynamicStyles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.textPrimary} />
        </TouchableOpacity>
        <Text style={dynamicStyles.headerTitle}>Attendance</Text>
      </View>

      <ScrollView style={dynamicStyles.content}>
        {/* Current Time & Check In/Out */}
        <View style={dynamicStyles.timeCard}>
          <Text style={dynamicStyles.currentTime}>
            {currentTime.toTimeString().slice(0, 5)}
          </Text>
          <Text style={dynamicStyles.currentDate}>
            {currentTime.toDateString()}
          </Text>
          
          {!isCheckedIn ? (
            <TouchableOpacity style={dynamicStyles.checkInButton} onPress={handleCheckIn}>
              <Ionicons name="log-in" size={24} color={colors.textInverse} />
              <Text style={dynamicStyles.buttonText}>Check In</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity style={dynamicStyles.checkOutButton} onPress={handleCheckOut}>
              <Ionicons name="log-out" size={24} color={colors.textInverse} />
              <Text style={dynamicStyles.buttonText}>Check Out</Text>
            </TouchableOpacity>
          )}
          
          <Text style={dynamicStyles.statusText}>
            {isCheckedIn ? `Checked in at ${todayAttendance.checkIn}` : 'Ready to check in'}
          </Text>
        </View>

        {/* Recent Attendance */}
        <Text style={dynamicStyles.sectionTitle}>Recent Attendance</Text>
        {recentAttendance.map((record) => (
          <View key={record.id} style={dynamicStyles.attendanceCard}>
            <View style={dynamicStyles.attendanceHeader}>
              <Text style={dynamicStyles.attendanceDate}>{record.date}</Text>
              <View style={[dynamicStyles.statusBadge, { backgroundColor: getStatusColor(record.status) }]}>
                <Text style={dynamicStyles.statusBadgeText}>{record.status.toUpperCase()}</Text>
              </View>
            </View>
            <View style={dynamicStyles.attendanceDetails}>
              <View style={dynamicStyles.detailItem}>
                <Text style={dynamicStyles.detailLabel}>Check In</Text>
                <Text style={dynamicStyles.detailValue}>{record.checkIn}</Text>
              </View>
              <View style={dynamicStyles.detailItem}>
                <Text style={dynamicStyles.detailLabel}>Check Out</Text>
                <Text style={dynamicStyles.detailValue}>{record.checkOut || '-'}</Text>
              </View>
              <View style={dynamicStyles.detailItem}>
                <Text style={dynamicStyles.detailLabel}>Hours</Text>
                <Text style={dynamicStyles.detailValue}>{record.workHours || '-'}</Text>
              </View>
              <View style={dynamicStyles.detailItem}>
                <Text style={dynamicStyles.detailLabel}>Location</Text>
                <Text style={dynamicStyles.detailValue}>{record.location}</Text>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>

      {/* Shift Selection Modal */}
      <Modal
        visible={showShiftModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowShiftModal(false)}
      >
        <View style={dynamicStyles.modalContainer}>
          <View style={dynamicStyles.modalContent}>
            <Text style={dynamicStyles.modalTitle}>Select Your Shift</Text>
            <FlatList
              data={shifts}
              renderItem={renderShiftItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
            />
            <View style={dynamicStyles.modalButtons}>
              <TouchableOpacity
                style={[dynamicStyles.modalButton, dynamicStyles.cancelButton]}
                onPress={() => setShowShiftModal(false)}
              >
                <Text style={[dynamicStyles.modalButtonText, { color: colors.textPrimary }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[dynamicStyles.modalButton, dynamicStyles.confirmButton]}
                onPress={confirmCheckIn}
                disabled={!selectedShift}
              >
                <Text style={[dynamicStyles.modalButtonText, { color: colors.textInverse }]}>
                  Check In
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default AttendanceScreen;
