import React from 'react';
import { View, StyleSheet } from 'react-native';
import BoardIcon from '../icons/BoardIcon';

interface DashboardIconBackgroundProps {
  children?: React.ReactNode;
  variant?: 'header' | 'content' | 'subtle';
}

const DashboardIconBackground: React.FC<DashboardIconBackgroundProps> = ({
  children,
  variant = 'header',
}) => {
  const getVariantConfig = () => {
    switch (variant) {
      case 'header':
        return {
          iconSize: 80,
          iconOpacity: 0.1,
          spacing: 120,
          overlayColor: '#1A365D',
          overlayOpacity: 0.85,
          pattern: 'diagonal',
        };
      case 'content':
        return {
          iconSize: 40,
          iconOpacity: 0.03,
          spacing: 80,
          overlayColor: '#FFFFFF',
          overlayOpacity: 0.95,
          pattern: 'grid',
        };
      case 'subtle':
        return {
          iconSize: 60,
          iconOpacity: 0.05,
          spacing: 100,
          overlayColor: '#F7FAFC',
          overlayOpacity: 0.8,
          pattern: 'scattered',
        };
      default:
        return {
          iconSize: 60,
          iconOpacity: 0.08,
          spacing: 100,
          overlayColor: '#1A365D',
          overlayOpacity: 0.85,
          pattern: 'diagonal',
        };
    }
  };

  const config = getVariantConfig();

  // Generate icon positions based on pattern
  const generateIcons = () => {
    const icons = [];
    const containerWidth = 400; // Approximate container width
    const containerHeight = 300; // Approximate container height
    
    const iconsPerRow = Math.ceil(containerWidth / config.spacing) + 2;
    const iconsPerColumn = Math.ceil(containerHeight / config.spacing) + 2;

    for (let row = 0; row < iconsPerColumn; row++) {
      for (let col = 0; col < iconsPerRow; col++) {
        let x, y;
        
        switch (config.pattern) {
          case 'diagonal':
            // Offset every other row for diagonal pattern
            const offsetX = row % 2 === 0 ? 0 : config.spacing / 2;
            x = col * config.spacing + offsetX - config.spacing / 2;
            y = row * config.spacing - config.spacing / 2;
            break;
            
          case 'grid':
            // Regular grid pattern
            x = col * config.spacing - config.spacing / 2;
            y = row * config.spacing - config.spacing / 2;
            break;
            
          case 'scattered':
            // Slightly randomized positions
            const randomOffsetX = (Math.random() - 0.5) * 20;
            const randomOffsetY = (Math.random() - 0.5) * 20;
            x = col * config.spacing + randomOffsetX - config.spacing / 2;
            y = row * config.spacing + randomOffsetY - config.spacing / 2;
            break;
            
          default:
            x = col * config.spacing - config.spacing / 2;
            y = row * config.spacing - config.spacing / 2;
        }
        
        icons.push(
          <View
            key={`icon-${row}-${col}`}
            style={[
              styles.iconContainer,
              {
                left: x,
                top: y,
                width: config.iconSize,
                height: config.iconSize,
              },
            ]}
          >
            <BoardIcon
              width={config.iconSize}
              height={config.iconSize}
              opacity={config.iconOpacity}
            />
          </View>
        );
      }
    }
    
    return icons;
  };

  return (
    <View style={styles.container}>
      {/* Background Icons Pattern */}
      <View style={styles.backgroundPattern}>
        {generateIcons()}
      </View>
      
      {/* Overlay */}
      <View 
        style={[
          styles.overlay, 
          { 
            backgroundColor: config.overlayColor,
            opacity: config.overlayOpacity,
          }
        ]} 
      />
      
      {/* Content */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  iconContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
    position: 'relative',
    zIndex: 1,
  },
});

export default DashboardIconBackground;
