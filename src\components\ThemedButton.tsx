import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { Layout } from '../constants/layout';

interface ThemedButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

const ThemedButton: React.FC<ThemedButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  fullWidth = false,
}) => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: Layout.borderRadius.md,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    };

    // Size variations
    switch (size) {
      case 'small':
        baseStyle.paddingHorizontal = Layout.spacing.sm;
        baseStyle.paddingVertical = Layout.spacing.xs;
        break;
      case 'large':
        baseStyle.paddingHorizontal = Layout.spacing.xl;
        baseStyle.paddingVertical = Layout.spacing.md;
        break;
      default:
        baseStyle.paddingHorizontal = Layout.spacing.lg;
        baseStyle.paddingVertical = Layout.spacing.sm;
    }

    if (fullWidth) {
      baseStyle.width = '100%';
    }

    // Variant styles
    switch (variant) {
      case 'primary':
        baseStyle.backgroundColor = colors.primary;
        break;
      case 'secondary':
        baseStyle.backgroundColor = colors.surfaceSecondary;
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = colors.border;
        break;
      case 'outline':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.borderWidth = 2;
        baseStyle.borderColor = colors.primary;
        break;
      case 'ghost':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.shadowOpacity = 0;
        baseStyle.elevation = 0;
        break;
      case 'danger':
        baseStyle.backgroundColor = colors.error;
        break;
    }

    if (disabled || loading) {
      baseStyle.opacity = 0.6;
    }

    return baseStyle;
  };

  const getTextStyle = (): TextStyle => {
    const baseTextStyle: TextStyle = {
      fontWeight: '600',
    };

    // Size variations
    switch (size) {
      case 'small':
        baseTextStyle.fontSize = Layout.fontSize.sm;
        break;
      case 'large':
        baseTextStyle.fontSize = Layout.fontSize.lg;
        break;
      default:
        baseTextStyle.fontSize = Layout.fontSize.md;
    }

    // Variant text colors
    switch (variant) {
      case 'primary':
      case 'danger':
        baseTextStyle.color = colors.textInverse;
        break;
      case 'secondary':
        baseTextStyle.color = colors.textPrimary;
        break;
      case 'outline':
      case 'ghost':
        baseTextStyle.color = colors.primary;
        break;
    }

    return baseTextStyle;
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 24;
      default: return 20;
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'primary':
      case 'danger':
        return colors.textInverse;
      case 'secondary':
        return colors.textPrimary;
      case 'outline':
      case 'ghost':
        return colors.primary;
      default:
        return colors.textInverse;
    }
  };

  const renderContent = () => {
    if (loading) {
      return <ActivityIndicator size="small" color={getIconColor()} />;
    }

    const iconElement = icon ? (
      <Ionicons
        name={icon}
        size={getIconSize()}
        color={getIconColor()}
        style={{ marginRight: iconPosition === 'left' ? Layout.spacing.xs : 0, marginLeft: iconPosition === 'right' ? Layout.spacing.xs : 0 }}
      />
    ) : null;

    const textElement = (
      <Text style={[getTextStyle(), textStyle]}>
        {title}
      </Text>
    );

    if (iconPosition === 'right') {
      return (
        <>
          {textElement}
          {iconElement}
        </>
      );
    }

    return (
      <>
        {iconElement}
        {textElement}
      </>
    );
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

export default ThemedButton;
