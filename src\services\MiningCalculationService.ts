/**
 * Mining Calculation Service - Single Source of Truth for all mining calculations
 * Standardized formulas for Strip Ratio (SR) and Fuel Ratio (FR)
 */

export interface MiningMetrics {
  stripRatio: {
    actual: number;
    plan: number;
    variance: number;
    variancePercent: number;
  };
  fuelRatio: {
    actual: number;
    plan: number;
    variance: number;
    variancePercent: number;
  };
  totalMaterial: {
    actual: number;
    plan: number;
  };
  achievement: {
    ob: number;
    ore: number;
    fuel: number;
  };
}

export interface ProductionData {
  actual_ob: number;
  plan_ob: number;
  actual_ore: number;
  plan_ore: number;
  actual_fuel: number;
  plan_fuel: number;
}

export class MiningCalculationService {
  private static instance: MiningCalculationService;

  // Mining industry constants
  private static readonly ORE_DENSITY_FACTOR = 3.39; // Conversion factor: tons to Bcm
  private static readonly PRECISION_DECIMALS = 4;

  private constructor() {}

  static getInstance(): MiningCalculationService {
    if (!MiningCalculationService.instance) {
      MiningCalculationService.instance = new MiningCalculationService();
    }
    return MiningCalculationService.instance;
  }

  /**
   * Calculate Strip Ratio (SR)
   * Formula: SR = Overburden (Bcm) / Ore (tons)
   * 
   * @param overburden - Overburden volume in Bcm
   * @param ore - Ore production in tons
   * @returns Strip ratio value
   */
  static calculateStripRatio(overburden: number, ore: number): number {
    if (ore <= 0) return 0;
    return Number((overburden / ore).toFixed(MiningCalculationService.PRECISION_DECIMALS));
  }

  /**
   * Calculate Fuel Ratio (FR) - STANDARDIZED FORMULA
   * Formula: FR = Fuel (L) / (OB (Bcm) + (Ore (tons) / 3.39))
   * Unit: L/Bcm
   * 
   * @param fuel - Fuel consumption in liters
   * @param overburden - Overburden volume in Bcm
   * @param ore - Ore production in tons
   * @returns Fuel ratio in L/Bcm
   */
  static calculateFuelRatio(fuel: number, overburden: number, ore: number): number {
    const totalMaterial = overburden + (ore / MiningCalculationService.ORE_DENSITY_FACTOR);
    if (totalMaterial <= 0) return 0;
    return Number((fuel / totalMaterial).toFixed(MiningCalculationService.PRECISION_DECIMALS));
  }

  /**
   * Calculate Total Material Moved in Bcm
   * Formula: Total Material = OB (Bcm) + (Ore (tons) / 3.39)
   * 
   * @param overburden - Overburden volume in Bcm
   * @param ore - Ore production in tons
   * @returns Total material in Bcm
   */
  static calculateTotalMaterial(overburden: number, ore: number): number {
    return Number((overburden + (ore / MiningCalculationService.ORE_DENSITY_FACTOR)).toFixed(2));
  }

  /**
   * Calculate Achievement Percentage
   * Formula: Achievement = (Actual / Plan) * 100
   * 
   * @param actual - Actual value
   * @param plan - Plan value
   * @returns Achievement percentage
   */
  static calculateAchievement(actual: number, plan: number): number {
    if (plan <= 0) return 0;
    return Number(((actual / plan) * 100).toFixed(1));
  }

  /**
   * Calculate Variance
   * Formula: Variance = Actual - Plan
   * 
   * @param actual - Actual value
   * @param plan - Plan value
   * @returns Variance value
   */
  static calculateVariance(actual: number, plan: number): number {
    return Number((actual - plan).toFixed(MiningCalculationService.PRECISION_DECIMALS));
  }

  /**
   * Calculate Variance Percentage
   * Formula: Variance % = ((Actual - Plan) / Plan) * 100
   * 
   * @param actual - Actual value
   * @param plan - Plan value
   * @returns Variance percentage
   */
  static calculateVariancePercent(actual: number, plan: number): number {
    if (plan <= 0) return 0;
    return Number((((actual - plan) / plan) * 100).toFixed(1));
  }

  /**
   * Calculate Complete Mining Metrics
   * Returns all standardized mining calculations
   * 
   * @param data - Production data
   * @returns Complete mining metrics
   */
  static calculateMiningMetrics(data: ProductionData): MiningMetrics {
    // Strip Ratio calculations
    const actualSR = MiningCalculationService.calculateStripRatio(data.actual_ob, data.actual_ore);
    const planSR = MiningCalculationService.calculateStripRatio(data.plan_ob, data.plan_ore);
    const srVariance = MiningCalculationService.calculateVariance(actualSR, planSR);
    const srVariancePercent = MiningCalculationService.calculateVariancePercent(actualSR, planSR);

    // Fuel Ratio calculations
    const actualFR = MiningCalculationService.calculateFuelRatio(data.actual_fuel, data.actual_ob, data.actual_ore);
    const planFR = MiningCalculationService.calculateFuelRatio(data.plan_fuel, data.plan_ob, data.plan_ore);
    const frVariance = MiningCalculationService.calculateVariance(actualFR, planFR);
    const frVariancePercent = MiningCalculationService.calculateVariancePercent(actualFR, planFR);

    // Total Material calculations
    const actualTotalMaterial = MiningCalculationService.calculateTotalMaterial(data.actual_ob, data.actual_ore);
    const planTotalMaterial = MiningCalculationService.calculateTotalMaterial(data.plan_ob, data.plan_ore);

    // Achievement calculations
    const obAchievement = MiningCalculationService.calculateAchievement(data.actual_ob, data.plan_ob);
    const oreAchievement = MiningCalculationService.calculateAchievement(data.actual_ore, data.plan_ore);
    const fuelAchievement = MiningCalculationService.calculateAchievement(data.actual_fuel, data.plan_fuel);

    return {
      stripRatio: {
        actual: actualSR,
        plan: planSR,
        variance: srVariance,
        variancePercent: srVariancePercent
      },
      fuelRatio: {
        actual: actualFR,
        plan: planFR,
        variance: frVariance,
        variancePercent: frVariancePercent
      },
      totalMaterial: {
        actual: actualTotalMaterial,
        plan: planTotalMaterial
      },
      achievement: {
        ob: obAchievement,
        ore: oreAchievement,
        fuel: fuelAchievement
      }
    };
  }

  /**
   * Validate production data for realistic values
   * 
   * @param data - Production data to validate
   * @returns Validation result with warnings
   */
  static validateProductionData(data: ProductionData): {
    isValid: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];

    // Validate Strip Ratio range (typical mining: 0.5 - 15.0)
    if (data.actual_ore > 0) {
      const sr = data.actual_ob / data.actual_ore;
      if (sr < 0.5 || sr > 15.0) {
        warnings.push(`Strip Ratio ${sr.toFixed(2)} is outside typical range (0.5-15.0)`);
      }
    }

    // Validate Fuel Ratio range (typical mining: 0.5 - 5.0 L/Bcm)
    const totalMaterial = data.actual_ob + (data.actual_ore / MiningCalculationService.ORE_DENSITY_FACTOR);
    if (totalMaterial > 0) {
      const fr = data.actual_fuel / totalMaterial;
      if (fr > 5.0) {
        warnings.push(`Fuel Ratio ${fr.toFixed(2)} L/Bcm seems high (typical: 0.5-5.0)`);
      }
    }

    // Validate negative values
    if (data.actual_ob < 0 || data.actual_ore < 0 || data.actual_fuel < 0) {
      warnings.push('Negative values detected in actual production data');
    }

    // Validate plan vs actual variance (>200% might indicate data entry error)
    if (data.plan_ob > 0 && data.actual_ob > data.plan_ob * 2) {
      warnings.push('Actual OB is >200% of plan - please verify data');
    }
    if (data.plan_ore > 0 && data.actual_ore > data.plan_ore * 2) {
      warnings.push('Actual Ore is >200% of plan - please verify data');
    }

    return {
      isValid: warnings.length === 0,
      warnings
    };
  }

  /**
   * Get formula documentation
   * Returns detailed explanation of all formulas used
   */
  static getFormulaDocumentation(): {
    stripRatio: string;
    fuelRatio: string;
    totalMaterial: string;
    constants: { [key: string]: string };
  } {
    return {
      stripRatio: 'SR = Overburden (Bcm) / Ore (tons) - Ratio of waste to ore',
      fuelRatio: 'FR = Fuel (L) / (OB (Bcm) + (Ore (tons) / 3.39)) - Fuel consumption per Bcm of material',
      totalMaterial: 'Total Material = OB (Bcm) + (Ore (tons) / 3.39) - Total material moved in Bcm',
      constants: {
        'ORE_DENSITY_FACTOR': '3.39 - Conversion factor from ore tons to Bcm equivalent',
        'PRECISION_DECIMALS': '4 - Decimal precision for ratio calculations'
      }
    };
  }

  /**
   * Batch calculate metrics for multiple records
   * 
   * @param dataArray - Array of production data
   * @returns Array of calculated metrics
   */
  static batchCalculateMiningMetrics(dataArray: ProductionData[]): MiningMetrics[] {
    return dataArray.map(data => MiningCalculationService.calculateMiningMetrics(data));
  }

  /**
   * Calculate period aggregated metrics
   * 
   * @param dataArray - Array of production data for the period
   * @returns Aggregated metrics for the period
   */
  static calculatePeriodMetrics(dataArray: ProductionData[]): MiningMetrics {
    const totals = dataArray.reduce((acc, data) => ({
      actual_ob: acc.actual_ob + data.actual_ob,
      plan_ob: acc.plan_ob + data.plan_ob,
      actual_ore: acc.actual_ore + data.actual_ore,
      plan_ore: acc.plan_ore + data.plan_ore,
      actual_fuel: acc.actual_fuel + data.actual_fuel,
      plan_fuel: acc.plan_fuel + data.plan_fuel
    }), {
      actual_ob: 0, plan_ob: 0,
      actual_ore: 0, plan_ore: 0,
      actual_fuel: 0, plan_fuel: 0
    });

    return MiningCalculationService.calculateMiningMetrics(totals);
  }
}

export default MiningCalculationService;
