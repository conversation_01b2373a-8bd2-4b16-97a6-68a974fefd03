import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  StyleSheet,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Layout } from '../constants';

interface PrivacySettingsScreenProps {
  navigation: any;
}

interface PrivacySettings {
  shareAnalytics: boolean;
  shareUsageData: boolean;
  shareLocationData: boolean;
  shareProfileData: boolean;
  allowDataExport: boolean;
  enableActivityLogging: boolean;
  shareWithThirdParty: boolean;
}

const PrivacySettingsScreen: React.FC<PrivacySettingsScreenProps> = ({ navigation }) => {
  const [settings, setSettings] = useState<PrivacySettings>({
    shareAnalytics: false,
    shareUsageData: false,
    shareLocationData: true, // Required for mining operations
    shareProfileData: false,
    allowDataExport: true,
    enableActivityLogging: true, // Required for audit trail
    shareWithThirdParty: false,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('privacySettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading privacy settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (newSettings: PrivacySettings) => {
    try {
      await AsyncStorage.setItem('privacySettings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const handleToggle = (key: keyof PrivacySettings) => {
    // Prevent disabling critical settings
    if ((key === 'shareLocationData' || key === 'enableActivityLogging') && settings[key]) {
      Alert.alert(
        'Required Setting',
        `This setting is required for mining operations and safety compliance. It cannot be disabled.`,
        [{ text: 'OK' }]
      );
      return;
    }

    const newSettings = {
      ...settings,
      [key]: !settings[key],
    };
    saveSettings(newSettings);
  };

  const handleDataExport = () => {
    Alert.alert(
      'Export Personal Data',
      'We will prepare your personal data export and send it to your registered email address within 7 business days.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Request Export',
          onPress: () => {
            Alert.alert(
              'Export Requested',
              'Your data export request has been submitted. You will receive an email confirmation shortly.'
            );
          },
        },
      ]
    );
  };

  const handleDataDeletion = () => {
    Alert.alert(
      'Delete Personal Data',
      'This will permanently delete all your personal data. Your account will be deactivated and cannot be recovered. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Data',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Contact Administrator',
              'For security and compliance reasons, data deletion requests must be processed by an administrator. Please contact your system administrator.',
              [{ text: 'OK' }]
            );
          },
        },
      ]
    );
  };

  const PrivacyItem = ({ 
    title, 
    description, 
    value, 
    onToggle, 
    icon,
    required = false,
    warning = false
  }: {
    title: string;
    description: string;
    value: boolean;
    onToggle: () => void;
    icon: string;
    required?: boolean;
    warning?: boolean;
  }) => (
    <View style={styles.privacyItem}>
      <View style={[
        styles.privacyIcon, 
        { backgroundColor: (required ? Colors.warning : warning ? Colors.error : Colors.primary) + '20' }
      ]}>
        <Ionicons 
          name={icon as any} 
          size={20} 
          color={required ? Colors.warning : warning ? Colors.error : Colors.primary} 
        />
      </View>
      <View style={styles.privacyContent}>
        <View style={styles.titleRow}>
          <Text style={styles.privacyTitle}>{title}</Text>
          {required && (
            <View style={styles.requiredBadge}>
              <Text style={styles.requiredText}>Required</Text>
            </View>
          )}
        </View>
        <Text style={styles.privacyDescription}>{description}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: Colors.border, true: Colors.primary + '40' }}
        thumbColor={value ? Colors.primary : Colors.textLight}
        disabled={required}
      />
    </View>
  );

  const ActionItem = ({ 
    title, 
    description, 
    onPress, 
    icon,
    danger = false
  }: {
    title: string;
    description: string;
    onPress: () => void;
    icon: string;
    danger?: boolean;
  }) => (
    <TouchableOpacity style={styles.actionItem} onPress={onPress}>
      <View style={[
        styles.actionIcon, 
        { backgroundColor: (danger ? Colors.error : Colors.primary) + '20' }
      ]}>
        <Ionicons 
          name={icon as any} 
          size={20} 
          color={danger ? Colors.error : Colors.primary} 
        />
      </View>
      <View style={styles.actionContent}>
        <Text style={[styles.actionTitle, danger && styles.dangerText]}>{title}</Text>
        <Text style={styles.actionDescription}>{description}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading privacy settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy Settings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Data Sharing */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Sharing</Text>
          
          <PrivacyItem
            title="Share Analytics Data"
            description="Help improve the app by sharing anonymous usage analytics"
            value={settings.shareAnalytics}
            onToggle={() => handleToggle('shareAnalytics')}
            icon="analytics"
          />

          <PrivacyItem
            title="Share Usage Data"
            description="Share how you use the app to help us improve features"
            value={settings.shareUsageData}
            onToggle={() => handleToggle('shareUsageData')}
            icon="bar-chart"
          />

          <PrivacyItem
            title="Share Location Data"
            description="Required for mining operations and safety tracking"
            value={settings.shareLocationData}
            onToggle={() => handleToggle('shareLocationData')}
            icon="location"
            required={true}
          />

          <PrivacyItem
            title="Share Profile Data"
            description="Allow sharing of profile information with team members"
            value={settings.shareProfileData}
            onToggle={() => handleToggle('shareProfileData')}
            icon="person"
          />
        </View>

        {/* Data Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          
          <PrivacyItem
            title="Allow Data Export"
            description="Enable personal data export requests"
            value={settings.allowDataExport}
            onToggle={() => handleToggle('allowDataExport')}
            icon="download"
          />

          <PrivacyItem
            title="Activity Logging"
            description="Required for audit trail and compliance"
            value={settings.enableActivityLogging}
            onToggle={() => handleToggle('enableActivityLogging')}
            icon="list"
            required={true}
          />

          <PrivacyItem
            title="Third-Party Sharing"
            description="Allow sharing data with approved third-party services"
            value={settings.shareWithThirdParty}
            onToggle={() => handleToggle('shareWithThirdParty')}
            icon="share"
            warning={true}
          />
        </View>

        {/* Data Rights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Data Rights</Text>
          
          <ActionItem
            title="Export My Data"
            description="Download a copy of all your personal data"
            onPress={handleDataExport}
            icon="download-outline"
          />

          <ActionItem
            title="Delete My Data"
            description="Permanently delete all your personal data"
            onPress={handleDataDeletion}
            icon="trash-outline"
            danger={true}
          />
        </View>

        {/* Privacy Information */}
        <View style={styles.infoSection}>
          <Ionicons name="shield-checkmark" size={20} color={Colors.info} />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>Your Privacy Matters</Text>
            <Text style={styles.infoText}>
              We are committed to protecting your privacy and personal data. 
              Some settings are required for mining operations and safety compliance. 
              You can request data export or deletion at any time.
            </Text>
          </View>
        </View>

        {/* Compliance Notice */}
        <View style={styles.complianceSection}>
          <Text style={styles.complianceTitle}>Compliance & Legal</Text>
          <Text style={styles.complianceText}>
            This application complies with applicable data protection regulations. 
            Some data collection is mandatory for mining safety and regulatory compliance. 
            For questions about data handling, contact your system administrator.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  placeholder: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  section: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
    marginTop: Layout.spacing.lg,
  },
  privacyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  privacyIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  privacyContent: {
    flex: 1,
    marginRight: Layout.spacing.md,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.xs,
  },
  privacyTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
    flex: 1,
  },
  requiredBadge: {
    backgroundColor: Colors.warning,
    paddingHorizontal: Layout.spacing.xs,
    paddingVertical: 2,
    borderRadius: 4,
  },
  requiredText: {
    fontSize: Layout.fontSize.xs,
    color: Colors.white,
    fontWeight: '600',
  },
  privacyDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  actionDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  dangerText: {
    color: Colors.error,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.info + '20',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.lg,
  },
  infoContent: {
    flex: 1,
    marginLeft: Layout.spacing.sm,
  },
  infoTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.info,
    marginBottom: Layout.spacing.xs,
  },
  infoText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.info,
    lineHeight: 20,
  },
  complianceSection: {
    backgroundColor: Colors.backgroundLight,
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.xl,
  },
  complianceTitle: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  complianceText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    lineHeight: 18,
  },
  loadingText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
  },
});

export default PrivacySettingsScreen;
