import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import ProfileScreenWithAuth from '../screens/ProfileScreenWithAuth';
import EditProfileScreen from '../screens/EditProfileScreen';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
import SecuritySettingsScreen from '../screens/SecuritySettingsScreen';
import ThemeSettingsScreen from '../screens/ThemeSettingsScreen';
import PrivacySettingsScreen from '../screens/PrivacySettingsScreen';
import HelpSupportScreen from '../screens/HelpSupportScreen';
import AboutScreen from '../screens/AboutScreen';
import HeaderImageSettingsScreen from '../screens/HeaderImageSettingsScreen';
import DashboardHeaderManagementScreen from '../screens/DashboardHeaderManagementScreen';

export type ProfileStackParamList = {
  ProfileMain: undefined;
  EditProfile: undefined;
  NotificationSettings: undefined;
  SecuritySettings: undefined;
  ThemeSettings: undefined;
  PrivacySettings: undefined;
  HeaderImageSettings: undefined;
  DashboardHeaderManagement: undefined;
  HelpSupport: undefined;
  About: undefined;
};

const Stack = createStackNavigator<ProfileStackParamList>();

const ProfileStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: 'transparent' },
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen
        name="ProfileMain"
        component={ProfileScreenWithAuth}
        options={{
          title: 'Profile',
        }}
      />
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          title: 'Edit Profile',
        }}
      />
      <Stack.Screen
        name="NotificationSettings"
        component={NotificationSettingsScreen}
        options={{
          title: 'Notifications',
        }}
      />
      <Stack.Screen
        name="SecuritySettings"
        component={SecuritySettingsScreen}
        options={{
          title: 'Security',
        }}
      />
      <Stack.Screen
        name="ThemeSettings"
        component={ThemeSettingsScreen}
        options={{
          title: 'Theme Settings',
        }}
      />
      <Stack.Screen
        name="PrivacySettings"
        component={PrivacySettingsScreen}
        options={{
          title: 'Privacy Settings',
        }}
      />
      <Stack.Screen
        name="HeaderImageSettings"
        component={HeaderImageSettingsScreen}
        options={{
          title: 'Header Image',
        }}
      />
      <Stack.Screen
        name="DashboardHeaderManagement"
        component={DashboardHeaderManagementScreen}
        options={{
          title: 'Dashboard Headers',
        }}
      />
      <Stack.Screen
        name="HelpSupport"
        component={HelpSupportScreen}
        options={{
          title: 'Help & Support',
        }}
      />
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          title: 'About',
        }}
      />
    </Stack.Navigator>
  );
};

export default ProfileStackNavigator;
