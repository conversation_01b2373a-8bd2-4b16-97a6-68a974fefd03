// Import polyfills first
import './src/utils/polyfills';

import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { StyleSheet, View, Platform, Text } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as NavigationBar from 'expo-navigation-bar';
import 'react-native-gesture-handler';
import TabNavigator from './src/navigation/TabNavigator';
import { LoginScreen } from './src/screens/LoginScreen';
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { Colors } from './src/constants/colors';
import { testEasingFunctions } from './src/utils/easingTest';

// Main App Component with Authentication Support
const AppContent: React.FC = () => {
  const { user, loading, isTestingMode } = useAuth();

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!user && !isTestingMode) {
    return <LoginScreen />;
  }

  return (
    <View style={styles.container}>
      <TabNavigator />
    </View>
  );
};

export default function App() {
  useEffect(() => {
    // Test easing functions on app startup
    testEasingFunctions();

    // Hide the Android system navigation bar for immersive experience
    if (Platform.OS === 'android') {
      NavigationBar.setVisibilityAsync('hidden');
    }
  }, []);

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <NavigationContainer>
            <AppContent />
          </NavigationContainer>
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? 0 : 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.textPrimary,
  },
});
