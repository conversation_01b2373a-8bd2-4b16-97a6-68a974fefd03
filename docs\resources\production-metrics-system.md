# Production Metrics System

## Overview

The Production Metrics System is a comprehensive solution for tracking, analyzing, and visualizing mining production data. It consists of a robust database schema, a sophisticated React Native frontend component (`ProductionOverviewScreen`), and advanced analytics capabilities.

### Key Features

- **Real-time Production Tracking**: Daily monitoring of key production metrics
- **Plan vs Actual Analysis**: Comprehensive comparison between planned and actual performance
- **Advanced Analytics**: Strip ratio, fuel efficiency, and material movement calculations
- **Interactive Visualizations**: Dynamic charts with multiple time period views
- **Performance Optimization**: Memoized components and efficient data handling
- **Export Capabilities**: CSV and JSON data export functionality

### Core Metrics Tracked

1. **Overburden (OB) Volume** - Material removal in Bank Cubic Meters (Bcm)
2. **Ore Production** - Ore extraction in tons
3. **Rain Impact Hours** - Weather-related operational delays
4. **Slippery Conditions Hours** - Safety-related operational constraints
5. **Fuel Consumption** - Fuel usage in liters
6. **Strip Ratio** - OB to Ore ratio for efficiency analysis
7. **Fuel Ratio** - Fuel consumption per unit of material moved
8. **Total Material Moved** - Combined OB and ore movement in Bcm

Each metric tracks both planned and actual values for comprehensive performance analysis and variance reporting.

## Frontend Implementation

### ProductionOverviewScreen Component

The `ProductionOverviewScreen` (`src/screens/ProductionOverviewScreen.tsx`) is the main interface for production metrics visualization and analysis.

#### Component Architecture

```typescript
// Main component with performance optimizations
const ProductionOverviewScreen: React.FC<ProductionOverviewScreenProps> = memo(({ navigation }) => {
  // State management for metrics, loading, and date ranges
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('Daily');
  const [productionMetrics, setProductionMetrics] = useState<ProductionMetric[]>([]);
  const [dailyData, setDailyData] = useState<any[]>([]);
  const [customDateRange, setCustomDateRange] = useState<{start: Date | null, end: Date | null}>();

  // Data service integration
  const [dataService] = useState(() => ProductionDataService.getInstance());
});
```

#### Key Features

##### 1. **Memoized Metric Cards**
```typescript
const MetricCard = memo(({
  metric,
  index,
  totalCards,
  formatMetricValue,
  getProgressPercentage,
  getProgressColor
}) => {
  // Optimized rendering with progress indicators
  const progressPercentage = getProgressPercentage(metric.actual, metric.plan);
  const progressColor = getProgressColor(progressPercentage);

  return (
    <View style={styles.metricCard}>
      {/* Progress visualization and metric display */}
    </View>
  );
});
```

##### 2. **Advanced Calculations**
```typescript
// Strip Ratio Calculation
const stripRatio = totalActualOre > 0 ? totalActualOb / totalActualOre : 0;

// Fuel Ratio using mining industry formula
// FR = Fuel Usage / (Volume OB + (Volume Ore / 3.39))
const totalActualMaterial = totalActualOb + (totalActualOre / 3.39);
const fuelRatio = totalActualMaterial > 0 ? totalActualFuel / totalActualMaterial : 0;

// Total Material Moved in Bcm
const totalMaterialMovedBcm = totalActualOb + (totalActualOre / 3.39);
```

##### 3. **Time Period Management**
```typescript
const getDateRangePreset = useCallback((preset: string) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (preset) {
    case 'today':
      return { start: today, end: today };
    case 'last7days':
      const last7Start = new Date(today);
      last7Start.setDate(today.getDate() - 6);
      return { start: last7Start, end: today };
    case 'thisMonth':
      const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      return { start: thisMonthStart, end: today };
    // Additional presets...
  }
}, []);
```

##### 4. **Data Export Functionality**
```typescript
const exportData = useCallback(async (format: 'csv' | 'json') => {
  const exportData = {
    period: selectedPeriod,
    exportDate: new Date().toISOString(),
    metrics: productionMetrics.map(metric => ({
      id: metric.id,
      name: metric.name,
      plan: metric.plan,
      actual: metric.actual,
      unit: metric.unit,
      achievement: getProgressPercentage(metric.actual, metric.plan),
      trend: getTrendPercentage(metric.id)
    }))
  };

  if (format === 'csv') {
    const csvHeader = 'Metric,Plan,Actual,Unit,Achievement %,Trend %\n';
    const csvData = exportData.metrics.map(m =>
      `${m.name},${m.plan},${m.actual},${m.unit},${m.achievement},${m.trend}`
    ).join('\n');
    // Export implementation
  }
}, [selectedPeriod, productionMetrics]);
```

#### Performance Optimizations

1. **Memoization**: Components and calculations are memoized to prevent unnecessary re-renders
2. **Debounced Loading**: Data loading is debounced to prevent excessive API calls
3. **Ref-based Loading Control**: Prevents multiple simultaneous data loading operations
4. **Efficient State Management**: Minimal state updates with proper dependency arrays

#### User Interface Features

1. **Interactive Metric Cards**: Visual progress indicators with color-coded performance
2. **Dynamic Charts**: Real-time chart updates with multiple visualization options
3. **Date Range Selection**: Flexible date range picker with preset options
4. **Period Switching**: Toggle between Daily, Weekly, Monthly, and Yearly views
5. **Pull-to-Refresh**: Intuitive data refresh functionality
6. **Loading States**: Skeleton screens and loading indicators

## Database Schema

### Table: `daily_production_metrics`

```sql
CREATE TABLE daily_production_metrics (
    id UUID PRIMARY KEY,
    date DATE NOT NULL,
    monthly VARCHAR(20) NOT NULL,
    week INTEGER NOT NULL,
    
    -- Overburden metrics
    actual_ob DECIMAL(12,2) DEFAULT 0,
    plan_ob DECIMAL(12,2) DEFAULT 0,
    
    -- Ore metrics
    actual_ore DECIMAL(12,2) DEFAULT 0,
    plan_ore DECIMAL(12,2) DEFAULT 0,
    
    -- Rain metrics
    actual_rain DECIMAL(8,2) DEFAULT 0,
    plan_rain DECIMAL(8,2) DEFAULT 0,
    
    -- Slippery conditions metrics
    actual_slippery DECIMAL(8,2) DEFAULT 0,
    plan_slippery DECIMAL(8,2) DEFAULT 0,
    
    -- Fuel metrics
    actual_fuel DECIMAL(10,2) DEFAULT 0,
    plan_fuel DECIMAL(10,2) DEFAULT 0,
    
    location_id UUID REFERENCES locations(id) NOT NULL,
    created_by UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_date_location UNIQUE(date, location_id)
);
```

### Indexes and Constraints

- **Primary Key**: `id` (UUID)
- **Unique Constraint**: `(date, location_id)` - prevents duplicate entries for the same date and location
- **Indexes**: Optimized for date-based queries, monthly grouping, and location filtering
- **Check Constraints**: Ensures all metric values are non-negative
- **Row Level Security**: Implemented for data access control

## Data Import

### CSV Format

The system accepts CSV data with the following columns:

```csv
Monthly,Week,Date,Actual OB,Plan OB,Actual ORE,Plan ORE,Actual Rain,Plan Rain,Actual Slippery,Plan Slippery,Actual Fuel,Plan Fuel
December 2023,49,2023-12-01,8500,10000,5200,6000,2.5,2.0,1.0,0.5,2400,2200
```

### Import Process

1. **Validation**: Data is validated for correct formats and ranges
2. **Transformation**: CSV data is converted to database format
3. **Batch Processing**: Large datasets are imported in batches to prevent timeouts
4. **Error Handling**: Detailed error reporting for failed imports
5. **Upsert Logic**: Existing records are updated, new records are inserted

### Using the Import Utility

```typescript
import { ProductionDataImporter } from '../utils/productionDataImporter';

// Import CSV data
const result = await ProductionDataImporter.importCSVData(
  csvContent,
  locationId,
  (progress, message) => console.log(`${progress}%: ${message}`)
);

console.log(`Imported ${result.success} records`);
```

## Data Flow Architecture

### Service Layer Integration

The ProductionOverviewScreen integrates with the `ProductionDataService` for all data operations:

```typescript
// Service initialization
const [dataService] = useState(() => ProductionDataService.getInstance());

// Data loading with error handling
const loadSampleData = async () => {
  if (isLoadingRef.current) return;

  isLoadingRef.current = true;
  setLoading(true);

  try {
    await dataService.initialize();

    const { startDate, endDate } = getEffectiveDateRange();
    const productionData = await dataService.getProductionDataByDateRange(startDate, endDate);

    if (productionData.length === 0) {
      console.log('⚠️ No production data found in database');
      setProductionMetrics([]);
      setDailyData([]);
      return;
    }

    setDailyData(productionData);
    const metrics = generateAggregatedMetricsFromLocal(
      productionData,
      selectedPeriod,
      (data) => getCardDataByPeriod(data, selectedPeriod)
    );
    setProductionMetrics(metrics);

  } catch (error) {
    console.error('❌ Failed to load production data:', error);
  } finally {
    setLoading(false);
    isLoadingRef.current = false;
  }
};
```

### Data Aggregation Logic

The system includes sophisticated data aggregation for different time periods:

```typescript
const generateAggregatedMetricsFromLocal = (
  dailyData: any[],
  period: string,
  getCardDataByPeriod?: (data: any[]) => any[]
): ProductionMetric[] => {
  if (!dailyData || dailyData.length === 0) {
    return [];
  }

  const cardData = getCardDataByPeriod ? getCardDataByPeriod(dailyData) : dailyData;

  // Aggregate totals
  const totalActualOb = cardData.reduce((sum, item) => sum + (item.actual_ob || 0), 0);
  const totalPlanOb = cardData.reduce((sum, item) => sum + (item.plan_ob || 0), 0);
  const totalActualOre = cardData.reduce((sum, item) => sum + (item.actual_ore || 0), 0);
  const totalPlanOre = cardData.reduce((sum, item) => sum + (item.plan_ore || 0), 0);
  const totalActualFuel = cardData.reduce((sum, item) => sum + (item.actual_fuel || 0), 0);
  const totalPlanFuel = cardData.reduce((sum, item) => sum + (item.plan_fuel || 0), 0);

  // Calculate derived metrics
  const stripRatio = totalActualOre > 0 ? totalActualOb / totalActualOre : 0;
  const planStripRatio = totalPlanOre > 0 ? totalPlanOb / totalPlanOre : 0;

  // Mining industry fuel ratio formula: FR = Fuel Usage / (Volume OB + (Volume Ore / 3.39))
  const totalActualMaterial = totalActualOb + (totalActualOre / 3.39);
  const totalPlanMaterial = totalPlanOb + (totalPlanOre / 3.39);
  const fuelRatio = totalActualMaterial > 0 ? totalActualFuel / totalActualMaterial : 0;
  const planFuelRatio = totalPlanMaterial > 0 ? totalPlanFuel / totalPlanMaterial : 0;

  // Return structured metrics array
  return [
    {
      id: 'overburden',
      name: 'Overburden',
      actual: totalActualOb,
      plan: totalPlanOb,
      unit: 'Bcm',
      icon: 'layers-outline',
      color: Colors.primary
    },
    {
      id: 'ore_production',
      name: 'Ore Production',
      actual: totalActualOre,
      plan: totalPlanOre,
      unit: 'tons',
      icon: 'cube-outline',
      color: Colors.secondary
    },
    {
      id: 'strip_ratio',
      name: 'Strip Ratio',
      actual: stripRatio,
      plan: planStripRatio,
      unit: 'ratio',
      icon: 'analytics-outline',
      color: Colors.warning
    },
    {
      id: 'fuel_ratio',
      name: 'Fuel Ratio',
      actual: fuelRatio,
      plan: planFuelRatio,
      unit: 'L/Bcm',
      icon: 'speedometer-outline',
      color: Colors.info
    },
    {
      id: 'total_material',
      name: 'Total Material Moved',
      actual: totalActualMaterial,
      plan: totalPlanMaterial,
      unit: 'Bcm',
      icon: 'swap-horizontal-outline',
      color: Colors.accent
    }
  ];
};
```

### Period-Based Data Filtering

The system supports multiple time period views with intelligent data filtering:

```typescript
const getCardDataByPeriod = (dailyData: any[], period: TimePeriod) => {
  // Custom date range takes precedence
  if (customDateRange.start && customDateRange.end) {
    const startStr = customDateRange.start.toISOString().split('T')[0];
    const endStr = customDateRange.end.toISOString().split('T')[0];

    return dailyData.filter(item => {
      return item.date >= startStr && item.date <= endStr;
    });
  }

  const now = new Date();

  switch (period) {
    case 'Daily':
      // Get today's data or fallback to most recent
      const todayStr = now.toISOString().split('T')[0];
      let dailyResult = dailyData.filter(item => item.date === todayStr);

      if (dailyResult.length === 0 && dailyData.length > 0) {
        const sortedData = [...dailyData].sort((a, b) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        dailyResult = [sortedData[0]];
      }
      return dailyResult;

    case 'Weekly':
      // Current week (Monday to Sunday)
      const currentWeekStart = new Date(now);
      const dayOfWeek = now.getDay();
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      currentWeekStart.setDate(now.getDate() - daysToMonday);

      const currentWeekEnd = new Date(currentWeekStart);
      currentWeekEnd.setDate(currentWeekStart.getDate() + 6);

      return dailyData.filter(item => {
        const itemDate = new Date(item.date);
        return itemDate >= currentWeekStart && itemDate <= currentWeekEnd;
      });

    case 'Monthly':
      // Current month data
      const currentMonth = now.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
      return dailyData.filter(item => item.monthly === currentMonth);

    case 'Yearly':
      // Current year data
      const currentYear = now.getFullYear();
      return dailyData.filter(item => {
        const itemYear = new Date(item.date).getFullYear();
        return itemYear === currentYear;
      });

    default:
      return dailyData;
  }
};
```

## API Functions

### DatabaseService Methods

#### Create Single Record
```typescript
await DatabaseService.createDailyProductionMetrics({
  date: '2024-01-15',
  monthly: 'January 2024',
  week: 3,
  actual_ob: 8500,
  plan_ob: 10000,
  actual_ore: 5200,
  plan_ore: 6000,
  // ... other metrics
  location_id: 'location-uuid'
});
```

#### Bulk Import
```typescript
await DatabaseService.bulkCreateDailyProductionMetrics(metricsArray);
```

#### Query Data
```typescript
// Get data for date range
const metrics = await DatabaseService.getDailyProductionMetrics(
  '2024-01-01',  // start date
  '2024-01-31',  // end date
  locationId     // optional location filter
);

// Get aggregated metrics
const aggregated = await DatabaseService.getProductionMetricsAggregated(
  'monthly',     // period: daily, weekly, monthly, yearly
  '2024-01-01',  // start date
  '2024-01-31',  // end date
  locationId     // optional location filter
);
```

#### Update Record
```typescript
await DatabaseService.updateDailyProductionMetrics(recordId, {
  actual_ob: 9000,
  notes: 'Updated production figures'
});
```

## Integration with Existing System

### Backward Compatibility

The new system maintains full backward compatibility with the existing `production_reports` table:

- Existing ProductionOverviewScreen automatically tries the new system first
- Falls back to legacy system if new data is not available
- No changes required to existing production report workflows

### Chart Integration

The ProductionOverviewScreen has been updated to use the new detailed metrics:

```typescript
// The screen automatically detects and uses the new data format
const data = await DatabaseService.getProductionMetricsAggregated(period);

// Charts display plan vs actual comparisons
const metrics: ProductionMetric[] = [
  {
    name: 'Overburden (OB) Volume',
    plan: data.overburdenVolume.target,
    actual: data.overburdenVolume.actual,
    achievementPercentage: data.overburdenVolume.achievementPercentage,
    // ...
  }
];
```

## Data Validation

### Input Validation

- **Date Format**: Must be valid ISO date (YYYY-MM-DD)
- **Week Number**: Must be between 1-53
- **Numeric Values**: Must be non-negative numbers
- **Monthly Format**: Free text for grouping (e.g., "December 2023")

### Business Rules

- Each location can have only one record per date
- All metric values must be non-negative
- Plan values should generally be positive for meaningful comparisons

## Performance Considerations

### Indexing Strategy

- **Date-based queries**: Primary index on `date` column
- **Location filtering**: Index on `location_id`
- **Time range queries**: Composite index on `(date, location_id)`
- **Monthly grouping**: Index on `monthly` column

### Query Optimization

- Use date ranges to limit result sets
- Leverage location filtering when possible
- Batch operations for bulk data processing

## Security

### Row Level Security (RLS)

- **Read Access**: All authenticated users can view production metrics
- **Write Access**: Only authenticated users can insert new records
- **Update Access**: Users can update their own records, supervisors can update any
- **Delete Access**: Only supervisors and managers can delete records

### Data Privacy

- All operations require authentication
- Audit trail maintained through `created_by` and timestamp fields
- Soft delete capability through status flags (if needed)

## Migration Guide

### Step 1: Apply Database Migration

```bash
# Apply the migration
npm run migrate:production-metrics

# Or manually run the migration script
node scripts/apply-production-metrics-migration.ts
```

### Step 2: Import Historical Data

```bash
# Import sample data
node scripts/import-sample-production-data.ts

# Or import your CSV file
node scripts/import-production-data.ts --file your-data.csv --location location-id
```

### Step 3: Verify Integration

1. Open the ProductionOverviewScreen
2. Verify that new metrics are displayed
3. Check that charts show plan vs actual comparisons
4. Test different time periods (daily, weekly, monthly, yearly)

## Troubleshooting

### Common Issues

1. **Import Failures**: Check CSV format and data validation errors
2. **Permission Errors**: Ensure user has proper authentication and roles
3. **Performance Issues**: Use date ranges and location filters for large datasets
4. **Chart Display Issues**: Verify data aggregation and metric calculations

### Debug Queries

```sql
-- Check table structure
\d daily_production_metrics

-- View recent records
SELECT * FROM daily_production_metrics 
ORDER BY date DESC LIMIT 10;

-- Check aggregated data
SELECT 
  monthly,
  COUNT(*) as record_count,
  SUM(actual_ob) as total_actual_ob,
  SUM(plan_ob) as total_plan_ob
FROM daily_production_metrics 
GROUP BY monthly 
ORDER BY monthly;
```

## Performance Optimizations

### Component-Level Optimizations

The ProductionOverviewScreen implements several performance optimization strategies:

#### 1. **Memoization**
```typescript
// Memoized metric cards to prevent unnecessary re-renders
const MetricCard = memo(({ metric, index, totalCards, formatMetricValue, getProgressPercentage, getProgressColor }) => {
  const progressPercentage = getProgressPercentage(metric.actual, metric.plan);
  const progressColor = getProgressColor(progressPercentage);

  return (
    <View style={styles.metricCard}>
      {/* Card content */}
    </View>
  );
});

// Memoized calculations
const memoizedMetrics = useMemo(() => {
  return productionMetrics.map(metric => ({
    ...metric,
    progressPercentage: getProgressPercentage(metric.actual, metric.plan),
    progressColor: getProgressColor(getProgressPercentage(metric.actual, metric.plan))
  }));
}, [productionMetrics, getProgressPercentage, getProgressColor]);
```

#### 2. **Debounced Data Loading**
```typescript
// Prevent excessive API calls during rapid date range changes
useEffect(() => {
  const timeoutId = setTimeout(() => {
    loadSampleData();
  }, 300); // 300ms debounce

  return () => clearTimeout(timeoutId);
}, [customDateRange, selectedPeriod]);
```

#### 3. **Loading State Management**
```typescript
// Prevent multiple simultaneous data loading operations
const isLoadingRef = useRef(false);

const loadSampleData = async () => {
  if (isLoadingRef.current) return;

  isLoadingRef.current = true;
  // Data loading logic
  isLoadingRef.current = false;
};
```

### Best Practices

#### 1. **Error Handling**
```typescript
// Comprehensive error handling with user feedback
const loadSampleData = async () => {
  try {
    setLoading(true);
    const data = await dataService.getProductionDataByDateRange(startDate, endDate);

    if (!data || data.length === 0) {
      setProductionMetrics([]);
      console.log('No data available for selected period');
      return;
    }

    processData(data);
  } catch (error) {
    console.error('Data loading failed:', error);
  } finally {
    setLoading(false);
  }
};
```

#### 2. **Data Validation**
```typescript
// Validate data before processing
const validateProductionData = (data) => {
  return data.filter(item => {
    return item.date &&
           typeof item.actual_ob === 'number' &&
           typeof item.actual_ore === 'number' &&
           item.actual_ob >= 0 &&
           item.actual_ore >= 0;
  });
};
```

### Troubleshooting

#### Common Issues

1. **No data showing in charts**
   - Verify database connection
   - Check if sample data was imported correctly
   - Ensure date ranges include available data
   - Check console for data loading errors

2. **Performance issues**
   - Monitor component re-renders with React DevTools
   - Check for memory leaks in useEffect cleanup
   - Verify memoization is working correctly
   - Consider implementing data pagination

3. **Incorrect calculations**
   - Verify that plan and actual values are properly set
   - Check for null or undefined values in calculations
   - Ensure proper data types (numbers vs strings)
   - Validate fuel ratio formula implementation

#### Debug Tips

- Enable console logging to track data flow
- Use React DevTools to monitor component re-renders
- Check network requests in browser developer tools
- Verify database queries return expected results
- Monitor performance with React Native Flipper

## Future Enhancements

### Planned Features

1. **Real-time Data Sync**: Automatic data updates from mining equipment
2. **Advanced Analytics**: Trend analysis and predictive modeling
3. **Custom Metrics**: User-defined production metrics
4. **Export Functionality**: Export data to various formats (Excel, PDF)
5. **Dashboard Widgets**: Customizable production dashboards

### API Extensions

1. **Bulk Update Operations**: Update multiple records efficiently
2. **Data Aggregation APIs**: Pre-calculated summaries and KPIs
3. **Notification System**: Alerts for target deviations
4. **Integration APIs**: Connect with external mining systems

## Related Documentation

- [Dashboard Screen](features/dashboard-screen.md) - Main dashboard implementation
- [Architecture Overview](architecture/overview.md) - System architecture
- [Navigation System](architecture/navigation.md) - Navigation patterns
- [API Endpoints](api/endpoints.md) - Backend API documentation
- [Design System](design/design-system.md) - UI/UX guidelines
- [Shadow System](development/shadow-system.md) - Cross-platform styling
- [Implementation Progress](implementation-progress.md) - Current development status

## File References

### Frontend Files
- `src/screens/ProductionOverviewScreen.tsx` - Main production metrics screen
- `src/services/ProductionDataService.ts` - Data service layer
- `src/components/charts/DynamicChart.tsx` - Chart visualization component
- `src/types/index.ts` - TypeScript type definitions
- `src/models/Production.ts` - Production data models

### Backend Files
- Database schema: `daily_production_metrics` table
- Migration scripts: Production metrics database setup
- Sample data: Production data import utilities

### Documentation Files
- `docs/production-metrics-system.md` - This comprehensive documentation
- `docs/features/dashboard-screen.md` - Dashboard screen documentation
- `docs/architecture/overview.md` - System architecture overview
