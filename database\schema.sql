-- Mining Operations App Database Schema
-- Created for Supabase PostgreSQL

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create custom types/enums
CREATE TYPE user_role AS ENUM ('supervisor', 'operator', 'safety_officer', 'maintenance_tech', 'admin');
CREATE TYPE location_type AS ENUM ('mine_site', 'processing_plant', 'office', 'warehouse');
CREATE TYPE equipment_type AS ENUM ('excavator', 'dump_truck', 'drill', 'conveyor', 'crusher', 'loader', 'bulldozer', 'grader');
CREATE TYPE equipment_status AS ENUM ('operational', 'maintenance', 'down', 'retired');
CREATE TYPE incident_severity AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE incident_type AS ENUM ('injury', 'near_miss', 'equipment_failure', 'environmental', 'security');
CREATE TYPE incident_status AS ENUM ('reported', 'investigating', 'resolved', 'closed');
CREATE TYPE maintenance_type AS ENUM ('preventive', 'corrective', 'emergency', 'inspection');
CREATE TYPE maintenance_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
CREATE TYPE shift_type AS ENUM ('day', 'night', 'swing');
CREATE TYPE shift_status AS ENUM ('scheduled', 'checked_in', 'checked_out', 'absent');

-- =============================================
-- CORE TABLES
-- =============================================

-- Locations table
CREATE TABLE locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    location_type location_type NOT NULL,
    coordinates POINT, -- PostGIS point for GPS coordinates
    address TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role user_role NOT NULL DEFAULT 'operator',
    location_id UUID REFERENCES locations(id),
    avatar_url TEXT,
    employee_id VARCHAR(50) UNIQUE,
    hire_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Equipment table
CREATE TABLE equipment (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    model VARCHAR(255),
    serial_number VARCHAR(255) UNIQUE,
    manufacturer VARCHAR(255),
    equipment_type equipment_type NOT NULL,
    location_id UUID REFERENCES locations(id),
    status equipment_status DEFAULT 'operational',
    purchase_date DATE,
    last_maintenance_date DATE,
    next_maintenance_due DATE,
    specifications JSONB, -- Flexible storage for equipment specs
    operating_hours INTEGER DEFAULT 0,
    fuel_capacity DECIMAL(10,2),
    max_load_capacity DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Safety incidents table
CREATE TABLE safety_incidents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    incident_number VARCHAR(50) UNIQUE NOT NULL, -- Auto-generated incident number
    reported_by UUID REFERENCES users(id) NOT NULL,
    incident_date TIMESTAMP WITH TIME ZONE NOT NULL,
    location_id UUID REFERENCES locations(id) NOT NULL,
    severity incident_severity NOT NULL,
    incident_type incident_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    corrective_actions TEXT,
    equipment_id UUID REFERENCES equipment(id), -- Optional if equipment involved
    injured_person_name VARCHAR(255), -- If different from reporter
    witnesses TEXT[], -- Array of witness names
    status incident_status DEFAULT 'reported',
    investigation_notes TEXT,
    resolved_date TIMESTAMP WITH TIME ZONE,
    attachments TEXT[], -- URLs to uploaded files
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Production reports table
CREATE TABLE production_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    report_number VARCHAR(50) UNIQUE NOT NULL,
    created_by UUID REFERENCES users(id) NOT NULL,
    report_date DATE NOT NULL,
    shift shift_type NOT NULL,
    location_id UUID REFERENCES locations(id) NOT NULL,
    production_metrics JSONB NOT NULL, -- Flexible metrics storage
    equipment_used UUID[], -- Array of equipment IDs
    total_tonnage DECIMAL(12,2),
    operating_hours DECIMAL(8,2),
    downtime_hours DECIMAL(8,2),
    fuel_consumed DECIMAL(10,2),
    notes TEXT,
    weather_conditions VARCHAR(255),
    crew_size INTEGER,
    approved_by UUID REFERENCES users(id), -- Supervisor approval
    approved_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Maintenance records table
CREATE TABLE maintenance_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    maintenance_number VARCHAR(50) UNIQUE NOT NULL,
    equipment_id UUID REFERENCES equipment(id) NOT NULL,
    performed_by UUID REFERENCES users(id) NOT NULL,
    maintenance_type maintenance_type NOT NULL,
    scheduled_date DATE NOT NULL,
    completed_date DATE,
    description TEXT NOT NULL,
    parts_used JSONB, -- Array of parts with quantities and costs
    labor_hours DECIMAL(8,2),
    total_cost DECIMAL(10,2),
    next_maintenance_due DATE,
    status maintenance_status DEFAULT 'scheduled',
    notes TEXT,
    attachments TEXT[], -- URLs to uploaded files
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Shifts table
CREATE TABLE shifts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    location_id UUID REFERENCES locations(id) NOT NULL,
    shift_type shift_type NOT NULL,
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    supervisor_id UUID REFERENCES users(id) NOT NULL,
    planned_crew_size INTEGER DEFAULT 0,
    actual_crew_size INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(location_id, shift_date, shift_type)
);

-- User shifts (many-to-many relationship)
CREATE TABLE user_shifts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) NOT NULL,
    shift_id UUID REFERENCES shifts(id) NOT NULL,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    status shift_status DEFAULT 'scheduled',
    overtime_hours DECIMAL(4,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, shift_id)
);
