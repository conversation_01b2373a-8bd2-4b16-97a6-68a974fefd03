-- =====================================================
-- Mining Operations Database - Daily Mining Report
-- =====================================================
-- File: 03-daily-mining-report.sql
-- Description: Main daily mining report table with production data and ratios
-- Dependencies: 01-core-setup.sql, 02-production-calendar.sql
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- DAILY MINING REPORT TABLE
-- =====================================================
CREATE TABLE daily_mining_report (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Report Identification
    report_number VARCHAR(50) UNIQUE GENERATED ALWAYS AS (
        generate_report_number(location, report_date)
    ) STORED,
    
    -- Location & Time Dimensions
    location VARCHAR(200) NOT NULL,
    report_date DATE NOT NULL,
    
    -- Auto-generated calendar fields
    week_number INTEGER GENERATED ALWAYS AS (EXTRACT(WEEK FROM report_date)) STORED,
    month_number INTEGER GENERATED ALWAYS AS (EXTRACT(MONTH FROM report_date)) STORED,
    year_number INTEGER GENERATED ALWAYS AS (EXTRACT(YEAR FROM report_date)) STORED,
    quarter_number INTEGER GENERATED ALWAYS AS (EXTRACT(QUARTER FROM report_date)) STORED,
    day_of_week INTEGER GENERATED ALWAYS AS (EXTRACT(DOW FROM report_date)) STORED,
    day_name VARCHAR(10) GENERATED ALWAYS AS (TRIM(TO_CHAR(report_date, 'Day'))) STORED,
    month_name VARCHAR(10) GENERATED ALWAYS AS (TRIM(TO_CHAR(report_date, 'Month'))) STORED,
    
    -- Production Data - Overburden (OB) in tons
    shift_1_ob DECIMAL(12, 2) DEFAULT 0,
    shift_2_ob DECIMAL(12, 2) DEFAULT 0,
    total_actual_ob DECIMAL(12, 2) GENERATED ALWAYS AS (shift_1_ob + shift_2_ob) STORED,
    plan_ob DECIMAL(12, 2) DEFAULT 0,
    ob_achievement_percent DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN plan_ob > 0 THEN ROUND((total_actual_ob / plan_ob * 100), 2) ELSE 0 END
    ) STORED,
    ob_variance DECIMAL(12, 2) GENERATED ALWAYS AS (total_actual_ob - plan_ob) STORED,
    
    -- Production Data - Ore in tons
    shift_1_ore DECIMAL(12, 2) DEFAULT 0,
    shift_2_ore DECIMAL(12, 2) DEFAULT 0,
    total_actual_ore DECIMAL(12, 2) GENERATED ALWAYS AS (shift_1_ore + shift_2_ore) STORED,
    plan_ore DECIMAL(12, 2) DEFAULT 0,
    ore_achievement_percent DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN plan_ore > 0 THEN ROUND((total_actual_ore / plan_ore * 100), 2) ELSE 0 END
    ) STORED,
    ore_variance DECIMAL(12, 2) GENERATED ALWAYS AS (total_actual_ore - plan_ore) STORED,
    
    -- Total Material Production
    total_actual_material DECIMAL(12, 2) GENERATED ALWAYS AS (total_actual_ob + total_actual_ore) STORED,
    total_plan_material DECIMAL(12, 2) GENERATED ALWAYS AS (plan_ob + plan_ore) STORED,
    total_achievement_percent DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN (plan_ob + plan_ore) > 0 
        THEN ROUND(((total_actual_ob + total_actual_ore) / (plan_ob + plan_ore) * 100), 2) 
        ELSE 0 END
    ) STORED,
    
    -- Fuel Data in liters
    fuel_actual DECIMAL(10, 2) DEFAULT 0,
    fuel_plan DECIMAL(10, 2) DEFAULT 0,
    fuel_efficiency_percent DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN fuel_plan > 0 THEN ROUND((fuel_plan / fuel_actual * 100), 2) ELSE 0 END
    ) STORED,
    fuel_variance DECIMAL(10, 2) GENERATED ALWAYS AS (fuel_actual - fuel_plan) STORED,
    
    -- Fuel Ratio (FR) - Fuel consumption per ton of material
    actual_fr DECIMAL(8, 4) GENERATED ALWAYS AS (
        CASE WHEN (total_actual_ob + total_actual_ore) > 0 
        THEN ROUND(fuel_actual / (total_actual_ob + total_actual_ore), 4)
        ELSE 0 END
    ) STORED,
    plan_fr DECIMAL(8, 4) DEFAULT 0,
    fr_efficiency_percent DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN plan_fr > 0 AND actual_fr > 0 
        THEN ROUND((plan_fr / actual_fr * 100), 2)
        ELSE 0 END
    ) STORED,
    fr_variance DECIMAL(8, 4) GENERATED ALWAYS AS (actual_fr - plan_fr) STORED,
    
    -- Stripping Ratio (SR) - Overburden to Ore ratio
    actual_sr DECIMAL(8, 4) GENERATED ALWAYS AS (
        CASE WHEN total_actual_ore > 0 
        THEN ROUND(total_actual_ob / total_actual_ore, 4)
        ELSE 0 END
    ) STORED,
    plan_sr DECIMAL(8, 4) DEFAULT 0,
    sr_variance DECIMAL(8, 4) GENERATED ALWAYS AS (actual_sr - plan_sr) STORED,
    sr_variance_percent DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN plan_sr > 0 
        THEN ROUND(((actual_sr - plan_sr) / plan_sr * 100), 2)
        ELSE 0 END
    ) STORED,
    
    -- Weather Conditions
    rain_actual DECIMAL(6, 2) DEFAULT 0, -- Hours of rain
    rain_plan DECIMAL(6, 2) DEFAULT 0,   -- Expected rain hours
    rain_variance DECIMAL(6, 2) GENERATED ALWAYS AS (rain_actual - rain_plan) STORED,
    
    slippery_actual DECIMAL(6, 2) DEFAULT 0, -- Hours of slippery conditions
    slippery_plan DECIMAL(6, 2) DEFAULT 0,   -- Expected slippery hours
    slippery_variance DECIMAL(6, 2) GENERATED ALWAYS AS (slippery_actual - slippery_plan) STORED,
    
    -- Additional Weather Data
    temperature_avg DECIMAL(4, 1),
    wind_speed_avg DECIMAL(4, 1),
    visibility_avg DECIMAL(4, 1), -- km
    weather_description TEXT,
    
    -- Operational Metrics
    working_hours DECIMAL(4, 2) DEFAULT 24.0,
    downtime_hours DECIMAL(4, 2) DEFAULT 0,
    operational_efficiency DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN working_hours > 0 
        THEN ROUND(((working_hours - downtime_hours) / working_hours * 100), 2)
        ELSE 0 END
    ) STORED,
    
    -- Equipment Performance
    equipment_availability DECIMAL(5, 2) DEFAULT 100.0,
    equipment_utilization DECIMAL(5, 2) DEFAULT 0,
    maintenance_hours DECIMAL(4, 2) DEFAULT 0,
    
    -- Quality & Status
    status report_status DEFAULT 'Draft',
    data_quality_score DECIMAL(3, 1) DEFAULT 10.0,
    weather_impact_level weather_impact DEFAULT 'None',
    
    -- Cost Information
    fuel_cost_actual DECIMAL(12, 2) DEFAULT 0,
    fuel_cost_plan DECIMAL(12, 2) DEFAULT 0,
    fuel_price_per_liter DECIMAL(8, 4) DEFAULT 0,
    
    -- Personnel Information
    shift_1_personnel INTEGER DEFAULT 0,
    shift_2_personnel INTEGER DEFAULT 0,
    total_personnel INTEGER GENERATED ALWAYS AS (shift_1_personnel + shift_2_personnel) STORED,
    
    -- Personnel & Approval
    reported_by VARCHAR(200),
    shift_1_supervisor VARCHAR(200),
    shift_2_supervisor VARCHAR(200),
    approved_by VARCHAR(200),
    approved_at TIMESTAMPTZ,
    
    -- Notes & Comments
    operational_notes TEXT,
    weather_notes TEXT,
    equipment_issues TEXT,
    safety_notes TEXT,
    production_notes TEXT,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by VARCHAR(200) DEFAULT current_user,
    
    -- Constraints
    UNIQUE(location, report_date),
    CHECK (shift_1_ob >= 0),
    CHECK (shift_2_ob >= 0),
    CHECK (shift_1_ore >= 0),
    CHECK (shift_2_ore >= 0),
    CHECK (plan_ob >= 0),
    CHECK (plan_ore >= 0),
    CHECK (fuel_actual >= 0),
    CHECK (fuel_plan >= 0),
    CHECK (plan_fr >= 0),
    CHECK (plan_sr >= 0),
    CHECK (rain_actual >= 0 AND rain_actual <= 24),
    CHECK (rain_plan >= 0 AND rain_plan <= 24),
    CHECK (slippery_actual >= 0 AND slippery_actual <= 24),
    CHECK (slippery_plan >= 0 AND slippery_plan <= 24),
    CHECK (working_hours >= downtime_hours),
    CHECK (working_hours >= 0 AND working_hours <= 24),
    CHECK (downtime_hours >= 0),
    CHECK (maintenance_hours >= 0),
    CHECK (equipment_availability >= 0 AND equipment_availability <= 100),
    CHECK (equipment_utilization >= 0 AND equipment_utilization <= 100),
    CHECK (data_quality_score >= 0 AND data_quality_score <= 10),
    CHECK (shift_1_personnel >= 0),
    CHECK (shift_2_personnel >= 0),
    CHECK (fuel_cost_actual >= 0),
    CHECK (fuel_cost_plan >= 0),
    CHECK (fuel_price_per_liter >= 0)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary query indexes
CREATE INDEX idx_daily_mining_report_location_date ON daily_mining_report(location, report_date);
CREATE INDEX idx_daily_mining_report_date ON daily_mining_report(report_date);
CREATE INDEX idx_daily_mining_report_location ON daily_mining_report(location);
CREATE INDEX idx_daily_mining_report_report_number ON daily_mining_report(report_number);

-- Calendar-based indexes
CREATE INDEX idx_daily_mining_report_year_month ON daily_mining_report(year_number, month_number);
CREATE INDEX idx_daily_mining_report_year_week ON daily_mining_report(year_number, week_number);
CREATE INDEX idx_daily_mining_report_location_year_month ON daily_mining_report(location, year_number, month_number);
CREATE INDEX idx_daily_mining_report_location_year_week ON daily_mining_report(location, year_number, week_number);

-- Status and approval indexes
CREATE INDEX idx_daily_mining_report_status ON daily_mining_report(status);
CREATE INDEX idx_daily_mining_report_approved ON daily_mining_report(approved_by, approved_at);
CREATE INDEX idx_daily_mining_report_weather_impact ON daily_mining_report(weather_impact_level);

-- Performance indexes
CREATE INDEX idx_daily_mining_report_achievement ON daily_mining_report(total_achievement_percent);
CREATE INDEX idx_daily_mining_report_efficiency ON daily_mining_report(operational_efficiency);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update updated_at timestamp
CREATE TRIGGER update_daily_mining_report_updated_at 
    BEFORE UPDATE ON daily_mining_report 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE daily_mining_report IS 'Daily mining operations report with production data, ratios, and operational metrics';

COMMENT ON COLUMN daily_mining_report.report_number IS 'Auto-generated unique report number based on location and date';
COMMENT ON COLUMN daily_mining_report.total_actual_ob IS 'Auto-calculated total overburden from both shifts';
COMMENT ON COLUMN daily_mining_report.total_actual_ore IS 'Auto-calculated total ore from both shifts';
COMMENT ON COLUMN daily_mining_report.actual_fr IS 'Auto-calculated fuel ratio (liters per ton of material)';
COMMENT ON COLUMN daily_mining_report.actual_sr IS 'Auto-calculated stripping ratio (overburden to ore ratio)';
COMMENT ON COLUMN daily_mining_report.ob_achievement_percent IS 'Auto-calculated overburden achievement percentage';
COMMENT ON COLUMN daily_mining_report.ore_achievement_percent IS 'Auto-calculated ore achievement percentage';
COMMENT ON COLUMN daily_mining_report.operational_efficiency IS 'Auto-calculated operational efficiency percentage';
COMMENT ON COLUMN daily_mining_report.data_quality_score IS 'Data quality score from 0-10 (10 being highest quality)';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('003', 'Daily mining report table with production data and ratios')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Daily mining report table created successfully';
    RAISE NOTICE 'Table: daily_mining_report with auto-calculated ratios and metrics';
    RAISE NOTICE 'Features: SR (Stripping Ratio), FR (Fuel Ratio), Achievement percentages';
    RAISE NOTICE 'Calendar integration: Week, Month, Year, Quarter dimensions';
    RAISE NOTICE 'Ready for data input and reporting';
END $$;
