# Design System Documentation

## Overview

The Mining Operations App design system provides a comprehensive set of design tokens, components, and guidelines to ensure consistency across the application.

## Color System

### Primary Colors
```typescript
// Mining industry inspired palette
primary: '#1A365D',      // Dark blue - Professional, trustworthy
primaryLight: '#2D5A87', // Lighter blue - Interactive states
primaryDark: '#0F2A44',  // Darker blue - Emphasis
```

### Secondary Colors
```typescript
secondary: '#E53E3E',    // Red - Alerts and critical actions
secondaryLight: '#FC8181', // Light red - Hover states
accent: '#38A169',       // Green - Success and positive actions
accentLight: '#68D391',  // Light green - Success feedback
```

### Status Colors
```typescript
warning: '#D69E2E',      // Orange/yellow - Warnings
warningLight: '#F6E05E', // Light yellow - Warning backgrounds
info: '#3182CE',         // Blue - Information
infoLight: '#63B3ED',    // Light blue - Info backgrounds
```

### Neutral Colors
```typescript
background: '#F7FAFC',   // Light gray - App background
surface: '#FFFFFF',      // White - Card and container backgrounds
surfaceSecondary: '#EDF2F7', // Light gray - Secondary surfaces

textPrimary: '#1A202C',  // Dark gray - Primary text
textSecondary: '#4A5568', // Medium gray - Secondary text
textLight: '#718096',    // Light gray - Tertiary text
textInverse: '#FFFFFF',  // White - Text on dark backgrounds

border: '#E2E8F0',       // Light border
borderDark: '#CBD5E0',   // Darker border
```

### Equipment Status Colors
```typescript
equipmentActive: '#38A169',    // Green - Equipment running
equipmentMaintenance: '#D69E2E', // Orange - Under maintenance
equipmentInactive: '#718096',   // Gray - Not in use
equipmentAlert: '#E53E3E',     // Red - Requires attention
```

## Typography

### Font Sizes
```typescript
fontSize: {
  xs: 12,    // Small labels, captions
  sm: 14,    // Body text, secondary information
  md: 16,    // Primary body text
  lg: 18,    // Subheadings
  xl: 20,    // Headings
  xxl: 24,   // Large headings
  xxxl: 32,  // Display text
}
```

### Font Weights
- **Regular (400)**: Body text
- **Medium (500)**: Emphasized text
- **Semi-bold (600)**: Subheadings
- **Bold (700)**: Headings and important text

### Typography Hierarchy
1. **Display Text (xxxl, bold)**: Page titles, hero text
2. **Headings (xl-xxl, bold)**: Section headers
3. **Subheadings (lg, semi-bold)**: Subsection headers
4. **Body Text (md, regular)**: Primary content
5. **Secondary Text (sm, regular)**: Supporting information
6. **Captions (xs, regular)**: Labels, metadata

## Spacing System

### Spacing Scale
```typescript
spacing: {
  xs: 4,     // Tight spacing
  sm: 8,     // Small spacing
  md: 16,    // Medium spacing (base unit)
  lg: 24,    // Large spacing
  xl: 32,    // Extra large spacing
  xxl: 48,   // Maximum spacing
}
```

### Usage Guidelines
- **xs (4px)**: Icon padding, tight element spacing
- **sm (8px)**: Button padding, form element spacing
- **md (16px)**: Card padding, section spacing (base unit)
- **lg (24px)**: Container padding, major section spacing
- **xl (32px)**: Page margins, large component spacing
- **xxl (48px)**: Major layout sections, hero spacing

## Border Radius

### Radius Scale
```typescript
borderRadius: {
  sm: 4,     // Small elements (buttons, inputs)
  md: 8,     // Cards, containers
  lg: 12,    // Large cards, modals
  xl: 16,    // Hero sections, major containers
  full: 9999, // Circular elements (avatars, badges)
}
```

## Shadow System

### Shadow Presets
```typescript
ShadowPresets = {
  small: {
    // Subtle shadow for small elements
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  
  medium: {
    // Standard shadow for cards
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  
  large: {
    // Prominent shadow for modals
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8,
  },
  
  card: {
    // Optimized for card components
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 4,
  },
  
  button: {
    // Interactive element shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
}
```

## Component Guidelines

### Cards
- **Background**: `Colors.surface`
- **Border Radius**: `Layout.borderRadius.md`
- **Shadow**: `ShadowPresets.card`
- **Padding**: `Layout.spacing.lg`

### Buttons
- **Primary**: `Colors.primary` background, `Colors.white` text
- **Secondary**: `Colors.surface` background, `Colors.primary` text
- **Border Radius**: `Layout.borderRadius.sm`
- **Shadow**: `ShadowPresets.button`
- **Padding**: `Layout.spacing.sm` vertical, `Layout.spacing.lg` horizontal

### Input Fields
- **Background**: `Colors.surface`
- **Border**: `Colors.border`
- **Border Radius**: `Layout.borderRadius.sm`
- **Padding**: `Layout.spacing.sm`
- **Height**: `Layout.inputHeight` (48px)

### Icons
- **Small**: 16px (navigation, inline)
- **Medium**: 24px (buttons, cards)
- **Large**: 32px (headers, features)
- **Extra Large**: 40px (hero sections)

## Layout Guidelines

### Grid System
- **Container Max Width**: 1200px
- **Gutter**: 16px (Layout.spacing.md)
- **Columns**: Flexible based on content

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Safe Areas
- **Top**: Status bar + navigation
- **Bottom**: Tab bar + home indicator
- **Sides**: Device-specific safe areas

## Accessibility

### Color Contrast
- **Normal Text**: Minimum 4.5:1 ratio
- **Large Text**: Minimum 3:1 ratio
- **UI Elements**: Minimum 3:1 ratio

### Touch Targets
- **Minimum Size**: 44px x 44px
- **Recommended**: 48px x 48px
- **Spacing**: Minimum 8px between targets

### Focus States
- **Visible Focus**: 2px outline with high contrast
- **Focus Order**: Logical tab sequence
- **Skip Links**: For complex navigation

## Animation Guidelines

### Duration
- **Micro**: 100-200ms (hover, focus)
- **Short**: 200-300ms (transitions, reveals)
- **Medium**: 300-500ms (page transitions)
- **Long**: 500ms+ (complex animations)

### Easing
- **Ease-out**: UI entering (cubic-bezier(0, 0, 0.2, 1))
- **Ease-in**: UI exiting (cubic-bezier(0.4, 0, 1, 1))
- **Ease-in-out**: UI transforming (cubic-bezier(0.4, 0, 0.2, 1))

## Usage Examples

### Card Component
```typescript
const cardStyle = {
  backgroundColor: Colors.surface,
  borderRadius: Layout.borderRadius.md,
  padding: Layout.spacing.lg,
  ...ShadowPresets.card,
};
```

### Button Component
```typescript
const buttonStyle = {
  backgroundColor: Colors.primary,
  borderRadius: Layout.borderRadius.sm,
  paddingVertical: Layout.spacing.sm,
  paddingHorizontal: Layout.spacing.lg,
  ...ShadowPresets.button,
};
```

### Text Styles
```typescript
const textStyles = {
  heading: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  body: {
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
  },
  caption: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
  },
};
```

## Implementation Files

### Core Files
- `src/constants/colors.ts` - Color definitions
- `src/constants/layout.ts` - Layout constants
- `src/utils/shadowHelper.ts` - Cross-platform shadows

### Component Examples
- `src/screens/DashboardScreen.tsx` - Modern layered design
- `src/components/` - Reusable UI components

## Best Practices

### Do's
- Use design tokens consistently
- Follow accessibility guidelines
- Test on multiple devices and platforms
- Maintain visual hierarchy
- Use appropriate shadow levels

### Don'ts
- Hard-code colors or spacing values
- Ignore platform-specific guidelines
- Use too many different font sizes
- Create overly complex shadow effects
- Neglect accessibility requirements

## Related Documentation
- [Dashboard Screen](../features/dashboard-screen.md)
- [Shadow System](../development/shadow-system.md)
- [Architecture Overview](../architecture/overview.md)
