/**
 * Populate Supabase Database with Sample Data
 * 
 * This script populates the Supabase database with sample data
 * for testing and development purposes.
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ohqbaimnhwvdfrmxvhxv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ocWJhaW1uaHd2ZGZybXh2aHh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4ODA3NzEsImV4cCI6MjA2ODQ1Njc3MX0.Qq-2pKIvW2SSJlgQqTW6I_gXdxt81oWv2wViadb9b-Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

class SampleDataPopulator {
  constructor() {
    this.results = [];
  }

  log(message, type = 'info') {
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${emoji} ${message}`);
  }

  async populateLocations() {
    try {
      this.log('Populating locations...');
      
      const locations = [
        {
          name: 'Main Mine Site',
          description: 'Primary mining operation site',
          location_type: 'mine_site',
          address: 'Mining District, Industrial Zone',
          is_active: true
        },
        {
          name: 'Processing Plant A',
          description: 'Primary ore processing facility',
          location_type: 'processing_plant',
          address: 'Plant District, Industrial Zone',
          is_active: true
        },
        {
          name: 'Administrative Office',
          description: 'Main administrative building',
          location_type: 'office',
          address: 'Office Complex, Business District',
          is_active: true
        },
        {
          name: 'Equipment Warehouse',
          description: 'Equipment storage and maintenance facility',
          location_type: 'warehouse',
          address: 'Warehouse District, Industrial Zone',
          is_active: true
        }
      ];

      const { data, error } = await supabase
        .from('locations')
        .insert(locations)
        .select();

      if (error) {
        this.log(`Failed to populate locations: ${error.message}`, 'error');
        return null;
      }

      this.log(`Successfully populated ${data.length} locations`, 'success');
      return data;
    } catch (error) {
      this.log(`Error populating locations: ${error.message}`, 'error');
      return null;
    }
  }

  async populateEquipment(locations) {
    try {
      this.log('Populating equipment...');
      
      if (!locations || locations.length === 0) {
        this.log('No locations available for equipment', 'warning');
        return null;
      }

      const mainSite = locations.find(l => l.location_type === 'mine_site');
      const warehouse = locations.find(l => l.location_type === 'warehouse');
      
      const equipment = [
        {
          name: 'Excavator CAT-001',
          equipment_type: 'excavator',
          model: 'Caterpillar 390F',
          serial_number: 'CAT390F001',
          location_id: mainSite?.id || locations[0].id,
          status: 'operational',
          purchase_date: '2022-01-15',
          last_maintenance: '2024-01-15',
          next_maintenance: '2024-04-15',
          is_active: true
        },
        {
          name: 'Dump Truck DT-001',
          equipment_type: 'dump_truck',
          model: 'Komatsu HD785-8',
          serial_number: 'KOM785001',
          location_id: mainSite?.id || locations[0].id,
          status: 'operational',
          purchase_date: '2022-03-20',
          last_maintenance: '2024-01-10',
          next_maintenance: '2024-04-10',
          is_active: true
        },
        {
          name: 'Drill Rig DR-001',
          equipment_type: 'drill',
          model: 'Atlas Copco SmartROC D65',
          serial_number: 'ATC65001',
          location_id: mainSite?.id || locations[0].id,
          status: 'maintenance',
          purchase_date: '2021-11-10',
          last_maintenance: '2024-01-20',
          next_maintenance: '2024-03-20',
          is_active: true
        },
        {
          name: 'Conveyor Belt CB-001',
          equipment_type: 'conveyor',
          model: 'Metso Outotec CV100',
          serial_number: 'MET100001',
          location_id: locations.find(l => l.location_type === 'processing_plant')?.id || locations[1].id,
          status: 'operational',
          purchase_date: '2021-08-05',
          last_maintenance: '2024-01-05',
          next_maintenance: '2024-04-05',
          is_active: true
        },
        {
          name: 'Loader LD-001',
          equipment_type: 'loader',
          model: 'Volvo L350H',
          serial_number: 'VOL350001',
          location_id: warehouse?.id || locations[0].id,
          status: 'down',
          purchase_date: '2022-06-12',
          last_maintenance: '2024-01-12',
          next_maintenance: '2024-02-12',
          is_active: true
        }
      ];

      const { data, error } = await supabase
        .from('equipment')
        .insert(equipment)
        .select();

      if (error) {
        this.log(`Failed to populate equipment: ${error.message}`, 'error');
        return null;
      }

      this.log(`Successfully populated ${data.length} equipment records`, 'success');
      return data;
    } catch (error) {
      this.log(`Error populating equipment: ${error.message}`, 'error');
      return null;
    }
  }

  async populateProductionReports(locations) {
    try {
      this.log('Populating production reports...');
      
      if (!locations || locations.length === 0) {
        this.log('No locations available for production reports', 'warning');
        return null;
      }

      const mainSite = locations.find(l => l.location_type === 'mine_site');
      
      const reports = [];
      const today = new Date();
      
      // Generate reports for the last 30 days
      for (let i = 0; i < 30; i++) {
        const reportDate = new Date(today);
        reportDate.setDate(today.getDate() - i);
        
        reports.push({
          location_id: mainSite?.id || locations[0].id,
          report_date: reportDate.toISOString().split('T')[0],
          shift_type: 'day',
          total_tonnage: Math.floor(Math.random() * 500) + 200, // 200-700 tons
          ore_grade: (Math.random() * 2 + 1).toFixed(2), // 1.00-3.00%
          waste_tonnage: Math.floor(Math.random() * 200) + 50, // 50-250 tons
          operating_hours: Math.floor(Math.random() * 4) + 8, // 8-12 hours
          downtime_hours: Math.random() * 2, // 0-2 hours
          notes: i === 0 ? 'Current day production' : `Production report for ${reportDate.toDateString()}`
        });
      }

      const { data, error } = await supabase
        .from('production_reports')
        .insert(reports)
        .select();

      if (error) {
        this.log(`Failed to populate production reports: ${error.message}`, 'error');
        return null;
      }

      this.log(`Successfully populated ${data.length} production reports`, 'success');
      return data;
    } catch (error) {
      this.log(`Error populating production reports: ${error.message}`, 'error');
      return null;
    }
  }

  async populateSafetyIncidents(locations) {
    try {
      this.log('Populating safety incidents...');
      
      if (!locations || locations.length === 0) {
        this.log('No locations available for safety incidents', 'warning');
        return null;
      }

      const incidents = [
        {
          location_id: locations[0].id,
          incident_type: 'near_miss',
          severity: 'medium',
          description: 'Near miss incident with excavator and personnel',
          incident_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'resolved',
          reported_by: 'Safety Officer',
          investigation_notes: 'Reviewed safety protocols with crew'
        },
        {
          location_id: locations[0].id,
          incident_type: 'equipment_failure',
          severity: 'low',
          description: 'Hydraulic leak in dump truck',
          incident_date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'closed',
          reported_by: 'Maintenance Tech',
          investigation_notes: 'Hydraulic line replaced, equipment returned to service'
        }
      ];

      const { data, error } = await supabase
        .from('safety_incidents')
        .insert(incidents)
        .select();

      if (error) {
        this.log(`Failed to populate safety incidents: ${error.message}`, 'error');
        return null;
      }

      this.log(`Successfully populated ${data.length} safety incidents`, 'success');
      return data;
    } catch (error) {
      this.log(`Error populating safety incidents: ${error.message}`, 'error');
      return null;
    }
  }

  async populateMaintenanceRecords(equipment) {
    try {
      this.log('Populating maintenance records...');
      
      if (!equipment || equipment.length === 0) {
        this.log('No equipment available for maintenance records', 'warning');
        return null;
      }

      const records = [
        {
          equipment_id: equipment[0].id,
          maintenance_type: 'preventive',
          description: 'Routine hydraulic system maintenance',
          scheduled_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          status: 'scheduled',
          estimated_hours: 4,
          priority: 'medium'
        },
        {
          equipment_id: equipment[1].id,
          maintenance_type: 'corrective',
          description: 'Brake system repair',
          scheduled_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completed_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          status: 'completed',
          actual_hours: 6,
          priority: 'high',
          cost: 2500.00,
          notes: 'Brake pads and rotors replaced'
        }
      ];

      const { data, error } = await supabase
        .from('maintenance_records')
        .insert(records)
        .select();

      if (error) {
        this.log(`Failed to populate maintenance records: ${error.message}`, 'error');
        return null;
      }

      this.log(`Successfully populated ${data.length} maintenance records`, 'success');
      return data;
    } catch (error) {
      this.log(`Error populating maintenance records: ${error.message}`, 'error');
      return null;
    }
  }

  async runPopulation() {
    console.log('🚀 Starting Sample Data Population...');
    console.log('Database URL:', supabaseUrl);
    
    // Check if data already exists
    const { data: existingLocations } = await supabase.from('locations').select('*').limit(1);
    if (existingLocations && existingLocations.length > 0) {
      this.log('Sample data already exists. Skipping population.', 'warning');
      return;
    }
    
    const locations = await this.populateLocations();
    const equipment = await this.populateEquipment(locations);
    await this.populateProductionReports(locations);
    await this.populateSafetyIncidents(locations);
    await this.populateMaintenanceRecords(equipment);
    
    console.log('\n✅ Sample data population completed!');
    console.log('You can now test the application with sample data.');
  }
}

// Run the populator
async function main() {
  const populator = new SampleDataPopulator();
  await populator.runPopulation();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { SampleDataPopulator };
