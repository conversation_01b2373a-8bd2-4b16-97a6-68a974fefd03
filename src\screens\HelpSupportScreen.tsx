import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Linking,
  Alert,
  StyleSheet,
  TextInput,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { Colors, Layout } from '../constants';

interface HelpSupportScreenProps {
  navigation: any;
}

interface FAQItem {
  question: string;
  answer: string;
}

const HelpSupportScreen: React.FC<HelpSupportScreenProps> = ({ navigation }) => {
  const { user, profile } = useAuth();
  const [showContactModal, setShowContactModal] = useState(false);
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
  });
  const [loading, setLoading] = useState(false);

  const faqData: FAQItem[] = [
    {
      question: "How do I update my profile information?",
      answer: "Go to Profile > Account Settings > Edit Profile. You can update your personal information, contact details, and work information there."
    },
    {
      question: "How do I change my profile picture?",
      answer: "On your profile page, tap the camera icon on your avatar. You can choose to take a new photo or select from your gallery."
    },
    {
      question: "How do I report a safety incident?",
      answer: "Use the Safety Incidents feature in the main menu. Fill out the incident report form with all required details and submit it immediately."
    },
    {
      question: "How do I change my password?",
      answer: "Go to Profile > Account Settings > Privacy & Security > Change Password. You'll need to enter your current password and create a new one."
    },
    {
      question: "How do I manage my notifications?",
      answer: "Go to Profile > Account Settings > Notifications. You can customize which types of notifications you want to receive."
    },
    {
      question: "What should I do if I forgot my password?",
      answer: "On the login screen, tap 'Forgot Password' and enter your email address. You'll receive instructions to reset your password."
    },
    {
      question: "How do I contact technical support?",
      answer: "You can contact support through this Help & Support page, call our support hotline, or email us directly."
    },
  ];

  const handleCall = (phoneNumber: string) => {
    Linking.openURL(`tel:${phoneNumber}`).catch(() => {
      Alert.alert('Error', 'Unable to make phone call');
    });
  };

  const handleEmail = (email: string) => {
    Linking.openURL(`mailto:${email}`).catch(() => {
      Alert.alert('Error', 'Unable to open email client');
    });
  };

  const handleSubmitContact = async () => {
    if (!contactForm.subject.trim() || !contactForm.message.trim()) {
      Alert.alert('Error', 'Please fill in both subject and message fields.');
      return;
    }

    setLoading(true);
    try {
      // Simulate sending contact form
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Message Sent',
        'Your message has been sent to our support team. We will get back to you within 24 hours.',
        [
          {
            text: 'OK',
            onPress: () => {
              setShowContactModal(false);
              setContactForm({ subject: '', message: '' });
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const ContactItem = ({ 
    title, 
    description, 
    onPress, 
    icon 
  }: {
    title: string;
    description: string;
    onPress: () => void;
    icon: string;
  }) => (
    <TouchableOpacity style={styles.contactItem} onPress={onPress}>
      <View style={styles.contactIcon}>
        <Ionicons name={icon as any} size={20} color={Colors.primary} />
      </View>
      <View style={styles.contactContent}>
        <Text style={styles.contactTitle}>{title}</Text>
        <Text style={styles.contactDescription}>{description}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
    </TouchableOpacity>
  );

  const FAQItem = ({ item, index }: { item: FAQItem; index: number }) => (
    <View style={styles.faqItem}>
      <TouchableOpacity
        style={styles.faqQuestion}
        onPress={() => setExpandedFAQ(expandedFAQ === index ? null : index)}
      >
        <Text style={styles.faqQuestionText}>{item.question}</Text>
        <Ionicons
          name={expandedFAQ === index ? "chevron-up" : "chevron-down"}
          size={20}
          color={Colors.textLight}
        />
      </TouchableOpacity>
      {expandedFAQ === index && (
        <View style={styles.faqAnswer}>
          <Text style={styles.faqAnswerText}>{item.answer}</Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Help & Support</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Get Help</Text>
          
          <ContactItem
            title="Contact Support"
            description="Send us a message and we'll get back to you"
            onPress={() => setShowContactModal(true)}
            icon="chatbubble"
          />

          <ContactItem
            title="Call Support"
            description="Speak directly with our support team"
            onPress={() => handleCall('+1-800-MINING')}
            icon="call"
          />

          <ContactItem
            title="Email Support"
            description="Send us an email for detailed inquiries"
            onPress={() => handleEmail('<EMAIL>')}
            icon="mail"
          />

          <ContactItem
            title="Emergency Hotline"
            description="24/7 emergency support line"
            onPress={() => handleCall('+1-800-EMERGENCY')}
            icon="warning"
          />
        </View>

        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          
          {faqData.map((item, index) => (
            <FAQItem key={index} item={item} index={index} />
          ))}
        </View>

        {/* App Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Information</Text>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Version</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Last Updated</Text>
            <Text style={styles.infoValue}>January 15, 2025</Text>
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Support Hours</Text>
            <Text style={styles.infoValue}>24/7 for emergencies, 8AM-6PM for general support</Text>
          </View>
        </View>

        {/* Additional Resources */}
        <View style={styles.infoSection}>
          <Ionicons name="information-circle" size={20} color={Colors.info} />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>Additional Resources</Text>
            <Text style={styles.infoText}>
              • User manual and training materials{'\n'}
              • Safety protocols and procedures{'\n'}
              • Company policies and guidelines{'\n'}
              • Technical documentation
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Contact Form Modal */}
      <Modal
        visible={showContactModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowContactModal(false)}
            >
              <Text style={styles.modalCancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Contact Support</Text>
            <TouchableOpacity
              onPress={handleSubmitContact}
              disabled={loading}
            >
              <Text style={[styles.modalSendButton, loading && styles.disabledText]}>
                {loading ? 'Sending...' : 'Send'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.userInfo}>
              <Text style={styles.userInfoTitle}>Your Information</Text>
              <Text style={styles.userInfoText}>Name: {profile?.full_name}</Text>
              <Text style={styles.userInfoText}>Email: {user?.email}</Text>
              <Text style={styles.userInfoText}>Employee ID: {profile?.employee_id}</Text>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Subject</Text>
              <TextInput
                style={styles.input}
                value={contactForm.subject}
                onChangeText={(value) => setContactForm(prev => ({ ...prev, subject: value }))}
                placeholder="Brief description of your issue"
                placeholderTextColor={Colors.textLight}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Message</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={contactForm.message}
                onChangeText={(value) => setContactForm(prev => ({ ...prev, message: value }))}
                placeholder="Describe your issue or question in detail..."
                placeholderTextColor={Colors.textLight}
                multiline
                numberOfLines={6}
                textAlignVertical="top"
              />
            </View>

            <View style={styles.supportInfo}>
              <Text style={styles.supportInfoTitle}>Response Time</Text>
              <Text style={styles.supportInfoText}>
                We typically respond within 24 hours during business days. 
                For urgent matters, please call our support hotline.
              </Text>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  placeholder: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  section: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
    marginTop: Layout.spacing.lg,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  contactContent: {
    flex: 1,
  },
  contactTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  contactDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  faqQuestion: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Layout.spacing.md,
  },
  faqQuestionText: {
    flex: 1,
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
    marginRight: Layout.spacing.sm,
  },
  faqAnswer: {
    paddingBottom: Layout.spacing.md,
    paddingRight: Layout.spacing.lg,
  },
  faqAnswerText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    lineHeight: 20,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  infoLabel: {
    fontSize: Layout.fontSize.md,
    color: Colors.text,
    fontWeight: '500',
    flex: 1,
  },
  infoValue: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    flex: 2,
    textAlign: 'right',
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.info + '20',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.xl,
  },
  infoContent: {
    flex: 1,
    marginLeft: Layout.spacing.sm,
  },
  infoTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.info,
    marginBottom: Layout.spacing.xs,
  },
  infoText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.info,
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalCancelButton: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
  },
  modalTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  modalSendButton: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.primary,
  },
  disabledText: {
    color: Colors.textLight,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  userInfo: {
    backgroundColor: Colors.backgroundLight,
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginTop: Layout.spacing.lg,
    marginBottom: Layout.spacing.lg,
  },
  userInfoTitle: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  userInfoText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    marginBottom: Layout.spacing.xs,
  },
  inputGroup: {
    marginBottom: Layout.spacing.lg,
  },
  inputLabel: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    fontSize: Layout.fontSize.md,
    color: Colors.text,
    backgroundColor: Colors.background,
  },
  textArea: {
    height: 120,
    paddingTop: Layout.spacing.sm,
  },
  supportInfo: {
    backgroundColor: Colors.success + '20',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginTop: Layout.spacing.lg,
  },
  supportInfoTitle: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '600',
    color: Colors.success,
    marginBottom: Layout.spacing.xs,
  },
  supportInfoText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.success,
    lineHeight: 18,
  },
});

export default HelpSupportScreen;
