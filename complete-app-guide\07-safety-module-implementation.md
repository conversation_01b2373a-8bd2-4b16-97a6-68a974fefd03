# Safety Module Implementation - Mining Operations App

## 🛡️ **Safety Module Architecture**

### **Overview**
The safety module is the most critical component of the mining operations app, focusing on incident prevention, reporting, investigation, and compliance management. It provides comprehensive safety management tools to ensure worker safety and regulatory compliance.

### **Key Features**
- **Incident reporting** with photo evidence and witness statements
- **Safety inspections** and digital checklists
- **Risk assessment** and hazard identification
- **Safety training** tracking and compliance
- **Emergency response** procedures and contacts
- **Safety analytics** and trend analysis
- **Regulatory compliance** tracking
- **Safety meetings** and toolbox talks
- **PPE (Personal Protective Equipment)** management
- **Safety performance** dashboards and KPIs

## 📁 **File Structure**

```
src/features/safety/
├── components/
│   ├── IncidentReportForm.tsx
│   ├── SafetyChecklistCard.tsx
│   ├── SafetyScoreGauge.tsx
│   ├── IncidentCard.tsx
│   ├── EmergencyContactCard.tsx
│   ├── RiskAssessmentForm.tsx
│   ├── SafetyPhotoCapture.tsx
│   └── SafetyMetricsChart.tsx
├── hooks/
│   ├── useSafetyData.ts
│   ├── useIncidentReporting.ts
│   ├── useSafetyChecklists.ts
│   └── useSafetyAnalytics.ts
├── screens/
│   ├── SafetyOverviewScreen.tsx
│   ├── IncidentReportingScreen.tsx
│   ├── SafetyChecklistScreen.tsx
│   ├── IncidentDetailScreen.tsx
│   ├── SafetyAnalyticsScreen.tsx
│   └── EmergencyContactsScreen.tsx
├── services/
│   ├── SafetyService.ts
│   ├── IncidentService.ts
│   ├── SafetyChecklistService.ts
│   └── SafetyAnalyticsService.ts
└── types/
    └── safety.types.ts
```

## 🎯 **Domain Models**

### **src/models/Safety.ts**
```typescript
export interface SafetyIncident {
  id: string;
  incidentNumber: string;
  title: string;
  description: string;
  incidentType: IncidentType;
  severity: IncidentSeverity;
  locationId: string;
  locationName?: string;
  exactLocation: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  occurredAt: string;
  reportedAt: string;
  reportedBy: string;
  reportedByName?: string;
  peopleInvolved: PersonInvolved[];
  injuriesCount: number;
  fatalitiesCount: number;
  equipmentInvolved?: string;
  equipmentName?: string;
  weatherConditions?: string;
  immediateActions: string;
  rootCause?: string;
  correctiveActions?: string;
  status: IncidentStatus;
  assignedInvestigator?: string;
  investigatorName?: string;
  investigationDeadline?: string;
  closedAt?: string;
  closedBy?: string;
  attachments: IncidentAttachment[];
  witnesses: WitnessStatement[];
  createdAt: string;
  updatedAt: string;
}

export interface PersonInvolved {
  id: string;
  name: string;
  employeeId?: string;
  role: string;
  department: string;
  injuryType?: InjuryType;
  injurySeverity?: InjurySeverity;
  bodyPartAffected?: string;
  medicalAttentionRequired: boolean;
  hospitalName?: string;
  treatmentDescription?: string;
  daysLostTime?: number;
}

export interface WitnessStatement {
  id: string;
  witnessName: string;
  witnessEmployeeId?: string;
  contactInfo: string;
  statement: string;
  statementDate: string;
  signature?: string;
}

export interface IncidentAttachment {
  id: string;
  fileName: string;
  fileType: 'photo' | 'document' | 'video';
  fileUrl: string;
  description?: string;
  uploadedBy: string;
  uploadedAt: string;
}

export interface SafetyChecklist {
  id: string;
  title: string;
  description: string;
  category: ChecklistCategory;
  locationId?: string;
  equipmentType?: string;
  frequency: ChecklistFrequency;
  isMandatory: boolean;
  items: ChecklistItem[];
  createdBy: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ChecklistItem {
  id: string;
  itemText: string;
  itemOrder: number;
  isCritical: boolean;
  requiresPhoto: boolean;
  requiresComment: boolean;
  acceptableCriteria?: string;
  hazardLevel?: HazardLevel;
}

export interface ChecklistCompletion {
  id: string;
  checklistId: string;
  checklistTitle?: string;
  completedBy: string;
  completedByName?: string;
  locationId: string;
  locationName?: string;
  shiftId?: string;
  shiftName?: string;
  completedAt: string;
  overallStatus: CompletionStatus;
  notes?: string;
  supervisorReviewRequired: boolean;
  reviewedBy?: string;
  reviewedAt?: string;
  itemResponses: ItemResponse[];
}

export interface ItemResponse {
  id: string;
  itemId: string;
  itemText: string;
  response: ResponseType;
  comment?: string;
  photoUrl?: string;
  correctionRequired: boolean;
  correctionNotes?: string;
  correctedAt?: string;
}

export interface RiskAssessment {
  id: string;
  title: string;
  description: string;
  locationId: string;
  activityType: string;
  assessedBy: string;
  assessmentDate: string;
  validUntil: string;
  hazards: IdentifiedHazard[];
  overallRiskLevel: RiskLevel;
  controlMeasures: ControlMeasure[];
  approvedBy?: string;
  approvedAt?: string;
  status: AssessmentStatus;
  createdAt: string;
  updatedAt: string;
}

export interface IdentifiedHazard {
  id: string;
  hazardType: HazardType;
  description: string;
  likelihood: LikelihoodLevel;
  consequence: ConsequenceLevel;
  riskLevel: RiskLevel;
  existingControls: string[];
}

export interface ControlMeasure {
  id: string;
  hazardId: string;
  controlType: ControlType;
  description: string;
  responsibility: string;
  implementationDate: string;
  reviewDate: string;
  effectiveness: EffectivenessLevel;
  status: ControlStatus;
}

export interface SafetyTraining {
  id: string;
  trainingName: string;
  trainingType: TrainingType;
  description: string;
  duration: number; // hours
  validityPeriod: number; // months
  isMandatory: boolean;
  targetRoles: string[];
  prerequisites: string[];
  createdBy: string;
  isActive: boolean;
  createdAt: string;
}

export interface TrainingRecord {
  id: string;
  trainingId: string;
  trainingName?: string;
  employeeId: string;
  employeeName?: string;
  completedDate: string;
  expiryDate: string;
  score?: number;
  certificateUrl?: string;
  instructorName: string;
  trainingLocation: string;
  status: TrainingStatus;
  createdAt: string;
}

export interface SafetyMetrics {
  safetyScore: number;
  daysWithoutIncident: number;
  totalIncidents: number;
  openIncidents: number;
  criticalIncidents: number;
  injuryRate: number; // per 100 employees
  lostTimeInjuryRate: number;
  nearMissCount: number;
  safetyInspectionsCompleted: number;
  safetyInspectionsPending: number;
  trainingComplianceRate: number;
  lastUpdated: string;
}

export interface EmergencyContact {
  id: string;
  name: string;
  role: string;
  department: string;
  primaryPhone: string;
  secondaryPhone?: string;
  email: string;
  location: string;
  isAvailable24x7: boolean;
  specializations: string[];
  priority: number;
  isActive: boolean;
}

// Enums and Types
export type IncidentType = 
  | 'Near Miss'
  | 'First Aid'
  | 'Medical Treatment'
  | 'Lost Time Injury'
  | 'Fatality'
  | 'Property Damage'
  | 'Environmental'
  | 'Security'
  | 'Fire'
  | 'Chemical Spill'
  | 'Equipment Failure'
  | 'Vehicle Accident';

export type IncidentSeverity = 
  | 'Low'
  | 'Medium'
  | 'High'
  | 'Critical';

export type IncidentStatus = 
  | 'Reported'
  | 'Under Investigation'
  | 'Investigation Complete'
  | 'Corrective Actions Pending'
  | 'Closed'
  | 'Rejected';

export type InjuryType = 
  | 'Cut/Laceration'
  | 'Bruise/Contusion'
  | 'Sprain/Strain'
  | 'Fracture'
  | 'Burn'
  | 'Eye Injury'
  | 'Respiratory'
  | 'Chemical Exposure'
  | 'Heat Exhaustion'
  | 'Electric Shock'
  | 'Other';

export type InjurySeverity = 
  | 'Minor'
  | 'Moderate'
  | 'Severe'
  | 'Critical';

export type ChecklistCategory = 
  | 'Pre-Shift Safety'
  | 'Equipment Inspection'
  | 'PPE Check'
  | 'Environmental'
  | 'Emergency Preparedness'
  | 'Housekeeping'
  | 'Chemical Safety'
  | 'Confined Space'
  | 'Working at Height'
  | 'Hot Work';

export type ChecklistFrequency = 
  | 'Daily'
  | 'Weekly'
  | 'Monthly'
  | 'Quarterly'
  | 'As Needed';

export type CompletionStatus = 
  | 'Pass'
  | 'Fail'
  | 'Partial'
  | 'Needs Review';

export type ResponseType = 
  | 'Pass'
  | 'Fail'
  | 'N/A'
  | 'Needs Attention';

export type HazardLevel = 
  | 'Low'
  | 'Medium'
  | 'High'
  | 'Extreme';

export type HazardType = 
  | 'Physical'
  | 'Chemical'
  | 'Biological'
  | 'Ergonomic'
  | 'Psychosocial'
  | 'Environmental'
  | 'Mechanical'
  | 'Electrical'
  | 'Fire/Explosion'
  | 'Radiation';

export type LikelihoodLevel = 
  | 'Very Unlikely'
  | 'Unlikely'
  | 'Possible'
  | 'Likely'
  | 'Very Likely';

export type ConsequenceLevel = 
  | 'Negligible'
  | 'Minor'
  | 'Moderate'
  | 'Major'
  | 'Catastrophic';

export type RiskLevel = 
  | 'Very Low'
  | 'Low'
  | 'Medium'
  | 'High'
  | 'Very High';

export type ControlType = 
  | 'Elimination'
  | 'Substitution'
  | 'Engineering'
  | 'Administrative'
  | 'PPE';

export type EffectivenessLevel = 
  | 'Not Effective'
  | 'Partially Effective'
  | 'Effective'
  | 'Very Effective';

export type ControlStatus = 
  | 'Planned'
  | 'In Progress'
  | 'Implemented'
  | 'Under Review'
  | 'Ineffective';

export type AssessmentStatus = 
  | 'Draft'
  | 'Under Review'
  | 'Approved'
  | 'Expired'
  | 'Rejected';

export type TrainingType = 
  | 'Safety Induction'
  | 'Equipment Operation'
  | 'Emergency Response'
  | 'First Aid'
  | 'Chemical Handling'
  | 'Confined Space'
  | 'Working at Height'
  | 'Fire Safety'
  | 'Environmental'
  | 'Leadership Safety';

export type TrainingStatus = 
  | 'Completed'
  | 'Expired'
  | 'Due Soon'
  | 'Overdue';

export interface SafetyFilters {
  startDate?: string;
  endDate?: string;
  locationId?: string;
  incidentType?: IncidentType;
  severity?: IncidentSeverity;
  status?: IncidentStatus;
  reportedBy?: string;
  assignedInvestigator?: string;
}

// Utility functions
export const calculateSafetyScore = (metrics: {
  daysWithoutIncident: number;
  openIncidents: number;
  criticalIncidents: number;
  complianceRate: number;
}): number => {
  const baseScore = Math.min(100, metrics.daysWithoutIncident * 2);
  const incidentPenalty = (metrics.openIncidents * 5) + (metrics.criticalIncidents * 15);
  const complianceBonus = metrics.complianceRate * 0.2;
  
  return Math.max(0, Math.min(100, baseScore - incidentPenalty + complianceBonus));
};

export const calculateRiskLevel = (
  likelihood: LikelihoodLevel,
  consequence: ConsequenceLevel
): RiskLevel => {
  const likelihoodScores = {
    'Very Unlikely': 1,
    'Unlikely': 2,
    'Possible': 3,
    'Likely': 4,
    'Very Likely': 5
  };
  
  const consequenceScores = {
    'Negligible': 1,
    'Minor': 2,
    'Moderate': 3,
    'Major': 4,
    'Catastrophic': 5
  };
  
  const riskScore = likelihoodScores[likelihood] * consequenceScores[consequence];
  
  if (riskScore <= 4) return 'Very Low';
  if (riskScore <= 8) return 'Low';
  if (riskScore <= 12) return 'Medium';
  if (riskScore <= 16) return 'High';
  return 'Very High';
};

export const getIncidentSeverityColor = (severity: IncidentSeverity): string => {
  const colors = {
    'Low': '#10B981',
    'Medium': '#F59E0B',
    'High': '#EF4444',
    'Critical': '#DC2626'
  };
  return colors[severity] || '#6B7280';
};

export const getRiskLevelColor = (riskLevel: RiskLevel): string => {
  const colors = {
    'Very Low': '#10B981',
    'Low': '#34D399',
    'Medium': '#F59E0B',
    'High': '#EF4444',
    'Very High': '#DC2626'
  };
  return colors[riskLevel] || '#6B7280';
};

export const formatIncidentNumber = (date: Date, sequence: number): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const seq = String(sequence).padStart(4, '0');
  return `INC-${year}${month}-${seq}`;
};

export const calculateInjuryRate = (
  injuries: number,
  totalEmployees: number,
  periodDays: number = 365
): number => {
  if (totalEmployees === 0) return 0;
  return (injuries / totalEmployees) * 100 * (365 / periodDays);
};

export const calculateLostTimeInjuryRate = (
  lostTimeInjuries: number,
  totalHoursWorked: number
): number => {
  if (totalHoursWorked === 0) return 0;
  return (lostTimeInjuries / totalHoursWorked) * 200000; // Per 200,000 hours worked
};

export const getTrainingStatusColor = (status: TrainingStatus): string => {
  const colors = {
    'Completed': '#10B981',
    'Expired': '#EF4444',
    'Due Soon': '#F59E0B',
    'Overdue': '#DC2626'
  };
  return colors[status] || '#6B7280';
};

export const validateIncidentReport = (incident: Partial<SafetyIncident>): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (!incident.title?.trim()) {
    errors.push('Incident title is required');
  }
  if (!incident.description?.trim()) {
    errors.push('Incident description is required');
  }
  if (!incident.incidentType) {
    errors.push('Incident type is required');
  }
  if (!incident.severity) {
    errors.push('Incident severity is required');
  }
  if (!incident.locationId) {
    errors.push('Location is required');
  }
  if (!incident.exactLocation?.trim()) {
    errors.push('Exact location is required');
  }
  if (!incident.occurredAt) {
    errors.push('Incident date and time is required');
  }
  if (!incident.reportedBy) {
    errors.push('Reporter is required');
  }
  if (!incident.immediateActions?.trim()) {
    errors.push('Immediate actions taken is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateChecklistCompletion = (completion: Partial<ChecklistCompletion>): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (!completion.checklistId) {
    errors.push('Checklist ID is required');
  }
  if (!completion.completedBy) {
    errors.push('Completed by is required');
  }
  if (!completion.locationId) {
    errors.push('Location is required');
  }
  if (!completion.overallStatus) {
    errors.push('Overall status is required');
  }
  if (!completion.itemResponses || completion.itemResponses.length === 0) {
    errors.push('At least one item response is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Safety KPI calculations
export const calculateSafetyKPIs = (
  incidents: SafetyIncident[],
  totalEmployees: number,
  totalHoursWorked: number,
  periodDays: number = 30
): {
  totalIncidents: number;
  injuryRate: number;
  lostTimeInjuryRate: number;
  nearMissRate: number;
  severityRate: number;
  frequencyRate: number;
} => {
  const totalIncidents = incidents.length;
  const injuries = incidents.filter(i => 
    ['First Aid', 'Medical Treatment', 'Lost Time Injury', 'Fatality'].includes(i.incidentType)
  ).length;
  const lostTimeInjuries = incidents.filter(i => i.incidentType === 'Lost Time Injury').length;
  const nearMisses = incidents.filter(i => i.incidentType === 'Near Miss').length;
  const totalLostDays = incidents.reduce((sum, i) => 
    sum + (i.peopleInvolved.reduce((daySum, p) => daySum + (p.daysLostTime || 0), 0)), 0
  );

  return {
    totalIncidents,
    injuryRate: calculateInjuryRate(injuries, totalEmployees, periodDays),
    lostTimeInjuryRate: calculateLostTimeInjuryRate(lostTimeInjuries, totalHoursWorked),
    nearMissRate: (nearMisses / totalEmployees) * 100,
    severityRate: totalHoursWorked > 0 ? (totalLostDays / totalHoursWorked) * 200000 : 0,
    frequencyRate: totalHoursWorked > 0 ? (totalIncidents / totalHoursWorked) * 200000 : 0,
  };
};
```

This completes the comprehensive safety domain models. The next section will cover the safety service implementation with detailed incident management, checklist handling, and safety analytics.
