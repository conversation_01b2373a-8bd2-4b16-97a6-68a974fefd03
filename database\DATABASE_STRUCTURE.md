# Mining Operations Database Structure

## Overview
This document describes the complete database structure for the Mining Operations App, including all tables, relationships, and sample data that has been populated in Supabase.

## Database Schema

### 🏢 Core Tables

#### 1. **locations**
Primary locations for mining operations including mine sites, processing plants, offices, and warehouses.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| name | varchar | Location name |
| code | varchar | Unique location code |
| description | text | Detailed description |
| address | text | Physical address |
| coordinates | point | GPS coordinates |
| timezone | varchar | Local timezone |
| country_code | varchar | Country code |
| region | varchar | Regional classification |
| operational_status | varchar | active, inactive, maintenance, closed |
| capacity_rating | numeric | Operational capacity |
| environmental_zone | varchar | Environmental classification |
| is_active | boolean | Active status |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 15 locations including mine sites, processing plants, offices, and warehouses

#### 2. **employees**
Employee information and organizational structure.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| employee_id | varchar | Unique employee identifier |
| full_name | varchar | Full name |
| email | varchar | Email address |
| phone | varchar | Phone number |
| emergency_contact | jsonb | Emergency contact information |
| job_title | varchar | Job position |
| job_level | varchar | Seniority level |
| department_id | uuid | Department reference |
| location_id | uuid | Primary work location |
| supervisor_id | uuid | Direct supervisor |
| hire_date | date | Employment start date |
| termination_date | date | Employment end date |
| address | text | Home address |
| skills | jsonb | Skills and certifications |
| clearance_level | varchar | Security clearance |
| salary_grade | varchar | Salary classification |
| work_schedule | jsonb | Work schedule details |
| status | enum | Employment status |
| notes | text | Additional notes |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 19 employees including supervisors, safety officers, maintenance technicians, and operators

#### 3. **users**
User authentication and authorization system.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| employee_id | varchar | Link to employee record |
| username | varchar | Login username |
| email | varchar | Email address |
| password_hash | varchar | Encrypted password |
| full_name | varchar | Display name |
| role | enum | User role (admin, supervisor, operator, etc.) |
| location_id | uuid | Primary location access |
| department_id | uuid | Department access |
| hire_date | date | Employment date |
| is_active | boolean | Account status |
| is_locked | boolean | Account lock status |
| failed_login_attempts | integer | Failed login counter |
| last_login | timestamptz | Last login time |
| password_changed_at | timestamptz | Password change date |
| must_change_password | boolean | Force password change |
| two_factor_enabled | boolean | 2FA status |
| is_super_admin | boolean | Super admin privileges |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 13 user accounts with role-based access

### 🚛 Equipment Management

#### 4. **equipment**
Mining equipment inventory and specifications.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| name | varchar | Equipment name |
| serial_number | varchar | Manufacturer serial number |
| asset_tag | varchar | Internal asset tag |
| model | varchar | Equipment model |
| manufacturer | varchar | Manufacturer name |
| equipment_type | enum | excavator, dump_truck, drill, loader, etc. |
| location_id | uuid | Current location |
| purchase_date | date | Purchase date |
| warranty_expiry | date | Warranty expiration |
| last_maintenance_date | date | Last maintenance |
| next_maintenance_due | date | Next maintenance due |
| operating_hours | numeric | Total operating hours |
| max_load_capacity | numeric | Maximum load capacity |
| fuel_capacity | numeric | Fuel tank capacity |
| fuel_type | varchar | Fuel type required |
| specifications | jsonb | Technical specifications |
| maintenance_schedule | jsonb | Maintenance schedule |
| safety_certifications | jsonb | Safety certifications |
| purchase_cost | numeric | Original purchase cost |
| current_value | numeric | Current market value |
| depreciation_rate | numeric | Depreciation rate |
| status | enum | operational, maintenance, breakdown, retired, standby |
| condition_rating | integer | Condition rating (1-10) |
| is_active | boolean | Active status |
| notes | text | Additional notes |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 10 pieces of equipment including excavators, dump trucks, drills, and loaders

#### 5. **maintenance_records**
Equipment maintenance history and scheduling.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| equipment_id | uuid | Equipment reference |
| maintenance_type | varchar | Type of maintenance |
| description | text | Maintenance description |
| scheduled_date | date | Scheduled date |
| started_at | timestamptz | Start timestamp |
| completed_date | date | Completion date |
| next_maintenance_due | date | Next maintenance due |
| performed_by | varchar | Technician name |
| technician_id | uuid | Technician reference |
| parts_used | jsonb | Parts and materials used |
| labor_hours | numeric | Labor hours spent |
| cost | numeric | Total cost |
| parts_cost | numeric | Parts cost |
| labor_cost | numeric | Labor cost |
| notes | text | Maintenance notes |
| attachments | array | File attachments |
| before_condition_rating | integer | Condition before maintenance |
| after_condition_rating | integer | Condition after maintenance |
| status | enum | Maintenance status |
| priority | enum | Priority level |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 5 maintenance records covering preventive, corrective, and emergency maintenance

### ⏰ Workforce Management

#### 6. **shifts**
Work shift definitions and schedules.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| name | varchar | Shift name |
| code | varchar | Shift code |
| start_time | time | Shift start time |
| end_time | time | Shift end time |
| location_id | uuid | Location reference |
| shift_type | enum | day, night, overtime, emergency |
| break_duration | integer | Break duration in minutes |
| overtime_threshold | integer | Overtime threshold in minutes |
| is_active | boolean | Active status |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 10 shifts covering day, night, overtime, and maintenance schedules

#### 7. **attendance**
Employee attendance tracking.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| employee_id | uuid | Employee reference |
| date | date | Attendance date |
| location_id | uuid | Work location |
| shift_id | uuid | Assigned shift |
| check_in_time | timestamptz | Check-in timestamp |
| check_out_time | timestamptz | Check-out timestamp |
| total_hours | numeric | Total hours worked |
| overtime_hours | numeric | Overtime hours |
| break_hours | numeric | Break hours |
| status | enum | present, absent, late, sick, vacation |
| notes | text | Attendance notes |
| approved_by | uuid | Supervisor approval |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 12 attendance records for recent dates

### 📊 Production Management

#### 8. **production_reports**
Daily production reports and metrics.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| report_number | varchar | Unique report number |
| created_by | uuid | Report creator |
| report_date | date | Production date |
| location_id | uuid | Production location |
| production_metrics | jsonb | Production metrics and KPIs |
| equipment_used | array | Equipment used in production |
| ~~total_tonnage~~ | ~~numeric~~ | **REMOVED - No longer needed** |
| operating_hours | numeric | Total operating hours |
| downtime_hours | numeric | Equipment downtime |
| fuel_consumed | numeric | Fuel consumption |
| notes | text | Production notes |
| weather_conditions | varchar | Weather conditions |
| crew_size | integer | Number of crew members |
| approved_by | uuid | Supervisor approval |
| status | enum | Report status |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 6 production reports with standardized metrics structure

**🔄 Recent Updates (2025-08-03):**
- **Removed `total_tonnage` column** - No longer needed as OB and ORE are tracked separately
- **Standardized `production_metrics` JSONB** - Now consistent with `production_daily_summary`
- **Added calculated ratios** - Stripping Ratio (SR) and Fuel Ratio (FR) automatically calculated

**Updated Production Metrics JSONB Structure:**
```json
{
  "ob_actual": 2450.75,           // Overburden actual (BCM)
  "ob_plan": 2328.21,             // Overburden planned (BCM)
  "ore_actual": 1325.50,          // Ore actual (TON)
  "ore_plan": 1259.23,            // Ore planned (TON)
  "fuel_actual": 385.20,          // Actual fuel consumption (L)
  "fuel_plan": 365.94,            // Planned fuel consumption (L)
  "stripping_ratio_actual": 1.8489,  // SR = OB/ORE
  "stripping_ratio_target": 1.8489,  // Target SR
  "fuel_ratio_actual": 0.1356,       // FR = Fuel/(OB + ORE/3.39)
  "fuel_ratio_plan": 0.1355,         // Planned FR
  "efficiency": 102.1,               // Overall efficiency %
  "achievement_percent": 105.26,     // Target achievement %
  "weather_condition": "Clear",      // Weather conditions
  "working_hours": 12.5,             // Working hours
  "downtime_hours": 0.5              // Downtime hours
}
```

### 🛡️ Safety & Health Management

#### 9. **incident_types**
Classification of safety incidents.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| name | varchar | Incident type name |
| code | varchar | Incident code |
| description | text | Type description |
| severity_level | varchar | Severity classification |
| is_active | boolean | Active status |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 8 incident types including Near Miss, First Aid, Medical Treatment, Lost Time Injury, Equipment Damage, Environmental Incident, Fire/Explosion, Chemical Spill

#### 10. **she_incidents**
Safety, Health, and Environment incident records.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| incident_number | varchar | Unique incident number |
| employee_id | uuid | Involved employee |
| incident_type_id | uuid | Incident type reference |
| location_id | uuid | Incident location |
| incident_date | date | Date of incident |
| incident_time | time | Time of incident |
| description | text | Incident description |
| immediate_cause | text | Immediate cause analysis |
| root_cause | text | Root cause analysis |
| corrective_action | text | Corrective actions taken |
| preventive_action | text | Preventive measures implemented |
| reported_by | uuid | Person who reported |
| investigated_by | uuid | Investigator |
| approved_by | uuid | Approval authority |
| severity | enum | Incident severity |
| status | enum | Investigation status |
| created_at | timestamptz | Creation timestamp |
| updated_at | timestamptz | Last update timestamp |

**Sample Data:** 5 safety incidents with complete investigation details

### 📋 Additional Tables

#### 11. **departments**
Organizational departments structure.

#### 12. **calendar_production**
Production calendar and scheduling.

#### 13. **environment_monitoring**
Environmental monitoring data.

#### 14. **health_monitoring**
Employee health monitoring records.

#### 15. **leave_requests**
Employee leave request management.

#### 16. **she_inspections**
Safety inspections and audits.

#### 17. **she_trainings**
Safety training records.

#### 18. **weather**
Weather data for operations.

#### 19. **audit_log**
System audit trail.

#### 20. **sync_metadata** & **sync_queue**
Data synchronization management.

## Data Relationships

### Primary Relationships
- **employees** ↔ **users** (1:1) - Employee profiles linked to user accounts
- **employees** → **locations** (N:1) - Employees assigned to locations
- **equipment** → **locations** (N:1) - Equipment stationed at locations
- **attendance** → **employees** (N:1) - Employee attendance records
- **attendance** → **shifts** (N:1) - Attendance linked to shifts
- **production_reports** → **locations** (N:1) - Reports from specific locations
- **she_incidents** → **employees** (N:1) - Incidents involving employees
- **she_incidents** → **incident_types** (N:1) - Incident classification
- **maintenance_records** → **equipment** (N:1) - Equipment maintenance history

### Foreign Key Constraints
All relationships are enforced through foreign key constraints to maintain data integrity.

## Sample Data Summary

| Table | Records | Description |
|-------|---------|-------------|
| locations | 15 | Mine sites, processing plants, offices, warehouses |
| employees | 19 | Supervisors, safety officers, technicians, operators |
| users | 13 | User accounts with role-based access |
| equipment | 10 | Excavators, dump trucks, drills, loaders |
| shifts | 10 | Day, night, overtime, maintenance shifts |
| production_reports | 6 | **Updated** daily production metrics with standardized structure |
| **production_sites** | **5** | **New mining-specific production sites** |
| **production_plans** | **5** | **Production planning and targets** |
| **production_records** | **8** | **Detailed shift-based production records** |
| **production_daily_summary** | **8** | **Materialized view for performance** |
| incident_types | 8 | Safety incident classifications |
| she_incidents | 5 | Real-world safety incident scenarios |
| maintenance_records | 5 | Preventive, corrective, emergency maintenance |
| attendance | 12 | Recent attendance data with check-in/out times |

### 🆕 **New Production Schema (Implemented)**

The database has been migrated to use the comprehensive production schema from `03_production_tables.sql` with the following enhancements:

#### **New Tables:**
- **`production_sites`**: Mining-specific sites (pits, dumps, stockpiles)
- **`production_plans`**: Production planning with targets and ratios
- **`production_records`**: Detailed shift-based production tracking
- **`production_daily_summary`**: Materialized view for dashboard performance

#### **New Features:**
- **Mining-Specific Metrics**: OB (Overburden), Ore, Stripping Ratios
- **Automatic Calculations**: Ratios calculated via database triggers
- **Shift-Based Tracking**: Day/Night shift production recording
- **Performance Optimization**: Materialized views for fast dashboard queries
- **Production Planning**: Target vs Actual tracking with achievement percentages

## Database Features

### ✅ **Data Integrity**
- Foreign key constraints
- Check constraints for enum values
- Unique constraints on critical fields
- NOT NULL constraints on required fields

### ✅ **Scalability**
- UUID primary keys for distributed systems
- JSONB columns for flexible data storage
- Indexed columns for performance
- Partitioning-ready structure

### ✅ **Security**
- Role-based access control
- Audit logging capabilities
- Encrypted sensitive data
- Row-level security policies

### ✅ **Flexibility**
- JSONB columns for dynamic data
- Extensible enum types
- Configurable business rules
- Multi-tenant architecture support

## Usage Notes

1. **Authentication**: Use the `users` table for login and authorization
2. **Production Data**: `production_reports` contains daily metrics and KPIs
3. **Equipment Status**: Monitor equipment through `equipment.status` and `maintenance_records`
4. **Safety Metrics**: Track incidents through `she_incidents` and `incident_types`
5. **Attendance**: Use `attendance` table for workforce management
6. **Locations**: All operations are location-based for proper segregation

## Next Steps

1. **Test Application**: Verify all data displays correctly in the dashboard
2. **Add More Data**: Extend sample data as needed for testing
3. **Configure RLS**: Implement Row Level Security policies
4. **Optimize Queries**: Add indexes for frequently accessed data
5. **Backup Strategy**: Implement regular database backups

---

*This database structure supports a comprehensive mining operations management system with real-world data for immediate testing and development.*

