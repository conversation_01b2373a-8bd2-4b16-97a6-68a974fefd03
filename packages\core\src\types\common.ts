// Common types used across the Mining Operations Platform

// Generic utility types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type Maybe<T> = T | null | undefined;

// Generic response wrapper
export interface Result<T, E = Error> {
  success: boolean;
  data?: T;
  error?: E;
  message?: string;
}

// Generic async operation result
export interface AsyncResult<T> extends Result<T> {
  loading: boolean;
  timestamp: string;
}

// Generic key-value pair
export interface KeyValuePair<K = string, V = any> {
  key: K;
  value: V;
}

// Generic option for dropdowns/selects
export interface SelectOption<T = any> {
  label: string;
  value: T;
  disabled?: boolean;
  group?: string;
}

// Generic tree node
export interface TreeNode<T = any> {
  id: string;
  label: string;
  data?: T;
  children?: TreeNode<T>[];
  parent?: TreeNode<T>;
  expanded?: boolean;
  selected?: boolean;
}

// Date range
export interface DateRange {
  startDate: string;
  endDate: string;
}

// Time period types
export type TimePeriod = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';

// Comparison operators
export type ComparisonOperator = 
  | 'eq'      // equals
  | 'neq'     // not equals
  | 'gt'      // greater than
  | 'gte'     // greater than or equal
  | 'lt'      // less than
  | 'lte'     // less than or equal
  | 'like'    // pattern matching
  | 'ilike'   // case-insensitive pattern matching
  | 'in'      // in array
  | 'not_in'  // not in array
  | 'between' // between two values
  | 'is_null' // is null
  | 'not_null'; // is not null

// Sort direction
export type SortDirection = 'asc' | 'desc';

// Generic filter
export interface Filter {
  field: string;
  operator: ComparisonOperator;
  value: any;
  values?: any[]; // for 'in', 'not_in', 'between' operators
}

// Generic sort
export interface Sort {
  field: string;
  direction: SortDirection;
}

// Generic pagination
export interface Pagination {
  page: number;
  limit: number;
  offset?: number;
  total?: number;
  totalPages?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

// Generic query options
export interface QueryOptions {
  filters?: Filter[];
  sorts?: Sort[];
  pagination?: Pagination;
  search?: string;
  fields?: string[];
}

// Generic list response
export interface ListResponse<T> {
  items: T[];
  pagination: Pagination;
  filters?: Filter[];
  sorts?: Sort[];
  search?: string;
}

// Generic form field
export interface FormField<T = any> {
  name: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'datetime' | 'file';
  value: T;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  validation?: ValidationRule[];
  options?: SelectOption<T>[];
  multiple?: boolean;
  accept?: string; // for file inputs
}

// Generic validation rule
export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'minLength' | 'maxLength' | 'pattern' | 'email' | 'url' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

// Generic form validation result
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
}

// Generic validation error
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  rule?: ValidationRule;
}

// Generic validation warning
export interface ValidationWarning {
  field: string;
  message: string;
  value?: any;
}

// Generic audit trail
export interface AuditTrail {
  id: string;
  entityType: string;
  entityId: string;
  action: 'create' | 'update' | 'delete' | 'view';
  changes?: Record<string, { old: any; new: any }>;
  userId: string;
  userEmail?: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

// Generic notification
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  userId?: string;
  metadata?: Record<string, any>;
  actions?: NotificationAction[];
}

// Generic notification action
export interface NotificationAction {
  id: string;
  label: string;
  type: 'button' | 'link';
  url?: string;
  action?: string;
  style?: 'primary' | 'secondary' | 'danger';
}

// Generic settings
export interface Settings {
  [key: string]: any;
}

// Generic preferences
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  numberFormat: string;
  currency: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    inApp: boolean;
  };
  dashboard: {
    layout: string;
    widgets: string[];
    refreshInterval: number;
  };
}

// Generic file info
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  path?: string;
  url?: string;
  thumbnail?: string;
}

// Generic progress info
export interface ProgressInfo {
  current: number;
  total: number;
  percentage: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  message?: string;
  startTime?: string;
  endTime?: string;
  estimatedTimeRemaining?: number;
}

// Generic cache info
export interface CacheInfo {
  key: string;
  value: any;
  timestamp: number;
  ttl: number;
  size?: number;
  hits?: number;
  misses?: number;
}

// Generic metric
export interface Metric {
  name: string;
  value: number;
  unit?: string;
  timestamp: string;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

// Generic coordinate
export interface Coordinate {
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
}

// Generic address
export interface Address {
  street?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  coordinates?: Coordinate;
}

// Generic contact info
export interface ContactInfo {
  email?: string;
  phone?: string;
  mobile?: string;
  fax?: string;
  website?: string;
  address?: Address;
}
