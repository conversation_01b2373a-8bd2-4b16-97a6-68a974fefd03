import React, { useState, useEffect, useCallback, useRef, memo, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';
import { ShadowPresets } from '../utils/shadowHelper';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { ProductionMetric } from '../types';
import { TimePeriod } from '../models/Production';
import DynamicChart from '../components/charts/DynamicChart';
import { ChartMetricConfig } from '../components/charts/ChartConfig';
import ProductionDataService from '../services/ProductionDataService';
import MiningCalculationService from '../services/MiningCalculationService';


interface ProductionOverviewScreenProps {
  navigation: any;
}

// Memoized Metric Card Component for better performance
const MetricCard = memo(({
  metric,
  index,
  totalCards,
  formatMetricValue,
  getProgressPercentage,
  getProgressColor
}: {
  metric: ProductionMetric;
  index: number;
  totalCards: number;
  formatMetricValue: (value: number, unit: string) => string;
  getProgressPercentage: (actual: number, plan: number) => number;
  getProgressColor: (percentage: number) => string;
}) => {
  const progressPercentage = getProgressPercentage(metric.actual, metric.plan);
  const progressColor = getProgressColor(progressPercentage);

  return (
    <View key={metric.id} style={[
      styles.metricCard,
      index === 0 && styles.firstCard,
      index === totalCards - 1 && styles.lastCard
    ]}>
      {/* Top section with icon and trend */}
      <View style={styles.cardTopSection}>
        <View style={[
          styles.iconContainer,
          { backgroundColor: `${Colors.primary}15` }
        ]}>
          <Ionicons
            name={metric.icon as any}
            size={24}
            color={Colors.primary}
          />
        </View>
        <View style={styles.trendContainer}>
          <Text style={[
            styles.trendText,
            { color: metric.trendPercentage >= 0 ? Colors.success : Colors.error }
          ]}>
            {metric.trendPercentage >= 0 ? '↗' : '↘'} {Math.abs(metric.trendPercentage).toFixed(1)}%
          </Text>
        </View>
      </View>

      {/* Metric name */}
      <Text style={styles.metricName}>{metric.name}</Text>

      {/* Values section */}
      <View style={styles.valuesSection}>
        <View style={styles.valueRow}>
          <Text style={styles.valueLabel}>Plan:</Text>
          <Text style={styles.planValue}>
            {formatMetricValue(metric.plan, metric.unit)}
          </Text>
        </View>
        <View style={styles.valueRow}>
          <Text style={styles.valueLabel}>Actual:</Text>
          <Text style={styles.actualValue}>
            {formatMetricValue(metric.actual, metric.unit)}
          </Text>
        </View>
      </View>

      {/* Progress bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${Math.min(progressPercentage, 100)}%`,
                backgroundColor: progressColor,
              },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: progressColor }]}>
          {progressPercentage.toFixed(0)}%
        </Text>
      </View>

      {/* Achievement badge */}
      <View style={[
        styles.achievementBadge,
        { backgroundColor: progressColor }
      ]}>
        <Text style={styles.achievementText}>
          {progressPercentage >= 90 ? 'Excellent' :
           progressPercentage >= 70 ? 'Good' : 'Needs Improvement'}
        </Text>
      </View>
    </View>
  );
});

// Memoized Loading Skeleton Component
const MetricCardSkeleton = memo(() => (
  <View style={[styles.metricCard, styles.skeletonCard]}>
    <View style={styles.skeletonHeader}>
      <View style={[styles.skeletonIcon, styles.skeletonShimmer]} />
      <View style={[styles.skeletonTrend, styles.skeletonShimmer]} />
    </View>
    <View style={[styles.skeletonTitle, styles.skeletonShimmer]} />
    <View style={styles.skeletonValues}>
      <View style={[styles.skeletonValue, styles.skeletonShimmer]} />
      <View style={[styles.skeletonValue, styles.skeletonShimmer]} />
    </View>
    <View style={[styles.skeletonProgress, styles.skeletonShimmer]} />
    <View style={[styles.skeletonBadge, styles.skeletonShimmer]} />
  </View>
));

// Helper function to calculate trend percentage
const calculateTrendPercentage = (achievementPercentage: number, target: number = 100): number => {
  const deviation = achievementPercentage - target;
  return Math.max(-10, Math.min(10, deviation * 0.1));
};

// REMOVED: Mock data helper functions - App now uses database data only

// REMOVED: All helper functions for mock data generation

// REMOVED: Mock data generation - App now uses database data only





// Helper function to generate aggregated metrics
const generateAggregatedMetricsFromLocal = (
  dailyData: any[],
  _period: string,
  getCardDataByPeriod?: (data: any[]) => any[]
): ProductionMetric[] => {
  if (!dailyData || dailyData.length === 0) {
    return [];
  }

  const cardData = getCardDataByPeriod ? getCardDataByPeriod(dailyData) : dailyData;

  const totalActualOb = cardData.reduce((sum, item) => sum + (item.actual_ob || 0), 0);
  const totalPlanOb = cardData.reduce((sum, item) => sum + (item.plan_ob || 0), 0);
  const totalActualOre = cardData.reduce((sum, item) => sum + (item.actual_ore || 0), 0);
  const totalPlanOre = cardData.reduce((sum, item) => sum + (item.plan_ore || 0), 0);
  const totalActualFuel = cardData.reduce((sum, item) => sum + (item.actual_fuel || 0), 0);
  const totalPlanFuel = cardData.reduce((sum, item) => sum + (item.plan_fuel || 0), 0);

  // Debug logging for Fuel Ratio calculation
  console.log(`🔍 Fuel Ratio Calculation - Period: ${_period}, Records: ${cardData.length}`);
  console.log(`📊 Raw Data - Fuel: ${totalActualFuel}L, OB: ${totalActualOb}Bcm, Ore: ${totalActualOre}tons`);

  // Log individual record details for debugging
  if (cardData.length <= 10) {
    console.log(`📋 Individual Records:`, cardData.map(item => ({
      date: item.date,
      fuel: item.actual_fuel,
      ob: item.actual_ob,
      ore: item.actual_ore
    })));
  }

  // Log summary for verification
  console.log(`📈 SUMMARY - Records: ${cardData.length}, Total Fuel: ${totalActualFuel}L, Total Material: ${(totalActualOb + (totalActualOre / 3.39)).toFixed(2)} Bcm`);

  // Calculate stripping ratio (OB/Ore)
  const stripRatio = totalActualOre > 0 ? totalActualOb / totalActualOre : 0;
  const planStripRatio = totalPlanOre > 0 ? totalPlanOb / totalPlanOre : 0;

  // Calculate fuel ratio using mining industry formula:
  // FR = Fuel Usage Actual / (Volume OB + (Volume Ore / 3.39))
  // Where:
  // - 3.39 is the density factor to convert ore tons to Bcm
  // - Result is fuel consumption per unit of total material moved (L/Bcm)
  // - Lower FR = more fuel efficient operation
  const totalActualMaterial = totalActualOb + (totalActualOre / 3.39);
  const totalPlanMaterial = totalPlanOb + (totalPlanOre / 3.39);

  const fuelRatio = totalActualMaterial > 0 ? totalActualFuel / totalActualMaterial : 0;
  const planFuelRatio = totalPlanMaterial > 0 ? totalPlanFuel / totalPlanMaterial : 0;

  // Debug logging for Fuel Ratio result
  console.log(`🧮 Calculation Steps:`);
  console.log(`   Ore in Bcm: ${totalActualOre} tons ÷ 3.39 = ${(totalActualOre / 3.39).toFixed(2)} Bcm`);
  console.log(`   Total Material: ${totalActualOb} + ${(totalActualOre / 3.39).toFixed(2)} = ${totalActualMaterial.toFixed(2)} Bcm`);
  console.log(`   Fuel Ratio: ${totalActualFuel} ÷ ${totalActualMaterial.toFixed(2)} = ${fuelRatio.toFixed(4)} L/Bcm`);
  console.log(`🎯 Fuel Ratio Result - Actual: ${fuelRatio.toFixed(2)} L/Bcm, Plan: ${planFuelRatio.toFixed(2)} L/Bcm`);
  console.log(`📏 Material Calculation - Total Actual: ${totalActualMaterial.toFixed(2)} Bcm, Total Plan: ${totalPlanMaterial.toFixed(2)} Bcm`);

  // Calculate Total Material Moved in Bcm using formula: OB + (Ore / 3.39)
  const totalMaterialMovedBcm = totalActualOb + (totalActualOre / 3.39);
  const totalPlanMaterialMovedBcm = totalPlanOb + (totalPlanOre / 3.39);

  return [
    {
      id: 'total_material',
      name: 'Total Material Moved',
      icon: 'layers-outline',
      unit: 'Bcm',
      plan: totalPlanMaterialMovedBcm,
      actual: totalMaterialMovedBcm,
      achievementPercentage: totalPlanMaterialMovedBcm > 0 ? (totalMaterialMovedBcm / totalPlanMaterialMovedBcm) * 100 : 0,
      trendPercentage: calculateTrendPercentage(totalPlanMaterialMovedBcm > 0 ? (totalMaterialMovedBcm / totalPlanMaterialMovedBcm) * 100 : 0),
      trendDirection: 'stable' as const,
      isPositiveMetric: true
    },
    {
      id: 'overburden',
      name: 'Overburden (OB) Volume',
      icon: 'construct-outline',
      unit: 'Bcm',
      plan: totalPlanOb,
      actual: totalActualOb,
      achievementPercentage: totalPlanOb > 0 ? (totalActualOb / totalPlanOb) * 100 : 0,
      trendPercentage: calculateTrendPercentage(totalPlanOb > 0 ? (totalActualOb / totalPlanOb) * 100 : 0),
      trendDirection: 'stable' as const,
      isPositiveMetric: true
    },
    {
      id: 'ore_production',
      name: 'Ore Volume',
      icon: 'cube-outline',
      unit: 'tons',
      plan: totalPlanOre,
      actual: totalActualOre,
      achievementPercentage: totalPlanOre > 0 ? (totalActualOre / totalPlanOre) * 100 : 0,
      trendPercentage: calculateTrendPercentage(totalPlanOre > 0 ? (totalActualOre / totalPlanOre) * 100 : 0),
      trendDirection: 'stable' as const,
      isPositiveMetric: true
    },
    {
      id: 'stripping_ratio',
      name: 'Stripping Ratio',
      icon: 'analytics-outline',
      unit: 'ratio',
      plan: planStripRatio,
      actual: stripRatio,
      achievementPercentage: planStripRatio > 0 ? Math.min((planStripRatio / stripRatio) * 100, 150) : 100,
      trendPercentage: calculateTrendPercentage(planStripRatio > 0 ? Math.min((planStripRatio / stripRatio) * 100, 150) : 100),
      trendDirection: 'stable' as const,
      isPositiveMetric: false
    },
    {
      id: 'fuel_usage',
      name: 'Fuel Usage',
      icon: 'car-outline',
      unit: 'L',
      plan: totalPlanFuel,
      actual: totalActualFuel,
      achievementPercentage: totalPlanFuel > 0 ? Math.min((totalPlanFuel / totalActualFuel) * 100, 150) : 100,
      trendPercentage: calculateTrendPercentage(totalPlanFuel > 0 ? Math.min((totalPlanFuel / totalActualFuel) * 100, 150) : 100),
      trendDirection: 'stable' as const,
      isPositiveMetric: false
    },
    {
      id: 'fuel_ratio',
      name: 'Fuel Ratio',
      icon: 'speedometer-outline',
      unit: 'L/Bcm',
      plan: planFuelRatio,
      actual: fuelRatio,
      achievementPercentage: planFuelRatio > 0 ? Math.min((planFuelRatio / fuelRatio) * 100, 150) : 100,
      trendPercentage: calculateTrendPercentage(planFuelRatio > 0 ? Math.min((planFuelRatio / fuelRatio) * 100, 150) : 100),
      trendDirection: 'stable' as const,
      isPositiveMetric: false
    }
  ];
};

const ProductionOverviewScreen: React.FC<ProductionOverviewScreenProps> = ({ navigation }) => {
  // State declarations
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('Daily');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [productionMetrics, setProductionMetrics] = useState<ProductionMetric[]>([]);
  const [dailyData, setDailyData] = useState<any[]>([]);

  // Data service
  const [dataService] = useState(() => ProductionDataService.getInstance());


  // Date range state
  const [customDateRange, setCustomDateRange] = useState<{start: Date | null, end: Date | null}>({
    start: null,
    end: null
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState<'start' | 'end'>('start');


  // Ref to prevent multiple simultaneous calls
  const isLoadingRef = useRef(false);

  // Format metric value based on unit with thousand separators
  const formatMetricValue = useCallback((value: number, unit: string) => {
    // Helper function to add thousand separators
    const formatNumber = (num: number, decimals: number = 2) => {
      return num.toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      });
    };

    switch (unit) {
      case 'Bcm':
        return `${formatNumber(value)} Bcm`;
      case 'ton':
        return `${formatNumber(value)} ton`;
      case 'tons':
        return `${formatNumber(value)} tons`;
      case 'L':
        return `${formatNumber(value)} L`;
      case 'ratio':
        return formatNumber(value);
      default:
        return formatNumber(value);
    }
  }, []);

  // Calculate progress percentage for Plan vs Actual
  const getProgressPercentage = useCallback((actual: number, plan: number) => {
    if (plan === 0) return 0;
    const percentage = (actual / plan) * 100;
    return Math.min(percentage, 100); // Cap at 100%
  }, []);

  // Get progress color based on percentage
  const getProgressColor = useCallback((percentage: number) => {
    if (percentage >= 90) return Colors.success;
    if (percentage >= 70) return Colors.warning;
    return Colors.error;
  }, []);

  // Date range preset functions
  const getDateRangePreset = useCallback((preset: string) => {
    // Use a fixed reference date to ensure consistency
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // Reset time to 00:00:00

    switch (preset) {
      case 'today':
        // Only today - use current date dynamically
        const todayStart = new Date(today);
        const todayEnd = new Date(today);
        todayEnd.setHours(23, 59, 59, 999);
        console.log(`📅 Today preset: ${todayStart.toISOString().split('T')[0]}`);
        return { start: todayStart, end: todayEnd };

      case 'yesterday':
        // Only yesterday - calculate dynamically
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        const yesterdayEnd = new Date(yesterday);
        yesterdayEnd.setHours(23, 59, 59, 999);
        console.log(`📅 Yesterday preset: ${yesterday.toISOString().split('T')[0]}`);
        return { start: yesterday, end: yesterdayEnd };

      case 'last7days':
        // Last 7 days including today (7 days total)
        const last7Start = new Date(today);
        last7Start.setDate(today.getDate() - 6); // 6 days ago + today = 7 days
        const last7End = new Date(today);
        last7End.setHours(23, 59, 59, 999);
        return { start: last7Start, end: last7End };

      case 'last30days':
        // Last 30 days including today (30 days total)
        const last30Start = new Date(today);
        last30Start.setDate(today.getDate() - 29); // 29 days ago + today = 30 days
        const last30End = new Date(today);
        last30End.setHours(23, 59, 59, 999);
        return { start: last30Start, end: last30End };

      case 'thisMonth':
        // From 1st of current month to today
        const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const thisMonthEnd = new Date(today);
        thisMonthEnd.setHours(23, 59, 59, 999);
        return { start: thisMonthStart, end: thisMonthEnd };

      case 'lastMonth':
        // Full previous month
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0); // Last day of previous month
        lastMonthEnd.setHours(23, 59, 59, 999);
        return { start: lastMonthStart, end: lastMonthEnd };

      default:
        return { start: null, end: null };
    }
  }, []);

  // Apply date range preset
  const applyDateRangePreset = useCallback((preset: string) => {
    const range = getDateRangePreset(preset);
    setCustomDateRange(range);
    if (preset !== 'custom') {
      setSelectedPeriod('Daily'); // Reset to daily when using custom range
    }
    console.log(`🔄 Applied date range preset: ${preset}`, range);
    console.log(`📊 This will trigger Fuel Ratio recalculation for new date range`);
  }, [getDateRangePreset]);

  // Date picker handlers
  const handleDatePickerOpen = useCallback((mode: 'start' | 'end') => {
    setDatePickerMode(mode);
    setShowDatePicker(true);
  }, []);

  const handleDatePickerChange = useCallback((_event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate) {
      setCustomDateRange(prev => ({
        ...prev,
        [datePickerMode]: selectedDate
      }));
      console.log(`Date picker: ${datePickerMode} date set to`, selectedDate);
    }
  }, [datePickerMode]);

  // Get trend percentage based on period
  const getTrendPercentage = useCallback((metricId: string) => {
    const trendData = {
      Daily: {
        overburden: 2.1,
        ore_production: 1.8,
        stripping_ratio: -0.5,
        fuel_usage: -0.3,
        fuel_ratio: 0.2
      },
      Weekly: {
        overburden: 4.4,
        ore_production: 3.9,
        stripping_ratio: -1.2,
        fuel_usage: -0.8,
        fuel_ratio: 0.5
      },
      Monthly: {
        overburden: 8.7,
        ore_production: 6.2,
        stripping_ratio: -2.8,
        fuel_usage: -1.5,
        fuel_ratio: 1.1
      },
      Yearly: {
        overburden: 15.3,
        ore_production: 12.4,
        stripping_ratio: -5.1,
        fuel_usage: -3.2,
        fuel_ratio: 2.3
      }
    };

    const periodData = trendData[selectedPeriod as keyof typeof trendData];
    return periodData[metricId as keyof typeof periodData] || 0;
  }, [selectedPeriod]);

  // Helper function for card data filtering
  const getCardDataByPeriod = (dailyData: any[], period: TimePeriod) => {
    // If custom date range is set, use that instead of period-based filtering
    if (customDateRange.start && customDateRange.end) {
      const startStr = customDateRange.start.toISOString().split('T')[0];
      const endStr = customDateRange.end.toISOString().split('T')[0];

      console.log(`Filtering data by custom range: ${startStr} to ${endStr}`);

      const filteredData = dailyData.filter(item => {
        return item.date >= startStr && item.date <= endStr;
      });

      console.log(`Custom range filter result: ${filteredData.length} records from ${dailyData.length} total`);
      return filteredData;
    }

    // Default period-based filtering
    const now = new Date();

    switch (period) {
      case 'Daily':
        const todayStr = now.toISOString().split('T')[0];
        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        let dailyResult = dailyData.filter(item => item.date === todayStr);
        if (dailyResult.length === 0) {
          dailyResult = dailyData.filter(item => item.date === yesterdayStr);
        }

        if (dailyResult.length === 0 && dailyData.length > 0) {
          const sortedData = [...dailyData].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
          dailyResult = [sortedData[0]];
        }

        console.log(`Daily filter result: ${dailyResult.length} records for ${todayStr}`);
        return dailyResult;

      case 'Weekly':
        const currentWeekStart = new Date(now);
        const dayOfWeek = now.getDay();
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        currentWeekStart.setDate(now.getDate() - daysToMonday);

        const currentWeekEnd = new Date(currentWeekStart);
        currentWeekEnd.setDate(currentWeekStart.getDate() + 6);

        const weeklyResult = dailyData.filter(item => {
          const itemDate = new Date(item.date);
          return itemDate >= currentWeekStart && itemDate <= currentWeekEnd;
        });

        console.log(`Weekly filter result: ${weeklyResult.length} records`);
        return weeklyResult;

      case 'Monthly':
        const currentMonth = now.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        const monthlyResult = dailyData.filter(item => item.monthly === currentMonth);

        console.log(`Monthly filter result: ${monthlyResult.length} records for ${currentMonth}`);
        return monthlyResult;

      case 'Yearly':
        const currentYear = now.getFullYear();
        const yearlyResult = dailyData.filter(item => {
          const itemYear = new Date(item.date).getFullYear();
          return itemYear === currentYear;
        });

        console.log(`Yearly filter result: ${yearlyResult.length} records for ${currentYear}`);
        return yearlyResult;

      default:
        return dailyData;
    }
  };





  // Get effective date range based on custom range or default
  const getEffectiveDateRange = useCallback(() => {
    if (customDateRange.start && customDateRange.end) {
      return {
        startDate: customDateRange.start.toISOString().split('T')[0],
        endDate: customDateRange.end.toISOString().split('T')[0]
      };
    }

    // Dynamic range - always use current date as end date
    const now = new Date();
    const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // Today
    const startDate = new Date(endDate); // Start from 30 days ago for better performance
    startDate.setDate(endDate.getDate() - 30);

    console.log(`📅 Dynamic date range: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  }, [customDateRange]);

  // Load production data function with enhanced error handling
  const loadSampleData = async () => {
    if (isLoadingRef.current) return;

    isLoadingRef.current = true;
    setLoading(true);

    try {
      // Initialize data service if not already done
      const initialized = await dataService.initialize();
      if (!initialized) {
        console.log('⚠️ Data service initialization failed, but continuing...');
      }

      const { startDate, endDate } = getEffectiveDateRange();
      console.log(`📊 Loading production data from ${startDate} to ${endDate}`);
      console.log(`🕐 Current time: ${new Date().toISOString()}`);

      // Get data from database with retry logic
      let productionData: any[] = [];
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries && productionData.length === 0) {
        try {
          productionData = await dataService.getProductionDataByDateRange(startDate, endDate);
          if (productionData.length > 0) break;
        } catch (error) {
          console.error(`❌ Attempt ${retryCount + 1} failed:`, error);
        }
        retryCount++;
        if (retryCount < maxRetries) {
          console.log(`🔄 Retrying... (${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
        }
      }

      if (productionData.length === 0) {
        console.log('⚠️ No production data found in database after retries');
        console.log(`📅 Requested range: ${startDate} to ${endDate}`);
        console.log('💡 Try selecting a different date range or check database connectivity');
        setDailyData([]);
        setProductionMetrics([]);
        return;
      }

      setDailyData(productionData);

      // Generate metrics for cards and set them
      const metrics = generateAggregatedMetricsFromLocal(productionData, selectedPeriod, (data) => getCardDataByPeriod(data, selectedPeriod));
      setProductionMetrics(metrics);

      console.log(`✅ Successfully loaded ${productionData.length} production records from database`);
      console.log(`📈 Generated ${metrics.length} production metrics for display`);
    } catch (error) {
      console.error('❌ Critical error loading production data:', error);
      console.log('🔧 Please check database connection and try again');

      // Show empty state with error indication
      setDailyData([]);
      setProductionMetrics([]);
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  };

  // Load data on mount and set up auto-refresh
  useEffect(() => {
    loadSampleData();

    // Auto-refresh every 5 minutes for real-time data
    const refreshInterval = setInterval(() => {
      console.log('🔄 Auto-refreshing production data...');
      loadSampleData();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(refreshInterval);
  }, []); // Only run once on mount

  // Update when period changes
  useEffect(() => {
    if (dailyData.length > 0) {
      console.log(`🔄 Period changed to: ${selectedPeriod} - Recalculating all metrics including Fuel Ratio`);
      // Update metrics for cards
      const metrics = generateAggregatedMetricsFromLocal(dailyData, selectedPeriod, (data) => getCardDataByPeriod(data, selectedPeriod));
      setProductionMetrics(metrics);
      console.log(`✅ Metrics updated for period: ${selectedPeriod}`);
    }
  }, [selectedPeriod]); // Only depend on period selection

  // Load data when date range changes (debounced for performance)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadSampleData();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [customDateRange]); // Reload data when date range changes

  // Refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadSampleData().finally(() => setRefreshing(false));
  }, [loadSampleData]);

  // Period change handler
  const handlePeriodChange = useCallback((period: TimePeriod) => {
    setSelectedPeriod(period);
  }, []);

  // Export functionality
  const exportData = useCallback(async (format: 'csv' | 'json') => {
    try {
      const exportData = {
        period: selectedPeriod,
        exportDate: new Date().toISOString(),
        metrics: productionMetrics.map(metric => ({
          id: metric.id,
          name: metric.name, // Use the actual name from the metric
          plan: metric.plan,
          actual: metric.actual,
          unit: metric.unit,
          achievement: Math.round((metric.actual / metric.plan) * 100),
          trend: getTrendPercentage(metric.id)
        })),
        summary: {
          totalRecords: dailyData.length,
          dateRange: {
            start: dailyData[0]?.date || 'N/A',
            end: dailyData[dailyData.length - 1]?.date || 'N/A'
          }
        }
      };

      if (format === 'csv') {
        // Convert to CSV format
        const csvHeader = 'Metric,Plan,Actual,Unit,Achievement %,Trend %\n';
        const csvData = exportData.metrics.map(m =>
          `${m.name},${m.plan},${m.actual},${m.unit},${m.achievement},${m.trend}`
        ).join('\n');
        const csvContent = csvHeader + csvData;

        console.log('CSV Export:', csvContent);
        // TODO: Implement actual file export
      } else {
        console.log('JSON Export:', JSON.stringify(exportData, null, 2));
        // TODO: Implement actual file export
      }

      // Show success message (you can replace with proper toast/alert)
      console.log(`Data exported successfully as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, [selectedPeriod, productionMetrics, dailyData, getTrendPercentage]);

  // Settings handler
  const handleSettings = useCallback(() => {
    console.log('Settings pressed');
    // TODO: Navigate to settings screen or show settings modal
  }, []);

  // Memoized calculations for better performance
  const memoizedMetrics = useMemo(() => {
    return productionMetrics.map(metric => ({
      ...metric,
      progressPercentage: getProgressPercentage(metric.actual, metric.plan),
      progressColor: getProgressColor(getProgressPercentage(metric.actual, metric.plan)),
      trendValue: getTrendPercentage(metric.id)
    }));
  }, [productionMetrics, getProgressPercentage, getProgressColor, getTrendPercentage]);

  // Memoized date range display
  const dateRangeDisplay = useMemo(() => {
    if (customDateRange.start && customDateRange.end) {
      return `${customDateRange.start.toLocaleDateString()} - ${customDateRange.end.toLocaleDateString()}`;
    }
    return null;
  }, [customDateRange.start, customDateRange.end]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        {navigation.canGoBack() && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.textInverse} />
          </TouchableOpacity>
        )}
        <Text style={styles.headerTitle}>Production Overview</Text>
        <View style={styles.headerActions}>
          {/* Export Button */}
          <TouchableOpacity
            onPress={() => exportData('csv')}
            style={styles.actionButton}
          >
            <Ionicons name="download-outline" size={20} color={Colors.textInverse} />
          </TouchableOpacity>

          {/* Date Range Button */}
          <TouchableOpacity
            onPress={() => handleDatePickerOpen('start')}
            style={styles.actionButton}
          >
            <Ionicons name="calendar-outline" size={20} color={Colors.textInverse} />
          </TouchableOpacity>

          {/* Settings Button */}
          <TouchableOpacity onPress={handleSettings} style={styles.actionButton}>
            <Ionicons name="settings-outline" size={20} color={Colors.textInverse} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Sticky Period Selection */}
      <View style={styles.stickyPeriodContainer}>
        <View style={styles.periodContainer}>
          {(['Daily', 'Weekly', 'Monthly', 'Yearly'] as TimePeriod[]).map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && styles.periodButtonActive
              ]}
              onPress={() => handlePeriodChange(period)}
            >
              <Text style={[
                styles.periodButtonText,
                selectedPeriod === period && styles.periodButtonTextActive
              ]}>
                {period}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Database Status - Simplified */}

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
      >

        {/* Quick Date Range Presets */}
        <View style={styles.datePresetsContainer}>
          <Text style={styles.datePresetsTitle}>Quick Ranges:</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.datePresetsScroll}
          >
            {[
              { key: 'today', label: 'Today' },
              { key: 'yesterday', label: 'Yesterday' },
              { key: 'last7days', label: 'Last 7 Days' },
              { key: 'last30days', label: 'Last 30 Days' },
              { key: 'thisMonth', label: 'This Month' },
              { key: 'lastMonth', label: 'Last Month' }
            ].map((preset) => (
              <TouchableOpacity
                key={preset.key}
                style={styles.datePresetButton}
                onPress={() => applyDateRangePreset(preset.key)}
              >
                <Text style={styles.datePresetText}>{preset.label}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Active Date Range Indicator */}
        {(customDateRange.start || customDateRange.end) && (
          <View style={styles.activeDateRangeContainer}>
            <Ionicons name="calendar" size={14} color={Colors.primary} />
            <Text style={styles.activeDateRangeText}>
              {dateRangeDisplay || `${customDateRange.start ? customDateRange.start.toLocaleDateString() : 'Start'} - ${customDateRange.end ? customDateRange.end.toLocaleDateString() : 'End'}`}
            </Text>
            <TouchableOpacity
              onPress={() => setCustomDateRange({ start: null, end: null })}
              style={styles.clearDateRangeButton}
            >
              <Ionicons name="close-circle" size={16} color={Colors.textSecondary} />
            </TouchableOpacity>
          </View>
        )}

        {loading ? (
          <View>
            <Text style={styles.sectionTitle}>Production Metrics</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.metricsScrollContainer}
              style={styles.metricsScrollView}
            >
              {Array.from({ length: 6 }).map((_, index) => (
                <MetricCardSkeleton key={index} />
              ))}
            </ScrollView>
          </View>
        ) : (
          <View>
            <Text style={styles.sectionTitle}>Production Metrics</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.metricsScrollContainer}
              style={styles.metricsScrollView}
            >
              {memoizedMetrics.map((metric, index) => (
                <MetricCard
                  key={metric.id}
                  metric={metric}
                  index={index}
                  totalCards={memoizedMetrics.length}
                  formatMetricValue={formatMetricValue}
                  getProgressPercentage={getProgressPercentage}
                  getProgressColor={getProgressColor}
                />
              ))}
            </ScrollView>

            {/* Dynamic Chart System */}
            <DynamicChart
              data={dailyData}
              timePeriod={selectedPeriod}
              isLoading={loading}
              error={null}
              onRetry={loadSampleData}
              isDarkMode={false}
              onMetricChange={(metric: ChartMetricConfig) => {
                console.log('Chart metric changed to:', metric.name);
              }}
              onChartTypeChange={(chartType) => {
                console.log('Chart type changed to:', chartType);
              }}
            />
          </View>
        )}
      </ScrollView>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={datePickerMode === 'start' ? (customDateRange.start || new Date()) : (customDateRange.end || new Date())}
          mode="date"
          display="default"
          onChange={handleDatePickerChange}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: 12,
    backgroundColor: Colors.primary,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  headerRight: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: Layout.spacing.md,
    paddingTop: 0, // Remove top padding since sticky container provides spacing
  },
  stickyPeriodContainer: {
    backgroundColor: Colors.background,
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    ...ShadowPresets.small,
  },
  periodContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 0,
    backgroundColor: Colors.surface,
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: Colors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: Colors.textInverse,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.textSecondary,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 16,
  },
  metricsScrollView: {
    marginHorizontal: -Layout.spacing.md,
  },
  metricsScrollContainer: {
    paddingHorizontal: Layout.spacing.md,
  },
  metricCard: {
    width: 200,
    backgroundColor: Colors.surface,
    padding: 10,
    borderRadius: 12,
    marginRight: 16,
    marginBottom: 20,
    ...ShadowPresets.button,
  },
  firstCard: {
    marginLeft: 0,
  },
  lastCard: {
    marginRight: Layout.spacing.md,
  },
  cardTopSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 16,
    lineHeight: 18,
  },
  valuesContainer: {
    marginBottom: 16,
  },
  valueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  valueLabel: {
    fontSize: 13,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  valueNumber: {
    fontSize: 13,
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  achievementBadgeContainer: {
    alignItems: 'flex-end',
  },
  achievementBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    gap: 4,
  },
  achievementBadgeText: {
    fontSize: 11,
    fontWeight: '600',
  },
  // New styles for optimized MetricCard
  metricName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.sm,
    textAlign: 'center',
  },
  valuesSection: {
    marginBottom: Layout.spacing.sm,
  },
  planValue: {
    fontSize: 13,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  actualValue: {
    fontSize: 13,
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
    gap: Layout.spacing.xs,
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: Colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
  achievementText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.textInverse,
    textAlign: 'center',
  },
  achievementLabel: {
    fontSize: 10,
    color: Colors.textSecondary,
    marginTop: 2,
    textAlign: 'right',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 11,
    fontWeight: '600',
    marginLeft: 2,
  },

  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Layout.spacing.sm,
  },
  actionButton: {
    padding: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  datePresetsContainer: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    backgroundColor: Colors.surface,
    marginHorizontal: Layout.spacing.md,
    marginBottom: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
  },
  datePresetsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginBottom: Layout.spacing.xs,
  },
  datePresetsScroll: {
    gap: Layout.spacing.xs,
  },
  datePresetButton: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    backgroundColor: Colors.background,
    borderRadius: Layout.borderRadius.sm,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  datePresetText: {
    fontSize: 11,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  activeDateRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    marginHorizontal: Layout.spacing.md,
    marginBottom: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    borderColor: Colors.primary,
    gap: Layout.spacing.sm,
  },
  activeDateRangeText: {
    flex: 1,
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '600',
  },
  clearDateRangeButton: {
    padding: Layout.spacing.xs,
  },
  // Skeleton loading styles
  skeletonCard: {
    opacity: 0.7,
  },
  skeletonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  skeletonIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  skeletonTrend: {
    width: 50,
    height: 16,
    borderRadius: 8,
  },
  skeletonTitle: {
    width: '80%',
    height: 16,
    borderRadius: 8,
    marginBottom: Layout.spacing.sm,
    alignSelf: 'center',
  },
  skeletonValues: {
    marginBottom: Layout.spacing.sm,
  },
  skeletonValue: {
    width: '100%',
    height: 14,
    borderRadius: 7,
    marginBottom: Layout.spacing.xs,
  },
  skeletonProgress: {
    width: '100%',
    height: 6,
    borderRadius: 3,
    marginBottom: Layout.spacing.sm,
  },
  skeletonBadge: {
    width: '60%',
    height: 24,
    borderRadius: 12,
    alignSelf: 'center',
  },
  skeletonShimmer: {
    backgroundColor: Colors.border,
  },
});

// Export memoized component for better performance
export default memo(ProductionOverviewScreen);