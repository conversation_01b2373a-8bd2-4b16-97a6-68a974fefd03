# 🔄 System Flow Diagrams

## 📋 Table of Contents
- [Data Flow Architecture](#data-flow-architecture)
- [User Authentication Flow](#user-authentication-flow)
- [Production Data Flow](#production-data-flow)
- [Offline Sync Flow](#offline-sync-flow)
- [Desktop-Mobile Integration](#desktop-mobile-integration)
- [Error Handling Flow](#error-handling-flow)

## 🏗️ Data Flow Architecture

### Overall System Architecture
```mermaid
graph TB
    subgraph "Mobile App"
        MA[Mobile App]
        MLS[Local SQLite]
        MSM[Sync Manager]
    end
    
    subgraph "Desktop App"
        DA[Desktop App]
        DFS[File System]
        DBM[DB Manager]
    end
    
    subgraph "Backend Services"
        SB[Supabase]
        PG[(PostgreSQL)]
        ST[Storage]
        RT[Realtime]
    end
    
    subgraph "External Systems"
        ERP[ERP Systems]
        IOT[IoT Sensors]
        WS[Weather Service]
    end
    
    MA <--> MLS
    MA <--> MSM
    MSM <--> SB
    
    DA <--> DFS
    DA <--> DBM
    DBM <--> SB
    
    SB <--> PG
    SB <--> ST
    SB <--> RT
    
    SB <--> ERP
    SB <--> IOT
    SB <--> WS
    
    RT -.-> MA
    RT -.-> DA
```

### Data Layer Architecture
```mermaid
graph LR
    subgraph "Presentation Layer"
        UI[User Interface]
        CH[Charts]
        FO[Forms]
    end
    
    subgraph "Business Logic Layer"
        SV[Services]
        RP[Repositories]
        UT[Utils]
    end
    
    subgraph "Data Access Layer"
        API[API Client]
        LS[Local Storage]
        CA[Cache]
    end
    
    subgraph "Data Sources"
        DB[(Database)]
        FS[File System]
        EX[External APIs]
    end
    
    UI --> SV
    CH --> SV
    FO --> SV
    
    SV --> RP
    SV --> UT
    
    RP --> API
    RP --> LS
    RP --> CA
    
    API --> DB
    LS --> FS
    CA --> DB
    
    DB --> EX
```

## 🔐 User Authentication Flow

### Login Process
```mermaid
sequenceDiagram
    participant U as User
    participant MA as Mobile App
    participant DA as Desktop App
    participant SB as Supabase Auth
    participant DB as Database
    
    Note over U,DB: Mobile Login Flow
    U->>MA: Enter credentials
    MA->>SB: Login request
    SB->>DB: Validate user
    DB-->>SB: User data
    SB-->>MA: JWT token + user info
    MA->>MA: Store token locally
    MA-->>U: Login success
    
    Note over U,DB: Desktop Login Flow
    U->>DA: Enter credentials
    DA->>SB: Login request
    SB->>DB: Validate user
    DB-->>SB: User data
    SB-->>DA: JWT token + user info
    DA->>DA: Store token securely
    DA-->>U: Login success
    
    Note over U,DB: Token Refresh
    MA->>SB: Refresh token
    SB-->>MA: New JWT token
    DA->>SB: Refresh token
    SB-->>DA: New JWT token
```

### Role-Based Access Control
```mermaid
graph TD
    A[User Login] --> B{Authenticate}
    B -->|Success| C[Get User Role]
    B -->|Failure| D[Login Failed]
    
    C --> E{Role Check}
    E -->|Admin| F[Full Access]
    E -->|Supervisor| G[Management Access]
    E -->|Operator| H[Field Access]
    E -->|Viewer| I[Read-Only Access]
    
    F --> J[Dashboard + Admin Panel]
    G --> K[Dashboard + Reports]
    H --> L[Data Entry + View]
    I --> M[View Only]
    
    J --> N[All Features]
    K --> O[Production + Equipment]
    L --> P[Basic Operations]
    M --> Q[Read-Only Mode]
```

## 📊 Production Data Flow

### Data Entry and Processing
```mermaid
flowchart TD
    A[Field Operator] --> B[Mobile Data Entry]
    B --> C{Validation}
    C -->|Valid| D[Local Storage]
    C -->|Invalid| E[Error Message]
    E --> B
    
    D --> F{Network Available?}
    F -->|Yes| G[Sync to Server]
    F -->|No| H[Queue for Sync]
    
    G --> I[Server Validation]
    I -->|Valid| J[Database Update]
    I -->|Invalid| K[Sync Error]
    K --> L[Conflict Resolution]
    L --> M[Manual Review]
    
    J --> N[Real-time Update]
    N --> O[Mobile Dashboard]
    N --> P[Desktop Dashboard]
    
    H --> Q[Auto Sync When Online]
    Q --> G
    
    subgraph "Desktop Data Management"
        R[Bulk Import] --> S[Data Validation]
        S --> T[Batch Processing]
        T --> U[Database Update]
        U --> V[Audit Log]
    end
    
    R --> I
```

### Chart Data Processing
```mermaid
graph LR
    subgraph "Data Sources"
        DB[(Database)]
        LS[Local Storage]
        CA[Cache]
    end
    
    subgraph "Data Processing"
        DP[Data Processor]
        AG[Aggregator]
        CA2[Calculator]
    end
    
    subgraph "Chart Generation"
        CD[Chart Data]
        CF[Chart Config]
        CR[Chart Renderer]
    end
    
    subgraph "Display"
        MC[Mobile Chart]
        DC[Desktop Chart]
    end
    
    DB --> DP
    LS --> DP
    CA --> DP
    
    DP --> AG
    AG --> CA2
    CA2 --> CD
    
    CD --> CF
    CF --> CR
    
    CR --> MC
    CR --> DC
```

## 🔄 Offline Sync Flow

### Offline Operation Flow
```mermaid
stateDiagram-v2
    [*] --> Online
    Online --> Offline : Network Lost
    Offline --> QueueOperations : User Actions
    QueueOperations --> LocalStorage : Store Locally
    LocalStorage --> ConflictCheck : Network Restored
    ConflictCheck --> AutoResolve : No Conflicts
    ConflictCheck --> ManualResolve : Conflicts Found
    AutoResolve --> Sync
    ManualResolve --> UserDecision
    UserDecision --> Sync : Resolution Complete
    Sync --> Online : Sync Complete
    
    state QueueOperations {
        [*] --> DataEntry
        DataEntry --> Validation
        Validation --> Queue
        Queue --> [*]
    }
    
    state ConflictCheck {
        [*] --> CompareTimestamps
        CompareTimestamps --> CheckDataIntegrity
        CheckDataIntegrity --> [*]
    }
```

### Sync Strategy
```mermaid
flowchart TD
    A[Network Restored] --> B[Check Sync Queue]
    B --> C{Queue Empty?}
    C -->|Yes| D[No Sync Needed]
    C -->|No| E[Process Queue Items]
    
    E --> F[Get Next Item]
    F --> G{Conflict Check}
    G -->|No Conflict| H[Upload to Server]
    G -->|Conflict| I[Conflict Resolution]
    
    H --> J{Upload Success?}
    J -->|Yes| K[Remove from Queue]
    J -->|No| L[Retry Logic]
    
    I --> M{Resolution Type}
    M -->|Auto| N[Apply Auto Rules]
    M -->|Manual| O[User Intervention]
    
    N --> H
    O --> P[User Decision]
    P --> H
    
    K --> Q{More Items?}
    L --> R[Exponential Backoff]
    R --> F
    
    Q -->|Yes| F
    Q -->|No| S[Sync Complete]
    
    S --> T[Update UI]
    T --> U[Notify User]
```

## 🖥️ Desktop-Mobile Integration

### Cross-Platform Data Sync
```mermaid
sequenceDiagram
    participant M as Mobile App
    participant S as Supabase
    participant D as Desktop App
    participant F as File System
    
    Note over M,F: Real-time Sync
    M->>S: Update production data
    S->>S: Process & validate
    S-->>D: Real-time notification
    D->>D: Update local cache
    D-->>D: Refresh UI
    
    Note over M,F: Bulk Operations (Desktop)
    D->>F: Read CSV file
    F-->>D: Raw data
    D->>D: Validate & process
    D->>S: Bulk upload
    S->>S: Process batch
    S-->>M: Real-time updates
    M->>M: Update local data
    
    Note over M,F: Report Generation
    D->>S: Request report data
    S-->>D: Aggregated data
    D->>D: Generate report
    D->>F: Save PDF/Excel
    D->>S: Store report metadata
    S-->>M: Report available notification
```

### Feature Distribution
```mermaid
graph TB
    subgraph "Mobile App Features"
        MF1[Field Data Entry]
        MF2[Real-time Dashboard]
        MF3[Offline Operations]
        MF4[Equipment Status]
        MF5[Basic Reports]
    end
    
    subgraph "Desktop App Features"
        DF1[Bulk Data Import/Export]
        DF2[Advanced Analytics]
        DF3[Document Management]
        DF4[User Administration]
        DF5[System Configuration]
        DF6[Report Builder]
        DF7[Data Validation Tools]
    end
    
    subgraph "Shared Features"
        SF1[Authentication]
        SF2[Production Metrics]
        SF3[Equipment Management]
        SF4[Basic Charts]
        SF5[Data Sync]
    end
    
    MF1 -.-> SF2
    MF2 -.-> SF4
    MF3 -.-> SF5
    MF4 -.-> SF3
    MF5 -.-> SF4
    
    DF1 -.-> SF2
    DF2 -.-> SF4
    DF6 -.-> SF4
    DF7 -.-> SF2
    
    SF1 --> MF1
    SF1 --> DF1
    SF2 --> MF2
    SF2 --> DF2
    SF3 --> MF4
    SF3 --> DF4
    SF4 --> MF5
    SF4 --> DF6
    SF5 --> MF3
    SF5 --> DF1
```

## ❌ Error Handling Flow

### Error Classification and Handling
```mermaid
flowchart TD
    A[Error Occurred] --> B{Error Type}
    
    B -->|Network| C[Network Error]
    B -->|Validation| D[Validation Error]
    B -->|Authentication| E[Auth Error]
    B -->|System| F[System Error]
    
    C --> G{Retry Possible?}
    G -->|Yes| H[Exponential Backoff]
    G -->|No| I[Offline Mode]
    H --> J[Retry Operation]
    J --> K{Success?}
    K -->|Yes| L[Continue]
    K -->|No| M{Max Retries?}
    M -->|No| H
    M -->|Yes| I
    
    D --> N[Show Validation Message]
    N --> O[Highlight Fields]
    O --> P[User Correction]
    P --> Q[Re-validate]
    
    E --> R[Clear Auth Token]
    R --> S[Redirect to Login]
    S --> T[Re-authenticate]
    
    F --> U[Log Error]
    U --> V[Show Error Boundary]
    V --> W[Recovery Options]
    W --> X{Recovery Type}
    X -->|Reload| Y[Reload Component]
    X -->|Reset| Z[Reset State]
    X -->|Report| AA[Send Error Report]
    
    I --> BB[Queue Operation]
    BB --> CC[Sync When Online]
```

### Error Recovery Strategies
```mermaid
graph TD
    A[Error Detection] --> B{Error Severity}
    
    B -->|Low| C[Silent Recovery]
    B -->|Medium| D[User Notification]
    B -->|High| E[Error Boundary]
    B -->|Critical| F[App Restart]
    
    C --> G[Log & Continue]
    D --> H[Show Toast/Alert]
    E --> I[Fallback UI]
    F --> J[Safe Mode]
    
    G --> K[Background Retry]
    H --> L[User Action Required]
    I --> M[Recovery Options]
    J --> N[Minimal Functionality]
    
    K --> O{Retry Success?}
    O -->|Yes| P[Normal Operation]
    O -->|No| Q[Escalate Error]
    
    L --> R{User Response}
    R -->|Retry| S[Retry Operation]
    R -->|Cancel| T[Abort Operation]
    R -->|Report| U[Send Report]
    
    M --> V[User Choice]
    V --> W[Attempt Recovery]
    
    N --> X[Core Features Only]
    X --> Y[Gradual Recovery]
```

---

**These flow diagrams provide a comprehensive view of the system's data flow, user interactions, and error handling mechanisms. They serve as a reference for developers and stakeholders to understand the system's behavior and integration points.**
