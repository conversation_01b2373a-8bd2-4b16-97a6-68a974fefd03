# Supabase Production Calendar Implementation

## Cortex 7 Metadata
- **Document Type**: Implementation Guide
- **Component**: Supabase Production Calendar Integration
- **Technology**: React Native, TypeScript, Supabase, PostgreSQL
- **Tags**: `#supabase` `#production-calendar` `#database-integration` `#postgresql`
- **Last Updated**: 2025-01-19
- **Status**: Implemented ✅

## Overview
Complete implementation of production calendar model integrated with Supabase database, providing persistent storage and real-time access to production calendar data for daily chart filtering.

## Database Schema

### Production Calendar Table
```sql
CREATE TABLE production_calendar (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  year INTEGER NOT NULL,
  month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);
```

### Key Features
- **UUID Primary Key**: Unique identifier for each production month
- **Unique Name**: Production month name (e.g., "July 2025")
- **Date Range**: Start and end dates for production month
- **Year/Month**: Calendar year and month for indexing
- **Audit Fields**: Created/updated timestamps and user tracking
- **RLS Enabled**: Row Level Security for data protection

### Indexes for Performance
```sql
-- Name lookups (most common query)
CREATE INDEX idx_production_calendar_name ON production_calendar(name);

-- Year and month queries
CREATE INDEX idx_production_calendar_year_month ON production_calendar(year, month);

-- Date range queries (finding current production month)
CREATE INDEX idx_production_calendar_dates ON production_calendar(start_date, end_date);
```

## Database Service Functions

### Core Production Calendar Functions
```typescript
// Get all production calendar entries
static async getProductionCalendar()

// Get production month by name
static async getProductionMonthByName(monthlyField: string)

// Get current production month based on today's date
static async getCurrentProductionMonth()

// Create single production calendar entry
static async createProductionCalendar(calendar: ProductionCalendarEntry)

// Bulk create production calendar entries
static async bulkCreateProductionCalendar(calendarArray: ProductionCalendarEntry[])
```

### Daily Data Filtering Functions
```typescript
// Get daily data filtered by production month
static async getDailyProductionMetricsForProductionMonth(monthlyField: string)

// Get daily data filtered by monthly field only
static async getDailyProductionMetricsByMonthly(monthlyField: string)

// Get complete current production month data
static async getCurrentProductionMonthData()
```

## Two-Stage Filtering Implementation

### Stage 1: Database Query with Monthly Filter
```typescript
const { data, error } = await supabase
  .from('daily_production_metrics')
  .select('*')
  .eq('monthly', monthlyField)  // Primary filter by monthly field
  .order('date', { ascending: true });
```

### Stage 2: Production Calendar Date Range Filter
```typescript
const productionMonth = await this.getProductionMonthByName(monthlyField);
const today = new Date().toISOString().split('T')[0];
const endDate = today <= productionMonth.end_date ? today : productionMonth.end_date;

const { data, error } = await supabase
  .from('daily_production_metrics')
  .select('*')
  .eq('monthly', monthlyField)
  .gte('date', productionMonth.start_date)  // Secondary filter: start date
  .lte('date', endDate)                     // Secondary filter: end date
  .order('date', { ascending: true });
```

## ProductionOverviewScreen Integration

### State Management
```typescript
const [currentProductionMonth, setCurrentProductionMonth] = useState<any>(null);
const [productionCalendarData, setProductionCalendarData] = useState<any>(null);
```

### Data Loading with Production Calendar
```typescript
if (selectedPeriod === 'Daily') {
  // Get current production month data from database
  productionCalendarInfo = await DatabaseService.getCurrentProductionMonthData();
  setCurrentProductionMonth(productionCalendarInfo.productionMonth);
  setProductionCalendarData(productionCalendarInfo);
  
  if (productionCalendarInfo.productionMonth) {
    // Use production calendar filtered data
    dailyMetrics = productionCalendarInfo.dailyData;
    setDailyData(dailyMetrics);
  }
}
```

### Chart Data Processing
```typescript
if (selectedPeriod === 'Daily') {
  // Data is already filtered by production calendar in loadProductionData
  // Just ensure chronological ordering and current date filtering
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  
  processedData = processedData
    .filter(item => new Date(item.date) <= today)
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}
```

### UI Display Enhancement
```typescript
{selectedPeriod === 'Daily' && currentProductionMonth && productionCalendarData && (
  `\n📅 Production Month: ${currentProductionMonth.name} (${productionCalendarData.dateRange?.startDate} to ${productionCalendarData.dateRange?.currentEndDate}, ${productionCalendarData.dateRange?.daysCount} days)`
)}
```

## Setup and Initialization

### 1. Database Schema Setup
```bash
# Run the SQL schema in Supabase SQL Editor
# File: database/production_calendar_schema.sql
```

### 2. Production Calendar Data Initialization
```typescript
import { initializeProductionCalendar } from '../utils/initializeProductionCalendar';

// Initialize production calendar data
const result = await initializeProductionCalendar();
console.log(`Inserted ${result.insertedCount} production calendar entries`);
```

### 3. Testing Integration
```typescript
import { runProductionCalendarTests } from '../utils/testSupabaseProductionCalendar';

// Run comprehensive tests
const testResults = await runProductionCalendarTests();
console.log(`Tests passed: ${testResults.passedTests}/${testResults.totalTests}`);
```

## Production Calendar Data Structure

### Sample Production Calendar Entries
```typescript
const PRODUCTION_CALENDAR_DATA = [
  {
    name: "July 2025",
    start_date: "2025-06-30",  // Starts June 30
    end_date: "2025-07-29",    // Ends July 29
    year: 2025,
    month: 7
  },
  {
    name: "August 2025",
    start_date: "2025-07-30",  // Starts July 30
    end_date: "2025-08-29",    // Ends August 29
    year: 2025,
    month: 8
  }
];
```

### Database Storage Format
```sql
INSERT INTO production_calendar (name, start_date, end_date, year, month) VALUES
('July 2025', '2025-06-30', '2025-07-29', 2025, 7),
('August 2025', '2025-07-30', '2025-08-29', 2025, 8);
```

## Query Performance Optimization

### Efficient Current Month Query
```sql
SELECT * FROM production_calendar 
WHERE CURRENT_DATE >= start_date 
  AND CURRENT_DATE <= end_date 
LIMIT 1;
```

### Optimized Daily Data Query
```sql
SELECT dm.* 
FROM daily_production_metrics dm
JOIN production_calendar pc ON dm.monthly = pc.name
WHERE pc.name = 'July 2025'
  AND dm.date >= pc.start_date
  AND dm.date <= LEAST(CURRENT_DATE, pc.end_date)
ORDER BY dm.date ASC;
```

### Index Usage
- `idx_production_calendar_name`: Fast name lookups
- `idx_production_calendar_dates`: Efficient current month queries
- `idx_production_calendar_year_month`: Year/month filtering

## Error Handling and Fallbacks

### Production Month Not Found
```typescript
if (!productionMonth) {
  console.warn(`Production month configuration not found for: ${monthlyField}`);
  // Fallback to monthly filter only
  return this.getDailyProductionMetricsByMonthly(monthlyField);
}
```

### No Current Production Month
```typescript
if (!currentProductionMonth) {
  console.warn('No current production month found, using standard date filtering');
  // Fallback to standard daily metrics
  dailyMetrics = await DatabaseService.getDailyProductionMetrics(startDate, endDate, locationId);
}
```

### Database Connection Issues
```typescript
try {
  const productionCalendarInfo = await DatabaseService.getCurrentProductionMonthData();
} catch (error) {
  console.error('Database error, falling back to legacy system:', error);
  // Fallback to legacy production data loading
}
```

## Security and Access Control

### Row Level Security (RLS)
```sql
-- All users can view production calendar
CREATE POLICY "Users can view production calendar" ON production_calendar
    FOR SELECT USING (true);

-- Only authenticated users can insert
CREATE POLICY "Authenticated users can insert production calendar" ON production_calendar
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');
```

### Data Validation
- **Date Range Validation**: Ensure start_date < end_date
- **Month Validation**: Month must be between 1-12
- **Unique Names**: Production month names must be unique
- **User Authentication**: All operations require authenticated user

## Testing and Verification

### Comprehensive Test Suite
```typescript
// Test current production month
const currentMonth = await DatabaseService.getCurrentProductionMonth();

// Test production month by name
const julyMonth = await DatabaseService.getProductionMonthByName('July 2025');

// Test daily data filtering
const julyData = await DatabaseService.getDailyProductionMetricsForProductionMonth('July 2025');

// Test complete integration
const currentData = await DatabaseService.getCurrentProductionMonthData();
```

### Test Results Validation
- ✅ Production calendar setup and initialization
- ✅ Current production month detection
- ✅ Production month lookup by name
- ✅ Daily data filtering with production calendar
- ✅ Complete integration with chart display
- ✅ Error handling and fallback scenarios

## Benefits of Supabase Integration

### Database Benefits
1. **Persistent Storage**: Production calendar data stored in database
2. **Real-time Access**: Live data updates across all clients
3. **Scalability**: PostgreSQL performance for large datasets
4. **Backup and Recovery**: Automatic database backups

### Development Benefits
1. **Type Safety**: TypeScript integration with database schema
2. **Query Optimization**: Efficient database queries with indexes
3. **Error Handling**: Robust error handling with fallbacks
4. **Testing**: Comprehensive test suite for reliability

### Operational Benefits
1. **Centralized Management**: Single source of truth for production calendar
2. **Easy Updates**: Update production calendar through database
3. **Audit Trail**: Track changes with created/updated timestamps
4. **Multi-user Support**: Concurrent access with RLS security

---
*Supabase production calendar implementation following Cortex 7 standards for comprehensive database integration documentation.*
