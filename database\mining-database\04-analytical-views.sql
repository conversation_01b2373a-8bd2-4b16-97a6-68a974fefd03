-- =====================================================
-- Mining Operations Database - Analytical Views
-- =====================================================
-- File: 04-analytical-views.sql
-- Description: Views for weekly, monthly, yearly analysis and reporting
-- Dependencies: 01-core-setup.sql, 02-production-calendar.sql, 03-daily-mining-report.sql
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- WEEKLY PRODUCTION SUMMARY VIEW
-- =====================================================
CREATE VIEW v_weekly_production_summary AS
SELECT 
    location,
    year_number,
    week_number,
    MIN(report_date) as week_start_date,
    MAX(report_date) as week_end_date,
    COUNT(*) as reporting_days,
    
    -- Production Totals
    SUM(total_actual_ob) as week_actual_ob,
    SUM(plan_ob) as week_plan_ob,
    ROUND(
        CASE WHEN SUM(plan_ob) > 0 
        THEN (SUM(total_actual_ob) / SUM(plan_ob) * 100) 
        ELSE 0 END, 2
    ) as week_ob_achievement_percent,
    SUM(ob_variance) as week_ob_variance,
    
    SUM(total_actual_ore) as week_actual_ore,
    SUM(plan_ore) as week_plan_ore,
    ROUND(
        CASE WHEN SUM(plan_ore) > 0 
        THEN (SUM(total_actual_ore) / SUM(plan_ore) * 100) 
        ELSE 0 END, 2
    ) as week_ore_achievement_percent,
    SUM(ore_variance) as week_ore_variance,
    
    -- Total Material
    SUM(total_actual_material) as week_actual_material,
    SUM(total_plan_material) as week_plan_material,
    ROUND(
        CASE WHEN SUM(total_plan_material) > 0 
        THEN (SUM(total_actual_material) / SUM(total_plan_material) * 100) 
        ELSE 0 END, 2
    ) as week_total_achievement_percent,
    
    -- Fuel Analysis
    SUM(fuel_actual) as week_fuel_actual,
    SUM(fuel_plan) as week_fuel_plan,
    ROUND(
        CASE WHEN SUM(total_actual_material) > 0 
        THEN SUM(fuel_actual) / SUM(total_actual_material) 
        ELSE 0 END, 4
    ) as week_actual_fr,
    ROUND(AVG(plan_fr), 4) as week_avg_target_fr,
    
    -- Stripping Ratio Analysis
    ROUND(
        CASE WHEN SUM(total_actual_ore) > 0 
        THEN SUM(total_actual_ob) / SUM(total_actual_ore) 
        ELSE 0 END, 4
    ) as week_actual_sr,
    ROUND(AVG(plan_sr), 4) as week_avg_target_sr,
    
    -- Weather Impact
    SUM(rain_actual) as week_total_rain_hours,
    SUM(slippery_actual) as week_total_slippery_hours,
    ROUND(AVG(operational_efficiency), 2) as week_avg_operational_efficiency,
    
    -- Personnel
    ROUND(AVG(total_personnel), 0) as week_avg_personnel,
    SUM(total_personnel) as week_total_personnel_hours,
    
    -- Cost Analysis
    SUM(fuel_cost_actual) as week_fuel_cost_actual,
    SUM(fuel_cost_plan) as week_fuel_cost_plan,
    ROUND(AVG(fuel_price_per_liter), 4) as week_avg_fuel_price,
    
    -- Performance Metrics
    ROUND(AVG(equipment_availability), 2) as week_avg_equipment_availability,
    ROUND(AVG(equipment_utilization), 2) as week_avg_equipment_utilization,
    SUM(maintenance_hours) as week_total_maintenance_hours
    
FROM daily_mining_report
GROUP BY location, year_number, week_number
ORDER BY location, year_number, week_number;

-- =====================================================
-- MONTHLY PRODUCTION SUMMARY VIEW
-- =====================================================
CREATE VIEW v_monthly_production_summary AS
SELECT 
    location,
    year_number,
    month_number,
    month_name,
    MIN(report_date) as month_start_date,
    MAX(report_date) as month_end_date,
    COUNT(*) as reporting_days,
    
    -- Production Totals
    SUM(total_actual_ob) as month_actual_ob,
    SUM(plan_ob) as month_plan_ob,
    ROUND(
        CASE WHEN SUM(plan_ob) > 0 
        THEN (SUM(total_actual_ob) / SUM(plan_ob) * 100) 
        ELSE 0 END, 2
    ) as month_ob_achievement_percent,
    SUM(ob_variance) as month_ob_variance,
    
    SUM(total_actual_ore) as month_actual_ore,
    SUM(plan_ore) as month_plan_ore,
    ROUND(
        CASE WHEN SUM(plan_ore) > 0 
        THEN (SUM(total_actual_ore) / SUM(plan_ore) * 100) 
        ELSE 0 END, 2
    ) as month_ore_achievement_percent,
    SUM(ore_variance) as month_ore_variance,
    
    -- Total Material
    SUM(total_actual_material) as month_actual_material,
    SUM(total_plan_material) as month_plan_material,
    ROUND(
        CASE WHEN SUM(total_plan_material) > 0 
        THEN (SUM(total_actual_material) / SUM(total_plan_material) * 100) 
        ELSE 0 END, 2
    ) as month_total_achievement_percent,
    
    -- Efficiency Metrics
    ROUND(
        CASE WHEN SUM(total_actual_material) > 0 
        THEN SUM(fuel_actual) / SUM(total_actual_material) 
        ELSE 0 END, 4
    ) as month_actual_fr,
    ROUND(
        CASE WHEN SUM(total_actual_ore) > 0 
        THEN SUM(total_actual_ob) / SUM(total_actual_ore) 
        ELSE 0 END, 4
    ) as month_actual_sr,
    
    -- Operational Metrics
    SUM(fuel_actual) as month_fuel_actual,
    SUM(fuel_plan) as month_fuel_plan,
    ROUND(
        CASE WHEN SUM(fuel_plan) > 0 
        THEN (SUM(fuel_plan) / SUM(fuel_actual) * 100) 
        ELSE 0 END, 2
    ) as month_fuel_efficiency_percent,
    
    ROUND(AVG(operational_efficiency), 2) as month_avg_operational_efficiency,
    
    -- Weather Impact
    SUM(rain_actual) as month_total_rain_hours,
    SUM(slippery_actual) as month_total_slippery_hours,
    ROUND(AVG(CASE WHEN weather_impact_level = 'High' THEN 1 ELSE 0 END) * 100, 2) as month_high_weather_impact_percent,
    
    -- Cost Analysis
    SUM(fuel_cost_actual) as month_fuel_cost_actual,
    SUM(fuel_cost_plan) as month_fuel_cost_plan,
    ROUND(
        CASE WHEN SUM(total_actual_material) > 0 
        THEN SUM(fuel_cost_actual) / SUM(total_actual_material) 
        ELSE 0 END, 2
    ) as month_fuel_cost_per_ton,
    
    -- Personnel Analysis
    ROUND(AVG(total_personnel), 0) as month_avg_personnel,
    ROUND(
        CASE WHEN SUM(total_personnel) > 0 
        THEN SUM(total_actual_material) / SUM(total_personnel) 
        ELSE 0 END, 2
    ) as month_productivity_per_person,
    
    -- Equipment Performance
    ROUND(AVG(equipment_availability), 2) as month_avg_equipment_availability,
    ROUND(AVG(equipment_utilization), 2) as month_avg_equipment_utilization,
    SUM(maintenance_hours) as month_total_maintenance_hours
    
FROM daily_mining_report
GROUP BY location, year_number, month_number, month_name
ORDER BY location, year_number, month_number;

-- =====================================================
-- YEARLY PRODUCTION SUMMARY VIEW
-- =====================================================
CREATE VIEW v_yearly_production_summary AS
SELECT 
    location,
    year_number,
    MIN(report_date) as year_start_date,
    MAX(report_date) as year_end_date,
    COUNT(*) as reporting_days,
    
    -- Production Totals
    SUM(total_actual_ob) as year_actual_ob,
    SUM(plan_ob) as year_plan_ob,
    ROUND(
        CASE WHEN SUM(plan_ob) > 0 
        THEN (SUM(total_actual_ob) / SUM(plan_ob) * 100) 
        ELSE 0 END, 2
    ) as year_ob_achievement_percent,
    
    SUM(total_actual_ore) as year_actual_ore,
    SUM(plan_ore) as year_plan_ore,
    ROUND(
        CASE WHEN SUM(plan_ore) > 0 
        THEN (SUM(total_actual_ore) / SUM(plan_ore) * 100) 
        ELSE 0 END, 2
    ) as year_ore_achievement_percent,
    
    -- Total Material
    SUM(total_actual_material) as year_actual_material,
    SUM(total_plan_material) as year_plan_material,
    ROUND(
        CASE WHEN SUM(total_plan_material) > 0 
        THEN (SUM(total_actual_material) / SUM(total_plan_material) * 100) 
        ELSE 0 END, 2
    ) as year_total_achievement_percent,
    
    -- Key Ratios
    ROUND(
        CASE WHEN SUM(total_actual_ore) > 0 
        THEN SUM(total_actual_ob) / SUM(total_actual_ore) 
        ELSE 0 END, 4
    ) as year_actual_sr,
    ROUND(
        CASE WHEN SUM(total_actual_material) > 0 
        THEN SUM(fuel_actual) / SUM(total_actual_material) 
        ELSE 0 END, 4
    ) as year_actual_fr,
    
    -- Totals
    SUM(fuel_actual) as year_fuel_actual,
    SUM(fuel_plan) as year_fuel_plan,
    SUM(fuel_cost_actual) as year_fuel_cost_actual,
    
    -- Averages
    ROUND(AVG(operational_efficiency), 2) as year_avg_operational_efficiency,
    ROUND(AVG(equipment_availability), 2) as year_avg_equipment_availability,
    ROUND(AVG(equipment_utilization), 2) as year_avg_equipment_utilization,
    
    -- Weather Impact Summary
    SUM(rain_actual) as year_total_rain_hours,
    SUM(slippery_actual) as year_total_slippery_hours,
    
    -- Performance Metrics
    ROUND(
        CASE WHEN COUNT(*) > 0 
        THEN SUM(CASE WHEN status = 'Approved' THEN 1 ELSE 0 END)::DECIMAL / COUNT(*) * 100 
        ELSE 0 END, 2
    ) as year_report_approval_rate,
    
    ROUND(AVG(data_quality_score), 2) as year_avg_data_quality_score
    
FROM daily_mining_report
GROUP BY location, year_number
ORDER BY location, year_number;

-- =====================================================
-- PRODUCTION PERFORMANCE DASHBOARD VIEW
-- =====================================================
CREATE VIEW v_production_dashboard AS
SELECT 
    dmr.location,
    dmr.report_date,
    dmr.day_name,
    
    -- Current Day Performance
    dmr.total_actual_material,
    dmr.total_plan_material,
    dmr.total_achievement_percent,
    dmr.actual_sr,
    dmr.plan_sr,
    dmr.actual_fr,
    dmr.plan_fr,
    dmr.operational_efficiency,
    dmr.weather_impact_level,
    
    -- Month-to-Date Performance
    mps.month_actual_material,
    mps.month_plan_material,
    mps.month_total_achievement_percent,
    mps.month_actual_sr,
    mps.month_actual_fr,
    
    -- Year-to-Date Performance
    yps.year_actual_material,
    yps.year_plan_material,
    yps.year_total_achievement_percent,
    yps.year_actual_sr,
    yps.year_actual_fr,
    
    -- Targets from Calendar
    ptc.target_total_material as monthly_target,
    ptc.target_sr as monthly_target_sr,
    ptc.target_fr as monthly_target_fr,
    
    -- Status
    dmr.status,
    dmr.approved_by,
    dmr.approved_at
    
FROM daily_mining_report dmr
LEFT JOIN v_monthly_production_summary mps ON (
    dmr.location = mps.location 
    AND dmr.year_number = mps.year_number 
    AND dmr.month_number = mps.month_number
)
LEFT JOIN v_yearly_production_summary yps ON (
    dmr.location = yps.location 
    AND dmr.year_number = yps.year_number
)
LEFT JOIN production_targets_calendar ptc ON (
    dmr.location = ptc.location 
    AND ptc.period_type = 'Monthly'
    AND dmr.report_date BETWEEN ptc.period_start_date AND ptc.period_end_date
    AND ptc.is_active = true
)
ORDER BY dmr.location, dmr.report_date DESC;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON VIEW v_weekly_production_summary IS 'Weekly aggregated production summary with key metrics and ratios';
COMMENT ON VIEW v_monthly_production_summary IS 'Monthly aggregated production summary with comprehensive analysis';
COMMENT ON VIEW v_yearly_production_summary IS 'Yearly aggregated production summary for annual reporting';
COMMENT ON VIEW v_production_dashboard IS 'Real-time production dashboard with current, MTD, and YTD performance';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('004', 'Analytical views for weekly, monthly, yearly reporting')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Analytical views created successfully';
    RAISE NOTICE 'Views available:';
    RAISE NOTICE '- v_weekly_production_summary: Weekly aggregated data';
    RAISE NOTICE '- v_monthly_production_summary: Monthly aggregated data';
    RAISE NOTICE '- v_yearly_production_summary: Yearly aggregated data';
    RAISE NOTICE '- v_production_dashboard: Real-time dashboard view';
    RAISE NOTICE 'Ready for reporting and analytics';
END $$;
