// import * as Notifications from 'expo-notifications'; // Commented out until expo-notifications is installed
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../config/supabase';

// Notification types for mining operations
export type NotificationType = 
  | 'safety_alert'
  | 'equipment_failure'
  | 'production_target'
  | 'maintenance_due'
  | 'shift_reminder'
  | 'emergency'
  | 'weather_warning';

export interface NotificationPayload {
  id: string;
  type: NotificationType;
  title: string;
  body: string;
  data?: Record<string, any>;
  priority: 'low' | 'normal' | 'high' | 'critical';
  userId?: string;
  locationId?: string;
  equipmentId?: string;
}

export interface NotificationSettings {
  enabled: boolean;
  safetyAlerts: boolean;
  equipmentFailures: boolean;
  productionTargets: boolean;
  maintenanceReminders: boolean;
  shiftReminders: boolean;
  emergencyAlerts: boolean;
  weatherWarnings: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string;   // HH:MM format
  };
}

// Configure notification behavior (commented out until expo-notifications is installed)
/*
Notifications.setNotificationHandler({
  handleNotification: async (notification) => {
    const { type, priority } = notification.request.content.data as any;
    
    // Always show critical notifications
    if (priority === 'critical' || type === 'emergency' || type === 'safety_alert') {
      return {
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      };
    }

    // Check quiet hours for non-critical notifications
    const settings = await this.getSettings();
    if (settings?.quietHours.enabled && NotificationService.isQuietHours(settings.quietHours)) {
      return {
        shouldShowAlert: false,
        shouldPlaySound: false,
        shouldSetBadge: true,
      };
    }

    return {
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    };
  },
});

export class NotificationService {
  private static instance: NotificationService;
  private expoPushToken?: string;
  private userId?: string;

  private constructor() {}

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Initialize notification service
  async initialize(userId: string): Promise<void> {
    this.userId = userId;
    
    try {
      // Request permissions
      const { status } = await this.requestPermissions();
      
      if (status === 'granted') {
        // Get push token
        this.expoPushToken = await this.registerForPushNotifications();
        
        if (this.expoPushToken) {
          // Save token to database
          await this.savePushTokenToDatabase(this.expoPushToken, userId);
          console.log('🔔 Notifications initialized successfully');
        }
      } else {
        console.warn('⚠️ Notification permissions not granted');
      }

      // Setup notification listeners
      this.setupNotificationListeners();
      
      // Setup real-time subscriptions
      this.setupRealTimeSubscriptions();
      
    } catch (error) {
      console.error('❌ Failed to initialize notifications:', error);
    }
  }

  // Request notification permissions
  async requestPermissions(): Promise<{ status: string }> {
    if (!Device.isDevice) {
      console.warn('Must use physical device for Push Notifications');
      return { status: 'denied' };
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    return { status: finalStatus };
  }

  // Register for push notifications
  async registerForPushNotifications(): Promise<string | undefined> {
    try {
      const token = (await Notifications.getExpoPushTokenAsync()).data;
      console.log('📱 Push token:', token);

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });

        // Create mining-specific channels
        await this.createNotificationChannels();
      }

      return token;
    } catch (error) {
      console.error('Failed to get push token:', error);
      return undefined;
    }
  }

  // Create Android notification channels
  private async createNotificationChannels(): Promise<void> {
    const channels = [
      {
        id: 'safety_alerts',
        name: 'Safety Alerts',
        importance: Notifications.AndroidImportance.MAX,
        sound: 'safety_alert.wav',
        vibrationPattern: [0, 500, 200, 500],
      },
      {
        id: 'equipment_failures',
        name: 'Equipment Failures',
        importance: Notifications.AndroidImportance.HIGH,
        sound: 'equipment_alert.wav',
        vibrationPattern: [0, 250, 250, 250],
      },
      {
        id: 'emergency',
        name: 'Emergency Alerts',
        importance: Notifications.AndroidImportance.MAX,
        sound: 'emergency.wav',
        vibrationPattern: [0, 1000, 500, 1000],
      },
      {
        id: 'production',
        name: 'Production Updates',
        importance: Notifications.AndroidImportance.DEFAULT,
      },
      {
        id: 'maintenance',
        name: 'Maintenance Reminders',
        importance: Notifications.AndroidImportance.DEFAULT,
      }
    ];

    for (const channel of channels) {
      await Notifications.setNotificationChannelAsync(channel.id, channel);
    }
  }

  // Setup notification listeners
  private setupNotificationListeners(): void {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener(notification => {
      console.log('🔔 Notification received:', notification);
      this.handleNotificationReceived(notification);
    });

    // Handle notification tapped
    Notifications.addNotificationResponseReceivedListener(response => {
      console.log('👆 Notification tapped:', response);
      this.handleNotificationTapped(response);
    });
  }

  // Setup real-time subscriptions for mining operations
  private setupRealTimeSubscriptions(): void {
    if (!this.userId) return;

    // Safety incidents subscription
    supabase
      .channel('safety_incidents')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'safety_incidents' },
        (payload) => {
          this.handleSafetyIncident(payload.new);
        }
      )
      .subscribe();

    // Equipment status changes
    supabase
      .channel('equipment_status')
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'equipment' },
        (payload) => {
          this.handleEquipmentStatusChange(payload.new, payload.old);
        }
      )
      .subscribe();

    // Production alerts
    supabase
      .channel('production_alerts')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'production_reports' },
        (payload) => {
          this.handleProductionUpdate(payload.new);
        }
      )
      .subscribe();
  }

  // Send local notification
  async sendLocalNotification(payload: NotificationPayload): Promise<void> {
    try {
      const settings = await this.getSettings();
      
      // Check if notifications are enabled for this type
      if (!this.isNotificationTypeEnabled(payload.type, settings)) {
        return;
      }

      const channelId = this.getChannelIdForType(payload.type);
      
      await Notifications.scheduleNotificationAsync({
        content: {
          title: payload.title,
          body: payload.body,
          data: payload.data,
          sound: payload.priority === 'critical' ? 'emergency.wav' : 'default',
          priority: payload.priority === 'critical' ? 
            Notifications.AndroidNotificationPriority.MAX : 
            Notifications.AndroidNotificationPriority.DEFAULT,
        },
        trigger: null, // Send immediately
        identifier: payload.id,
      });

      console.log(`🔔 Local notification sent: ${payload.title}`);
    } catch (error) {
      console.error('Failed to send local notification:', error);
    }
  }

  // Handle different types of real-time events
  private async handleSafetyIncident(incident: any): Promise<void> {
    await this.sendLocalNotification({
      id: `safety_${incident.id}`,
      type: 'safety_alert',
      title: '🚨 Safety Alert',
      body: `${incident.incident_type}: ${incident.title}`,
      priority: incident.severity === 'critical' ? 'critical' : 'high',
      data: { incidentId: incident.id, type: 'safety_incident' }
    });
  }

  private async handleEquipmentStatusChange(newStatus: any, oldStatus: any): Promise<void> {
    if (newStatus.status === 'down' && oldStatus.status !== 'down') {
      await this.sendLocalNotification({
        id: `equipment_${newStatus.id}`,
        type: 'equipment_failure',
        title: '⚠️ Equipment Failure',
        body: `${newStatus.name} is now offline`,
        priority: 'high',
        data: { equipmentId: newStatus.id, type: 'equipment_failure' }
      });
    }
  }

  private async handleProductionUpdate(report: any): Promise<void> {
    // Check if production target is significantly missed
    const achievement = (report.actual_tonnage / report.target_tonnage) * 100;
    
    if (achievement < 80) {
      await this.sendLocalNotification({
        id: `production_${report.id}`,
        type: 'production_target',
        title: '📊 Production Alert',
        body: `Production target missed: ${achievement.toFixed(1)}% achieved`,
        priority: 'normal',
        data: { reportId: report.id, type: 'production_alert' }
      });
    }
  }

  // Notification settings management
  async getSettings(): Promise<NotificationSettings> {
    try {
      const stored = await AsyncStorage.getItem('notification_settings');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to get notification settings:', error);
    }

    // Default settings
    return {
      enabled: true,
      safetyAlerts: true,
      equipmentFailures: true,
      productionTargets: true,
      maintenanceReminders: true,
      shiftReminders: true,
      emergencyAlerts: true,
      weatherWarnings: true,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '06:00'
      }
    };
  }

  async updateSettings(settings: NotificationSettings): Promise<void> {
    try {
      await AsyncStorage.setItem('notification_settings', JSON.stringify(settings));
      console.log('🔔 Notification settings updated');
    } catch (error) {
      console.error('Failed to update notification settings:', error);
    }
  }

  // Utility methods
  private isNotificationTypeEnabled(type: NotificationType, settings: NotificationSettings): boolean {
    if (!settings.enabled) return false;

    switch (type) {
      case 'safety_alert': return settings.safetyAlerts;
      case 'equipment_failure': return settings.equipmentFailures;
      case 'production_target': return settings.productionTargets;
      case 'maintenance_due': return settings.maintenanceReminders;
      case 'shift_reminder': return settings.shiftReminders;
      case 'emergency': return settings.emergencyAlerts;
      case 'weather_warning': return settings.weatherWarnings;
      default: return true;
    }
  }

  private getChannelIdForType(type: NotificationType): string {
    switch (type) {
      case 'safety_alert':
      case 'emergency':
        return 'safety_alerts';
      case 'equipment_failure':
        return 'equipment_failures';
      case 'production_target':
        return 'production';
      case 'maintenance_due':
        return 'maintenance';
      default:
        return 'default';
    }
  }

  static isQuietHours(quietHours: { start: string; end: string }): boolean {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    return currentTime >= quietHours.start || currentTime <= quietHours.end;
  }

  private handleNotificationReceived(notification: Notifications.Notification): void {
    // Handle notification received while app is open
    // You can show in-app notification or update UI
  }

  private handleNotificationTapped(response: Notifications.NotificationResponse): void {
    // Handle notification tap - navigate to relevant screen
    const { data } = response.notification.request.content;
    
    if (data?.type === 'safety_incident') {
      // Navigate to safety incident details
    } else if (data?.type === 'equipment_failure') {
      // Navigate to equipment details
    } else if (data?.type === 'production_alert') {
      // Navigate to production overview
    }
  }

  private async savePushTokenToDatabase(token: string, userId: string): Promise<void> {
    try {
      await supabase
        .from('user_push_tokens')
        .upsert({
          user_id: userId,
          push_token: token,
          platform: Platform.OS,
          updated_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Failed to save push token:', error);
    }
  }
}
*/

// Placeholder NotificationService until expo-notifications is installed
class NotificationService {
  static getInstance() {
    return new NotificationService();
  }

  async initialize() {
    console.log('NotificationService: Placeholder implementation');
  }

  async sendNotification() {
    console.log('NotificationService: sendNotification placeholder');
  }

  async getSettings() {
    return null;
  }
}

export default NotificationService;
