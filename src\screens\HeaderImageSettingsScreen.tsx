import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  StyleSheet,
  Alert,
  ActionSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import HeaderImageService from '../services/HeaderImageService';
import { Colors, Layout } from '../constants';

interface HeaderImageSettingsScreenProps {
  navigation: any;
}

const { width: screenWidth } = Dimensions.get('window');
const headerHeight = (screenWidth * 9) / 16; // 16:9 aspect ratio

const HeaderImageSettingsScreen: React.FC<HeaderImageSettingsScreenProps> = ({ navigation }) => {
  const { user, profile } = useAuth();
  const [currentHeaderUrl, setCurrentHeaderUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(true);
  const headerImageService = HeaderImageService.getInstance();

  useEffect(() => {
    loadCurrentHeader();
  }, [user]);

  const loadCurrentHeader = async () => {
    if (!user) return;
    
    try {
      const savedHeaderUrl = await headerImageService.getHeaderImageUrl(user.id);
      setCurrentHeaderUrl(savedHeaderUrl);
    } catch (error) {
      console.error('Error loading current header:', error);
    } finally {
      setLoading(false);
    }
  };

  const showImagePickerOptions = () => {
    Alert.alert(
      'Change Header Image',
      'Choose how you want to set your header image',
      [
        {
          text: 'Take Photo',
          onPress: () => handleImagePicker('camera'),
        },
        {
          text: 'Choose from Gallery',
          onPress: () => handleImagePicker('gallery'),
        },
        {
          text: 'Choose Preset',
          onPress: () => handlePresetSelection(),
        },
        {
          text: 'Remove Header',
          style: 'destructive',
          onPress: () => handleRemoveHeader(),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleImagePicker = async (source: 'camera' | 'gallery') => {
    if (!user) return;

    setUploading(true);
    try {
      let pickerResult;
      
      if (source === 'camera') {
        pickerResult = await headerImageService.openCamera();
      } else {
        pickerResult = await headerImageService.openGallery();
      }

      if (!pickerResult.success || !pickerResult.imageUri) {
        Alert.alert('Error', pickerResult.error || 'No image selected');
        return;
      }

      // Upload the image
      const uploadResult = await headerImageService.uploadHeaderImage(
        pickerResult.imageUri,
        user.id,
        profile?.full_name || undefined
      );

      if (uploadResult.success && uploadResult.publicUrl) {
        // Save to local storage
        await headerImageService.saveHeaderImageUrl(user.id, uploadResult.publicUrl);
        
        // Delete old header if exists
        if (currentHeaderUrl) {
          await headerImageService.deleteOldHeaderImage(currentHeaderUrl);
        }

        setCurrentHeaderUrl(uploadResult.publicUrl);
        Alert.alert('Success', 'Header image updated successfully!');
      } else {
        Alert.alert('Error', uploadResult.error || 'Failed to upload image');
      }
    } catch (error: any) {
      console.error('Error updating header image:', error);
      Alert.alert('Error', 'Failed to update header image');
    } finally {
      setUploading(false);
    }
  };

  const handlePresetSelection = () => {
    // For now, show preset selection inline
    showPresetSelection();
  };

  const showPresetSelection = () => {
    const presets = headerImageService.getPredefinedHeaders();
    const options = presets.map(preset => preset.name);
    options.push('Cancel');

    Alert.alert(
      'Choose Preset Header',
      'Select a predefined header image',
      [
        ...presets.map((preset, index) => ({
          text: preset.name,
          onPress: () => handlePresetSelected(preset.url),
        })),
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handlePresetSelected = async (presetUrl: string) => {
    if (!user) return;

    try {
      // Delete old header if exists
      if (currentHeaderUrl) {
        await headerImageService.deleteOldHeaderImage(currentHeaderUrl);
      }

      // Save preset URL
      await headerImageService.saveHeaderImageUrl(user.id, presetUrl);
      setCurrentHeaderUrl(presetUrl);
      
      Alert.alert('Success', 'Header image updated successfully!');
    } catch (error) {
      console.error('Error setting preset header:', error);
      Alert.alert('Error', 'Failed to set header image');
    }
  };

  const handleRemoveHeader = async () => {
    if (!user) return;

    Alert.alert(
      'Remove Header Image',
      'Are you sure you want to remove your header image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete from storage if it's a custom upload
              if (currentHeaderUrl && currentHeaderUrl.includes('supabase')) {
                await headerImageService.deleteOldHeaderImage(currentHeaderUrl);
              }

              // Remove from local storage
              await headerImageService.saveHeaderImageUrl(user.id, '');
              setCurrentHeaderUrl(null);
              
              Alert.alert('Success', 'Header image removed successfully!');
            } catch (error) {
              console.error('Error removing header image:', error);
              Alert.alert('Error', 'Failed to remove header image');
            }
          },
        },
      ]
    );
  };

  const HeaderPreview = () => (
    <View style={styles.headerPreview}>
      {currentHeaderUrl ? (
        <Image
          source={{ uri: currentHeaderUrl }}
          style={styles.headerImage}
          resizeMode="cover"
        />
      ) : (
        <View style={styles.placeholderHeader}>
          <Ionicons name="image-outline" size={48} color={Colors.textLight} />
          <Text style={styles.placeholderText}>No header image set</Text>
        </View>
      )}
      
      {/* Overlay with user info */}
      <View style={styles.headerOverlay}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{profile?.full_name || 'User Name'}</Text>
          <Text style={styles.userRole}>{profile?.jabatan || profile?.departemen || 'Employee'}</Text>
        </View>
      </View>

      {/* Change button */}
      <TouchableOpacity
        style={styles.changeButton}
        onPress={showImagePickerOptions}
        disabled={uploading}
      >
        {uploading ? (
          <ActivityIndicator size="small" color={Colors.white} />
        ) : (
          <Ionicons name="camera" size={20} color={Colors.white} />
        )}
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading header settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Header Image</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header Preview */}
        <HeaderPreview />

        {/* Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Options</Text>
          
          <TouchableOpacity
            style={styles.optionItem}
            onPress={() => handleImagePicker('camera')}
            disabled={uploading}
          >
            <View style={styles.optionIcon}>
              <Ionicons name="camera" size={20} color={Colors.primary} />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Take Photo</Text>
              <Text style={styles.optionDescription}>Use camera to take a new header image</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.optionItem}
            onPress={() => handleImagePicker('gallery')}
            disabled={uploading}
          >
            <View style={styles.optionIcon}>
              <Ionicons name="images" size={20} color={Colors.primary} />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Choose from Gallery</Text>
              <Text style={styles.optionDescription}>Select an image from your photo library</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.optionItem}
            onPress={handlePresetSelection}
            disabled={uploading}
          >
            <View style={styles.optionIcon}>
              <Ionicons name="grid" size={20} color={Colors.primary} />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Choose Preset</Text>
              <Text style={styles.optionDescription}>Select from predefined header images</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
          </TouchableOpacity>

          {currentHeaderUrl && (
            <TouchableOpacity
              style={styles.optionItem}
              onPress={handleRemoveHeader}
              disabled={uploading}
            >
              <View style={[styles.optionIcon, { backgroundColor: Colors.error + '20' }]}>
                <Ionicons name="trash" size={20} color={Colors.error} />
              </View>
              <View style={styles.optionContent}>
                <Text style={[styles.optionTitle, { color: Colors.error }]}>Remove Header</Text>
                <Text style={styles.optionDescription}>Remove current header image</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={Colors.textLight} />
            </TouchableOpacity>
          )}
        </View>

        {/* Tips */}
        <View style={styles.tipsSection}>
          <Text style={styles.tipsTitle}>Tips for Best Results</Text>
          <View style={styles.tipItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
            <Text style={styles.tipText}>Use landscape orientation (16:9 ratio)</Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
            <Text style={styles.tipText}>Minimum resolution: 800x450 pixels</Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
            <Text style={styles.tipText}>Avoid busy backgrounds for better text readability</Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
            <Text style={styles.tipText}>Consider lighting and contrast</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  placeholder: {
    width: 24,
  },
  content: {
    flex: 1,
  },
  headerPreview: {
    height: headerHeight,
    backgroundColor: Colors.backgroundLight,
    position: 'relative',
    marginBottom: Layout.spacing.lg,
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  placeholderHeader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    marginTop: Layout.spacing.sm,
  },
  headerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
    backgroundColor: 'rgba(0,0,0,0.4)',
    padding: Layout.spacing.lg,
  },
  userInfo: {
    alignItems: 'flex-start',
  },
  userName: {
    fontSize: Layout.fontSize.xl,
    fontWeight: '700',
    color: Colors.white,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  userRole: {
    fontSize: Layout.fontSize.md,
    color: Colors.white,
    opacity: 0.9,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  changeButton: {
    position: 'absolute',
    top: Layout.spacing.md,
    right: Layout.spacing.md,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    paddingHorizontal: Layout.spacing.lg,
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Layout.spacing.md,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  optionDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
  },
  tipsSection: {
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.lg,
    backgroundColor: Colors.backgroundLight,
    marginBottom: Layout.spacing.xl,
  },
  tipsTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  tipText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    marginLeft: Layout.spacing.sm,
    flex: 1,
  },
  loadingText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    marginTop: Layout.spacing.sm,
  },
});

export default HeaderImageSettingsScreen;
