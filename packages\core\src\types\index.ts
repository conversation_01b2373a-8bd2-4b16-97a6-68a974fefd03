// Core type definitions for Mining Operations Platform

export interface ProductionMetric {
  id: string;
  date: string;
  location_id: string;
  created_by: string;
  
  // Production data
  actual_ob: number;
  plan_ob: number;
  actual_ore: number;
  plan_ore: number;
  
  // Fuel consumption
  actual_fuel: number;
  plan_fuel: number;
  
  // Weather impact
  actual_rain: number;
  plan_rain: number;
  actual_slippery: number;
  plan_slippery: number;
  
  // Metadata
  monthly: string;
  week: number;
  notes?: string;
  weather_conditions?: string;
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface Equipment {
  id: string;
  name: string;
  equipment_number: string;
  type: EquipmentType;
  manufacturer?: string;
  model?: string;
  year_manufactured?: number;
  capacity?: number;
  capacity_unit?: string;
  status: EquipmentStatus;
  location_id?: string;
  assigned_operator_id?: string;
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role: UserRole;
  phone?: string;
  employee_id?: string;
  department?: string;
  supervisor_id?: string;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Location {
  id: string;
  name: string;
  code: string;
  type: LocationType;
  description?: string;
  latitude?: number;
  longitude?: number;
  elevation?: number;
  timezone: string;
  is_active: boolean;
  parent_location_id?: string;
  created_at: string;
  updated_at: string;
}

// Enums
export enum UserRole {
  ADMIN = 'admin',
  SUPERVISOR = 'supervisor',
  OPERATOR = 'operator',
  VIEWER = 'viewer'
}

export enum EquipmentType {
  EXCAVATOR = 'excavator',
  DUMP_TRUCK = 'dump_truck',
  BULLDOZER = 'bulldozer',
  LOADER = 'loader',
  DRILL = 'drill',
  CRUSHER = 'crusher',
  CONVEYOR = 'conveyor'
}

export enum EquipmentStatus {
  OPERATIONAL = 'operational',
  MAINTENANCE = 'maintenance',
  REPAIR = 'repair',
  RETIRED = 'retired'
}

export enum LocationType {
  MINE_SITE = 'mine_site',
  PIT = 'pit',
  DUMP = 'dump',
  PROCESSING_PLANT = 'processing_plant',
  OFFICE = 'office',
  WAREHOUSE = 'warehouse'
}

// API Response Types
export interface ApiResponse<T> {
  data: T | null;
  error: ApiError | null;
  count?: number;
  status: number;
  statusText: string;
}

export interface ApiError {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
}

// Chart Data Types
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  color?: (opacity?: number) => string;
  strokeWidth?: number;
  strokeDashArray?: string;
}

export interface ChartPoint {
  x: string | number;
  y: number;
  label?: string;
}

// Sync Types
export interface SyncOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: string;
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  retryCount: number;
  error?: string;
}

export interface SyncStatus {
  isOnline: boolean;
  lastSyncTime?: string;
  pendingOperations: number;
  failedOperations: number;
  issyncing: boolean;
}

// Validation Types
export interface ValidationRule {
  field: string;
  type: 'required' | 'range' | 'format' | 'custom';
  parameters: any;
  errorMessage: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Filter and Sort Types
export interface FilterCriteria {
  field: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'between';
  value: any;
}

export interface SortCriteria {
  field: string;
  direction: 'asc' | 'desc';
}

// Pagination Types
export interface PaginationOptions {
  page: number;
  limit: number;
  offset?: number;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Configuration Types
export interface DatabaseConfig {
  url: string;
  apiKey: string;
  timeout: number;
}

export interface AppConfig {
  database: DatabaseConfig;
  features: {
    offlineMode: boolean;
    realTimeSync: boolean;
    analytics: boolean;
    notifications: boolean;
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    dateFormat: string;
    numberFormat: string;
  };
}

// Platform Adapter Types
export interface StorageAdapter {
  save(key: string, data: any): Promise<void>;
  load(key: string): Promise<any>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  getAllKeys(): Promise<string[]>;
}

export interface NetworkAdapter {
  request<T>(config: RequestConfig): Promise<ApiResponse<T>>;
  upload(file: File | Blob, config: UploadConfig): Promise<ApiResponse<any>>;
  download(url: string, config: DownloadConfig): Promise<Blob>;
}

export interface NotificationAdapter {
  show(notification: NotificationConfig): Promise<void>;
  schedule(notification: ScheduledNotificationConfig): Promise<string>;
  cancel(id: string): Promise<void>;
  clearAll(): Promise<void>;
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  url: string;
  data?: any;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

export interface UploadConfig {
  url: string;
  fieldName: string;
  headers?: Record<string, string>;
  onProgress?: (progress: number) => void;
}

export interface DownloadConfig {
  headers?: Record<string, string>;
  onProgress?: (progress: number) => void;
}

export interface NotificationConfig {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  duration?: number;
  actions?: NotificationAction[];
}

export interface ScheduledNotificationConfig extends NotificationConfig {
  scheduledTime: Date;
  repeat?: 'daily' | 'weekly' | 'monthly';
}

// Production Calendar types
export interface ProductionMonth {
  name: string;           // e.g., "July 2025"
  startDate: string;      // e.g., "2025-06-30" (ISO date string)
  endDate: string;        // e.g., "2025-07-29" (ISO date string)
  year: number;
  month: number;          // 1-12
}

export interface NotificationAction {
  label: string;
  action: () => void;
}

// Export all types
export * from './database';
export * from './api';
export {
  Result,
  AsyncResult,
  KeyValuePair,
  SelectOption,
  TreeNode,
  DateRange,
  TimePeriod,
  ComparisonOperator,
  SortDirection,
  Filter,
  Sort,
  Pagination,
  ListResponse,
  FormField,
  ValidationRule as CommonValidationRule,
  ValidationResult as CommonValidationResult,
  ValidationError as CommonValidationError,
  ValidationWarning,
  AuditTrail,
  Notification,
  NotificationAction,
  Settings,
  UserPreferences,
  FileInfo,
  ProgressInfo,
  CacheInfo,
  Metric,
  Coordinate,
  Address,
  ContactInfo
} from './common';
