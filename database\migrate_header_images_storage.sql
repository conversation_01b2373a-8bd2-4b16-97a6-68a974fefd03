-- =====================================================
-- Migrate Header Images from profile-photos to header bucket
-- Run this AFTER setting up header bucket
-- =====================================================

-- Step 1: Check existing header images in database
SELECT 
    'Existing Header Images:' AS status,
    COUNT(*) AS total_images,
    COUNT(CASE WHEN image_url LIKE '%profile-photos%' THEN 1 END) AS old_bucket_images,
    COUNT(CASE WHEN image_url LIKE '%header%' THEN 1 END) AS new_bucket_images
FROM dashboard_header_images;

-- Step 2: List images that need migration
SELECT 
    'Images to Migrate:' AS section,
    id,
    title,
    image_url,
    CASE 
        WHEN image_url LIKE '%profile-photos/header/%' THEN 'NEEDS MIGRATION'
        WHEN image_url LIKE '%header/images/%' THEN 'ALREADY MIGRATED'
        ELSE 'EXTERNAL URL'
    END AS migration_status
FROM dashboard_header_images
ORDER BY created_at;

-- Step 3: Update image URLs for new bucket structure
-- This updates the database URLs to point to the new bucket
-- Note: You'll need to manually copy files in Supabase Storage UI
UPDATE dashboard_header_images 
SET image_url = REPLACE(
    image_url, 
    '/storage/v1/object/public/profile-photos/header/', 
    '/storage/v1/object/public/header/images/'
)
WHERE image_url LIKE '%profile-photos/header/%';

-- Step 4: Verify migration
SELECT 
    'Migration Results:' AS status,
    COUNT(*) AS total_images,
    COUNT(CASE WHEN image_url LIKE '%profile-photos%' THEN 1 END) AS old_bucket_remaining,
    COUNT(CASE WHEN image_url LIKE '%header/images%' THEN 1 END) AS new_bucket_migrated,
    COUNT(CASE WHEN image_url NOT LIKE '%supabase%' THEN 1 END) AS external_urls
FROM dashboard_header_images;

-- Step 5: List updated URLs
SELECT 
    'Updated Image URLs:' AS section,
    id,
    title,
    image_url,
    updated_at
FROM dashboard_header_images
WHERE image_url LIKE '%header/images%'
ORDER BY updated_at DESC;

-- =====================================================
-- Manual Steps Required in Supabase Storage UI:
-- =====================================================
-- 
-- 1. Go to Supabase Dashboard → Storage
-- 2. Open 'profile-photos' bucket
-- 3. Navigate to 'header' folder
-- 4. Select all dashboard_header_*.jpg files
-- 5. Download them to your computer
-- 6. Go to 'header' bucket
-- 7. Create 'images' folder if not exists
-- 8. Upload all downloaded files to header/images/
-- 9. Verify URLs are accessible
-- 10. Delete old files from profile-photos/header/
-- 
-- Alternative: Use Supabase CLI for bulk migration
-- supabase storage cp profile-photos/header/ header/images/ --recursive
-- =====================================================

-- Step 6: Cleanup verification
-- Run this AFTER manually moving files in storage
SELECT 
    'Final Verification:' AS test_section,
    id,
    title,
    image_url,
    CASE 
        WHEN image_url LIKE '%header/images/%' THEN '✅ MIGRATED'
        WHEN image_url LIKE '%profile-photos%' THEN '❌ OLD BUCKET'
        ELSE '🌐 EXTERNAL'
    END AS status
FROM dashboard_header_images
ORDER BY created_at;

-- Success message
SELECT 
    '✅ Header images migration script complete!' AS message,
    'Next: Manually move files in Supabase Storage UI' AS next_step,
    'From: profile-photos/header/' AS source,
    'To: header/images/' AS destination,
    'Then verify all URLs are working' AS verification;

-- =====================================================
-- Rollback Script (if needed):
-- =====================================================
-- 
-- UPDATE dashboard_header_images 
-- SET image_url = REPLACE(
--     image_url, 
--     '/storage/v1/object/public/header/images/', 
--     '/storage/v1/object/public/profile-photos/header/'
-- )
-- WHERE image_url LIKE '%header/images/%';
-- =====================================================
