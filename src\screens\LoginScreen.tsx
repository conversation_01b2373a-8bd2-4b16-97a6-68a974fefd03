import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';

export const LoginScreen: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isOffline, setIsOffline] = useState(false);
  const [canLoginOffline, setCanLoginOffline] = useState(false);

  const { signIn, enableTestingMode } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();

  // Simplified initialization - platform agnostic
  React.useEffect(() => {
    // Set default network status
    setIsOffline(false); // Assume online by default
    setCanLoginOffline(false); // Disable offline login for now
  }, []);

  // Real-time validation functions
  const validateEmail = (emailValue: string) => {
    setEmailError('');
    if (!emailValue.trim()) {
      setEmailError('Email is required');
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailValue.trim())) {
      setEmailError('Please enter a valid email address');
      return false;
    }
    return true;
  };

  const validatePassword = (passwordValue: string) => {
    setPasswordError('');
    if (!passwordValue.trim()) {
      setPasswordError('Password is required');
      return false;
    }
    if (passwordValue.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      return false;
    }
    return true;
  };

  // Handle input changes with validation
  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (emailError && value.trim()) {
      validateEmail(value);
    }
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    if (passwordError && value.trim()) {
      validatePassword(value);
    }
  };

  const getLoginErrorMessage = (error: any) => {
    const errorMessage = error?.message || '';

    // Handle specific Supabase auth errors
    if (errorMessage.includes('Invalid login credentials')) {
      return {
        title: '🔐 Login Failed',
        message: 'The email or password you entered is incorrect.\n\nPlease check your credentials and try again.',
        suggestions: [
          '• Make sure your email is spelled correctly',
          '• Check if Caps Lock is on',
          '• Try the demo account: <EMAIL>'
        ]
      };
    }

    if (errorMessage.includes('Email not confirmed')) {
      return {
        title: '📧 Email Not Verified',
        message: 'Please check your email and click the verification link before signing in.',
        suggestions: [
          '• Check your spam/junk folder',
          '• Contact admin if you need help'
        ]
      };
    }

    if (errorMessage.includes('Too many requests')) {
      return {
        title: '⏰ Too Many Attempts',
        message: 'Too many login attempts. Please wait a few minutes before trying again.',
        suggestions: [
          '• Wait 5-10 minutes before retrying',
          '• Use the demo account for testing'
        ]
      };
    }

    if (errorMessage.includes('Network')) {
      return {
        title: '🌐 Connection Error',
        message: 'Unable to connect to the server. Please check your internet connection.',
        suggestions: [
          '• Check your internet connection',
          '• Try again in a few moments',
          '• Use Testing Mode for offline demo'
        ]
      };
    }

    // Default error message
    return {
      title: '❌ Login Error',
      message: 'An unexpected error occurred during login.',
      suggestions: [
        '• Try again in a few moments',
        '• Use demo account: <EMAIL>',
        '• Contact support if problem persists'
      ]
    };
  };

  const showLoginError = (error: any) => {
    const { title, message, suggestions } = getLoginErrorMessage(error);

    Alert.alert(
      title,
      `${message}\n\n${suggestions.join('\n')}`,
      [
        {
          text: 'Try Demo',
          onPress: handleDemoLogin,
          style: 'default'
        },
        {
          text: 'OK',
          style: 'cancel'
        }
      ]
    );
  };

  const handleLogin = async () => {
    // Clear previous errors
    setEmailError('');
    setPasswordError('');

    // Validate input fields
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);

    if (!isEmailValid || !isPasswordValid) {
      // Errors are already set by validation functions
      return;
    }

    try {
      setLoading(true);
      await signIn(email.trim(), password);
      // Navigation will be handled by the auth state change
    } catch (error: any) {
      showLoginError(error);
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    // Demo credentials for testing
    setEmail('<EMAIL>');
    setPassword('demo123');

    try {
      setLoading(true);
      await signIn('<EMAIL>', 'demo123');
    } catch (error: any) {
      console.error('Demo login error:', error);
      Alert.alert(
        '🚀 Demo Account Issue',
        'The demo account is temporarily unavailable.\n\nWould you like to continue in Testing Mode?\n\n• Full app functionality\n• Sample data included\n• No internet required',
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: '🧪 Testing Mode',
            onPress: () => {
              enableTestingMode();
              Alert.alert(
                '✅ Testing Mode Active',
                'Welcome to Mining Operations!\n\n🎯 You are logged in as Demo Supervisor\n📊 All features are available\n💾 Sample data is loaded\n\nEnjoy exploring the app!',
                [{ text: 'Get Started', style: 'default' }]
              );
            }
          }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  // Offline login disabled for simplified app
  const handleOfflineLogin = async () => {
    Alert.alert(
      'Offline Login Disabled',
      'Offline login is currently disabled. Please connect to the internet to login.',
      [{ text: 'OK' }]
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar style="light" />
      
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Ionicons name="construct" size={60} color={Colors.primary} />
          </View>
          <Text style={styles.title}>Mining Operations</Text>
          <Text style={styles.subtitle}>Sign in to your account</Text>
        </View>

        {/* Login Form */}
        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Ionicons name="mail-outline" size={20} color={Colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={[styles.input, emailError ? styles.inputError : null]}
              placeholder="Email address"
              placeholderTextColor={Colors.textLight}
              value={email}
              onChangeText={handleEmailChange}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              onBlur={() => email.trim() && validateEmail(email)}
            />
          </View>
          {emailError ? (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle" size={16} color={Colors.secondary} />
              <Text style={styles.errorText}>{emailError}</Text>
            </View>
          ) : null}

          <View style={styles.inputContainer}>
            <Ionicons name="lock-closed-outline" size={20} color={Colors.textLight} style={styles.inputIcon} />
            <TextInput
              style={[styles.input, passwordError ? styles.inputError : null]}
              placeholder="Password"
              placeholderTextColor={Colors.textLight}
              value={password}
              onChangeText={handlePasswordChange}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
              autoCorrect={false}
              onBlur={() => password.trim() && validatePassword(password)}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Ionicons
                name={showPassword ? 'eye-outline' : 'eye-off-outline'}
                size={20}
                color={Colors.textLight}
              />
            </TouchableOpacity>
          </View>
          {passwordError ? (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle" size={16} color={Colors.secondary} />
              <Text style={styles.errorText}>{passwordError}</Text>
            </View>
          ) : null}

          <TouchableOpacity
            style={[styles.loginButton, loading && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={Colors.textInverse} />
            ) : (
              <Text style={styles.loginButtonText}>Sign In</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.demoButton}
            onPress={handleDemoLogin}
            disabled={loading}
          >
            <Text style={styles.demoButtonText}>Try Demo Account</Text>
          </TouchableOpacity>

          {/* Offline Login Button - Show when offline */}
          {isOffline && (
            <TouchableOpacity
              style={[styles.offlineButton, { opacity: canLoginOffline ? 1 : 0.5 }]}
              onPress={handleOfflineLogin}
              disabled={loading || !canLoginOffline}
            >
              <Ionicons name="cloud-offline-outline" size={20} color={Colors.textInverse} />
              <Text style={styles.offlineButtonText}>
                {canLoginOffline ? 'Continue Offline' : 'Enable Offline Mode'}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={styles.forgotPassword}>
            <Text style={styles.forgotPasswordText}>Forgot your password?</Text>
          </TouchableOpacity>

          {/* Offline Status Indicator */}
          {isOffline && (
            <View style={styles.offlineIndicator}>
              <Ionicons name="wifi-outline" size={16} color={Colors.warning} />
              <Text style={styles.offlineIndicatorText}>
                No internet connection - Offline mode available
              </Text>
            </View>
          )}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Don't have an account? Contact your administrator
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: Layout.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: Layout.spacing.xxl,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
    ...Layout.shadow,
  },
  title: {
    fontSize: Layout.fontSize.xxxl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.xs,
  },
  subtitle: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
  },
  form: {
    marginBottom: Layout.spacing.xxl,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.md,
    paddingHorizontal: Layout.spacing.md,
    height: Layout.inputHeight,
    ...Layout.shadowLight,
  },
  inputIcon: {
    marginRight: Layout.spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
  },
  eyeIcon: {
    padding: Layout.spacing.xs,
  },
  loginButton: {
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.lg,
    height: Layout.buttonHeight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
    ...Layout.shadow,
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    color: Colors.textInverse,
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
  },
  demoButton: {
    backgroundColor: Colors.secondary,
    borderRadius: Layout.borderRadius.lg,
    height: Layout.buttonHeight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
  },
  demoButtonText: {
    color: Colors.textInverse,
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
  },
  offlineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.warning,
    paddingVertical: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginTop: Layout.spacing.sm,
  },
  offlineButtonText: {
    color: Colors.textInverse,
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    marginLeft: Layout.spacing.xs,
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.warningLight + '20',
    paddingVertical: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.md,
    borderRadius: Layout.borderRadius.sm,
    marginTop: Layout.spacing.sm,
    borderWidth: 1,
    borderColor: Colors.warning + '40',
  },
  offlineIndicatorText: {
    color: Colors.warning,
    fontSize: Layout.fontSize.xs,
    marginLeft: Layout.spacing.xs,
  },
  forgotPassword: {
    alignItems: 'center',
  },
  forgotPasswordText: {
    color: Colors.primary,
    fontSize: Layout.fontSize.sm,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    color: Colors.textLight,
    fontSize: Layout.fontSize.sm,
    textAlign: 'center',
  },
  inputError: {
    borderColor: Colors.secondary,
    borderWidth: 1,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Layout.spacing.xs,
    marginBottom: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.sm,
  },
  errorText: {
    color: Colors.secondary,
    fontSize: Layout.fontSize.sm,
    marginLeft: Layout.spacing.xs,
    flex: 1,
  },
});
