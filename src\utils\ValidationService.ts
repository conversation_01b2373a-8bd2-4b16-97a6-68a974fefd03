import { z } from 'zod';

// Mining-specific validation schemas
export const ValidationSchemas = {
  // User Authentication
  loginSchema: z.object({
    email: z.string()
      .email('Invalid email format')
      .min(1, 'Email is required'),
    password: z.string()
      .min(6, 'Password must be at least 6 characters')
      .max(50, 'Password too long'),
  }),

  registerSchema: z.object({
    email: z.string().email('Invalid email format'),
    password: z.string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
    confirmPassword: z.string(),
    fullName: z.string()
      .min(2, 'Full name must be at least 2 characters')
      .max(100, 'Full name too long'),
    employeeId: z.string()
      .regex(/^[A-Z]{3}\d{3}$/, 'Employee ID must be format: ABC123'),
    role: z.enum(['admin', 'supervisor', 'safety_officer', 'maintenance_tech', 'operator']),
    locationId: z.string().uuid('Invalid location ID'),
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  }),

  // Production Data
  productionReportSchema: z.object({
    reportDate: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
      .refine((date) => {
        const reportDate = new Date(date);
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        return reportDate >= thirtyDaysAgo && reportDate <= today;
      }, 'Report date must be within last 30 days'),
    
    shift: z.enum(['day', 'night', 'swing']),
    locationId: z.string().uuid('Invalid location ID'),
    
    // Production metrics
    actualOB: z.number()
      .min(0, 'Overburden cannot be negative')
      .max(50000, 'Overburden value seems too high')
      .multipleOf(0.01, 'Use up to 2 decimal places'),
    
    actualOre: z.number()
      .min(0, 'Ore production cannot be negative')
      .max(20000, 'Ore production value seems too high')
      .multipleOf(0.01, 'Use up to 2 decimal places'),
    
    actualFuel: z.number()
      .min(0, 'Fuel consumption cannot be negative')
      .max(50000, 'Fuel consumption seems too high')
      .multipleOf(0.1, 'Use up to 1 decimal place'),
    
    operatingHours: z.number()
      .min(0, 'Operating hours cannot be negative')
      .max(24, 'Operating hours cannot exceed 24 per day')
      .multipleOf(0.1, 'Use up to 1 decimal place'),
    
    downtimeHours: z.number()
      .min(0, 'Downtime cannot be negative')
      .max(24, 'Downtime cannot exceed 24 hours')
      .multipleOf(0.1, 'Use up to 1 decimal place'),
    
    crewSize: z.number()
      .int('Crew size must be a whole number')
      .min(1, 'Crew size must be at least 1')
      .max(50, 'Crew size seems too large'),
    
    weatherConditions: z.string()
      .max(200, 'Weather description too long')
      .optional(),
    
    notes: z.string()
      .max(1000, 'Notes too long')
      .optional(),
    
    equipmentUsed: z.array(z.string().uuid()).optional(),
  }).refine((data) => {
    // Validate that operating hours + downtime doesn't exceed 24
    return (data.operatingHours + data.downtimeHours) <= 24;
  }, {
    message: 'Operating hours + downtime cannot exceed 24 hours',
    path: ['downtimeHours'],
  }).refine((data) => {
    // Validate stripping ratio makes sense
    if (data.actualOre > 0) {
      const strippingRatio = data.actualOB / data.actualOre;
      return strippingRatio >= 0.1 && strippingRatio <= 20;
    }
    return true;
  }, {
    message: 'Stripping ratio seems unrealistic (should be between 0.1 and 20)',
    path: ['actualOB'],
  }),

  // Safety Incident
  safetyIncidentSchema: z.object({
    title: z.string()
      .min(5, 'Title must be at least 5 characters')
      .max(200, 'Title too long'),
    
    description: z.string()
      .min(10, 'Description must be at least 10 characters')
      .max(2000, 'Description too long'),
    
    incidentDate: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, 'Invalid date format'),
    
    severity: z.enum(['low', 'medium', 'high', 'critical']),
    
    incidentType: z.enum(['injury', 'near_miss', 'equipment_failure', 'environmental', 'security']),
    
    locationId: z.string().uuid('Invalid location ID'),
    
    equipmentId: z.string().uuid().optional(),
    
    injuredPersonName: z.string()
      .max(100, 'Name too long')
      .optional(),
    
    witnesses: z.array(z.string().max(100)).optional(),
    
    correctiveActions: z.string()
      .max(2000, 'Corrective actions description too long')
      .optional(),
    
    attachments: z.array(z.string().url()).optional(),
  }),

  // Equipment Management
  equipmentSchema: z.object({
    name: z.string()
      .min(2, 'Equipment name must be at least 2 characters')
      .max(100, 'Equipment name too long'),
    
    model: z.string()
      .max(50, 'Model name too long')
      .optional(),
    
    serialNumber: z.string()
      .min(3, 'Serial number must be at least 3 characters')
      .max(50, 'Serial number too long')
      .regex(/^[A-Z0-9-]+$/, 'Serial number can only contain uppercase letters, numbers, and hyphens'),
    
    manufacturer: z.string()
      .max(50, 'Manufacturer name too long')
      .optional(),
    
    equipmentType: z.enum(['excavator', 'dump_truck', 'drill', 'conveyor', 'crusher', 'loader', 'bulldozer', 'grader']),
    
    locationId: z.string().uuid('Invalid location ID'),
    
    purchaseDate: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
      .optional(),
    
    operatingHours: z.number()
      .min(0, 'Operating hours cannot be negative')
      .max(100000, 'Operating hours seem too high')
      .int('Operating hours must be a whole number')
      .optional(),
    
    fuelCapacity: z.number()
      .min(0, 'Fuel capacity cannot be negative')
      .max(10000, 'Fuel capacity seems too high')
      .multipleOf(0.1)
      .optional(),
    
    maxLoadCapacity: z.number()
      .min(0, 'Load capacity cannot be negative')
      .max(1000, 'Load capacity seems too high')
      .multipleOf(0.1)
      .optional(),
  }),

  // Maintenance Record
  maintenanceSchema: z.object({
    equipmentId: z.string().uuid('Invalid equipment ID'),
    
    maintenanceType: z.enum(['preventive', 'corrective', 'emergency', 'inspection']),
    
    scheduledDate: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
    
    completedDate: z.string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
      .optional(),
    
    description: z.string()
      .min(10, 'Description must be at least 10 characters')
      .max(1000, 'Description too long'),
    
    laborHours: z.number()
      .min(0, 'Labor hours cannot be negative')
      .max(100, 'Labor hours seem too high')
      .multipleOf(0.1)
      .optional(),
    
    totalCost: z.number()
      .min(0, 'Cost cannot be negative')
      .max(1000000, 'Cost seems too high')
      .multipleOf(0.01)
      .optional(),
    
    notes: z.string()
      .max(1000, 'Notes too long')
      .optional(),
  }),

  // User Profile Update
  profileUpdateSchema: z.object({
    fullName: z.string()
      .min(2, 'Full name must be at least 2 characters')
      .max(100, 'Full name too long'),
    
    phone: z.string()
      .regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format')
      .optional(),
    
    emergencyContact: z.object({
      name: z.string().max(100),
      phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/),
      relationship: z.string().max(50),
    }).optional(),
  }),
};

// Validation service class
export class ValidationService {
  // Generic validation method
  static validate<T>(schema: z.ZodSchema<T>, data: unknown): {
    success: boolean;
    data?: T;
    errors?: Record<string, string>;
  } {
    try {
      const result = schema.parse(data);
      return { success: true, data: result };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join('.');
          errors[path] = err.message;
        });
        return { success: false, errors };
      }
      return { success: false, errors: { general: 'Validation failed' } };
    }
  }

  // Mining-specific validations
  static validateProductionData(data: unknown) {
    return this.validate(ValidationSchemas.productionReportSchema, data);
  }

  static validateSafetyIncident(data: unknown) {
    return this.validate(ValidationSchemas.safetyIncidentSchema, data);
  }

  static validateEquipment(data: unknown) {
    return this.validate(ValidationSchemas.equipmentSchema, data);
  }

  static validateMaintenance(data: unknown) {
    return this.validate(ValidationSchemas.maintenanceSchema, data);
  }

  static validateLogin(data: unknown) {
    return this.validate(ValidationSchemas.loginSchema, data);
  }

  static validateRegistration(data: unknown) {
    return this.validate(ValidationSchemas.registerSchema, data);
  }

  // Business rule validations
  static validateBusinessRules = {
    // Check if stripping ratio is within acceptable range
    strippingRatio: (ob: number, ore: number): { valid: boolean; message?: string } => {
      if (ore === 0) return { valid: true };
      
      const ratio = ob / ore;
      if (ratio < 0.5 || ratio > 15) {
        return {
          valid: false,
          message: `Stripping ratio ${ratio.toFixed(2)} is outside normal range (0.5-15.0)`
        };
      }
      return { valid: true };
    },

    // Check if fuel consumption is reasonable
    fuelEfficiency: (fuel: number, material: number): { valid: boolean; message?: string } => {
      if (material === 0) return { valid: true };
      
      const efficiency = fuel / material;
      if (efficiency > 5) {
        return {
          valid: false,
          message: `Fuel efficiency ${efficiency.toFixed(2)} L/ton seems too high`
        };
      }
      return { valid: true };
    },

    // Check if production targets are realistic
    productionTarget: (actual: number, target: number): { valid: boolean; message?: string } => {
      if (target === 0) return { valid: true };
      
      const achievement = (actual / target) * 100;
      if (achievement > 150) {
        return {
          valid: false,
          message: `Achievement ${achievement.toFixed(1)}% seems unrealistically high`
        };
      }
      return { valid: true };
    },

    // Validate equipment operating hours
    operatingHours: (hours: number, equipmentAge: number): { valid: boolean; message?: string } => {
      const maxHoursPerYear = 8760; // 24 * 365
      const expectedMaxHours = equipmentAge * maxHoursPerYear;
      
      if (hours > expectedMaxHours * 1.2) {
        return {
          valid: false,
          message: `Operating hours ${hours} seem too high for equipment age`
        };
      }
      return { valid: true };
    },
  };

  // Sanitization methods
  static sanitize = {
    // Remove potentially dangerous characters
    text: (input: string): string => {
      return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                  .replace(/[<>]/g, '')
                  .trim();
    },

    // Sanitize numbers
    number: (input: string | number): number | null => {
      const num = typeof input === 'string' ? parseFloat(input) : input;
      return isNaN(num) ? null : num;
    },

    // Sanitize email
    email: (input: string): string => {
      return input.toLowerCase().trim();
    },

    // Sanitize phone number
    phone: (input: string): string => {
      return input.replace(/[^\d\+\-\(\)\s]/g, '').trim();
    },
  };
}

export default ValidationService;
