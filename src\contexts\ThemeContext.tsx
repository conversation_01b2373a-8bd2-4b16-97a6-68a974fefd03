import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance, ColorSchemeName } from 'react-native';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeContextType {
  themeMode: ThemeMode;
  isDarkMode: boolean;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

const THEME_STORAGE_KEY = '@mining_app_theme';

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );

  // Calculate if dark mode should be active
  const isDarkMode = 
    themeMode === 'dark' || 
    (themeMode === 'system' && systemColorScheme === 'dark');

  // Load saved theme preference on app start
  useEffect(() => {
    loadThemePreference();
    
    // Listen to system theme changes
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription?.remove();
  }, []);

  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        setThemeModeState(savedTheme as ThemeMode);
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    }
  };

  const setThemeMode = async (mode: ThemeMode) => {
    try {
      setThemeModeState(mode);
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
      console.log(`🎨 Theme changed to: ${mode}`);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const toggleTheme = () => {
    const newMode = isDarkMode ? 'light' : 'dark';
    setThemeMode(newMode);
  };

  const value: ThemeContextType = {
    themeMode,
    isDarkMode,
    setThemeMode,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Theme-aware colors hook
export const useThemeColors = () => {
  const { isDarkMode } = useTheme();
  
  return {
    // Background colors - Professional dark palette
    background: isDarkMode ? '#0F172A' : '#F7FAFC',        // Dark slate vs Light gray
    surface: isDarkMode ? '#1E293B' : '#FFFFFF',           // Slate 800 vs White
    surfaceSecondary: isDarkMode ? '#334155' : '#EDF2F7',  // Slate 700 vs Light gray

    // Text colors - High contrast for readability
    textPrimary: isDarkMode ? '#F8FAFC' : '#1A202C',       // Slate 50 vs Dark gray
    textSecondary: isDarkMode ? '#CBD5E1' : '#4A5568',     // Slate 300 vs Medium gray
    textLight: isDarkMode ? '#94A3B8' : '#718096',         // Slate 400 vs Light gray
    textInverse: isDarkMode ? '#1A202C' : '#FFFFFF',       // Dark vs White
    
    // Primary colors (consistent across themes)
    primary: '#1A365D',
    primaryLight: '#2D5A87',
    primaryDark: '#0F2A44',
    
    // Status colors
    success: isDarkMode ? '#48BB78' : '#38A169',
    warning: isDarkMode ? '#ED8936' : '#D69E2E',
    error: isDarkMode ? '#F56565' : '#E53E3E',
    info: isDarkMode ? '#4299E1' : '#3182CE',
    
    // Border colors - Subtle but visible
    border: isDarkMode ? '#475569' : '#E2E8F0',           // Slate 600 vs Light border
    borderDark: isDarkMode ? '#64748B' : '#CBD5E0',       // Slate 500 vs Darker border

    // Card colors - Professional elevation
    cardBackground: isDarkMode ? '#1E293B' : '#FFFFFF',   // Slate 800 vs White
    cardBorder: isDarkMode ? '#475569' : '#E2E8F0',       // Slate 600 vs Light border
    cardShadow: isDarkMode ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.1)',

    // Header colors - Consistent branding
    headerBackground: isDarkMode ? '#0F172A' : '#1A365D', // Dark slate vs Primary blue
    headerText: '#FFFFFF',
    
    // Mining specific colors (consistent)
    equipmentActive: '#38A169',
    equipmentMaintenance: '#D69E2E',
    equipmentInactive: isDarkMode ? '#8C8C8C' : '#718096',
    equipmentAlert: '#E53E3E',
  };
};
