import { Colors } from '../../constants/colors';
import { Layout } from '../../constants/layout';

export type ChartType = 'line' | 'bar' | 'area';
export type MetricType = 'overburden' | 'ore_production' | 'stripping_ratio' | 'fuel_usage' | 'fuel_ratio' | 'total_material';

export interface ChartDataPoint {
  label: string;
  value: number;
  date: string;
  actualValue?: number;
  planValue?: number;
}

export interface ChartDataset {
  data: number[];
  color: (opacity?: number) => string;
  strokeWidth?: number;
  label: string;
  gradient?: {
    from: string;
    to: string;
  };
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
  legend?: string[];
}

export interface ChartMetricConfig {
  id: MetricType;
  name: string;
  shortName: string;
  unit: string;
  color: string;
  gradientFrom: string;
  gradientTo: string;
  icon: string;
  decimalPlaces: number;
  yAxisSuffix: string;
  chartType: ChartType;
}

// Chart metric configurations
export const CHART_METRICS: ChartMetricConfig[] = [
  {
    id: 'total_material',
    name: 'Total Material Moved',
    shortName: 'Total Material',
    unit: 'Bcm',
    color: Colors.primary,
    gradientFrom: '#3B82F6',
    gradientTo: '#1E40AF',
    icon: 'cube-outline',
    decimalPlaces: 2,
    yAxisSuffix: ' Bcm',
    chartType: 'area',
  },
  {
    id: 'overburden',
    name: 'Overburden Volume',
    shortName: 'Overburden',
    unit: 'Bcm',
    color: '#10B981',
    gradientFrom: '#10B981',
    gradientTo: '#059669',
    icon: 'layers-outline',
    decimalPlaces: 2,
    yAxisSuffix: ' Bcm',
    chartType: 'bar',
  },
  {
    id: 'ore_production',
    name: 'Ore Production',
    shortName: 'Ore',
    unit: 'tons',
    color: '#F59E0B',
    gradientFrom: '#F59E0B',
    gradientTo: '#D97706',
    icon: 'diamond-outline',
    decimalPlaces: 0,
    yAxisSuffix: ' tons',
    chartType: 'line',
  },
  {
    id: 'stripping_ratio',
    name: 'Stripping Ratio',
    shortName: 'SR',
    unit: 'ratio',
    color: '#8B5CF6',
    gradientFrom: '#8B5CF6',
    gradientTo: '#7C3AED',
    icon: 'analytics-outline',
    decimalPlaces: 2,
    yAxisSuffix: '',
    chartType: 'line',
  },
  {
    id: 'fuel_usage',
    name: 'Fuel Usage',
    shortName: 'Fuel',
    unit: 'L',
    color: '#EF4444',
    gradientFrom: '#EF4444',
    gradientTo: '#DC2626',
    icon: 'car-outline',
    decimalPlaces: 0,
    yAxisSuffix: ' L',
    chartType: 'bar',
  },
  {
    id: 'fuel_ratio',
    name: 'Fuel Ratio',
    shortName: 'FR',
    unit: 'L/Bcm',
    color: '#06B6D4',
    gradientFrom: '#06B6D4',
    gradientTo: '#0891B2',
    icon: 'speedometer-outline',
    decimalPlaces: 2,
    yAxisSuffix: ' L/Bcm',
    chartType: 'line',
  },
];

// Chart configuration for react-native-chart-kit
export const getChartConfig = (metric: ChartMetricConfig, isDark = false) => ({
  backgroundColor: isDark ? '#1F2937' : '#FFFFFF',
  backgroundGradientFrom: isDark ? '#1F2937' : '#FFFFFF',
  backgroundGradientTo: isDark ? '#111827' : '#F9FAFB',
  decimalPlaces: metric.decimalPlaces,
  color: (opacity = 1) => `${metric.color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
  labelColor: (opacity = 1) => isDark ? `rgba(255, 255, 255, ${opacity})` : `rgba(55, 65, 81, ${opacity})`,
  style: {
    borderRadius: Layout.borderRadius.md,
    paddingRight: 40,
  },
  propsForDots: {
    r: '4',
    strokeWidth: '2',
    stroke: metric.color,
    fill: metric.color,
  },
  propsForBackgroundLines: {
    strokeDasharray: '2,2',
    stroke: isDark ? '#374151' : '#E5E7EB',
    strokeWidth: 1,
  },
  propsForVerticalLabels: {
    fontSize: 10,
    fill: isDark ? '#D1D5DB' : '#6B7280',
    fontWeight: '400',
  },
  propsForHorizontalLabels: {
    fontSize: 10,
    fill: isDark ? '#D1D5DB' : '#374151',
    fontWeight: '500',
  },
  fillShadowGradient: metric.color,
  fillShadowGradientOpacity: 0.1,
  yAxisSuffix: metric.yAxisSuffix,
  formatYLabel: (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num) || !isFinite(num)) return '0';
    return num.toFixed(metric.decimalPlaces);
  },
  formatXLabel: (value: string) => String(value),
});

// Chart dimensions
export const getChartDimensions = (dataLength: number, screenWidth: number) => {
  const minWidth = screenWidth * 0.9;
  const dynamicWidth = Math.max(minWidth, dataLength * 40);
  return {
    width: dynamicWidth,
    height: 220,
  };
};

// Animation configurations
export const CHART_ANIMATIONS = {
  duration: 300,
  easing: 'ease-in-out',
  delay: 0,
};

// Chart interaction configurations
export const CHART_INTERACTIONS = {
  enablePan: true,
  enableZoom: false,
  enableTooltip: true,
  tooltipDelay: 100,
};
