const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ohqbaimnhwvdfrmxvhxv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ocWJhaW1uaHd2ZGZybXh2aHh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4ODA3NzEsImV4cCI6MjA2ODQ1Njc3MX0.Qq-2pKIvW2SSJlgQqTW6I_gXdxt81oWv2wViadb9b-Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function getAllTables() {
  console.log('🔍 Discovering all tables in Supabase database...\n');
  
  // List of potential tables based on schema files
  const potentialTables = [
    'locations',
    'users', 
    'equipment',
    'safety_incidents',
    'production_reports',
    'maintenance_records',
    'shifts',
    'user_shifts',
    'daily_production_metrics',
    'production_calendar',
    'daily_mining_report',
    'employees',
    'departments',
    'user_roles',
    'equipment_types',
    'maintenance_types',
    'incident_types',
    'activity_documentation',
    'certificates',
    'certificate_renewals',
    'safety_activities',
    'plan_vs_actual'
  ];

  console.log('📋 Checking tables and their record counts:\n');
  
  const existingTables = [];
  
  for (const tableName of potentialTables) {
    try {
      const { count, error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        if (error.message.includes('does not exist')) {
          console.log(`❌ Table '${tableName}': does not exist`);
        } else {
          console.log(`⚠️  Table '${tableName}': ${error.message}`);
        }
      } else {
        console.log(`✅ Table '${tableName}': ${count || 0} records`);
        existingTables.push({ name: tableName, count: count || 0 });
      }
    } catch (err) {
      console.log(`❌ Table '${tableName}': ${err.message}`);
    }
  }

  return existingTables;
}

async function exploreTableStructures(tables) {
  console.log('\n🏗️ Exploring table structures...\n');
  
  for (const table of tables) {
    if (table.count > 0) {
      console.log(`📊 Table: ${table.name} (${table.count} records)`);
      
      try {
        const { data } = await supabase
          .from(table.name)
          .select('*')
          .limit(1);

        if (data && data.length > 0) {
          const columns = Object.keys(data[0]);
          console.log(`   Columns (${columns.length}): ${columns.join(', ')}`);
          
          // Show sample data for key columns
          const sampleData = data[0];
          const keyColumns = columns.slice(0, 5); // Show first 5 columns
          console.log('   Sample data:');
          keyColumns.forEach(col => {
            let value = sampleData[col];
            if (typeof value === 'string' && value.length > 50) {
              value = value.substring(0, 50) + '...';
            }
            console.log(`     ${col}: ${value}`);
          });
        }
      } catch (error) {
        console.log(`   ❌ Error reading structure: ${error.message}`);
      }
      
      console.log(''); // Empty line for readability
    }
  }
}

async function getRecentData(tables) {
  console.log('\n📊 Recent data from active tables...\n');
  
  for (const table of tables) {
    if (table.count > 0) {
      console.log(`📋 Recent records from ${table.name}:`);
      
      try {
        // Try to get recent records, assuming there might be a date or created_at field
        let query = supabase.from(table.name).select('*').limit(3);
        
        // Try to order by common date fields
        const { data: sampleForOrder } = await supabase
          .from(table.name)
          .select('*')
          .limit(1);
        
        if (sampleForOrder && sampleForOrder.length > 0) {
          const columns = Object.keys(sampleForOrder[0]);
          const dateColumns = columns.filter(col => 
            col.includes('date') || col.includes('created_at') || col.includes('updated_at')
          );
          
          if (dateColumns.length > 0) {
            query = query.order(dateColumns[0], { ascending: false });
          }
        }
        
        const { data, error } = await query;
        
        if (error) {
          console.log(`   ❌ Error: ${error.message}`);
        } else if (data && data.length > 0) {
          data.forEach((record, index) => {
            console.log(`   ${index + 1}. Record ID: ${record.id || 'N/A'}`);
            
            // Show key fields
            const keyFields = Object.keys(record).slice(0, 4);
            keyFields.forEach(field => {
              let value = record[field];
              if (typeof value === 'string' && value.length > 40) {
                value = value.substring(0, 40) + '...';
              }
              console.log(`      ${field}: ${value}`);
            });
          });
        }
      } catch (error) {
        console.log(`   ❌ Error reading data: ${error.message}`);
      }
      
      console.log(''); // Empty line
    }
  }
}

async function main() {
  console.log('🚀 Complete Supabase Database Analysis...\n');
  
  try {
    // Get all tables
    const existingTables = await getAllTables();
    
    if (existingTables.length === 0) {
      console.log('❌ No accessible tables found in the database.');
      return;
    }
    
    console.log(`\n✅ Found ${existingTables.length} accessible tables`);
    
    // Show summary
    console.log('\n📊 Database Summary:');
    const totalRecords = existingTables.reduce((sum, table) => sum + table.count, 0);
    console.log(`   Total Records: ${totalRecords.toLocaleString()}`);
    console.log(`   Active Tables: ${existingTables.filter(t => t.count > 0).length}`);
    console.log(`   Empty Tables: ${existingTables.filter(t => t.count === 0).length}`);
    
    // Explore structures
    await exploreTableStructures(existingTables.filter(t => t.count > 0));
    
    // Get recent data
    await getRecentData(existingTables.filter(t => t.count > 0));

    console.log('\n🌐 Access your Supabase dashboard at:');
    console.log(`   https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv`);
    
    console.log('\n📋 Quick Access Links:');
    console.log('   - Table Editor: https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv/editor');
    console.log('   - SQL Editor: https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv/sql');
    console.log('   - API Docs: https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv/api');
    console.log('   - Database Schema: https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv/database/tables');

  } catch (error) {
    console.error('\n❌ Database analysis failed:', error.message);
  }
}

main().catch(console.error);
