-- =====================================================
-- Mining Operations Database - Complete Deployment Script
-- =====================================================
-- File: deploy.sql
-- Description: Complete database deployment in correct order
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- DEPLOYMENT INFORMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MINING OPERATIONS DATABASE DEPLOYMENT';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Version: 1.0';
    RAISE NOTICE 'Date: %', NOW();
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'User: %', current_user;
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Starting deployment...';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- EXECUTE SCHEMA FILES IN CORRECT ORDER
-- =====================================================

-- 1. Core Setup (Extensions, Types, Functions)
\echo 'Executing 01-core-setup.sql...'
\i 01-core-setup.sql

-- 2. Production Calendar (Calendar and Targets)
\echo 'Executing 02-production-calendar.sql...'
\i 02-production-calendar.sql

-- 3. Daily Mining Report (Main Data Table)
\echo 'Executing 03-daily-mining-report.sql...'
\i 03-daily-mining-report.sql

-- 4. Analytical Views (Reporting Views)
\echo 'Executing 04-analytical-views.sql...'
\i 04-analytical-views.sql

-- 5. User Management (Authentication & Authorization)
\echo 'Executing 06-user-management.sql...'
\i 06-user-management.sql

-- 6. Equipment Management (Equipment tracking and maintenance)
\echo 'Executing 07-equipment-management.sql...'
\i 07-equipment-management.sql

-- 7. Safety Management (Safety incidents, training, compliance)
\echo 'Executing 08-safety-management.sql...'
\i 08-safety-management.sql

-- 8. Row Level Security Policies (Data access control)
\echo 'Executing 09-rls-policies.sql...'
\i 09-rls-policies.sql

-- 9. Performance Indexes (Database optimization)
\echo 'Executing 10-indexes-performance.sql...'
\i 10-indexes-performance.sql

-- 10. Sample Data (Optional - for testing)
\echo 'Executing 05-sample-data.sql...'
\i 05-sample-data.sql

-- =====================================================
-- POST-DEPLOYMENT VERIFICATION
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    view_count INTEGER;
    function_count INTEGER;
    enum_count INTEGER;
    user_count INTEGER;
    report_count INTEGER;
    target_count INTEGER;
    migration_count INTEGER;
BEGIN
    -- Count database objects
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    
    SELECT COUNT(*) INTO view_count 
    FROM information_schema.views 
    WHERE table_schema = 'public';
    
    SELECT COUNT(*) INTO function_count 
    FROM information_schema.routines 
    WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';
    
    SELECT COUNT(*) INTO enum_count 
    FROM pg_type 
    WHERE typtype = 'e' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
    
    SELECT COUNT(*) INTO user_count FROM user_profiles;
    SELECT COUNT(*) INTO report_count FROM daily_mining_report;
    SELECT COUNT(*) INTO target_count FROM production_targets_calendar;
    SELECT COUNT(*) INTO migration_count FROM schema_migrations;
    
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'DEPLOYMENT VERIFICATION';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Database Objects Created:';
    RAISE NOTICE '- Tables: %', table_count;
    RAISE NOTICE '- Views: %', view_count;
    RAISE NOTICE '- Functions: %', function_count;
    RAISE NOTICE '- Enums: %', enum_count;
    RAISE NOTICE '';
    RAISE NOTICE 'Sample Data Loaded:';
    RAISE NOTICE '- Users: %', user_count;
    RAISE NOTICE '- Daily Reports: %', report_count;
    RAISE NOTICE '- Production Targets: %', target_count;
    RAISE NOTICE '';
    RAISE NOTICE 'Migrations Applied: %', migration_count;
    RAISE NOTICE '';
    
    -- Verify critical tables exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'daily_mining_report') THEN
        RAISE EXCEPTION 'Critical table daily_mining_report not found!';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'production_calendar') THEN
        RAISE EXCEPTION 'Critical table production_calendar not found!';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        RAISE EXCEPTION 'Critical table user_profiles not found!';
    END IF;
    
    RAISE NOTICE 'All critical tables verified successfully!';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- PERFORMANCE OPTIMIZATION
-- =====================================================

DO $$
DECLARE
    table_record RECORD;
BEGIN
    RAISE NOTICE 'Optimizing database performance...';
    
    -- Analyze all tables for query optimization
    FOR table_record IN 
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ANALYZE ' || table_record.tablename;
    END LOOP;
    
    RAISE NOTICE 'Database statistics updated for all tables';
END $$;

-- =====================================================
-- SAMPLE QUERIES FOR TESTING
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'SAMPLE QUERIES TO TEST THE DATABASE';
    RAISE NOTICE '================================================';
    RAISE NOTICE '';
    RAISE NOTICE '1. View recent daily reports:';
    RAISE NOTICE '   SELECT location, report_date, total_actual_material, total_achievement_percent, status';
    RAISE NOTICE '   FROM daily_mining_report ORDER BY report_date DESC LIMIT 10;';
    RAISE NOTICE '';
    RAISE NOTICE '2. View weekly production summary:';
    RAISE NOTICE '   SELECT * FROM v_weekly_production_summary WHERE year_number = 2024;';
    RAISE NOTICE '';
    RAISE NOTICE '3. View monthly production summary:';
    RAISE NOTICE '   SELECT * FROM v_monthly_production_summary WHERE year_number = 2024;';
    RAISE NOTICE '';
    RAISE NOTICE '4. View production dashboard:';
    RAISE NOTICE '   SELECT * FROM v_production_dashboard WHERE report_date >= CURRENT_DATE - 7;';
    RAISE NOTICE '';
    RAISE NOTICE '5. Check user permissions:';
    RAISE NOTICE '   SELECT up.full_name, up.role, up.primary_location, up.is_active';
    RAISE NOTICE '   FROM user_profiles up ORDER BY up.role, up.full_name;';
    RAISE NOTICE '';
    RAISE NOTICE '6. View production targets:';
    RAISE NOTICE '   SELECT location, period_type, target_ob, target_ore, target_sr, target_fr';
    RAISE NOTICE '   FROM production_targets_calendar WHERE is_active = true;';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- FINAL DEPLOYMENT STATUS
-- =====================================================

DO $$
DECLARE
    deployment_summary TEXT;
BEGIN
    deployment_summary := format(
        'Mining Operations Database deployed successfully at %s. ' ||
        'Database contains %s tables, %s views, and %s sample records. ' ||
        'Ready for production use.',
        NOW()::TEXT,
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public')::TEXT,
        (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = 'public')::TEXT,
        (SELECT COUNT(*) FROM daily_mining_report)::TEXT
    );
    
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'DEPLOYMENT COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '================================================';
    RAISE NOTICE '%', deployment_summary;
    RAISE NOTICE '';
    RAISE NOTICE 'Next Steps:';
    RAISE NOTICE '1. Connect your application to the database';
    RAISE NOTICE '2. Configure user authentication (Supabase Auth recommended)';
    RAISE NOTICE '3. Test the sample queries above';
    RAISE NOTICE '4. Customize the data as needed for your operations';
    RAISE NOTICE '5. Set up regular backups and monitoring';
    RAISE NOTICE '';
    RAISE NOTICE 'Database is ready for production use!';
    RAISE NOTICE '================================================';
END $$;

-- =====================================================
-- DEPLOYMENT LOG
-- =====================================================

-- Log successful deployment
INSERT INTO audit_logs (
    user_id, action, resource_type, event_description, success
) VALUES (
    (SELECT id FROM user_profiles WHERE email = '<EMAIL>' LIMIT 1),
    'DEPLOY',
    'database',
    'Complete database deployment executed successfully',
    true
);

-- Final migration record
INSERT INTO schema_migrations (version, description) 
VALUES ('999', 'Complete database deployment executed')
ON CONFLICT (version) DO NOTHING;
