# Database Integration Documentation

## Cortex 7 Metadata
- **Document Type**: Integration Guide
- **Component**: Database Layer
- **Technology**: Supabase, PostgreSQL, TypeScript
- **Tags**: `#database` `#production-metrics` `#jwt-handling` `#supabase` `#integration`
- **Last Updated**: 2025-01-19
- **Status**: Implemented ✅

## Overview
Comprehensive database integration for the MiningOperationsApp, including production metrics, JWT error handling, and real-time data processing.

## Database Architecture

### Core Tables
1. **daily_production_metrics** - Primary production data
2. **users** - User authentication and profiles
3. **locations** - Mining site locations
4. **equipment** - Equipment tracking
5. **safety_incidents** - Safety reporting
6. **activity_documentation** - Activity documentation carousel (NEW)

### Integration Components
- [Production Metrics Integration](./production-metrics.md)
- [JWT Error Handling](./jwt-handling.md)
- [Data Processing](./data-processing.md)
- [Testing and Verification](./database-testing.md)
- [Activity Documentation](../features/activity-documentation.md) - NEW

## Activity Documentation Table (NEW - 2025-01-19)

### Schema
```sql
CREATE TABLE activity_documentation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    activity_date TIMESTAMP WITH TIME ZONE NOT NULL,
    location_id UUID REFERENCES locations(id),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0
);
```

### Purpose
- Store activity documentation items for dashboard carousel
- Support image thumbnails with metadata
- Enable chronological and custom ordering
- Integrate with existing location and user systems

### Key Features
- **Image Support**: URLs for thumbnail images
- **Flexible Ordering**: Both chronological and custom display order
- **Soft Delete**: `is_active` flag for safe deletion
- **Audit Trail**: Created/updated timestamps and user tracking
- **Location Integration**: Links to existing locations table

### Sample Data
1. Equipment Maintenance Check
2. Safety Training Session
3. Production Milestone
4. Environmental Inspection
5. New Equipment Arrival

### Database Service Methods
- `getActivityDocumentation()` - Fetch active items with joins
- `createActivityDocumentation()` - Insert new activity
- `updateActivityDocumentation()` - Update existing activity
- `deleteActivityDocumentation()` - Soft delete activity

## Profile Photo Storage (NEW - 2025-01-19)

### Users Table Enhancement
```sql
-- Enhanced users table with avatar support
users table:
├── id (UUID)
├── avatar_url (VARCHAR) -- Profile photo URL
├── full_name (VARCHAR)
├── email (VARCHAR)
└── ... other existing fields
```

### Supabase Storage Integration
```sql
-- Storage bucket: 'profile-photos'
Bucket Configuration:
├── Public Access: true
├── File Size Limit: 5MB
├── Allowed MIME Types: ['image/jpeg', 'image/png', 'image/webp']
├── RLS Policies: User-specific access control
└── CDN: Built-in Supabase CDN delivery
```

### Current Implementation
```typescript
// Demo user with actual photo
{
  "id": "f1860292-976f-4e06-bd7d-2df00b7a5e83",
  "email": "<EMAIL>",
  "full_name": "Demo Supervisor",
  "avatar_url": "https://ohqbaimnhwvdfrmxvhxv.supabase.co/storage/v1/object/public/profile-photos/anakku.jpeg"
}
```

### Profile Photo Service Methods
- `updateProfilePhoto()` - Update user avatar_url in database
- `uploadProfilePhoto()` - Upload photo to Supabase Storage
- `deleteProfilePhoto()` - Remove photo and update database

## Key Features Implemented

### 1. Production Metrics Integration ✅
- **Real-time Data**: Direct integration with daily_production_metrics table
- **420 Records**: Complete dataset from December 2023 - July 2025
- **Comprehensive Metrics**: Overburden, Ore, Rain Impact, Fuel Consumption
- **Plan vs Actual**: Achievement percentage calculations

### 2. JWT Error Handling ✅
- **Automatic Refresh**: Session refresh on JWT expiration
- **Error Recovery**: Graceful fallback to legacy system
- **Retry Logic**: Automatic retry for failed requests
- **User Experience**: Seamless authentication handling

### 3. Data Processing ✅
- **Aggregation**: Weekly, Monthly, Yearly data grouping
- **Chart Data**: Optimized for chart rendering
- **Performance**: Efficient data processing and caching
- **Filtering**: Date range and location-based filtering

## Database Schema

### daily_production_metrics Table
```sql
CREATE TABLE daily_production_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL,
  monthly VARCHAR(20) NOT NULL,
  week INTEGER NOT NULL,
  location_id UUID REFERENCES locations(id),
  actual_ob DECIMAL(10,2) DEFAULT 0,
  plan_ob DECIMAL(10,2) DEFAULT 0,
  actual_ore DECIMAL(10,2) DEFAULT 0,
  plan_ore DECIMAL(10,2) DEFAULT 0,
  rain_impact_hours DECIMAL(5,2) DEFAULT 0,
  slippery_conditions_hours DECIMAL(5,2) DEFAULT 0,
  fuel_consumption DECIMAL(10,2) DEFAULT 0,
  fuel_plan DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);
```

### Data Format Examples
```typescript
{
  id: "48dbd199-af92-4f1e-8b5a-7c9d8e6f5a4b",
  date: "2025-07-12",
  monthly: "July 2025",
  week: 28,
  actual_ob: 3540.00,
  plan_ob: 6261.00,
  actual_ore: 0.00,
  plan_ore: 0.00,
  rain_impact_hours: 0.00,
  slippery_conditions_hours: 0.00,
  fuel_consumption: 0.00,
  fuel_plan: 0.00
}
```

## Service Layer Implementation

### DatabaseService Class
```typescript
export class DatabaseService {
  // Production Metrics
  static async getDailyProductionMetrics(startDate?, endDate?, locationId?)
  static async getProductionMetricsAggregated(period, startDate?, endDate?)
  static async updateDailyProductionMetrics(id, updates)
  static async createDailyProductionMetrics(data)
  
  // JWT Error Handling
  static async getCurrentUser()
  static async refreshSession()
  static async getSession()
  
  // Equipment Management
  static async getEquipment()
  static async updateEquipmentStatus(id, status)
  
  // Safety Incidents
  static async getSafetyIncidents()
  static async createSafetyIncident(incident)
}
```

### JWT Error Handling Implementation
```typescript
const withJWTRetry = async <T>(operation: () => Promise<T>, maxRetries = 1): Promise<T> => {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt < maxRetries) {
        const wasJWTError = await handleJWTError(error);
        if (wasJWTError) {
          continue; // Retry the operation
        }
      }
      
      throw error;
    }
  }
  
  throw lastError;
};
```

## Data Processing Pipeline

### Chart Data Generation
1. **Data Retrieval**: Fetch from daily_production_metrics
2. **Aggregation**: Group by period (Daily/Weekly/Monthly)
3. **Calculation**: Compute achievement percentages
4. **Formatting**: Prepare for chart rendering
5. **Optimization**: Limit to 8 data points for charts

### Performance Optimizations
- **Indexed Queries**: Optimized database indexes
- **Caching**: In-memory caching for frequently accessed data
- **Pagination**: Efficient data loading for large datasets
- **Connection Pooling**: Optimized database connections

## Integration Points

### ProductionOverviewScreen.tsx
```typescript
// Real-time data integration
const loadProductionData = async () => {
  try {
    const data = await DatabaseService.getDailyProductionMetrics(
      startDate,
      endDate,
      selectedLocation
    );
    
    const aggregated = await DatabaseService.getProductionMetricsAggregated(
      selectedPeriod,
      startDate,
      endDate
    );
    
    setChartData(processChartData(aggregated));
  } catch (error) {
    console.error('Failed to load production data:', error);
  }
};
```

### Chart Data Processing
```typescript
const processChartData = (data) => {
  return {
    trends: generateTrendsChart(data),
    impact: generateImpactChart(data),
    fuel: generateFuelChart(data)
  };
};
```

## Error Handling Strategy

### JWT Expiration Handling
1. **Detection**: Identify JWT expired errors (PGRST301)
2. **Refresh**: Attempt session refresh
3. **Retry**: Retry original operation
4. **Fallback**: Use legacy system if refresh fails
5. **User Notification**: Inform user if login required

### Database Connection Issues
1. **Connection Retry**: Automatic retry with exponential backoff
2. **Offline Mode**: Cache data for offline access
3. **Error Logging**: Comprehensive error tracking
4. **User Feedback**: Clear error messages

## Testing and Verification

### Test Coverage
- ✅ Database connection and authentication
- ✅ Data retrieval and aggregation
- ✅ JWT error handling and recovery
- ✅ Chart data processing
- ✅ Performance under load

### Test Utilities
- `testProductionIntegration.ts` - Integration testing
- `testDatabaseConnection.js` - Connection verification
- `testJWTHandling.js` - Authentication testing

## Performance Metrics

### Database Performance
- **Query Response Time**: <100ms for typical queries
- **Data Processing**: <50ms for chart data generation
- **Memory Usage**: Optimized for mobile devices
- **Connection Efficiency**: Pooled connections for better performance

### User Experience
- **Loading Time**: <2 seconds for full data load
- **Smooth Scrolling**: 60fps chart interactions
- **Responsive Design**: Works on all screen sizes
- **Offline Capability**: Cached data available offline

## Security Implementation

### Authentication
- JWT token management
- Automatic session refresh
- Secure token storage
- User session validation

### Data Protection
- Row Level Security (RLS) policies
- User-specific data access
- Encrypted data transmission
- Audit logging for data changes

## Future Enhancements

### Planned Features
1. **Real-time Updates**: WebSocket integration for live data
2. **Advanced Analytics**: Machine learning insights
3. **Data Export**: CSV/Excel export functionality
4. **Backup System**: Automated data backup and recovery

### Performance Improvements
1. **Query Optimization**: Advanced database indexing
2. **Caching Strategy**: Redis integration for better caching
3. **Data Compression**: Optimize data transfer
4. **Background Sync**: Offline-first data synchronization

---
*Database integration documentation following Cortex 7 standards for comprehensive data management reference.*
