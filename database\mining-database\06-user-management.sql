-- =====================================================
-- Mining Operations Database - User Management
-- =====================================================
-- File: 06-user-management.sql
-- Description: User management and authentication support
-- Dependencies: 01-core-setup.sql
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- USER PROFILES TABLE
-- =====================================================
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Basic Information
    email VARCHAR(255) NOT NULL UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    full_name VARCHAR(200) GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    
    -- Work Information
    employee_id VARCHAR(50) UNIQUE,
    role user_role NOT NULL DEFAULT 'Observer',
    department VARCHAR(100),
    job_title VARCHAR(150),
    hire_date DATE,
    
    -- Contact Information
    phone_number VARCHAR(20),
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    
    -- Location Assignment
    primary_location VARCHAR(200), -- Primary assigned location
    allowed_locations TEXT[], -- Array of locations user can access
    
    -- Profile Settings
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'Asia/Jakarta',
    date_format VARCHAR(20) DEFAULT 'YYYY-MM-DD',
    
    -- Account Status
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMPTZ,
    login_count INTEGER DEFAULT 0,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by VARCHAR(200) DEFAULT current_user,
    
    -- Constraints
    CHECK (length(first_name) > 0),
    CHECK (length(last_name) > 0),
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CHECK (login_count >= 0)
);

-- =====================================================
-- USER PERMISSIONS TABLE
-- =====================================================
CREATE TABLE user_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    -- Permission Details
    permission VARCHAR(100) NOT NULL, -- e.g., 'reports.create', 'data.approve'
    resource_type VARCHAR(50), -- e.g., 'daily_report', 'production_target'
    location VARCHAR(200), -- Limit permission to specific location
    
    -- Permission Lifecycle
    granted_by UUID REFERENCES user_profiles(id),
    granted_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ, -- NULL for permanent permissions
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Audit
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, permission, resource_type, location),
    CHECK (expires_at IS NULL OR expires_at > granted_at)
);

-- =====================================================
-- USER SESSIONS TABLE
-- =====================================================
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    
    -- Session Details
    session_token VARCHAR(255) NOT NULL UNIQUE,
    device_id VARCHAR(255),
    device_type VARCHAR(50), -- 'mobile', 'tablet', 'desktop'
    device_name VARCHAR(200),
    app_version VARCHAR(50),
    
    -- Location Information
    ip_address INET,
    location_country VARCHAR(100),
    location_city VARCHAR(100),
    
    -- Session Lifecycle
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    ended_at TIMESTAMPTZ,
    
    -- Session Status
    is_active BOOLEAN DEFAULT true,
    end_reason VARCHAR(100), -- 'logout', 'timeout', 'expired'
    
    -- Constraints
    CHECK (expires_at > created_at),
    CHECK (ended_at IS NULL OR ended_at >= created_at)
);

-- =====================================================
-- AUDIT LOGS TABLE
-- =====================================================
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Audit Event
    user_id UUID REFERENCES user_profiles(id),
    session_id UUID REFERENCES user_sessions(id),
    
    -- Event Details
    action VARCHAR(50) NOT NULL, -- 'CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT'
    resource_type VARCHAR(100), -- Table name or resource type
    resource_id UUID, -- ID of the affected resource
    
    -- Event Context
    location VARCHAR(200), -- Location context
    event_description TEXT NOT NULL,
    
    -- Data Changes (for UPDATE operations)
    old_values JSONB,
    new_values JSONB,
    
    -- Request Context
    ip_address INET,
    user_agent TEXT,
    
    -- Event Outcome
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    
    -- Timing
    event_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CHECK (length(event_description) > 0)
);

-- =====================================================
-- APP SETTINGS TABLE
-- =====================================================
CREATE TABLE app_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Setting Identification
    setting_key VARCHAR(200) NOT NULL UNIQUE,
    setting_category VARCHAR(100) NOT NULL, -- 'system', 'security', 'ui'
    
    -- Setting Value
    setting_value JSONB NOT NULL,
    default_value JSONB,
    
    -- Setting Metadata
    description TEXT,
    data_type VARCHAR(50) NOT NULL, -- 'string', 'number', 'boolean', 'object'
    is_system_setting BOOLEAN DEFAULT false,
    
    -- Access Control
    required_permission VARCHAR(100),
    
    -- Change Tracking
    last_modified_by UUID REFERENCES user_profiles(id),
    last_modified_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User Profiles Indexes
CREATE INDEX idx_user_profiles_email ON user_profiles(email);
CREATE INDEX idx_user_profiles_employee_id ON user_profiles(employee_id);
CREATE INDEX idx_user_profiles_role ON user_profiles(role);
CREATE INDEX idx_user_profiles_active ON user_profiles(is_active);
CREATE INDEX idx_user_profiles_location ON user_profiles(primary_location);

-- User Permissions Indexes
CREATE INDEX idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX idx_user_permissions_permission ON user_permissions(permission);
CREATE INDEX idx_user_permissions_active ON user_permissions(is_active, expires_at);
CREATE INDEX idx_user_permissions_location ON user_permissions(location);

-- User Sessions Indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active, expires_at);
CREATE INDEX idx_user_sessions_activity ON user_sessions(last_activity_at);

-- Audit Logs Indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(event_timestamp);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);

-- App Settings Indexes
CREATE INDEX idx_app_settings_key ON app_settings(setting_key);
CREATE INDEX idx_app_settings_category ON app_settings(setting_category);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update updated_at timestamp
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_permissions_updated_at 
    BEFORE UPDATE ON user_permissions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to check if user has permission
CREATE OR REPLACE FUNCTION user_has_permission(user_uuid UUID, permission_name TEXT, location_name TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
    user_role_val user_role;
    has_permission BOOLEAN := false;
BEGIN
    -- Get user role
    SELECT role INTO user_role_val FROM user_profiles WHERE id = user_uuid AND is_active = true;
    
    -- Super Admin has all permissions
    IF user_role_val = 'Super Admin' THEN
        RETURN TRUE;
    END IF;
    
    -- Check direct user permissions
    SELECT EXISTS (
        SELECT 1 FROM user_permissions 
        WHERE user_id = user_uuid 
        AND permission = permission_name 
        AND (location IS NULL OR location = location_name OR location_name IS NULL)
        AND is_active = true
        AND (expires_at IS NULL OR expires_at > NOW())
    ) INTO has_permission;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log audit events
CREATE OR REPLACE FUNCTION log_audit_event(
    user_uuid UUID,
    action_name TEXT,
    resource_type_name TEXT,
    resource_uuid UUID DEFAULT NULL,
    description TEXT DEFAULT NULL,
    old_data JSONB DEFAULT NULL,
    new_data JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id, 
        event_description, old_values, new_values
    ) VALUES (
        user_uuid, action_name, resource_type_name, resource_uuid,
        COALESCE(description, action_name || ' on ' || resource_type_name),
        old_data, new_data
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on sensitive tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- User Profiles: Users can see their own profile and admins can see all
CREATE POLICY user_profiles_select ON user_profiles FOR SELECT USING (
    id = auth.uid() OR 
    EXISTS (
        SELECT 1 FROM user_profiles up 
        WHERE up.id = auth.uid() 
        AND up.role IN ('Super Admin', 'Site Manager')
        AND up.is_active = true
    )
);

-- User Sessions: Users can only see their own sessions
CREATE POLICY user_sessions_select ON user_sessions FOR SELECT USING (
    user_id = auth.uid() OR 
    EXISTS (
        SELECT 1 FROM user_profiles up 
        WHERE up.id = auth.uid() 
        AND up.role = 'Super Admin'
        AND up.is_active = true
    )
);

-- Audit Logs: Restricted access
CREATE POLICY audit_logs_select ON audit_logs FOR SELECT USING (
    user_id = auth.uid() OR 
    EXISTS (
        SELECT 1 FROM user_profiles up 
        WHERE up.id = auth.uid() 
        AND up.role IN ('Super Admin', 'Site Manager')
        AND up.is_active = true
    )
);

-- =====================================================
-- SAMPLE USERS
-- =====================================================

-- Insert sample users
INSERT INTO user_profiles (
    id, email, first_name, last_name, role, department, employee_id, 
    primary_location, allowed_locations, is_active, created_by
) VALUES
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'System', 'Administrator', 'Super Admin', 'IT', 'EMP001', 'All Sites', ARRAY['Pit A', 'Pit B', 'Pit C'], true, 'System'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Sarah', 'Johnson', 'Site Manager', 'Operations', 'EMP002', 'Pit A', ARRAY['Pit A', 'Pit B'], true, 'System'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'John', 'Supervisor', 'Shift Supervisor', 'Operations', 'EMP003', 'Pit A', ARRAY['Pit A'], true, 'System'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', 'Mike', 'Wilson', 'Shift Supervisor', 'Operations', 'EMP004', 'Pit B', ARRAY['Pit B'], true, 'System'),
('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'Lisa', 'Davis', 'Shift Supervisor', 'Operations', 'EMP005', 'Pit C', ARRAY['Pit C'], true, 'System'),
('550e8400-e29b-41d4-a716-446655440006', '<EMAIL>', 'David', 'Brown', 'Equipment Operator', 'Operations', 'EMP006', 'Pit A', ARRAY['Pit A'], true, 'System'),
('550e8400-e29b-41d4-a716-446655440007', '<EMAIL>', 'Tom', 'Miller', 'Safety Officer', 'Safety', 'EMP007', 'All Sites', ARRAY['Pit A', 'Pit B', 'Pit C'], true, 'System');

-- Insert sample permissions
INSERT INTO user_permissions (user_id, permission, resource_type, location, granted_by) VALUES
-- Site Manager permissions
('550e8400-e29b-41d4-a716-************', 'reports.create', 'daily_mining_report', 'Pit A', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'reports.approve', 'daily_mining_report', 'Pit A', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'targets.manage', 'production_targets_calendar', 'Pit A', '550e8400-e29b-41d4-a716-************'),

-- Shift Supervisor permissions
('550e8400-e29b-41d4-a716-************', 'reports.create', 'daily_mining_report', 'Pit A', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', 'reports.create', 'daily_mining_report', 'Pit B', '550e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440005', 'reports.create', 'daily_mining_report', 'Pit C', '550e8400-e29b-41d4-a716-************');

-- Insert sample app settings
INSERT INTO app_settings (setting_key, setting_category, setting_value, description, data_type, created_by) VALUES
('app.name', 'system', '"Mining Operations App"', 'Application name', 'string', '550e8400-e29b-41d4-a716-************'),
('app.version', 'system', '"1.0.0"', 'Application version', 'string', '550e8400-e29b-41d4-a716-************'),
('reports.auto_approve_threshold', 'system', '95', 'Auto-approve reports with achievement above this percentage', 'number', '550e8400-e29b-41d4-a716-************'),
('fuel.default_price_per_liter', 'system', '1.25', 'Default fuel price per liter', 'number', '550e8400-e29b-41d4-a716-************'),
('weather.high_impact_rain_hours', 'system', '4', 'Rain hours threshold for high weather impact', 'number', '550e8400-e29b-41d4-a716-************');

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE user_profiles IS 'User profiles for mining operations personnel';
COMMENT ON TABLE user_permissions IS 'Granular permissions for users';
COMMENT ON TABLE user_sessions IS 'User session tracking for security';
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all system activities';
COMMENT ON TABLE app_settings IS 'Application-wide settings and configuration';

COMMENT ON FUNCTION user_has_permission(UUID, TEXT, TEXT) IS 'Check if user has specific permission for location';
COMMENT ON FUNCTION log_audit_event(UUID, TEXT, TEXT, UUID, TEXT, JSONB, JSONB) IS 'Log audit events for compliance tracking';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('006', 'User management and authentication support')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    user_count INTEGER;
    permission_count INTEGER;
    setting_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM user_profiles;
    SELECT COUNT(*) INTO permission_count FROM user_permissions;
    SELECT COUNT(*) INTO setting_count FROM app_settings;
    
    RAISE NOTICE 'User management setup completed successfully';
    RAISE NOTICE 'Sample users created: %', user_count;
    RAISE NOTICE 'Sample permissions: %', permission_count;
    RAISE NOTICE 'App settings: %', setting_count;
    RAISE NOTICE 'RLS policies enabled for security';
END $$;
