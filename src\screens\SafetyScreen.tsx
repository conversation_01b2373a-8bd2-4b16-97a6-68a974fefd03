import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Modal,
  TextInput,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';
import { supabase } from '../config/supabase';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';

interface SafetyIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  incident_type: 'injury' | 'near_miss' | 'equipment_failure' | 'environmental' | 'security';
  status: 'reported' | 'investigating' | 'resolved' | 'closed';
  incident_date: string;
  location_id: string;
  location?: { name: string };
  reporter?: { full_name: string; employee_id: string };
  equipment?: { name: string; model: string };
  injured_person_name?: string;
  witnesses?: string[];
  corrective_actions?: string;
  investigation_notes?: string;
  resolved_date?: string;
}

const SafetyScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();
  const [modalVisible, setModalVisible] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<SafetyIncident | null>(null);
  const [incidents, setIncidents] = useState<SafetyIncident[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  const [newReport, setNewReport] = useState({
    severity: 'medium' as SafetyIncident['severity'],
    incident_type: 'near_miss' as SafetyIncident['incident_type'],
    title: '',
    description: '',
    location_id: '550e8400-e29b-41d4-a716-446655440001', // Default to North Mine Site
    injured_person_name: '',
    witnesses: '',
  });

  useEffect(() => {
    loadIncidents();
  }, []);

  const loadIncidents = async () => {
    try {
      setLoading(true);
      // const data = await DatabaseService.getSafetyIncidents();
      const data = []; // Placeholder until DatabaseService is implemented
      setIncidents(data || []);
    } catch (error) {
      Alert.alert('Error', 'Failed to load safety incidents');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadIncidents().finally(() => setRefreshing(false));
  }, []);

  const submitReport = async () => {
    if (!newReport.title.trim() || !newReport.description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      // await DatabaseService.createSafetyIncident({
      console.log('Creating safety incident:', {
        incident_date: new Date().toISOString(),
        location_id: newReport.location_id,
        severity: newReport.severity,
        incident_type: newReport.incident_type,
        title: newReport.title.trim(),
        description: newReport.description.trim(),
        injured_person_name: newReport.injured_person_name.trim() || undefined,
        witnesses: newReport.witnesses.trim() ? newReport.witnesses.split(',').map(w => w.trim()) : undefined,
      }); // Placeholder until DatabaseService is implemented

      Alert.alert('Success', 'Safety incident reported successfully');
      setReportModalVisible(false);
      setNewReport({
        severity: 'medium',
        incident_type: 'near_miss',
        title: '',
        description: '',
        location_id: '550e8400-e29b-41d4-a716-446655440001',
        injured_person_name: '',
        witnesses: '',
      });
      loadIncidents(); // Reload data
    } catch (error) {
      Alert.alert('Error', 'Failed to submit safety incident report');
    } finally {
      setSubmitting(false);
    }
  };

  const getSeverityColor = (severity: SafetyIncident['severity']) => {
    switch (severity) {
      case 'critical': return '#F44336';
      case 'high': return '#FF5722';
      case 'medium': return '#FF9800';
      case 'low': return '#4CAF50';
      default: return '#9E9E9E';
    }
  };

  const getStatusColor = (status: SafetyIncident['status']) => {
    switch (status) {
      case 'reported': return '#2196F3';
      case 'investigating': return '#FF9800';
      case 'resolved': return '#4CAF50';
      case 'closed': return '#9E9E9E';
      default: return '#9E9E9E';
    }
  };

  const getTypeIcon = (type: SafetyIncident['incident_type']): keyof typeof Ionicons.glyphMap => {
    switch (type) {
      case 'injury': return 'medical';
      case 'near_miss': return 'warning';
      case 'equipment_failure': return 'construct';
      case 'environmental': return 'leaf';
      case 'security': return 'shield';
      default: return 'alert-circle';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const IncidentCard: React.FC<{ incident: SafetyIncident }> = ({ incident }) => (
    <TouchableOpacity
      style={styles.incidentCard}
      onPress={() => {
        setSelectedIncident(incident);
        setModalVisible(true);
      }}
    >
      <View style={styles.incidentHeader}>
        <View style={styles.incidentInfo}>
          <View style={[styles.severityIndicator, { backgroundColor: getSeverityColor(incident.severity) }]} />
          <View style={styles.incidentDetails}>
            <Text style={styles.incidentTitle}>{incident.title}</Text>
            <Text style={styles.incidentMeta}>
              {incident.location?.name || 'Unknown Location'} • {formatDate(incident.incident_date)}
            </Text>
            <Text style={styles.incidentDescription} numberOfLines={2}>
              {incident.description}
            </Text>
          </View>
        </View>
        <View style={styles.incidentBadges}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(incident.status) }]}>
            <Text style={styles.statusText}>{incident.status.toUpperCase()}</Text>
          </View>
          <View style={styles.typeIcon}>
            <Ionicons name={getTypeIcon(incident.incident_type)} size={20} color={getSeverityColor(incident.severity)} />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" translucent={true} />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Safety Management</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => setReportModalVisible(true)}
        >
          <Ionicons name="add" size={24} color={Colors.textInverse} />
        </TouchableOpacity>
      </View>

      {/* Safety Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{incidents.filter(i => i.status !== 'closed').length}</Text>
          <Text style={styles.statLabel}>Active Incidents</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{incidents.filter(i => i.severity === 'critical' || i.severity === 'high').length}</Text>
          <Text style={styles.statLabel}>High Priority</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{incidents.filter(i => i.status === 'resolved').length}</Text>
          <Text style={styles.statLabel}>Resolved</Text>
        </View>
      </View>

      {/* Incidents List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading safety incidents...</Text>
        </View>
      ) : (
        <ScrollView 
          style={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {incidents.length > 0 ? (
            incidents.map((incident) => (
              <IncidentCard key={incident.id} incident={incident} />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="shield-checkmark-outline" size={64} color={Colors.textLight} />
              <Text style={styles.emptyText}>No safety incidents</Text>
              <Text style={styles.emptySubtext}>Safety incidents will appear here</Text>
            </View>
          )}
        </ScrollView>
      )}

      {/* Incident Detail Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedIncident && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>{selectedIncident.title}</Text>
                  <TouchableOpacity onPress={() => setModalVisible(false)}>
                    <Ionicons name="close" size={24} color={Colors.textPrimary} />
                  </TouchableOpacity>
                </View>
                
                <ScrollView style={styles.modalBody}>
                  <View style={styles.detailSection}>
                    <Text style={styles.sectionTitle}>Incident Details</Text>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Severity:</Text>
                      <View style={[styles.severityBadge, { backgroundColor: getSeverityColor(selectedIncident.severity) }]}>
                        <Text style={styles.severityText}>{selectedIncident.severity.toUpperCase()}</Text>
                      </View>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Type:</Text>
                      <Text style={styles.detailValue}>{selectedIncident.incident_type.replace('_', ' ').toUpperCase()}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Status:</Text>
                      <Text style={styles.detailValue}>{selectedIncident.status.toUpperCase()}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Date:</Text>
                      <Text style={styles.detailValue}>{formatDate(selectedIncident.incident_date)}</Text>
                    </View>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Location:</Text>
                      <Text style={styles.detailValue}>{selectedIncident.location?.name || 'Unknown'}</Text>
                    </View>
                    {selectedIncident.reporter && (
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Reported by:</Text>
                        <Text style={styles.detailValue}>{selectedIncident.reporter.full_name}</Text>
                      </View>
                    )}
                  </View>

                  <View style={styles.detailSection}>
                    <Text style={styles.sectionTitle}>Description</Text>
                    <Text style={styles.descriptionText}>{selectedIncident.description}</Text>
                  </View>

                  {selectedIncident.corrective_actions && (
                    <View style={styles.detailSection}>
                      <Text style={styles.sectionTitle}>Corrective Actions</Text>
                      <Text style={styles.descriptionText}>{selectedIncident.corrective_actions}</Text>
                    </View>
                  )}

                  {selectedIncident.investigation_notes && (
                    <View style={styles.detailSection}>
                      <Text style={styles.sectionTitle}>Investigation Notes</Text>
                      <Text style={styles.descriptionText}>{selectedIncident.investigation_notes}</Text>
                    </View>
                  )}
                </ScrollView>

                <View style={styles.modalActions}>
                  <TouchableOpacity 
                    style={[styles.actionButton, { backgroundColor: Colors.primary }]}
                    onPress={() => setModalVisible(false)}
                  >
                    <Text style={styles.actionButtonText}>Close</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>

      {/* Report Incident Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={reportModalVisible}
        onRequestClose={() => setReportModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Report Safety Incident</Text>
              <TouchableOpacity onPress={() => setReportModalVisible(false)}>
                <Ionicons name="close" size={24} color={Colors.textPrimary} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Title *</Text>
                <TextInput
                  style={styles.formInput}
                  value={newReport.title}
                  onChangeText={(text) => setNewReport({...newReport, title: text})}
                  placeholder="Brief description of the incident"
                  placeholderTextColor={Colors.textLight}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Description *</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={newReport.description}
                  onChangeText={(text) => setNewReport({...newReport, description: text})}
                  placeholder="Detailed description of what happened"
                  placeholderTextColor={Colors.textLight}
                  multiline
                  numberOfLines={4}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Severity</Text>
                <View style={styles.pickerContainer}>
                  {(['low', 'medium', 'high', 'critical'] as const).map((severity) => (
                    <TouchableOpacity
                      key={severity}
                      style={[
                        styles.pickerOption,
                        newReport.severity === severity && styles.pickerOptionSelected
                      ]}
                      onPress={() => setNewReport({...newReport, severity})}
                    >
                      <Text style={[
                        styles.pickerOptionText,
                        newReport.severity === severity && styles.pickerOptionTextSelected
                      ]}>
                        {severity.toUpperCase()}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Type</Text>
                <View style={styles.pickerContainer}>
                  {(['near_miss', 'injury', 'equipment_failure', 'environmental', 'security'] as const).map((type) => (
                    <TouchableOpacity
                      key={type}
                      style={[
                        styles.pickerOption,
                        newReport.incident_type === type && styles.pickerOptionSelected
                      ]}
                      onPress={() => setNewReport({...newReport, incident_type: type})}
                    >
                      <Text style={[
                        styles.pickerOptionText,
                        newReport.incident_type === type && styles.pickerOptionTextSelected
                      ]}>
                        {type.replace('_', ' ').toUpperCase()}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Injured Person (if applicable)</Text>
                <TextInput
                  style={styles.formInput}
                  value={newReport.injured_person_name}
                  onChangeText={(text) => setNewReport({...newReport, injured_person_name: text})}
                  placeholder="Name of injured person"
                  placeholderTextColor={Colors.textLight}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Witnesses (comma separated)</Text>
                <TextInput
                  style={styles.formInput}
                  value={newReport.witnesses}
                  onChangeText={(text) => setNewReport({...newReport, witnesses: text})}
                  placeholder="Names of witnesses, separated by commas"
                  placeholderTextColor={Colors.textLight}
                />
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: Colors.textLight }]}
                onPress={() => setReportModalVisible(false)}
              >
                <Text style={[styles.actionButtonText, { color: Colors.textPrimary }]}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: Colors.secondary }]}
                onPress={submitReport}
                disabled={submitting}
              >
                {submitting ? (
                  <ActivityIndicator size="small" color={Colors.textInverse} />
                ) : (
                  <Text style={styles.actionButtonText}>Submit Report</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + Layout.spacing.md : Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.primary,
  },
  headerTitle: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.accent,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.md,
    backgroundColor: Colors.surface,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: Layout.spacing.md,
  },
  statNumber: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  statLabel: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
    marginTop: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  incidentCard: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  incidentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  incidentInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  severityIndicator: {
    width: 4,
    borderRadius: 2,
    marginRight: Layout.spacing.md,
  },
  incidentDetails: {
    flex: 1,
  },
  incidentTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  incidentMeta: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
  incidentDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    lineHeight: 20,
  },
  incidentBadges: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: 4,
    borderRadius: Layout.borderRadius.sm,
    marginBottom: Layout.spacing.sm,
  },
  statusText: {
    fontSize: Layout.fontSize.xs,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  typeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.xl,
  },
  loadingText: {
    marginTop: Layout.spacing.md,
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Layout.spacing.xl,
  },
  emptyText: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textSecondary,
    marginTop: Layout.spacing.md,
  },
  emptySubtext: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    textAlign: 'center',
    marginTop: Layout.spacing.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.lg,
    margin: Layout.spacing.lg,
    maxHeight: '90%',
    width: '95%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: Layout.fontSize.xl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  modalBody: {
    flex: 1,
    marginBottom: Layout.spacing.lg,
  },
  detailSection: {
    marginBottom: Layout.spacing.lg,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Layout.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  detailLabel: {
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    fontWeight: 'bold',
  },
  severityBadge: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: 4,
    borderRadius: Layout.borderRadius.sm,
  },
  severityText: {
    fontSize: Layout.fontSize.xs,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
  descriptionText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    lineHeight: 22,
  },
  formGroup: {
    marginBottom: Layout.spacing.lg,
  },
  formLabel: {
    fontSize: Layout.fontSize.md,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.sm,
  },
  formInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    backgroundColor: Colors.background,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Layout.spacing.sm,
  },
  pickerOption: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.sm,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  pickerOptionSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  pickerOptionText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
  },
  pickerOptionTextSelected: {
    color: Colors.textInverse,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    paddingVertical: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    marginHorizontal: Layout.spacing.sm,
  },
  actionButtonText: {
    fontSize: Layout.fontSize.sm,
    fontWeight: 'bold',
    color: Colors.textInverse,
  },
});

export default SafetyScreen;
