# 📚 AUGMENT AGENT DOCUMENTATION RULES

> **🎯 Mandatory Rules for Augment Agent**  
> **📋 Scope**: All documentation work on Mining Operations App  
> **🔄 Status**: MUST FOLLOW - No exceptions

---

## 🚨 CRITICAL RULES - NEVER VIOLATE

### **❌ ABSOLUTELY FORBIDDEN:**
1. **Creating .md files in docs/ root** (except README.md, index.md, implementation-progress.md)
2. **Creating new folders** without updating structure documentation
3. **Duplicating content** across multiple files
4. **Ignoring existing file structure**
5. **Making UPPERCASE file names** outside resources/ folder

### **✅ MANDATORY ACTIONS:**
1. **Always read docs/AI_AGENT_DOCUMENTATION_RULES.md first**
2. **Check existing structure before creating new files**
3. **Update existing files instead of creating new ones**
4. **Follow naming convention: [number]-[name].md**
5. **Use mandatory header template with all required fields**
6. **Update docs/index.md when adding/changing documentation**
7. **Follow established folder structure**

---

## 📁 FOLDER STRUCTURE (IMMUTABLE)

```
docs/
├── README.md                    # Main entry - project overview
├── index.md                     # Navigation hub - ALWAYS UPDATE
├── implementation-progress.md   # Status tracking
├── architecture/               # System design only
├── design/                     # UI/UX design system only
├── features/                   # Application features only
├── development/                # Dev setup & guides only
│   └── setup.md               # MASTER GUIDE - merge all setup content here
├── api/                        # API documentation only
├── user-guides/               # End-user guides only
└── resources/                 # Reports, changelogs, references only
```

---

## 🔄 WORKFLOW FOR DOCUMENTATION

### **Before Creating Any Documentation:**
```bash
1. Read docs/AI_AGENT_DOCUMENTATION_RULES.md
2. Check if relevant file already exists
3. If exists → UPDATE existing file
4. If new → Determine correct folder based on content type
5. Never create files in docs/ root
```

### **Content Type → Folder Mapping:**
```bash
Setup/Configuration → development/setup.md (MASTER GUIDE)
Feature Documentation → features/
System Architecture → architecture/
UI/UX Design → design/
API Documentation → api/
User Guides → user-guides/
Reports/Summaries → resources/
Implementation Files → resources/
```

### **After Creating/Updating Documentation:**
```bash
1. Update docs/index.md with new/changed links
2. Update docs/implementation-progress.md if relevant
3. Update resources/changelog.md if significant change
4. Verify all links work correctly
```

---

## 📋 TEMPLATES

### **File Header Template (MANDATORY):**
```markdown
# [Nomor]. [Judul Lengkap]

> **📝 File**: `[nomor]-[nama-file].md`
> **📅 Created**: [DD Month YYYY]
> **🔄 Last Updated**: [DD Month YYYY]
> **👤 Author**: [AI Agent/Developer Name]
> **📋 Version**: [v1.0/v2.0/etc]
> **✅ Status**: [Draft/In Progress/Complete/Production Ready]
> **🎯 Purpose**: [Brief description of file purpose]

---

[Content]
```

### **Naming Convention:**
```bash
# Format: [number]-[name].md
✅ CORRECT: 01-overview.md, 02-database-design.md
❌ WRONG: overview.md, Database-Design.md, navigation_system.md

# Date Format: DD Month YYYY
✅ CORRECT: 15 January 2025, 03 March 2025
❌ WRONG: Jan 15 2025, 2025-01-15, 15/01/2025
```

### **Index.md Link Template:**
```markdown
- **📊 [Title]**: [folder/file.md] - [Brief description]
```

---

## 🎯 DECISION EXAMPLES

### **"User wants setup documentation"**
```
✅ CORRECT: Update development/setup.md (MASTER GUIDE)
❌ WRONG: Create SETUP_GUIDE.md in docs/
```

### **"User wants feature documentation"**
```
✅ CORRECT: Create/update in features/
❌ WRONG: Create FEATURE_GUIDE.md in docs/
```

### **"User wants report/summary"**
```
✅ CORRECT: Create in resources/
❌ WRONG: Create REPORT.md in docs/
```

---

## 🚨 EMERGENCY PROTOCOL

### **If Documentation Structure is Messy:**
1. **STOP** - Don't create more files
2. **REORGANIZE** existing files to correct structure
3. **UPDATE** all navigation links
4. **VERIFY** everything works
5. **DOCUMENT** changes in changelog

### **If Unsure About Placement:**
1. **ASK** user for clarification
2. **EXPLAIN** available options
3. **FOLLOW** established structure
4. **DON'T** create new structure without approval

---

## ✅ COMPLIANCE CHECKLIST

```bash
□ Read AI_AGENT_DOCUMENTATION_RULES.md before starting
□ Checked existing files before creating new ones
□ Used correct folder for content type
□ Used naming convention: [number]-[name].md
□ Added mandatory header with all required fields
□ Used correct date format: DD Month YYYY
□ Updated "Last Updated" field when editing
□ Updated index.md with changes
□ No duplicate content across files
□ All links work correctly
□ Validated with: npm run docs:validate
```

---

**🎯 REMEMBER: Consistency is key for long-term maintainability!**  
**📋 ALWAYS follow these rules - no exceptions!**  
**🔄 This ensures seamless documentation across all AI interactions!**
