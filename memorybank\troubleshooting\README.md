# Troubleshooting Guide

## Cortex 7 Metadata
- **Document Type**: Troubleshooting Guide
- **Component**: System-wide Issues
- **Technology**: React Native, TypeScript, Supabase
- **Tags**: `#troubleshooting` `#debugging` `#error-resolution` `#maintenance`
- **Last Updated**: 2025-01-19
- **Status**: Active ✅

## Overview
Comprehensive troubleshooting guide for common issues in the MiningOperationsApp, including chart problems, database connectivity, TypeScript errors, and performance issues.

## Common Issues and Solutions

### 1. Chart Display Issues

#### Problem: Chart Labels Overlapping
**Symptoms**: Text overlap on X-axis, unreadable labels
**Cause**: Insufficient spacing for label length
**Solution**:
```typescript
// Verify minimum label spacing
const MIN_LABEL_SPACING = 50; // Increase if needed
const calculateChartWidth = (dataPoints, maxLabelLength) => {
  const minWidthPerPoint = Math.max(MIN_LABEL_SPACING, maxLabelLength * 8 + 20);
  return dataPoints * minWidthPerPoint;
};
```

#### Problem: Charts Not Scrolling
**Symptoms**: Chart content cut off, no horizontal scroll
**Cause**: ScrollView not properly configured
**Solution**:
```typescript
// Check ScrollView configuration
<ScrollView
  horizontal
  showsHorizontalScrollIndicator={true}
  style={styles.chartScrollView}
  contentContainerStyle={styles.chartScrollContent}
>
  <LineChart {...chartProps} />
</ScrollView>
```

#### Problem: Chart Data Not Loading
**Symptoms**: Empty charts, loading indicators stuck
**Cause**: Database connection or data processing issues
**Diagnostic Steps**:
1. Check network connectivity
2. Verify JWT token validity
3. Test database query directly
4. Check data processing pipeline

### 2. Database Connection Issues

#### Problem: JWT Token Expired (PGRST301)
**Symptoms**: "JWT expired" errors, authentication failures
**Cause**: Session timeout, invalid token
**Solution**:
```typescript
// Automatic JWT refresh implementation
const handleJWTError = async (error) => {
  if (error.code === 'PGRST301' || error.message?.includes('JWT')) {
    try {
      const { data, error: refreshError } = await supabase.auth.refreshSession();
      if (refreshError) throw refreshError;
      return true; // JWT was refreshed
    } catch (refreshError) {
      console.error('Failed to refresh session:', refreshError);
      return false;
    }
  }
  return false;
};
```

#### Problem: Database Query Timeout
**Symptoms**: Long loading times, timeout errors
**Cause**: Large dataset, inefficient queries, network issues
**Solution**:
```typescript
// Implement query optimization
const optimizedQuery = supabase
  .from('daily_production_metrics')
  .select('date, actual_ob, plan_ob, week, monthly')
  .gte('date', startDate)
  .lte('date', endDate)
  .order('date', { ascending: false })
  .limit(100); // Limit results for performance
```

#### Problem: Row Level Security (RLS) Blocking Access
**Symptoms**: Empty results despite data existing
**Cause**: RLS policies preventing data access
**Diagnostic Steps**:
1. Check user authentication status
2. Verify RLS policies in Supabase dashboard
3. Test with service key (temporarily)
4. Review user permissions

### 3. TypeScript Compilation Errors

#### Problem: Variable Used Before Assignment (TS2454)
**Symptoms**: Compilation fails with TS2454 error
**Cause**: Variable declared but not initialized in all code paths
**Solution**:
```typescript
// Add default fallback case
let key: string;
if (condition1) {
  key = value1;
} else if (condition2) {
  key = value2;
} else {
  key = defaultValue; // Always provide fallback
}
```

#### Problem: Implicit Any Type (TS7006)
**Symptoms**: "Parameter implicitly has 'any' type" errors
**Cause**: Missing type annotations
**Solution**:
```typescript
// Add explicit type annotations
const processData = (data: ProductionMetric[]): ChartData => {
  return data.map((item: ProductionMetric) => ({
    // ... processing logic
  }));
};
```

#### Problem: Unknown Error Type in Catch Blocks
**Symptoms**: Cannot access error.message in catch blocks
**Cause**: TypeScript 4.4+ strict error handling
**Solution**:
```typescript
// Proper error type checking
try {
  // ... operation
} catch (error) {
  const errorMessage = error instanceof Error 
    ? error.message 
    : 'Unknown error occurred';
  console.error(errorMessage);
}
```

### 4. Performance Issues

#### Problem: Slow Chart Rendering
**Symptoms**: Laggy scrolling, delayed chart updates
**Cause**: Large datasets, inefficient rendering
**Solution**:
```typescript
// Implement data limitation and optimization
const limitedData = chartData.slice(0, 8); // Limit chart points
const memoizedChart = React.memo(ScrollableChart); // Memoize component
```

#### Problem: Memory Leaks
**Symptoms**: Increasing memory usage, app crashes
**Cause**: Uncleared timers, event listeners, large objects
**Solution**:
```typescript
// Proper cleanup in useEffect
useEffect(() => {
  const interval = setInterval(updateData, 5000);
  
  return () => {
    clearInterval(interval); // Cleanup timer
  };
}, []);
```

#### Problem: Slow Database Queries
**Symptoms**: Long loading times, timeout errors
**Cause**: Missing indexes, large result sets
**Solution**:
```sql
-- Add database indexes
CREATE INDEX idx_daily_production_date ON daily_production_metrics(date);
CREATE INDEX idx_daily_production_week ON daily_production_metrics(week);
CREATE INDEX idx_daily_production_monthly ON daily_production_metrics(monthly);
```

## Debugging Tools and Techniques

### 1. React Native Debugger
```javascript
// Enable debugging
if (__DEV__) {
  console.log('Chart data:', chartData);
  console.log('Database response:', data);
}
```

### 2. Network Debugging
```javascript
// Monitor network requests
const debugNetworkRequest = async (operation) => {
  console.log('Starting network request...');
  const startTime = Date.now();
  
  try {
    const result = await operation();
    const endTime = Date.now();
    console.log(`Request completed in ${endTime - startTime}ms`);
    return result;
  } catch (error) {
    console.error('Network request failed:', error);
    throw error;
  }
};
```

### 3. Performance Monitoring
```javascript
// Monitor component performance
const usePerformanceMonitor = (componentName) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      console.log(`${componentName} render time: ${endTime - startTime}ms`);
    };
  });
};
```

## Error Recovery Strategies

### 1. Graceful Degradation
```typescript
// Fallback to cached data
const loadDataWithFallback = async () => {
  try {
    const freshData = await DatabaseService.getDailyProductionMetrics();
    setCachedData(freshData);
    return freshData;
  } catch (error) {
    console.warn('Using cached data due to error:', error);
    return cachedData || [];
  }
};
```

### 2. Retry Logic
```typescript
// Automatic retry with exponential backoff
const retryOperation = async (operation, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};
```

### 3. User Feedback
```typescript
// Provide clear error messages to users
const handleError = (error, context) => {
  let userMessage = 'An unexpected error occurred';
  
  if (error.code === 'PGRST301') {
    userMessage = 'Session expired. Please refresh the app.';
  } else if (error.message?.includes('network')) {
    userMessage = 'Network connection issue. Please check your internet.';
  }
  
  Alert.alert('Error', userMessage);
};
```

## Maintenance Procedures

### 1. Regular Health Checks
```javascript
// Automated health check script
const performHealthCheck = async () => {
  const checks = [
    { name: 'Database Connection', test: testDatabaseConnection },
    { name: 'Chart Rendering', test: testChartRendering },
    { name: 'Authentication', test: testAuthentication },
    { name: 'Data Processing', test: testDataProcessing }
  ];
  
  const results = [];
  for (const check of checks) {
    try {
      const result = await check.test();
      results.push({ name: check.name, status: 'PASS', ...result });
    } catch (error) {
      results.push({ name: check.name, status: 'FAIL', error: error.message });
    }
  }
  
  return results;
};
```

### 2. Performance Monitoring
```javascript
// Monitor key performance metrics
const monitorPerformance = () => {
  const metrics = {
    chartRenderTime: measureChartRenderTime(),
    databaseQueryTime: measureDatabaseQueryTime(),
    memoryUsage: getMemoryUsage(),
    networkLatency: measureNetworkLatency()
  };
  
  // Log metrics for analysis
  console.log('Performance Metrics:', metrics);
  
  // Alert if metrics exceed thresholds
  if (metrics.chartRenderTime > 1000) {
    console.warn('Chart rendering is slow:', metrics.chartRenderTime);
  }
};
```

### 3. Data Integrity Checks
```javascript
// Verify data consistency
const verifyDataIntegrity = async () => {
  const checks = [
    { name: 'Date Format', test: verifyDateFormats },
    { name: 'Numeric Values', test: verifyNumericValues },
    { name: 'Required Fields', test: verifyRequiredFields },
    { name: 'Data Relationships', test: verifyDataRelationships }
  ];
  
  const issues = [];
  for (const check of checks) {
    const result = await check.test();
    if (!result.valid) {
      issues.push({ check: check.name, issues: result.issues });
    }
  }
  
  return issues;
};
```

## Emergency Procedures

### 1. Database Connection Failure
1. Check Supabase service status
2. Verify network connectivity
3. Test with service key
4. Switch to offline mode if available
5. Contact Supabase support if needed

### 2. App Crash Recovery
1. Check crash logs and error reports
2. Identify root cause (memory, network, code)
3. Implement immediate fix or workaround
4. Deploy hotfix if critical
5. Monitor for recurrence

### 3. Performance Degradation
1. Monitor performance metrics
2. Identify bottlenecks (database, rendering, network)
3. Implement performance optimizations
4. Test improvements
5. Deploy optimized version

---
*Troubleshooting guide following Cortex 7 standards for comprehensive issue resolution reference.*
