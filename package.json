{"name": "mining-operations-platform", "version": "1.0.0", "private": true, "workspaces": ["packages/*", "apps/*"], "main": "index.ts", "scripts": {"bootstrap": "lerna bootstrap", "clean": "lerna clean", "build": "lerna run build", "build:core": "lerna run build --scope=@mining-ops/core", "test": "lerna run test", "test:core": "lerna run test --scope=@mining-ops/core", "lint": "lerna run lint", "lint:fix": "lerna run lint:fix", "docs:validate": "node scripts/validate-docs-structure.js", "docs:check": "node scripts/check-documentation.js", "dev:mobile": "expo start", "dev:desktop": "cd packages/desktop && npm run dev", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "start": "npm run dev:mobile", "test:coverage": "jest --coverage", "test:watch": "jest --watch"}, "dependencies": {"@expo/metro-runtime": "~6.1.2", "@expo/vector-icons": "^15.0.2", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-community/datetimepicker": "8.4.4", "@react-native-community/netinfo": "11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.52.0", "buffer": "^6.0.3", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "expo": "^54.0.0", "expo-battery": "~10.0.7", "expo-blur": "~15.0.7", "expo-device": "~8.0.8", "expo-file-system": "~19.0.14", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-image-picker": "~17.0.8", "expo-linear-gradient": "~15.0.7", "expo-local-authentication": "~17.0.7", "expo-location": "~19.0.7", "expo-navigation-bar": "~5.0.8", "expo-screen-orientation": "~9.0.7", "expo-sharing": "~14.0.7", "expo-sqlite": "~16.0.8", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.28.0", "react-native-get-random-values": "^1.9.0", "react-native-gifted-charts": "^1.4.63", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-native-uuid": "^2.0.1", "react-native-web": "^0.21.0", "react-native-worklets": "0.5.1", "text-encoding": "^0.7.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.2", "@types/jest": "^29.5.8", "@types/react": "~19.1.10", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-expo": "~54.0.12", "lerna": "^8.1.0", "react-test-renderer": "19.0.0", "typescript": "~5.9.2"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["<rootDir>/src/tests/setup.ts"], "testMatch": ["**/__tests__/**/*.(ts|tsx|js)", "**/*.(test|spec).(ts|tsx|js)"], "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/tests/**/*"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}, "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "identity-obj-proxy"}}}