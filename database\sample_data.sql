-- Mining Operations App - Sample Data for Testing

-- =============================================
-- SAMPLE DATA INSERTION
-- =============================================

-- Insert sample locations
INSERT INTO locations (id, name, description, location_type, address, coordinates) VALUES
('550e8400-e29b-41d4-a716-************', 'North Mine Site', 'Primary excavation site in the northern sector', 'mine_site', '1234 Mining Road, North Sector', POINT(-74.0059, 40.7128)),
('550e8400-e29b-41d4-a716-************', 'Processing Plant Alpha', 'Main ore processing facility', 'processing_plant', '5678 Industrial Ave, Central', POINT(-74.0159, 40.7228)),
('550e8400-e29b-41d4-a716-446655440003', 'South Mine Site', 'Secondary excavation site in the southern sector', 'mine_site', '9012 Mining Road, South Sector', POINT(-74.0259, 40.7328)),
('550e8400-e29b-41d4-a716-446655440004', 'Central Office', 'Administrative headquarters', 'office', '3456 Corporate Blvd, Downtown', POINT(-74.0359, 40.7428)),
('550e8400-e29b-41d4-a716-************', 'Equipment Warehouse', 'Parts and equipment storage facility', 'warehouse', '7890 Storage St, Industrial Zone', POINT(-74.0459, 40.7528));

-- Note: In a real implementation, you would insert users through Supabase Auth
-- This is just for reference of the data structure
-- Sample users data structure (would be created through auth signup):
/*
INSERT INTO users (id, email, full_name, phone, role, location_id, employee_id, hire_date) VALUES
('auth-user-id-1', '<EMAIL>', 'John Smith', '******-0101', 'supervisor', '550e8400-e29b-41d4-a716-************', 'EMP001', '2023-01-15'),
('auth-user-id-2', '<EMAIL>', 'Sarah Johnson', '******-0102', 'safety_officer', '550e8400-e29b-41d4-a716-************', 'EMP002', '2023-02-01'),
('auth-user-id-3', '<EMAIL>', 'Mike Wilson', '******-0103', 'operator', '550e8400-e29b-41d4-a716-************', 'EMP003', '2023-03-10'),
('auth-user-id-4', '<EMAIL>', 'Lisa Brown', '******-0104', 'maintenance_tech', '550e8400-e29b-41d4-a716-************', 'EMP004', '2023-01-20'),
('auth-user-id-5', '<EMAIL>', 'Admin User', '******-0100', 'admin', '550e8400-e29b-41d4-a716-446655440004', 'EMP000', '2022-12-01');
*/

-- Insert sample equipment
INSERT INTO equipment (id, name, model, serial_number, manufacturer, equipment_type, location_id, status, purchase_date, specifications, operating_hours, fuel_capacity, max_load_capacity) VALUES
('660e8400-e29b-41d4-a716-************', 'Excavator Alpha-1', 'CAT 390F', 'CAT390F-001', 'Caterpillar', 'excavator', '550e8400-e29b-41d4-a716-************', 'operational', '2022-06-15', '{"engine_power": "402 HP", "bucket_capacity": "2.3 m³", "operating_weight": "90 tons"}', 2450, 680.0, 45.0),
('660e8400-e29b-41d4-a716-************', 'Dump Truck Beta-1', 'Volvo A60H', 'VOLVO-A60H-001', 'Volvo', 'dump_truck', '550e8400-e29b-41d4-a716-************', 'operational', '2022-08-20', '{"engine_power": "469 HP", "payload": "55 tons", "fuel_tank": "400L"}', 1890, 400.0, 55.0),
('660e8400-e29b-41d4-a716-446655440003', 'Drill Gamma-1', 'Atlas Copco ROC D65', 'ATLAS-ROC-D65-001', 'Atlas Copco', 'drill', '550e8400-e29b-41d4-a716-************', 'maintenance', '2021-11-10', '{"drilling_diameter": "89-127mm", "drilling_depth": "32m", "compressor": "25 bar"}', 3200, 300.0, 0.0),
('660e8400-e29b-41d4-a716-446655440004', 'Conveyor Delta-1', 'Metso CV100', 'METSO-CV100-001', 'Metso', 'conveyor', '550e8400-e29b-41d4-a716-************', 'operational', '2023-01-05', '{"belt_width": "1200mm", "capacity": "1000 t/h", "length": "500m"}', 1200, 0.0, 1000.0),
('660e8400-e29b-41d4-a716-************', 'Loader Epsilon-1', 'Komatsu WA600-8', 'KOMATSU-WA600-001', 'Komatsu', 'loader', '550e8400-e29b-41d4-a716-446655440003', 'operational', '2022-12-01', '{"engine_power": "469 HP", "bucket_capacity": "5.5 m³", "operating_weight": "38 tons"}', 1650, 520.0, 25.0);

-- Insert sample shifts
INSERT INTO shifts (id, location_id, shift_type, shift_date, start_time, end_time, supervisor_id, planned_crew_size, actual_crew_size) VALUES
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-15', '06:00:00', '18:00:00', 'auth-user-id-1', 12, 11),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'night', '2024-01-15', '18:00:00', '06:00:00', 'auth-user-id-1', 8, 8),
('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-************', 'day', '2024-01-15', '07:00:00', '19:00:00', 'auth-user-id-1', 6, 6);

-- Insert sample safety incidents
INSERT INTO safety_incidents (id, reported_by, incident_date, location_id, severity, incident_type, title, description, equipment_id, status) VALUES
('880e8400-e29b-41d4-a716-************', 'auth-user-id-2', '2024-01-14 10:30:00', '550e8400-e29b-41d4-a716-************', 'medium', 'near_miss', 'Near miss with excavator', 'Operator almost struck by falling rock during excavation. No injuries occurred but safety protocols need review.', '660e8400-e29b-41d4-a716-************', 'investigating'),
('880e8400-e29b-41d4-a716-************', 'auth-user-id-3', '2024-01-13 14:15:00', '550e8400-e29b-41d4-a716-************', 'low', 'equipment_failure', 'Hydraulic leak in dump truck', 'Minor hydraulic fluid leak detected in dump truck Beta-1. Equipment taken out of service for repair.', '660e8400-e29b-41d4-a716-************', 'resolved');

-- Insert sample production reports
INSERT INTO production_reports (id, created_by, report_date, shift, location_id, production_metrics, equipment_used, total_tonnage, operating_hours, downtime_hours, fuel_consumed, crew_size, weather_conditions) VALUES
('990e8400-e29b-41d4-a716-************', 'auth-user-id-1', '2024-01-15', 'day', '550e8400-e29b-41d4-a716-************', 
'{"ore_extracted": 1250, "waste_removed": 850, "blast_holes_drilled": 45, "equipment_efficiency": 87.5}', 
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}', 
2100.0, 11.5, 0.5, 1250.0, 11, 'Clear, 22°C'),
('990e8400-e29b-41d4-a716-************', 'auth-user-id-1', '2024-01-14', 'night', '550e8400-e29b-41d4-a716-************', 
'{"ore_extracted": 980, "waste_removed": 620, "blast_holes_drilled": 32, "equipment_efficiency": 82.3}', 
'{"660e8400-e29b-41d4-a716-************", "660e8400-e29b-41d4-a716-************"}', 
1600.0, 7.8, 0.2, 890.0, 8, 'Partly cloudy, 18°C');

-- Insert sample maintenance records
INSERT INTO maintenance_records (id, equipment_id, performed_by, maintenance_type, scheduled_date, completed_date, description, parts_used, labor_hours, total_cost, status) VALUES
('aa0e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440003', 'auth-user-id-4', 'corrective', '2024-01-12', '2024-01-13', 'Replace worn drill bits and hydraulic seals', 
'[{"part": "Drill bit set", "quantity": 3, "cost": 450.00}, {"part": "Hydraulic seal kit", "quantity": 1, "cost": 125.00}]', 
6.5, 575.00, 'completed'),
('aa0e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'auth-user-id-4', 'preventive', '2024-01-20', NULL, 'Scheduled 500-hour maintenance service', 
'[{"part": "Engine oil", "quantity": 25, "cost": 200.00}, {"part": "Hydraulic fluid", "quantity": 50, "cost": 300.00}]', 
0.0, 500.00, 'scheduled');
