# Phase 1 Implementation Guide - Foundation & Critical Operations

## Cortex 7 Metadata
- **Document Type**: Technical Implementation Guide
- **Component**: Phase 1 Development Plan
- **Technology**: React Native, TypeScript, Supabase, Expo Notifications
- **Tags**: `#phase1` `#implementation` `#notifications` `#safety-checklists` `#offline-architecture`
- **Last Updated**: 2025-01-19
- **Status**: Implementation Ready ✅

## Overview

Detailed technical implementation guide for Phase 1 development (2-3 months), focusing on foundation and critical operations features that build upon existing Mining Operations App architecture.

## Priority 1: Real-time Notifications System

### Database Schema Implementation

```sql
-- Notifications table
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  type VARCHAR(50) NOT NULL, -- 'emergency', 'equipment', 'shift', 'safety'
  title VARCHAR(255) NOT NULL,
  message TEXT,
  priority INTEGER DEFAULT 3, -- 1=critical, 2=high, 3=medium, 4=low
  read_at TIMESTAMP,
  location_id UUID REFERENCES locations(id),
  metadata JSONB, -- Additional context data
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP -- For temporary notifications
);

-- Indexes for performance
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_unread ON notifications(user_id, read_at) WHERE read_at IS NULL;

-- RLS Policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can insert notifications" ON notifications
  FOR INSERT WITH CHECK (true); -- Allow system to create notifications

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid());
```

### Component Architecture

```typescript
// NotificationManager.tsx
interface NotificationManagerProps {
  userId: string;
  onNotificationPress?: (notification: Notification) => void;
  maxDisplayCount?: number;
}

export const NotificationManager: React.FC<NotificationManagerProps> = ({
  userId,
  onNotificationPress,
  maxDisplayCount = 5
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Real-time subscription
  useEffect(() => {
    const subscription = supabase
      .channel('notifications')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          const newNotification = payload.new as Notification;
          setNotifications(prev => [newNotification, ...prev]);
          setUnreadCount(prev => prev + 1);
          
          // Show push notification
          showPushNotification(newNotification);
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [userId]);

  const showPushNotification = async (notification: Notification) => {
    await Notifications.scheduleNotificationAsync({
      content: {
        title: notification.title,
        body: notification.message,
        data: { notificationId: notification.id },
        priority: notification.priority === 1 ? 'high' : 'normal',
        sound: notification.priority === 1 ? 'emergency.wav' : 'default'
      },
      trigger: null // Show immediately
    });
  };

  return (
    <NotificationProvider>
      <NotificationBadge count={unreadCount} />
      <NotificationList 
        notifications={notifications}
        onPress={onNotificationPress}
        maxCount={maxDisplayCount}
      />
    </NotificationProvider>
  );
};
```

### Integration with Existing Header

```typescript
// Update DashboardScreenWithDB.tsx header
<TouchableOpacity style={styles.notificationButton}>
  <Ionicons name="notifications-outline" size={24} color={Colors.textInverse} />
  <NotificationBadge count={unreadNotifications} />
  {unreadNotifications > 0 && (
    <View style={styles.notificationBadge}>
      <Text style={styles.notificationBadgeText}>
        {unreadNotifications > 99 ? '99+' : unreadNotifications}
      </Text>
    </View>
  )}
</TouchableOpacity>
```

### Database Service Methods

```typescript
// Add to DatabaseService in supabase.ts
static async getNotifications(userId: string, limit: number = 20) {
  return withJWTRetry(async () => {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  });
}

static async markNotificationAsRead(notificationId: string) {
  return withJWTRetry(async () => {
    const { error } = await supabase
      .from('notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('id', notificationId);

    if (error) throw error;
    return true;
  });
}

static async createNotification(notification: {
  user_id: string;
  type: string;
  title: string;
  message: string;
  priority?: number;
  location_id?: string;
  metadata?: any;
}) {
  return withJWTRetry(async () => {
    const { data, error } = await supabase
      .from('notifications')
      .insert([notification])
      .select()
      .single();

    if (error) throw error;
    return data;
  });
}
```

## Priority 2: Enhanced Safety Checklists Module

### Database Schema Implementation

```sql
-- Safety checklists
CREATE TABLE safety_checklists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  frequency VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'monthly'
  location_id UUID REFERENCES locations(id),
  category VARCHAR(100), -- 'pre_shift', 'equipment', 'environmental'
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Checklist items
CREATE TABLE checklist_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  checklist_id UUID REFERENCES safety_checklists(id) ON DELETE CASCADE,
  item_text TEXT NOT NULL,
  is_critical BOOLEAN DEFAULT false,
  requires_photo BOOLEAN DEFAULT false,
  requires_signature BOOLEAN DEFAULT false,
  display_order INTEGER DEFAULT 0,
  help_text TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Checklist completions
CREATE TABLE checklist_completions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  checklist_id UUID REFERENCES safety_checklists(id),
  user_id UUID REFERENCES users(id),
  completed_at TIMESTAMP DEFAULT NOW(),
  completion_data JSONB NOT NULL, -- Store answers, photos, signatures
  location_id UUID REFERENCES locations(id),
  shift_id UUID, -- Future reference to shifts
  notes TEXT,
  is_valid BOOLEAN DEFAULT true
);

-- Indexes
CREATE INDEX idx_safety_checklists_location ON safety_checklists(location_id);
CREATE INDEX idx_safety_checklists_frequency ON safety_checklists(frequency);
CREATE INDEX idx_checklist_items_checklist ON checklist_items(checklist_id);
CREATE INDEX idx_checklist_completions_user ON checklist_completions(user_id);
CREATE INDEX idx_checklist_completions_date ON checklist_completions(completed_at);

-- RLS Policies
ALTER TABLE safety_checklists ENABLE ROW LEVEL SECURITY;
ALTER TABLE checklist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE checklist_completions ENABLE ROW LEVEL SECURITY;

-- Users can view checklists for their location
CREATE POLICY "Users can view location checklists" ON safety_checklists
  FOR SELECT USING (
    location_id IN (
      SELECT location_id FROM users WHERE id = auth.uid()
    )
  );

-- Users can complete checklists
CREATE POLICY "Users can complete checklists" ON checklist_completions
  FOR INSERT WITH CHECK (user_id = auth.uid());
```

### Component Implementation

```typescript
// SafetyChecklistCarousel.tsx
interface SafetyChecklistCarouselProps {
  locationId: string;
  frequency: 'daily' | 'weekly' | 'monthly';
  onComplete?: (completion: ChecklistCompletion) => void;
}

export const SafetyChecklistCarousel: React.FC<SafetyChecklistCarouselProps> = ({
  locationId,
  frequency,
  onComplete
}) => {
  const [checklists, setChecklists] = useState<SafetyChecklist[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [completions, setCompletions] = useState<ChecklistCompletion[]>([]);

  // Load checklists for location and frequency
  useEffect(() => {
    loadChecklists();
    loadTodayCompletions();
  }, [locationId, frequency]);

  const loadChecklists = async () => {
    try {
      const data = await DatabaseService.getSafetyChecklists(locationId, frequency);
      setChecklists(data);
    } catch (error) {
      console.error('Failed to load safety checklists:', error);
    }
  };

  const handleChecklistComplete = async (checklistId: string, completionData: any) => {
    try {
      const completion = await DatabaseService.completeChecklist({
        checklist_id: checklistId,
        completion_data: completionData,
        location_id: locationId
      });
      
      setCompletions(prev => [...prev, completion]);
      onComplete?.(completion);
      
      // Show success notification
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Checklist Completed',
          body: 'Safety checklist has been successfully completed',
        },
        trigger: null
      });
    } catch (error) {
      console.error('Failed to complete checklist:', error);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        {checklists.map((checklist, index) => (
          <ChecklistCard
            key={checklist.id}
            checklist={checklist}
            isCompleted={completions.some(c => c.checklist_id === checklist.id)}
            onComplete={(data) => handleChecklistComplete(checklist.id, data)}
          />
        ))}
      </ScrollView>
      
      <PaginationIndicator
        total={checklists.length}
        current={currentIndex}
      />
    </View>
  );
};

// ChecklistCard.tsx
interface ChecklistCardProps {
  checklist: SafetyChecklist;
  isCompleted: boolean;
  onComplete: (data: ChecklistCompletionData) => void;
}

export const ChecklistCard: React.FC<ChecklistCardProps> = ({
  checklist,
  isCompleted,
  onComplete
}) => {
  const [responses, setResponses] = useState<ChecklistResponse[]>([]);
  const [photos, setPhotos] = useState<string[]>([]);

  const handleItemResponse = (itemId: string, response: any) => {
    setResponses(prev => [
      ...prev.filter(r => r.item_id !== itemId),
      { item_id: itemId, response }
    ]);
  };

  const handlePhotoCapture = async (itemId: string) => {
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      // Upload photo to Supabase Storage
      const photoUrl = await uploadChecklistPhoto(result.assets[0]);
      setPhotos(prev => [...prev, photoUrl]);
    }
  };

  const handleSubmit = () => {
    const completionData = {
      responses,
      photos,
      completed_at: new Date().toISOString()
    };
    onComplete(completionData);
  };

  return (
    <View style={styles.card}>
      <Text style={styles.title}>{checklist.title}</Text>
      <Text style={styles.description}>{checklist.description}</Text>
      
      <ScrollView style={styles.itemsList}>
        {checklist.items.map((item, index) => (
          <ChecklistItem
            key={item.id}
            item={item}
            onResponse={(response) => handleItemResponse(item.id, response)}
            onPhotoCapture={() => handlePhotoCapture(item.id)}
          />
        ))}
      </ScrollView>
      
      <TouchableOpacity
        style={[styles.submitButton, isCompleted && styles.completedButton]}
        onPress={handleSubmit}
        disabled={isCompleted}
      >
        <Text style={styles.submitButtonText}>
          {isCompleted ? 'Completed' : 'Complete Checklist'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
```

## Priority 3: Offline-First Architecture (Basic)

### Architecture Implementation

```typescript
// OfflineManager.tsx
interface OfflineOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: number;
  synced: boolean;
  retryCount: number;
}

export class OfflineManager {
  private static instance: OfflineManager;
  private pendingOperations: OfflineOperation[] = [];
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;

  static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager();
    }
    return OfflineManager.instance;
  }

  async initialize() {
    // Load pending operations from storage
    const stored = await AsyncStorage.getItem('pendingOperations');
    if (stored) {
      this.pendingOperations = JSON.parse(stored);
    }

    // Monitor network status
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (wasOffline && this.isOnline) {
        this.syncPendingOperations();
      }
    });
  }

  async queueOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'synced' | 'retryCount'>) {
    const queuedOperation: OfflineOperation = {
      ...operation,
      id: uuid.v4(),
      timestamp: Date.now(),
      synced: false,
      retryCount: 0
    };

    this.pendingOperations.push(queuedOperation);
    await this.savePendingOperations();

    if (this.isOnline) {
      this.syncPendingOperations();
    }

    return queuedOperation.id;
  }

  private async syncPendingOperations() {
    if (this.syncInProgress || !this.isOnline) return;

    this.syncInProgress = true;
    const operations = [...this.pendingOperations.filter(op => !op.synced)];

    for (const operation of operations) {
      try {
        await this.executeOperation(operation);
        operation.synced = true;
      } catch (error) {
        operation.retryCount++;
        console.error(`Failed to sync operation ${operation.id}:`, error);
        
        // Remove operation if retry limit exceeded
        if (operation.retryCount > 3) {
          this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
        }
      }
    }

    // Remove synced operations
    this.pendingOperations = this.pendingOperations.filter(op => !op.synced);
    await this.savePendingOperations();
    this.syncInProgress = false;
  }

  private async executeOperation(operation: OfflineOperation) {
    switch (operation.type) {
      case 'create':
        return await supabase.from(operation.table).insert(operation.data);
      case 'update':
        return await supabase.from(operation.table).update(operation.data).eq('id', operation.data.id);
      case 'delete':
        return await supabase.from(operation.table).delete().eq('id', operation.data.id);
    }
  }

  private async savePendingOperations() {
    await AsyncStorage.setItem('pendingOperations', JSON.stringify(this.pendingOperations));
  }
}

// OfflineProvider.tsx
export const OfflineProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isOnline, setIsOnline] = useState(true);
  const [pendingCount, setPendingCount] = useState(0);

  useEffect(() => {
    const offlineManager = OfflineManager.getInstance();
    offlineManager.initialize();

    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected ?? false);
    });

    return unsubscribe;
  }, []);

  return (
    <OfflineContext.Provider value={{ isOnline, pendingCount }}>
      {children}
      {!isOnline && <OfflineIndicator />}
    </OfflineContext.Provider>
  );
};
```

### Local Storage Strategy

```typescript
// LocalStorageManager.tsx
export class LocalStorageManager {
  // Critical data that must be available offline
  static async cacheEssentialData() {
    try {
      // Cache user profile
      const profile = await DatabaseService.getUserProfile();
      await AsyncStorage.setItem('cached_profile', JSON.stringify(profile));

      // Cache safety checklists
      const checklists = await DatabaseService.getSafetyChecklists();
      await AsyncStorage.setItem('cached_checklists', JSON.stringify(checklists));

      // Cache equipment list
      const equipment = await DatabaseService.getEquipment();
      await AsyncStorage.setItem('cached_equipment', JSON.stringify(equipment));

      console.log('Essential data cached successfully');
    } catch (error) {
      console.error('Failed to cache essential data:', error);
    }
  }

  static async getCachedData<T>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(`cached_${key}`);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error(`Failed to get cached data for ${key}:`, error);
      return null;
    }
  }

  static async setCachedData<T>(key: string, data: T): Promise<void> {
    try {
      await AsyncStorage.setItem(`cached_${key}`, JSON.stringify(data));
    } catch (error) {
      console.error(`Failed to cache data for ${key}:`, error);
    }
  }
}
```

## Priority 4: Performance Optimizations

### Database Query Optimization

```sql
-- Add missing indexes for better performance
CREATE INDEX CONCURRENTLY idx_daily_production_metrics_date_location 
ON daily_production_metrics(date, location_id);

CREATE INDEX CONCURRENTLY idx_safety_incidents_date_severity 
ON safety_incidents(incident_date, severity);

CREATE INDEX CONCURRENTLY idx_equipment_status_location 
ON equipment(status, location_id);

-- Optimize production metrics query
CREATE OR REPLACE FUNCTION get_production_metrics_optimized(
  p_start_date DATE,
  p_end_date DATE,
  p_location_id UUID DEFAULT NULL
)
RETURNS TABLE (
  date DATE,
  tonnage DECIMAL,
  efficiency DECIMAL,
  location_name VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dpm.date,
    dpm.tonnage,
    dpm.efficiency,
    l.name as location_name
  FROM daily_production_metrics dpm
  JOIN locations l ON dpm.location_id = l.id
  WHERE dpm.date BETWEEN p_start_date AND p_end_date
    AND (p_location_id IS NULL OR dpm.location_id = p_location_id)
  ORDER BY dpm.date DESC;
END;
$$ LANGUAGE plpgsql;
```

### Component Performance

```typescript
// Memoized components for better performance
export const MemoizedActivityCard = React.memo<ActivityCardProps>(({ activity, onPress }) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <OptimizedImage uri={activity.image_url} />
      <Text style={styles.title}>{activity.title}</Text>
      <Text style={styles.description}>{activity.description}</Text>
    </TouchableOpacity>
  );
});

// Optimized image component
export const OptimizedImage: React.FC<{ uri: string }> = ({ uri }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  return (
    <View style={styles.imageContainer}>
      {loading && <ActivityIndicator size="small" />}
      <Image
        source={{ uri: `${uri}?w=400&h=300&q=80` }}
        style={styles.image}
        onLoadStart={() => setLoading(true)}
        onLoadEnd={() => setLoading(false)}
        onError={() => setError(true)}
        resizeMode="cover"
      />
    </View>
  );
};
```

## Implementation Timeline

### Week 1-2: Setup & Foundation
- [ ] Set up Expo Notifications
- [ ] Create notification database schema
- [ ] Implement basic NotificationManager component
- [ ] Set up offline architecture foundation

### Week 3-4: Notifications System
- [ ] Complete real-time notification system
- [ ] Integrate with existing dashboard header
- [ ] Implement push notification handling
- [ ] Add notification history and management

### Week 5-6: Safety Checklists
- [ ] Create safety checklist database schema
- [ ] Implement SafetyChecklistCarousel component
- [ ] Add photo capture and storage
- [ ] Integrate with offline system

### Week 7-8: Offline Architecture
- [ ] Complete offline operation queuing
- [ ] Implement sync mechanisms
- [ ] Add conflict resolution
- [ ] Test offline scenarios

### Week 9-10: Performance & Testing
- [ ] Database query optimization
- [ ] Component performance improvements
- [ ] Comprehensive testing
- [ ] Bug fixes and refinements

### Week 11-12: Integration & Deployment
- [ ] Integration testing
- [ ] User acceptance testing
- [ ] Performance monitoring setup
- [ ] Production deployment

This implementation guide provides the technical foundation for Phase 1 development, building upon the existing Mining Operations App architecture while introducing critical new capabilities for enhanced operational efficiency.
