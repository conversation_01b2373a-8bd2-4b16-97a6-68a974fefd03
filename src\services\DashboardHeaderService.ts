import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { supabase } from '../config/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface DashboardHeaderImage {
  id: string;
  title: string;
  description?: string;
  image_url: string;
  thumbnail_url?: string;
  display_order: number;
  is_active: boolean;
  created_by?: string;
  created_by_name?: string;
  created_by_email?: string;
  created_at: string;
  updated_at: string;
}

interface UploadResult {
  success: boolean;
  imageUrl?: string;
  thumbnailUrl?: string;
  error?: string;
}

interface ImagePickerResult {
  success: boolean;
  imageUri?: string;
  error?: string;
}

class DashboardHeaderService {
  private static instance: DashboardHeaderService;
  private cacheKey = 'dashboard_header_images';
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  static getInstance(): DashboardHeaderService {
    if (!DashboardHeaderService.instance) {
      DashboardHeaderService.instance = new DashboardHeaderService();
    }
    return DashboardHeaderService.instance;
  }

  /**
   * Get all active dashboard header images for display
   */
  async getDashboardHeaderImages(useCache: boolean = true): Promise<DashboardHeaderImage[]> {
    try {
      // Check cache first
      if (useCache) {
        const cached = await this.getCachedImages();
        if (cached) {
          return cached;
        }
      }

      console.log('🖼️ Fetching dashboard header images from database...');

      const { data, error } = await supabase
        .rpc('get_dashboard_header_images');

      if (error) {
        console.error('❌ Error fetching dashboard header images:', error);
        return this.getDefaultImages();
      }

      const images: DashboardHeaderImage[] = data || [];
      
      // Cache the results
      await this.cacheImages(images);
      
      console.log(`✅ Fetched ${images.length} dashboard header images`);
      return images.length > 0 ? images : this.getDefaultImages();

    } catch (error) {
      console.error('❌ Error in getDashboardHeaderImages:', error);
      return this.getDefaultImages();
    }
  }

  /**
   * Get all dashboard header images (including inactive) for management
   */
  async getAllDashboardHeaderImages(): Promise<DashboardHeaderImage[]> {
    try {
      console.log('🖼️ Fetching all dashboard header images (including inactive)...');

      // Use raw SQL query to get images with creator info
      const { data, error } = await supabase
        .rpc('get_all_dashboard_header_images_with_creator');

      if (error) {
        console.error('❌ Error fetching all dashboard header images:', error);
        return [];
      }

      const images: DashboardHeaderImage[] = data || [];
      console.log(`✅ Fetched ${images.length} total dashboard header images`);
      return images;

    } catch (error) {
      console.error('❌ Error in getAllDashboardHeaderImages:', error);
      return [];
    }
  }

  /**
   * Get default images as fallback
   */
  private getDefaultImages(): DashboardHeaderImage[] {
    const now = new Date().toISOString();
    return [
      {
        id: 'default-1',
        title: 'Mining Site Operations',
        description: 'Heavy machinery and mining operations in action',
        image_url: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
        thumbnail_url: undefined,
        display_order: 1,
        is_active: true,
        created_by: undefined,
        created_by_name: undefined,
        created_by_email: undefined,
        created_at: now,
        updated_at: now
      },
      {
        id: 'default-2',
        title: 'Open Pit Mining',
        description: 'Large scale open pit mining operations with sunset view',
        image_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
        thumbnail_url: undefined,
        display_order: 2,
        is_active: true,
        created_by: undefined,
        created_by_name: undefined,
        created_by_email: undefined,
        created_at: now,
        updated_at: now
      },
      {
        id: 'default-3',
        title: 'Heavy Equipment',
        description: 'Mining trucks and heavy equipment in industrial setting',
        image_url: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
        thumbnail_url: undefined,
        display_order: 3,
        is_active: true,
        created_by: undefined,
        created_by_name: undefined,
        created_by_email: undefined,
        created_at: now,
        updated_at: now
      }
    ];
  }

  /**
   * Cache images locally
   */
  private async cacheImages(images: DashboardHeaderImage[]): Promise<void> {
    try {
      const cacheData = {
        images,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ Failed to cache dashboard header images:', error);
    }
  }

  /**
   * Get cached images if not expired
   */
  private async getCachedImages(): Promise<DashboardHeaderImage[] | null> {
    try {
      const cached = await AsyncStorage.getItem(this.cacheKey);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const isExpired = Date.now() - cacheData.timestamp > this.cacheExpiry;
      
      if (isExpired) {
        await AsyncStorage.removeItem(this.cacheKey);
        return null;
      }

      console.log('📱 Using cached dashboard header images');
      return cacheData.images;
    } catch (error) {
      console.warn('⚠️ Failed to get cached dashboard header images:', error);
      return null;
    }
  }

  /**
   * Clear cache
   */
  async clearCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.cacheKey);
      console.log('🧹 Dashboard header images cache cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear cache:', error);
    }
  }

  /**
   * Request camera and media library permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      return cameraPermission.status === 'granted' && mediaLibraryPermission.status === 'granted';
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Open gallery to select image
   */
  async openGallery(): Promise<ImagePickerResult> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        return { success: false, error: 'Gallery permissions required' };
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [16, 9], // Landscape aspect ratio for header
        quality: 0.9,
        exif: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return { success: false, error: 'Image selection cancelled' };
      }

      return {
        success: true,
        imageUri: result.assets[0].uri,
      };
    } catch (error) {
      console.error('Error opening gallery:', error);
      return { success: false, error: 'Failed to open gallery' };
    }
  }

  /**
   * Upload image to Supabase Storage in storage/header folder
   */
  async uploadHeaderImage(imageUri: string, title: string): Promise<UploadResult> {
    try {
      // Read the image file
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!fileInfo.exists) {
        return { success: false, error: 'Image file not found' };
      }

      // Create filename for header image with organized structure
      const fileExtension = imageUri.split('.').pop() || 'jpg';
      const fileName = `dashboard_header_${Date.now()}.${fileExtension}`;
      const filePath = `images/${fileName}`; // Organized in images subfolder

      console.log('📸 Starting dashboard header image upload:', { 
        fileName, 
        filePath, 
        imageUri,
        title
      });

      // Read file as base64
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Convert base64 to binary
      const binaryString = atob(base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Upload to Supabase Storage - Header bucket
      const { data, error: uploadError } = await supabase.storage
        .from('header') // Dedicated bucket for header images
        .upload(filePath, bytes, {
          contentType: `image/${fileExtension}`,
          upsert: true,
        });

      if (uploadError) {
        console.error('❌ Dashboard header image upload failed:', uploadError);
        return { success: false, error: uploadError.message };
      }

      console.log('✅ Dashboard header image uploaded successfully:', data);

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from('header')
        .getPublicUrl(filePath);

      if (!publicUrlData.publicUrl) {
        return { success: false, error: 'Failed to get public URL' };
      }

      console.log('🔗 Dashboard header image public URL:', publicUrlData.publicUrl);

      return {
        success: true,
        imageUrl: publicUrlData.publicUrl,
      };

    } catch (error) {
      console.error('❌ Error uploading dashboard header image:', error);
      return { success: false, error: 'Failed to upload image' };
    }
  }

  /**
   * Add new dashboard header image
   */
  async addDashboardHeaderImage(
    title: string,
    description: string,
    imageUri: string,
    displayOrder?: number
  ): Promise<{ success: boolean; id?: string; error?: string }> {
    try {
      // Upload image first
      const uploadResult = await this.uploadHeaderImage(imageUri, title);
      
      if (!uploadResult.success || !uploadResult.imageUrl) {
        return { success: false, error: uploadResult.error || 'Upload failed' };
      }

      // Add to database
      const { data, error } = await supabase
        .rpc('add_dashboard_header_image', {
          p_title: title,
          p_description: description,
          p_image_url: uploadResult.imageUrl,
          p_thumbnail_url: null,
          p_display_order: displayOrder
        });

      if (error) {
        console.error('❌ Error adding dashboard header image to database:', error);
        return { success: false, error: error.message };
      }

      // Clear cache to force refresh
      await this.clearCache();

      console.log('✅ Dashboard header image added successfully:', data);
      return { success: true, id: data };

    } catch (error) {
      console.error('❌ Error in addDashboardHeaderImage:', error);
      return { success: false, error: 'Failed to add header image' };
    }
  }

  /**
   * Update display order of header image
   */
  async updateDisplayOrder(id: string, newOrder: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .rpc('update_dashboard_header_image_order', {
          p_id: id,
          p_new_order: newOrder
        });

      if (error) {
        console.error('❌ Error updating display order:', error);
        return false;
      }

      // Clear cache to force refresh
      await this.clearCache();
      
      console.log('✅ Display order updated successfully');
      return true;
    } catch (error) {
      console.error('❌ Error in updateDisplayOrder:', error);
      return false;
    }
  }

  /**
   * Toggle active status of header image
   */
  async toggleActiveStatus(id: string, isActive: boolean): Promise<boolean> {
    try {
      const { error } = await supabase
        .rpc('toggle_dashboard_header_image_status', {
          p_id: id,
          p_is_active: isActive
        });

      if (error) {
        console.error('❌ Error toggling active status:', error);
        return false;
      }

      // Clear cache to force refresh
      await this.clearCache();
      
      console.log('✅ Active status toggled successfully');
      return true;
    } catch (error) {
      console.error('❌ Error in toggleActiveStatus:', error);
      return false;
    }
  }

  /**
   * Delete header image
   */
  async deleteHeaderImage(id: string, imageUrl?: string): Promise<boolean> {
    try {
      // Delete from storage if URL provided
      if (imageUrl && imageUrl.includes('supabase')) {
        const urlParts = imageUrl.split('/storage/v1/object/public/header/');
        if (urlParts.length === 2) {
          const filePath = urlParts[1];
          await supabase.storage
            .from('header')
            .remove([filePath]);
          console.log('🗑️ Deleted header image from storage:', filePath);
        }
      }

      // Delete from database
      const { error } = await supabase
        .rpc('delete_dashboard_header_image', {
          p_id: id
        });

      if (error) {
        console.error('❌ Error deleting dashboard header image:', error);
        return false;
      }

      // Clear cache to force refresh
      await this.clearCache();
      
      console.log('✅ Dashboard header image deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ Error in deleteHeaderImage:', error);
      return false;
    }
  }
}

export default DashboardHeaderService;
