const fs = require('fs');
const path = require('path');

console.log('📦 Updating package.json with missing dependencies...');

// Read current package.json
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Dependencies to add
const newDependencies = {
  // Expo mobile dependencies
  'expo-device': '~6.0.2',
  'expo-battery': '~7.0.0',
  'expo-location': '~17.0.1',
  'expo-sharing': '~12.0.1',
  'expo-image-picker': '~15.0.7',
  'expo-file-system': '~17.0.1',
  'expo-haptics': '~13.0.1',
  'expo-screen-orientation': '~7.0.5',
  'expo-local-authentication': '~14.0.1',
  
  // Polyfills and utilities
  'buffer': '^6.0.3',
  'react-native-get-random-values': '^1.9.0',
  'text-encoding': '^0.7.0',
  
  // State management
  'zustand': '^4.4.7',
  
  // Form validation
  'zod': '^3.22.4',
  
  // Charts and visualization
  'react-native-chart-kit': '^6.12.0',
  'react-native-svg': '15.2.0',
  
  // Additional utilities
  'react-native-uuid': '^2.0.1',
  'date-fns': '^2.30.0'
};

const newDevDependencies = {
  // Testing
  'jest': '^29.7.0',
  '@testing-library/react-native': '^12.4.2',
  '@testing-library/jest-native': '^5.4.3',
  'react-test-renderer': '19.0.0',
  'jest-expo': '^51.0.3',
  
  // Types
  '@types/react-native': '^0.72.8',
  '@types/jest': '^29.5.8'
};

// Merge dependencies
packageJson.dependencies = {
  ...packageJson.dependencies,
  ...newDependencies
};

packageJson.devDependencies = {
  ...packageJson.devDependencies,
  ...newDevDependencies
};

// Add Jest configuration if not exists
if (!packageJson.jest) {
  packageJson.jest = {
    "preset": "jest-expo",
    "setupFilesAfterEnv": [
      "@testing-library/jest-native/extend-expect"
    ],
    "testMatch": [
      "**/__tests__/**/*.(ts|tsx|js)",
      "**/*.(test|spec).(ts|tsx|js)"
    ],
    "collectCoverageFrom": [
      "src/**/*.{ts,tsx}",
      "!src/**/*.d.ts",
      "!src/tests/**/*"
    ],
    "coverageThreshold": {
      "global": {
        "branches": 70,
        "functions": 70,
        "lines": 70,
        "statements": 70
      }
    },
    "transformIgnorePatterns": [
      "node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"
    ]
  };
}

// Add scripts if not exists
if (!packageJson.scripts.test) {
  packageJson.scripts.test = 'jest';
}
if (!packageJson.scripts['test:coverage']) {
  packageJson.scripts['test:coverage'] = 'jest --coverage';
}
if (!packageJson.scripts['test:watch']) {
  packageJson.scripts['test:watch'] = 'jest --watch';
}

// Write updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

console.log('✅ package.json updated successfully!');
console.log('');
console.log('📋 Next steps:');
console.log('1. Run: npm install');
console.log('2. Run: npx expo install --fix');
console.log('3. Run: npm test (to verify testing setup)');
console.log('');
console.log('🔧 Added dependencies:');
Object.keys(newDependencies).forEach(dep => {
  console.log(`  - ${dep}@${newDependencies[dep]}`);
});
console.log('');
console.log('🧪 Added dev dependencies:');
Object.keys(newDevDependencies).forEach(dep => {
  console.log(`  - ${dep}@${newDevDependencies[dep]}`);
});
