# Dashboard Screen Documentation

## Overview

The DashboardScreen serves as the main landing page after user authentication, providing a comprehensive overview of mining operations with modern layered design and intuitive navigation.

## Features

### 🖼️ Header Section
- **Background Image**: Mining landscape with gradient overlay
- **Profile Section**: User avatar with online status indicator
- **Search Bar**: Global search functionality with modern styling
- **Date Display**: Current date in Indonesian format

### 🏷️ Tab Navigation
- **Favorit**: Frequently used features
- **Terbaru**: Recently accessed items
- **Sering dilihat**: Most viewed content

### 🎯 Menu Grid
4x3 grid layout with background image icons and the following menu items:
- Attendance Recording (calendar icon)
- ATR Pribadi (time icon)
- Attendance Position (location icon)
- <PERSON><PERSON><PERSON><PERSON> (phone-portrait icon)
- Info Ban<PERSON>an & Tunjangan (information-circle icon)
- <PERSON><PERSON><PERSON> WFO (people icon)
- Presensi Meeting (people-circle icon)
- <PERSON><PERSON> (person icon)
- Raport Operator (document-text icon)
- iPeak (videocam icon)
- <PERSON>hat <PERSON>mua (apps icon)

Each menu item uses `assets/menu bg.png` as background with Ionicons overlaid on top.

### 📺 Video Section
- Video populer header
- "Lihat Semua" navigation link

## Technical Implementation

### Component Structure
```
DashboardScreen
├── ImageBackground (Header)
│   ├── Profile Section
│   ├── Search Bar
│   └── Date Display
├── Content Container
│   ├── Tab Navigation
│   ├── Menu Grid
│   └── Video Section
```

### Key Technologies
- React Native with TypeScript
- Cross-platform shadow system
- Modern CSS-in-JS styling
- Platform-specific optimizations

### Navigation Integration
- Integrates with TabNavigator
- Routes to specific feature screens
- Maintains navigation state

## Design Principles

### Visual Hierarchy
1. **Header (35% screen height)**: Background image with overlays
2. **Content Area**: White background with rounded top corners
3. **Menu Grid**: 4-column responsive layout
4. **Interactive Elements**: Touch-friendly with visual feedback

### Color Scheme
- Primary: Mining industry blues and earth tones
- Accent: Status-based colors (green, orange, red)
- Background: Clean whites and light grays
- Text: High contrast for readability

### Typography
- Headers: Bold, large fonts for hierarchy
- Body: Medium weight for readability
- Labels: Small, descriptive text

## File Structure

```
src/screens/DashboardScreen.tsx
├── Interfaces
│   ├── MenuItem
│   └── TabItem
├── Component Logic
│   ├── State Management
│   ├── Navigation Handlers
│   └── Render Functions
├── Styling
│   ├── Header Styles
│   ├── Content Styles
│   ├── Menu Styles
│   └── Cross-platform Shadows
```

## Dependencies

### Core Dependencies
- `react-native`: Core framework
- `@react-navigation/native`: Navigation
- `@expo/vector-icons`: Icon system
- `react-native-safe-area-context`: Safe area handling

### Custom Dependencies
- `../constants/colors`: Color system
- `../constants/layout`: Layout constants
- `../utils/shadowHelper`: Cross-platform shadows

## Performance Considerations

### Optimization Strategies
- Lazy loading for images
- Memoized render functions
- Efficient state management
- Platform-specific styling

### Memory Management
- Proper cleanup of event listeners
- Optimized image loading
- Minimal re-renders

## Accessibility

### Features
- Screen reader support
- Touch target sizing (minimum 44px)
- High contrast color ratios
- Keyboard navigation support

### Implementation
- Semantic HTML elements
- ARIA labels for complex interactions
- Focus management
- Voice-over compatibility

## Testing Strategy

### Unit Tests
- Component rendering
- State management
- Navigation handlers
- Utility functions

### Integration Tests
- Navigation flow
- API integration
- Cross-platform compatibility
- Performance benchmarks

## Future Enhancements

### Planned Features
- Real-time notifications
- Customizable dashboard layout
- Advanced search filters
- Offline mode support

### Technical Improvements
- Performance optimizations
- Enhanced accessibility
- Better error handling
- Improved caching strategies

## Troubleshooting

### Common Issues
1. **Shadow warnings**: Use ShadowPresets from shadowHelper
2. **Navigation errors**: Ensure proper screen registration
3. **Image loading**: Check network connectivity and URLs
4. **Performance**: Monitor component re-renders

### Debug Tips
- Use React DevTools for component inspection
- Enable performance monitoring
- Check console for warnings
- Test on multiple devices/platforms

## Related Documentation
- [Architecture Overview](../architecture/overview.md)
- [Navigation System](../architecture/navigation.md)
- [Design System](../design/design-system.md)
- [API Integration](../api/endpoints.md)
