# Component Documentation

## Cortex 7 Metadata
- **Document Type**: Component Library
- **Component**: UI Components and Patterns
- **Technology**: React Native, TypeScript, Component Composition
- **Tags**: `#components` `#ui-library` `#reusable` `#patterns`
- **Last Updated**: 2025-01-19
- **Status**: Active ✅
- **Source**: Consolidated from memory-bank/components

## Overview
Comprehensive documentation for the component library and design patterns used in the MiningOperationsApp React Native project.

## Component Categories

### 1. Chart Components ✅
Advanced chart system with horizontal scrolling and mobile optimization.

### 2. Carousel Components ✅ (NEW - 2025-01-19)
Interactive carousel components with auto-scroll and manual navigation.

#### ActivityDocumentationCarousel Component
```typescript
interface ActivityDocumentationCarouselProps {
  data: ActivityDocumentationItem[];
  loading?: boolean;
  onRefresh?: () => void;
}

interface ActivityDocumentationItem {
  id: string;
  title: string;
  description?: string;
  image_url?: string;
  activity_date: string;
  location?: { name: string };
  creator?: { full_name: string };
}
```

**Key Features:**
- **Auto-scroll**: Advances every 3 seconds automatically
- **Manual Navigation**: Touch-based left/right swipe gestures
- **User Interaction**: Pauses auto-scroll on user touch, resumes after 5 seconds
- **Visual Indicators**: Pagination dots showing current position
- **Image Loading**: Proper loading states and error handling
- **Responsive Design**: Adapts to different screen sizes (80% screen width)
- **Smooth Animations**: Snap-to behavior with momentum scrolling

**Implementation Details:**
- Card width: 80% of screen width
- Auto-scroll interval: 3000ms (3 seconds)
- User interaction timeout: 5000ms (5 seconds)
- Image container height: 120px
- Pagination dots: Active (12px) vs Inactive (8px)

**Usage in Dashboard:**
```typescript
<View style={styles.activityDocumentationSection}>
  <Text style={styles.sectionTitle}>Activity Documentation</Text>
  <ActivityDocumentationCarousel
    data={activityDocumentation}
    loading={activityLoading}
    onRefresh={loadActivityDocumentation}
  />
</View>
```

### 3. Profile Management Components ✅ (NEW - 2025-01-19)
User profile photo management with database integration.

#### ProfilePhotoManager Component
```typescript
interface ProfilePhotoManagerProps {
  userId: string;
  currentPhotoUrl?: string | null;
  userInitials: string;
  onPhotoUpdated?: (newPhotoUrl: string) => void;
  size?: number;
  editable?: boolean;
}
```

**Key Features:**
- **Database Integration**: Connects to Supabase users table and storage
- **Circular Display**: Configurable size with perfect circle cropping
- **Fallback System**: User initials when photo unavailable
- **Upload Capability**: Expo ImagePicker integration for photo management
- **Permission Handling**: Camera roll access with user-friendly prompts
- **Error Handling**: Graceful degradation on image load failures
- **Loading States**: Visual feedback during upload operations
- **Security**: RLS policies for user-specific photo access

**Implementation Details:**
- Photo size: Configurable (default 48px for header)
- Border: 2px semi-transparent white border
- Fallback: User initials with primary color
- Upload: 1:1 aspect ratio, 5MB limit, JPEG/PNG/WebP support
- Storage: Supabase Storage with CDN delivery

**Usage in Dashboard Header:**
```typescript
<ProfilePhotoManager
  userId={profile?.id || ''}
  currentPhotoUrl={profile?.avatar_url}
  userInitials={profile?.full_name ? profile.full_name.split(' ').map(n => n[0]).join('').substring(0, 2) : 'U'}
  size={48}
  editable={false} // Read-only in dashboard
  onPhotoUpdated={(newPhotoUrl) => {
    console.log('Profile photo updated:', newPhotoUrl);
  }}
/>
```

#### ScrollableChart Component
```typescript
interface ScrollableChartProps {
  data: ChartData;
  chartType: 'trends' | 'impact' | 'fuel';
  width?: number;
  height?: number;
}

const ScrollableChart: React.FC<ScrollableChartProps> = ({
  data,
  chartType,
  width,
  height = 220
}) => {
  // Dynamic width calculation and scrolling logic
};
```

**Features**:
- Dynamic width calculation based on data points
- Horizontal scrolling for large datasets
- Mobile-optimized label spacing
- Clean design without grid lines

#### LineChart Integration
```typescript
<LineChart
  data={chartData}
  width={dynamicWidth}
  height={220}
  chartConfig={{
    backgroundColor: Colors.surface,
    color: () => Colors.primary,
    bezier: false,
    withDots: true,
    withShadow: false,
    withInnerLines: false,
    withOuterLines: false,
  }}
  style={styles.chart}
/>
```

### 2. UI Components ✅
Core user interface components following design system principles.

#### Button Component
```typescript
interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false
}) => {
  // Button implementation with variants
};
```

#### Card Component
```typescript
interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  shadow?: boolean;
  padding?: keyof typeof Layout.spacing;
}

const Card: React.FC<CardProps> = ({
  children,
  style,
  shadow = true,
  padding = 'md'
}) => {
  // Card container with consistent styling
};
```

#### Header Component
```typescript
interface HeaderProps {
  title: string;
  subtitle?: string;
  leftAction?: () => void;
  rightAction?: () => void;
  leftIcon?: string;
  rightIcon?: string;
}

const Header: React.FC<HeaderProps> = ({
  title,
  subtitle,
  leftAction,
  rightAction,
  leftIcon,
  rightIcon
}) => {
  // Header with navigation and actions
};
```

### 3. Form Components ✅
Input components with validation and consistent styling.

#### Input Component
```typescript
interface InputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  error?: string;
  required?: boolean;
  multiline?: boolean;
  keyboardType?: KeyboardTypeOptions;
}

const Input: React.FC<InputProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  error,
  required = false,
  multiline = false,
  keyboardType = 'default'
}) => {
  // Input with label, validation, and error states
};
```

#### Picker Component
```typescript
interface PickerProps {
  label: string;
  value: string;
  onValueChange: (value: string) => void;
  items: PickerItem[];
  placeholder?: string;
  error?: string;
}

const Picker: React.FC<PickerProps> = ({
  label,
  value,
  onValueChange,
  items,
  placeholder,
  error
}) => {
  // Dropdown picker with consistent styling
};
```

### 4. Layout Components ✅
Structural components for consistent layout and spacing.

#### Container Component
```typescript
interface ContainerProps {
  children: React.ReactNode;
  padding?: keyof typeof Layout.spacing;
  backgroundColor?: string;
  safe?: boolean;
}

const Container: React.FC<ContainerProps> = ({
  children,
  padding = 'md',
  backgroundColor = Colors.background,
  safe = true
}) => {
  // Main container with safe area and consistent padding
};
```

#### Section Component
```typescript
interface SectionProps {
  title?: string;
  children: React.ReactNode;
  spacing?: keyof typeof Layout.spacing;
}

const Section: React.FC<SectionProps> = ({
  title,
  children,
  spacing = 'lg'
}) => {
  // Section with optional title and consistent spacing
};
```

## Component Patterns

### 1. Composition Pattern
```typescript
// Composable components for flexibility
<Card>
  <Header title="Production Metrics" />
  <ScrollableChart data={chartData} chartType="trends" />
  <Section title="Summary">
    <MetricsSummary data={summaryData} />
  </Section>
</Card>
```

### 2. Render Props Pattern
```typescript
// Flexible data rendering
<DataProvider>
  {({ data, loading, error }) => (
    <>
      {loading && <LoadingSpinner />}
      {error && <ErrorMessage error={error} />}
      {data && <ScrollableChart data={data} chartType="trends" />}
    </>
  )}
</DataProvider>
```

### 3. Higher-Order Component Pattern
```typescript
// Reusable functionality
const withLoading = <P extends object>(Component: React.ComponentType<P>) => {
  return (props: P & { loading?: boolean }) => {
    const { loading, ...componentProps } = props;
    
    if (loading) {
      return <LoadingSpinner />;
    }
    
    return <Component {...(componentProps as P)} />;
  };
};

const ChartWithLoading = withLoading(ScrollableChart);
```

### 4. Custom Hooks Pattern
```typescript
// Reusable stateful logic
const useChartData = (period: TimePeriod) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    loadChartData(period)
      .then(setData)
      .catch(setError)
      .finally(() => setLoading(false));
  }, [period]);
  
  return { data, loading, error };
};
```

## Styling Patterns

### 1. StyleSheet Pattern
```typescript
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: Layout.spacing.md,
  },
  card: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.sm,
    ...Layout.shadow,
  },
  text: {
    fontSize: Layout.fontSize.md,
    color: Colors.textPrimary,
    fontWeight: '600',
  },
});
```

### 2. Dynamic Styling Pattern
```typescript
const getDynamicStyles = (theme: Theme, size: Size) => ({
  container: {
    backgroundColor: theme.background,
    padding: Layout.spacing[size],
  },
  text: {
    fontSize: Layout.fontSize[size],
    color: theme.textPrimary,
  },
});
```

### 3. Responsive Styling Pattern
```typescript
const getResponsiveStyles = (screenWidth: number) => ({
  container: {
    flexDirection: screenWidth > 768 ? 'row' : 'column',
    padding: screenWidth > 768 ? Layout.spacing.lg : Layout.spacing.md,
  },
});
```

## Component Testing

### Unit Testing Pattern
```typescript
describe('ScrollableChart', () => {
  it('should render chart with correct data', () => {
    const mockData = {
      labels: ['Jan', 'Feb', 'Mar'],
      datasets: [{ data: [100, 200, 300] }],
    };
    
    const { getByTestId } = render(
      <ScrollableChart data={mockData} chartType="trends" />
    );
    
    expect(getByTestId('scrollable-chart')).toBeTruthy();
  });
  
  it('should enable scrolling for large datasets', () => {
    const largeData = {
      labels: Array.from({ length: 20 }, (_, i) => `Label ${i}`),
      datasets: [{ data: Array.from({ length: 20 }, () => Math.random() * 100) }],
    };
    
    const { getByTestId } = render(
      <ScrollableChart data={largeData} chartType="trends" />
    );
    
    expect(getByTestId('chart-scroll-view')).toBeTruthy();
  });
});
```

### Integration Testing Pattern
```typescript
describe('ProductionOverviewScreen', () => {
  it('should load and display chart data', async () => {
    const { getByTestId, findByText } = render(<ProductionOverviewScreen />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(getByTestId('scrollable-chart')).toBeTruthy();
    });
    
    // Verify chart is displayed
    expect(await findByText('Production Trends')).toBeTruthy();
  });
});
```

## Performance Optimization

### Memoization Pattern
```typescript
const MemoizedChart = React.memo(ScrollableChart, (prevProps, nextProps) => {
  return (
    prevProps.data === nextProps.data &&
    prevProps.chartType === nextProps.chartType
  );
});
```

### Lazy Loading Pattern
```typescript
const LazyChart = React.lazy(() => import('./ScrollableChart'));

const ChartContainer = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <LazyChart data={chartData} chartType="trends" />
  </Suspense>
);
```

### Virtual Scrolling Pattern
```typescript
const VirtualizedList = ({ data, renderItem }) => {
  const [visibleItems, setVisibleItems] = useState([]);
  
  // Calculate visible items based on scroll position
  const updateVisibleItems = useCallback((scrollOffset) => {
    const startIndex = Math.floor(scrollOffset / itemHeight);
    const endIndex = startIndex + visibleCount;
    setVisibleItems(data.slice(startIndex, endIndex));
  }, [data]);
  
  return (
    <ScrollView onScroll={({ nativeEvent }) => 
      updateVisibleItems(nativeEvent.contentOffset.y)
    }>
      {visibleItems.map(renderItem)}
    </ScrollView>
  );
};
```

---
*Component documentation following Cortex 7 standards for comprehensive UI library reference.*
