-- =====================================================
-- Mining Operations Database - Production Calendar
-- =====================================================
-- File: 02-production-calendar.sql
-- Description: Production calendar and time dimension tables
-- Dependencies: 01-core-setup.sql
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- PRODUCTION CALENDAR TABLE
-- =====================================================
CREATE TABLE production_calendar (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Calendar Dimensions
    calendar_date DATE NOT NULL UNIQUE,
    day_of_week INTEGER GENERATED ALWAYS AS (EXTRACT(DOW FROM calendar_date)) STORED,
    day_name VARCHAR(10) GENERATED ALWAYS AS (TRIM(TO_CHAR(calendar_date, 'Day'))) STORED,
    week_number INTEGER GENERATED ALWAYS AS (EXTRACT(WEEK FROM calendar_date)) STORED,
    month_number INTEGER GENERATED ALWAYS AS (EXTRACT(MONTH FROM calendar_date)) STORED,
    month_name VARCHAR(10) GENERATED ALWAYS AS (TRIM(TO_CHAR(calendar_date, 'Month'))) STORED,
    quarter_number INTEGER GENERATED ALWAYS AS (EXTRACT(QUARTER FROM calendar_date)) STORED,
    year_number INTEGER GENERATED ALWAYS AS (EXTRACT(YEAR FROM calendar_date)) STORED,
    
    -- ISO Week (for international reporting)
    iso_week INTEGER GENERATED ALWAYS AS (EXTRACT(WEEK FROM calendar_date)) STORED,
    iso_year INTEGER GENERATED ALWAYS AS (EXTRACT(ISOYEAR FROM calendar_date)) STORED,
    
    -- Business Calendar
    is_working_day BOOLEAN DEFAULT true,
    is_weekend BOOLEAN GENERATED ALWAYS AS (EXTRACT(DOW FROM calendar_date) IN (0, 6)) STORED,
    is_holiday BOOLEAN DEFAULT false,
    holiday_name VARCHAR(200),
    holiday_type VARCHAR(50), -- 'National', 'Religious', 'Company', 'Local'
    
    -- Production Planning
    planned_working_hours DECIMAL(4, 2) DEFAULT 24.0, -- 24 hours for mining operations
    planned_shifts INTEGER DEFAULT 2,
    shift_1_hours DECIMAL(4, 2) DEFAULT 12.0,
    shift_2_hours DECIMAL(4, 2) DEFAULT 12.0,
    
    -- Weather Planning
    expected_weather VARCHAR(100),
    expected_rain_hours DECIMAL(4, 2) DEFAULT 0,
    expected_temperature_min DECIMAL(4, 1),
    expected_temperature_max DECIMAL(4, 1),
    expected_wind_speed DECIMAL(4, 1),
    
    -- Operational Notes
    special_instructions TEXT,
    maintenance_scheduled BOOLEAN DEFAULT false,
    equipment_availability_percent DECIMAL(5, 2) DEFAULT 100.0,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by VARCHAR(200) DEFAULT current_user,
    
    -- Constraints
    CHECK (planned_working_hours >= 0 AND planned_working_hours <= 24),
    CHECK (planned_shifts >= 0 AND planned_shifts <= 3),
    CHECK (shift_1_hours >= 0 AND shift_1_hours <= 24),
    CHECK (shift_2_hours >= 0 AND shift_2_hours <= 24),
    CHECK (expected_rain_hours >= 0 AND expected_rain_hours <= 24),
    CHECK (equipment_availability_percent >= 0 AND equipment_availability_percent <= 100)
);

-- =====================================================
-- PRODUCTION TARGETS BY PERIOD
-- =====================================================
CREATE TABLE production_targets_calendar (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Location and Period
    location VARCHAR(200) NOT NULL,
    target_name VARCHAR(200), -- Optional descriptive name
    
    -- Period Definition
    period_type period_type NOT NULL,
    period_start_date DATE NOT NULL,
    period_end_date DATE NOT NULL,
    
    -- Calendar References
    year_number INTEGER NOT NULL,
    month_number INTEGER, -- NULL for yearly targets
    week_number INTEGER,  -- NULL for monthly/yearly targets
    quarter_number INTEGER, -- NULL for non-quarterly targets
    
    -- Production Targets (in tons)
    target_ob DECIMAL(15, 2) DEFAULT 0,
    target_ore DECIMAL(15, 2) DEFAULT 0,
    target_total_material DECIMAL(15, 2) GENERATED ALWAYS AS (target_ob + target_ore) STORED,
    
    -- Ratio Targets
    target_sr DECIMAL(8, 4) DEFAULT 0, -- Stripping ratio target (OB:ORE)
    target_fr DECIMAL(8, 4) DEFAULT 0, -- Fuel ratio target (Liters/Ton)
    
    -- Fuel Targets (in liters)
    target_fuel DECIMAL(12, 2) DEFAULT 0,
    target_fuel_efficiency DECIMAL(8, 4) DEFAULT 0, -- Target liters per ton
    
    -- Working Parameters
    planned_working_days INTEGER,
    planned_working_hours DECIMAL(8, 2),
    planned_production_days INTEGER, -- Days with actual production
    
    -- Budget and Cost Targets
    target_fuel_cost DECIMAL(12, 2) DEFAULT 0,
    fuel_price_per_liter DECIMAL(8, 4) DEFAULT 0,
    
    -- Weather Allowances
    expected_weather_delays_hours DECIMAL(6, 2) DEFAULT 0,
    weather_contingency_percent DECIMAL(5, 2) DEFAULT 10.0,
    
    -- Status and Approval
    is_active BOOLEAN DEFAULT true,
    is_locked BOOLEAN DEFAULT false, -- Lock targets after period starts
    approved_by VARCHAR(200),
    approved_at TIMESTAMPTZ,
    approval_notes TEXT,
    
    -- Revision Tracking
    revision_number INTEGER DEFAULT 1,
    previous_version_id UUID REFERENCES production_targets_calendar(id),
    revision_reason TEXT,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by VARCHAR(200) DEFAULT current_user,
    
    -- Constraints
    UNIQUE(location, period_type, period_start_date, revision_number),
    CHECK (period_end_date >= period_start_date),
    CHECK (target_ob >= 0),
    CHECK (target_ore >= 0),
    CHECK (target_sr >= 0),
    CHECK (target_fr >= 0),
    CHECK (target_fuel >= 0),
    CHECK (planned_working_days >= 0),
    CHECK (planned_working_hours >= 0),
    CHECK (weather_contingency_percent >= 0 AND weather_contingency_percent <= 100),
    CHECK (revision_number > 0)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Production Calendar Indexes
CREATE INDEX idx_production_calendar_date ON production_calendar(calendar_date);
CREATE INDEX idx_production_calendar_year_month ON production_calendar(year_number, month_number);
CREATE INDEX idx_production_calendar_year_week ON production_calendar(year_number, week_number);
CREATE INDEX idx_production_calendar_working_day ON production_calendar(is_working_day, is_holiday);
CREATE INDEX idx_production_calendar_weekend ON production_calendar(is_weekend);

-- Production Targets Indexes
CREATE INDEX idx_production_targets_location ON production_targets_calendar(location);
CREATE INDEX idx_production_targets_period ON production_targets_calendar(period_type, period_start_date, period_end_date);
CREATE INDEX idx_production_targets_location_period ON production_targets_calendar(location, period_type, period_start_date);
CREATE INDEX idx_production_targets_year_month ON production_targets_calendar(year_number, month_number);
CREATE INDEX idx_production_targets_active ON production_targets_calendar(is_active, is_locked);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update updated_at timestamp
CREATE TRIGGER update_production_calendar_updated_at 
    BEFORE UPDATE ON production_calendar 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_production_targets_updated_at 
    BEFORE UPDATE ON production_targets_calendar 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to populate calendar for a year
CREATE OR REPLACE FUNCTION populate_production_calendar(target_year INTEGER)
RETURNS INTEGER AS $$
DECLARE
    start_date DATE := (target_year || '-01-01')::DATE;
    end_date DATE := (target_year || '-12-31')::DATE;
    current_date DATE := start_date;
    inserted_count INTEGER := 0;
BEGIN
    WHILE current_date <= end_date LOOP
        INSERT INTO production_calendar (
            calendar_date,
            is_working_day,
            is_holiday
        ) VALUES (
            current_date,
            CASE WHEN EXTRACT(DOW FROM current_date) IN (0, 6) THEN false ELSE true END,
            false
        ) ON CONFLICT (calendar_date) DO NOTHING;
        
        IF FOUND THEN
            inserted_count := inserted_count + 1;
        END IF;
        
        current_date := current_date + 1;
    END LOOP;
    
    RETURN inserted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get working days in a period
CREATE OR REPLACE FUNCTION get_working_days_in_period(start_date DATE, end_date DATE)
RETURNS INTEGER AS $$
DECLARE
    working_days INTEGER;
BEGIN
    SELECT COUNT(*) INTO working_days
    FROM production_calendar
    WHERE calendar_date BETWEEN start_date AND end_date
    AND is_working_day = true
    AND is_holiday = false;
    
    RETURN COALESCE(working_days, 0);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE production_calendar IS 'Production calendar with business days, holidays, and planning information';
COMMENT ON TABLE production_targets_calendar IS 'Production targets by different time periods with revision tracking';

COMMENT ON COLUMN production_calendar.iso_week IS 'ISO week number for international reporting standards';
COMMENT ON COLUMN production_calendar.planned_working_hours IS 'Planned working hours per day (typically 24 for mining)';
COMMENT ON COLUMN production_targets_calendar.target_sr IS 'Target stripping ratio (Overburden:Ore)';
COMMENT ON COLUMN production_targets_calendar.target_fr IS 'Target fuel ratio (Liters per ton of material)';
COMMENT ON COLUMN production_targets_calendar.revision_number IS 'Version number for target revisions';

COMMENT ON FUNCTION populate_production_calendar(INTEGER) IS 'Populate calendar table for a specific year';
COMMENT ON FUNCTION get_working_days_in_period(DATE, DATE) IS 'Get count of working days in a date range';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('002', 'Production calendar and targets tables')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Production calendar setup completed successfully';
    RAISE NOTICE 'Tables created: production_calendar, production_targets_calendar';
    RAISE NOTICE 'Utility functions: populate_production_calendar, get_working_days_in_period';
    RAISE NOTICE 'Use SELECT populate_production_calendar(2024) to populate calendar for 2024';
END $$;
