// @ts-nocheck
import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import DashboardScreen from '../screens/DashboardScreen.new';
import ProductionOverviewScreen from '../screens/ProductionOverviewScreen';
import SAPIntegrationScreen from '../screens/SAPIntegrationScreen';
import AttendanceScreen from '../screens/AttendanceScreen';
import AnalyticsScreen from '../screens/AnalyticsScreen';

export type DashboardStackParamList = {
  DashboardMain: undefined;
  ProductionOverview: undefined;
  SAPIntegration: undefined;
  Attendance: undefined;
  Analytics: undefined;
};

const Stack = createStackNavigator<DashboardStackParamList>();

const DashboardStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="DashboardMain"
        component={DashboardScreen}
      />
      <Stack.Screen
        name="ProductionOverview"
        component={ProductionOverviewScreen}
      />
      <Stack.Screen
        name="SAPIntegration"
        component={SAPIntegrationScreen}
      />
      <Stack.Screen
        name="Attendance"
        component={AttendanceScreen}
      />
      <Stack.Screen
        name="Analytics"
        component={AnalyticsScreen}
      />
    </Stack.Navigator>
  );
};

export default DashboardStackNavigator;
