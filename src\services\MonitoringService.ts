import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

// Types for monitoring
export interface ErrorEvent {
  id: string;
  timestamp: number;
  error: Error;
  context?: string;
  userId?: string;
  screen?: string;
  action?: string;
  deviceInfo: DeviceInfo;
  networkInfo: NetworkInfo;
}

export interface PerformanceEvent {
  id: string;
  timestamp: number;
  eventType: 'screen_load' | 'api_call' | 'cache_hit' | 'cache_miss' | 'user_action';
  duration: number;
  screen?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface DeviceInfo {
  platform: string;
  version: string;
  model?: string;
  memory?: number;
}

export interface NetworkInfo {
  isConnected: boolean;
  type: string;
  strength?: number;
}

export interface AnalyticsEvent {
  id: string;
  timestamp: number;
  eventName: string;
  properties: Record<string, any>;
  userId?: string;
  sessionId: string;
}

export class MonitoringService {
  private static instance: MonitoringService;
  private sessionId: string;
  private userId?: string;
  private deviceInfo: DeviceInfo;
  private networkInfo: NetworkInfo = { isConnected: true, type: 'unknown' };
  private performanceMarks: Map<string, number> = new Map();

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.deviceInfo = this.getDeviceInfo();
    this.initializeNetworkMonitoring();
  }

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  // Initialize monitoring
  async initialize(userId?: string): Promise<void> {
    this.userId = userId;
    console.log('📊 Monitoring service initialized');
    
    // Track app start
    this.trackEvent('app_start', {
      platform: Platform.OS,
      version: Platform.Version,
      sessionId: this.sessionId
    });
  }

  // Error Monitoring
  async trackError(error: Error, context?: string, metadata?: Record<string, any>): Promise<void> {
    const errorEvent: ErrorEvent = {
      id: this.generateId(),
      timestamp: Date.now(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      } as Error,
      context,
      userId: this.userId,
      deviceInfo: this.deviceInfo,
      networkInfo: this.networkInfo
    };

    // Store locally
    await this.storeEvent('errors', errorEvent);

    // Log to console in development
    if (__DEV__) {
      console.error('🚨 Error tracked:', errorEvent);
    }

    // Send to monitoring service (implement based on your choice)
    this.sendToMonitoringService('error', errorEvent);
  }

  // Performance Monitoring
  startPerformanceTimer(key: string): void {
    this.performanceMarks.set(key, Date.now());
  }

  endPerformanceTimer(key: string, eventType: PerformanceEvent['eventType'], metadata?: Record<string, any>): void {
    const startTime = this.performanceMarks.get(key);
    if (!startTime) return;

    const duration = Date.now() - startTime;
    this.performanceMarks.delete(key);

    const performanceEvent: PerformanceEvent = {
      id: this.generateId(),
      timestamp: Date.now(),
      eventType,
      duration,
      metadata
    };

    this.storeEvent('performance', performanceEvent);

    if (__DEV__) {
      console.log(`⏱️ Performance: ${key} took ${duration}ms`);
    }
  }

  // Analytics Tracking
  async trackEvent(eventName: string, properties: Record<string, any> = {}): Promise<void> {
    const analyticsEvent: AnalyticsEvent = {
      id: this.generateId(),
      timestamp: Date.now(),
      eventName,
      properties: {
        ...properties,
        platform: Platform.OS,
        networkType: this.networkInfo.type,
        isOnline: this.networkInfo.isConnected
      },
      userId: this.userId,
      sessionId: this.sessionId
    };

    await this.storeEvent('analytics', analyticsEvent);

    if (__DEV__) {
      console.log('📈 Event tracked:', eventName, properties);
    }

    // Send to analytics service
    this.sendToAnalyticsService(analyticsEvent);
  }

  // Screen Tracking
  async trackScreenView(screenName: string, metadata?: Record<string, any>): Promise<void> {
    this.startPerformanceTimer(`screen_${screenName}`);
    
    await this.trackEvent('screen_view', {
      screen_name: screenName,
      ...metadata
    });
  }

  async trackScreenExit(screenName: string): Promise<void> {
    this.endPerformanceTimer(`screen_${screenName}`, 'screen_load');
  }

  // Mining-specific tracking
  async trackProductionAction(action: string, data: Record<string, any>): Promise<void> {
    await this.trackEvent('production_action', {
      action,
      ...data,
      timestamp: Date.now()
    });
  }

  async trackSafetyEvent(eventType: string, severity: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackEvent('safety_event', {
      event_type: eventType,
      severity,
      ...metadata
    });
  }

  async trackEquipmentEvent(equipmentId: string, eventType: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackEvent('equipment_event', {
      equipment_id: equipmentId,
      event_type: eventType,
      ...metadata
    });
  }

  // Cache Performance Tracking
  async trackCacheEvent(eventType: 'hit' | 'miss' | 'set' | 'invalidate', key: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackEvent('cache_event', {
      cache_event_type: eventType,
      cache_key: key,
      ...metadata
    });
  }

  // Network Performance
  async trackNetworkRequest(url: string, method: string, duration: number, status: number): Promise<void> {
    await this.trackEvent('network_request', {
      url,
      method,
      duration,
      status,
      network_type: this.networkInfo.type,
      is_online: this.networkInfo.isConnected
    });
  }

  // User Behavior Tracking
  async trackUserAction(action: string, screen: string, metadata?: Record<string, any>): Promise<void> {
    await this.trackEvent('user_action', {
      action,
      screen,
      ...metadata
    });
  }

  // Data Export for Analysis
  async exportMonitoringData(): Promise<{
    errors: ErrorEvent[];
    performance: PerformanceEvent[];
    analytics: AnalyticsEvent[];
  }> {
    const [errors, performance, analytics] = await Promise.all([
      this.getStoredEvents('errors'),
      this.getStoredEvents('performance'),
      this.getStoredEvents('analytics')
    ]);

    return { errors, performance, analytics };
  }

  // Cleanup old data
  async cleanupOldData(daysToKeep: number = 30): Promise<void> {
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    
    for (const eventType of ['errors', 'performance', 'analytics']) {
      const events = await this.getStoredEvents(eventType);
      const filteredEvents = events.filter(event => event.timestamp > cutoffTime);
      await AsyncStorage.setItem(`monitoring_${eventType}`, JSON.stringify(filteredEvents));
    }

    console.log(`🧹 Cleaned up monitoring data older than ${daysToKeep} days`);
  }

  // Private methods
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private getDeviceInfo(): DeviceInfo {
    return {
      platform: Platform.OS,
      version: Platform.Version.toString(),
      // Add more device info as needed
    };
  }

  private async initializeNetworkMonitoring(): Promise<void> {
    try {
      const state = await NetInfo.fetch();
      this.networkInfo = {
        isConnected: state.isConnected ?? false,
        type: state.type,
        strength: (state.details as any)?.strength || 0
      };

      NetInfo.addEventListener(state => {
        this.networkInfo = {
          isConnected: state.isConnected ?? false,
          type: state.type,
          strength: (state.details as any)?.strength || 0
        };
      });
    } catch (error) {
      console.error('Failed to initialize network monitoring:', error);
    }
  }

  private async storeEvent(eventType: string, event: any): Promise<void> {
    try {
      const key = `monitoring_${eventType}`;
      const existing = await AsyncStorage.getItem(key);
      const events = existing ? JSON.parse(existing) : [];
      
      events.push(event);
      
      // Keep only last 1000 events per type
      if (events.length > 1000) {
        events.splice(0, events.length - 1000);
      }
      
      await AsyncStorage.setItem(key, JSON.stringify(events));
    } catch (error) {
      console.error(`Failed to store ${eventType} event:`, error);
    }
  }

  private async getStoredEvents(eventType: string): Promise<any[]> {
    try {
      const key = `monitoring_${eventType}`;
      const stored = await AsyncStorage.getItem(key);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error(`Failed to get ${eventType} events:`, error);
      return [];
    }
  }

  private sendToMonitoringService(type: string, data: any): void {
    // Implement based on your monitoring service choice
    // Examples: Sentry, Bugsnag, Firebase Crashlytics
    
    if (__DEV__) {
      console.log(`📤 Would send ${type} to monitoring service:`, data);
    }
    
    // Example for Sentry:
    // Sentry.captureException(data.error, {
    //   extra: data,
    //   tags: { context: data.context }
    // });
  }

  private sendToAnalyticsService(event: AnalyticsEvent): void {
    // Implement based on your analytics service choice
    // Examples: Firebase Analytics, Mixpanel, Amplitude
    
    if (__DEV__) {
      console.log('📤 Would send to analytics service:', event);
    }
    
    // Example for Firebase Analytics:
    // analytics().logEvent(event.eventName, event.properties);
  }
}

export default MonitoringService;
