# Styling Documentation

## Cortex 7 Metadata
- **Document Type**: Design System Guide
- **Component**: Styling and Design System
- **Technology**: React Native StyleSheet, Design Tokens, Theme System
- **Tags**: `#styling` `#design-system` `#colors` `#layout` `#typography`
- **Last Updated**: 2025-01-19
- **Status**: Active ✅
- **Source**: Consolidated from memory-bank/styling

## Overview
Comprehensive design system and styling guide for the MiningOperationsApp, including color palette, typography, layout constants, and component styling patterns.

## Design System Architecture

### Design Tokens Structure
```
Constants/
├── Colors.ts          # Color palette and theme
├── Layout.ts          # Spacing, sizing, and layout
├── Typography.ts      # Font sizes, weights, and styles
└── Theme.ts           # Theme configuration and variants
```

## Color System

### Primary Color Palette
```typescript
export const Colors = {
  // Primary colors
  primary: '#2563EB',           // Blue - Main brand color
  primaryLight: '#3B82F6',      // Light blue
  primaryDark: '#1D4ED8',       // Dark blue
  
  // Secondary colors
  secondary: '#64748B',         // Slate gray
  secondaryLight: '#94A3B8',    // Light slate
  secondaryDark: '#475569',     // Dark slate
  
  // Accent colors
  accent: '#F59E0B',            // Amber - Highlights and CTAs
  accentLight: '#FCD34D',       // Light amber
  accentDark: '#D97706',        // Dark amber
  
  // Status colors
  success: '#10B981',           // Green - Success states
  warning: '#F59E0B',           // Amber - Warning states
  error: '#EF4444',             // Red - Error states
  info: '#3B82F6',              // Blue - Info states
  
  // Neutral colors
  background: '#F8FAFC',        // Light gray - App background
  surface: '#FFFFFF',           // White - Card/surface background
  border: '#E2E8F0',            // Light gray - Borders
  
  // Text colors
  textPrimary: '#1E293B',       // Dark gray - Primary text
  textSecondary: '#64748B',     // Medium gray - Secondary text
  textTertiary: '#94A3B8',      // Light gray - Tertiary text
  textInverse: '#FFFFFF',       // White - Text on dark backgrounds
  
  // Chart colors
  chartPrimary: '#2563EB',      // Blue - Primary chart color
  chartSecondary: '#F59E0B',    // Amber - Secondary chart color
  chartTertiary: '#10B981',     // Green - Tertiary chart color
  chartQuaternary: '#EF4444',   // Red - Quaternary chart color
};
```

### Dark Theme Support
```typescript
export const DarkColors = {
  // Primary colors (same as light)
  primary: '#3B82F6',
  primaryLight: '#60A5FA',
  primaryDark: '#2563EB',
  
  // Background colors (inverted)
  background: '#0F172A',        // Dark blue-gray
  surface: '#1E293B',           // Medium dark
  border: '#334155',            // Medium border
  
  // Text colors (inverted)
  textPrimary: '#F1F5F9',       // Light gray
  textSecondary: '#CBD5E1',     // Medium light
  textTertiary: '#94A3B8',      // Medium gray
  textInverse: '#1E293B',       // Dark on light
};
```

## Typography System

### Font Configuration
```typescript
export const Typography = {
  // Font families
  fontFamily: {
    regular: 'System',          // System default
    medium: 'System',           // System medium
    bold: 'System',             // System bold
    mono: 'Courier',            // Monospace for code
  },
  
  // Font sizes
  fontSize: {
    xs: 12,                     // Extra small
    sm: 14,                     // Small
    md: 16,                     // Medium (base)
    lg: 18,                     // Large
    xl: 20,                     // Extra large
    '2xl': 24,                  // 2X large
    '3xl': 30,                  // 3X large
    '4xl': 36,                  // 4X large
  },
  
  // Font weights
  fontWeight: {
    normal: '400',              // Regular
    medium: '500',              // Medium
    semibold: '600',            // Semi-bold
    bold: '700',                // Bold
  },
  
  // Line heights
  lineHeight: {
    tight: 1.2,                 // Tight spacing
    normal: 1.4,                // Normal spacing
    relaxed: 1.6,               // Relaxed spacing
    loose: 1.8,                 // Loose spacing
  },
};
```

### Typography Styles
```typescript
export const TextStyles = StyleSheet.create({
  // Headings
  h1: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize['3xl'] * Typography.lineHeight.tight,
    color: Colors.textPrimary,
  },
  h2: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize['2xl'] * Typography.lineHeight.tight,
    color: Colors.textPrimary,
  },
  h3: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.xl * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  
  // Body text
  body: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: Typography.fontSize.md * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  bodySmall: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
    color: Colors.textSecondary,
  },
  
  // Labels and captions
  label: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
    color: Colors.textPrimary,
  },
  caption: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: Typography.fontSize.xs * Typography.lineHeight.normal,
    color: Colors.textTertiary,
  },
});
```

## Layout System

### Spacing Scale
```typescript
export const Layout = {
  // Spacing scale (based on 4px grid)
  spacing: {
    xs: 4,                      // Extra small
    sm: 8,                      // Small
    md: 16,                     // Medium
    lg: 24,                     // Large
    xl: 32,                     // Extra large
    '2xl': 48,                  // 2X large
    '3xl': 64,                  // 3X large
  },
  
  // Border radius
  borderRadius: {
    none: 0,                    // No radius
    sm: 4,                      // Small radius
    md: 8,                      // Medium radius
    lg: 12,                     // Large radius
    xl: 16,                     // Extra large radius
    full: 9999,                 // Fully rounded
  },
  
  // Shadows
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  
  // Screen dimensions
  screen: {
    padding: 16,                // Default screen padding
    headerHeight: 60,           // Header height
    tabBarHeight: 60,           // Tab bar height
  },
};
```

### Layout Components
```typescript
export const LayoutStyles = StyleSheet.create({
  // Containers
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: Layout.spacing.md,
  },
  safeContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  
  // Cards and surfaces
  card: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.md,
    ...Layout.shadow,
  },
  surface: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
  },
  
  // Flex layouts
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  
  // Spacing utilities
  marginTop: {
    marginTop: Layout.spacing.md,
  },
  marginBottom: {
    marginBottom: Layout.spacing.md,
  },
  paddingHorizontal: {
    paddingHorizontal: Layout.spacing.md,
  },
  paddingVertical: {
    paddingVertical: Layout.spacing.md,
  },
});
```

## Component Styling Patterns

### Button Styles
```typescript
export const ButtonStyles = StyleSheet.create({
  // Base button
  button: {
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,              // Minimum touch target
  },
  
  // Button variants
  primary: {
    backgroundColor: Colors.primary,
  },
  secondary: {
    backgroundColor: Colors.secondary,
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  
  // Button text
  buttonText: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
  },
  primaryText: {
    color: Colors.textInverse,
  },
  secondaryText: {
    color: Colors.textInverse,
  },
  outlineText: {
    color: Colors.primary,
  },
  
  // Button states
  disabled: {
    opacity: 0.5,
  },
  loading: {
    opacity: 0.7,
  },
});
```

### Input Styles
```typescript
export const InputStyles = StyleSheet.create({
  // Input container
  inputContainer: {
    marginBottom: Layout.spacing.md,
  },
  
  // Label
  label: {
    ...TextStyles.label,
    marginBottom: Layout.spacing.xs,
  },
  
  // Input field
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.sm,
    fontSize: Typography.fontSize.md,
    color: Colors.textPrimary,
    backgroundColor: Colors.surface,
    minHeight: 44,
  },
  
  // Input states
  inputFocused: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  inputError: {
    borderColor: Colors.error,
  },
  
  // Error text
  errorText: {
    ...TextStyles.caption,
    color: Colors.error,
    marginTop: Layout.spacing.xs,
  },
});
```

### Chart Styles
```typescript
export const ChartStyles = StyleSheet.create({
  // Chart container
  chartContainer: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.sm,
    marginHorizontal: Layout.spacing.md,
    ...Layout.shadow,
  },
  
  // Chart wrapper
  chart: {
    borderRadius: Layout.borderRadius.lg,
  },
  
  // Scrollable chart
  scrollableChartContainer: {
    position: 'relative',
  },
  chartScrollView: {
    flexGrow: 0,
  },
  chartScrollContent: {
    paddingRight: Layout.spacing.md,
  },
  
  // Chart indicators
  scrollIndicator: {
    marginTop: Layout.spacing.xs,
    paddingHorizontal: Layout.spacing.sm,
    alignItems: 'center',
  },
  scrollIndicatorText: {
    ...TextStyles.caption,
    fontStyle: 'italic',
  },
  
  // Chart tabs
  chartTab: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
    marginHorizontal: Layout.spacing.xs,
  },
  chartTabSelected: {
    backgroundColor: Colors.primary,
  },
  chartTabText: {
    ...TextStyles.label,
    color: Colors.textSecondary,
  },
  chartTabTextSelected: {
    color: Colors.textInverse,
  },
});
```

## Theme System

### Theme Configuration
```typescript
export interface Theme {
  colors: typeof Colors;
  typography: typeof Typography;
  layout: typeof Layout;
  isDark: boolean;
}

export const LightTheme: Theme = {
  colors: Colors,
  typography: Typography,
  layout: Layout,
  isDark: false,
};

export const DarkTheme: Theme = {
  colors: { ...Colors, ...DarkColors },
  typography: Typography,
  layout: Layout,
  isDark: true,
};
```

### Theme Context
```typescript
import React, { createContext, useContext, useState } from 'react';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isDark, setIsDark] = useState(false);
  
  const theme = isDark ? DarkTheme : LightTheme;
  
  const toggleTheme = () => {
    setIsDark(!isDark);
  };
  
  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, isDark }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
```

### Responsive Design
```typescript
import { Dimensions } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const Breakpoints = {
  small: 320,
  medium: 768,
  large: 1024,
};

export const useResponsiveStyle = () => {
  const isSmall = screenWidth < Breakpoints.medium;
  const isMedium = screenWidth >= Breakpoints.medium && screenWidth < Breakpoints.large;
  const isLarge = screenWidth >= Breakpoints.large;
  
  return {
    isSmall,
    isMedium,
    isLarge,
    screenWidth,
    screenHeight,
  };
};

// Responsive style helper
export const responsive = (styles: {
  small?: any;
  medium?: any;
  large?: any;
}) => {
  if (screenWidth < Breakpoints.medium) {
    return styles.small || {};
  } else if (screenWidth < Breakpoints.large) {
    return styles.medium || {};
  } else {
    return styles.large || {};
  }
};
```

---
*Styling documentation following Cortex 7 standards for comprehensive design system reference.*
