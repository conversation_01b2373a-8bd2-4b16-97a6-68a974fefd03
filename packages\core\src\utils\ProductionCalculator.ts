import { ProductionMetric } from '../types';

/**
 * Production calculation utilities
 */
export class ProductionCalculator {
  /**
   * Calculate strip ratio (overburden / ore)
   */
  static calculateStripRatio(overburden: number, ore: number): number {
    if (ore <= 0) return 0;
    return Number((overburden / ore).toFixed(2));
  }

  /**
   * Calculate achievement percentage (actual / plan * 100)
   */
  static calculateAchievement(actual: number, plan: number): number {
    if (plan <= 0) return 0;
    return Number(((actual / plan) * 100).toFixed(1));
  }

  /**
   * Calculate efficiency percentage (plan / actual * 100)
   * Used for metrics where lower actual values are better (e.g., fuel consumption)
   */
  static calculateEfficiency(actual: number, plan: number): number {
    if (actual <= 0) return 0;
    return Number(((plan / actual) * 100).toFixed(1));
  }

  /**
   * Calculate variance (actual - plan)
   */
  static calculateVariance(actual: number, plan: number): number {
    return Number((actual - plan).toFixed(2));
  }

  /**
   * Calculate variance percentage ((actual - plan) / plan * 100)
   */
  static calculateVariancePercentage(actual: number, plan: number): number {
    if (plan <= 0) return 0;
    return Number((((actual - plan) / plan) * 100).toFixed(1));
  }

  /**
   * Calculate production summary for a set of metrics
   */
  static calculateSummary(metrics: ProductionMetric[]): {
    totalOb: number;
    totalOre: number;
    averageStripRatio: number;
    totalFuel: number;
    achievementPercentage: number;
    recordCount: number;
  } {
    if (metrics.length === 0) {
      return {
        totalOb: 0,
        totalOre: 0,
        averageStripRatio: 0,
        totalFuel: 0,
        achievementPercentage: 0,
        recordCount: 0
      };
    }

    const totals = metrics.reduce(
      (acc, metric) => ({
        actualOb: acc.actualOb + (metric.actual_ob || 0),
        planOb: acc.planOb + (metric.plan_ob || 0),
        actualOre: acc.actualOre + (metric.actual_ore || 0),
        planOre: acc.planOre + (metric.plan_ore || 0),
        actualFuel: acc.actualFuel + (metric.actual_fuel || 0),
        planFuel: acc.planFuel + (metric.plan_fuel || 0)
      }),
      {
        actualOb: 0,
        planOb: 0,
        actualOre: 0,
        planOre: 0,
        actualFuel: 0,
        planFuel: 0
      }
    );

    const averageStripRatio = this.calculateStripRatio(totals.actualOb, totals.actualOre);
    const totalProduction = totals.actualOb + totals.actualOre;
    const totalPlan = totals.planOb + totals.planOre;
    const achievementPercentage = this.calculateAchievement(totalProduction, totalPlan);

    return {
      totalOb: totals.actualOb,
      totalOre: totals.actualOre,
      averageStripRatio,
      totalFuel: totals.actualFuel,
      achievementPercentage,
      recordCount: metrics.length
    };
  }

  /**
   * Calculate production trends for charting
   */
  static calculateTrends(
    metrics: ProductionMetric[],
    groupBy: 'daily' | 'weekly' | 'monthly'
  ): {
    labels: string[];
    obData: number[];
    oreData: number[];
    stripRatioData: number[];
    fuelData: number[];
  } {
    if (metrics.length === 0) {
      return {
        labels: [],
        obData: [],
        oreData: [],
        stripRatioData: [],
        fuelData: []
      };
    }

    // Sort metrics by date
    const sortedMetrics = [...metrics].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    let groupedData: Map<string, ProductionMetric[]>;

    switch (groupBy) {
      case 'daily':
        groupedData = this.groupByDaily(sortedMetrics);
        break;
      case 'weekly':
        groupedData = this.groupByWeekly(sortedMetrics);
        break;
      case 'monthly':
        groupedData = this.groupByMonthly(sortedMetrics);
        break;
      default:
        throw new Error(`Unsupported groupBy value: ${groupBy}`);
    }

    const labels: string[] = [];
    const obData: number[] = [];
    const oreData: number[] = [];
    const stripRatioData: number[] = [];
    const fuelData: number[] = [];

    for (const [label, groupMetrics] of groupedData) {
      const summary = this.calculateSummary(groupMetrics);
      
      labels.push(label);
      obData.push(summary.totalOb);
      oreData.push(summary.totalOre);
      stripRatioData.push(summary.averageStripRatio);
      fuelData.push(summary.totalFuel);
    }

    return {
      labels,
      obData,
      oreData,
      stripRatioData,
      fuelData
    };
  }

  /**
   * Group metrics by daily
   */
  private static groupByDaily(metrics: ProductionMetric[]): Map<string, ProductionMetric[]> {
    const grouped = new Map<string, ProductionMetric[]>();

    for (const metric of metrics) {
      const date = new Date(metric.date);
      const label = date.toISOString().split('T')[0]; // YYYY-MM-DD format

      if (!grouped.has(label)) {
        grouped.set(label, []);
      }
      grouped.get(label)!.push(metric);
    }

    return grouped;
  }

  /**
   * Group metrics by weekly
   */
  private static groupByWeekly(metrics: ProductionMetric[]): Map<string, ProductionMetric[]> {
    const grouped = new Map<string, ProductionMetric[]>();

    for (const metric of metrics) {
      const date = new Date(metric.date);
      const year = date.getFullYear();
      const week = this.getWeekNumber(date);
      const label = `${year}-W${week.toString().padStart(2, '0')}`;

      if (!grouped.has(label)) {
        grouped.set(label, []);
      }
      grouped.get(label)!.push(metric);
    }

    return grouped;
  }

  /**
   * Group metrics by monthly
   */
  private static groupByMonthly(metrics: ProductionMetric[]): Map<string, ProductionMetric[]> {
    const grouped = new Map<string, ProductionMetric[]>();

    for (const metric of metrics) {
      const date = new Date(metric.date);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const label = `${year}-${month.toString().padStart(2, '0')}`;

      if (!grouped.has(label)) {
        grouped.set(label, []);
      }
      grouped.get(label)!.push(metric);
    }

    return grouped;
  }

  /**
   * Get week number for a date
   */
  private static getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  }

  /**
   * Calculate moving average
   */
  static calculateMovingAverage(values: number[], windowSize: number): number[] {
    if (windowSize <= 0 || windowSize > values.length) {
      return values;
    }

    const result: number[] = [];
    
    for (let i = 0; i < values.length; i++) {
      const start = Math.max(0, i - windowSize + 1);
      const end = i + 1;
      const window = values.slice(start, end);
      const average = window.reduce((sum, val) => sum + val, 0) / window.length;
      result.push(Number(average.toFixed(2)));
    }

    return result;
  }

  /**
   * Calculate trend direction and percentage change
   */
  static calculateTrend(current: number, previous: number): {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
  } {
    if (previous === 0) {
      return { direction: 'stable', percentage: 0 };
    }

    const change = ((current - previous) / previous) * 100;
    const percentage = Number(Math.abs(change).toFixed(1));

    let direction: 'up' | 'down' | 'stable';
    if (Math.abs(change) < 0.1) {
      direction = 'stable';
    } else if (change > 0) {
      direction = 'up';
    } else {
      direction = 'down';
    }

    return { direction, percentage };
  }

  /**
   * Calculate production rate (tons per hour)
   */
  static calculateProductionRate(tonnage: number, hours: number): number {
    if (hours <= 0) return 0;
    return Number((tonnage / hours).toFixed(2));
  }

  /**
   * Calculate fuel efficiency (liters per ton) - DEPRECATED
   * Use calculateFuelRatio for standardized mining calculations
   */
  static calculateFuelEfficiency(fuel: number, tonnage: number): number {
    if (tonnage <= 0) return 0;
    return Number((fuel / tonnage).toFixed(2));
  }

  /**
   * Calculate fuel ratio using mining industry standard formula
   * FR = Fuel (L) / (OB (Bcm) + (Ore (tons) / 3.39))
   * Unit: L/Bcm
   */
  static calculateFuelRatio(fuel: number, overburden: number, ore: number): number {
    const totalMaterial = overburden + (ore / 3.39);
    if (totalMaterial <= 0) return 0;
    return Number((fuel / totalMaterial).toFixed(4));
  }

  /**
   * Calculate total material moved in Bcm
   * Total Material = OB (Bcm) + (Ore (tons) / 3.39)
   */
  static calculateTotalMaterial(overburden: number, ore: number): number {
    return Number((overburden + (ore / 3.39)).toFixed(2));
  }

  /**
   * Calculate equipment utilization percentage
   */
  static calculateUtilization(operatingHours: number, availableHours: number): number {
    if (availableHours <= 0) return 0;
    return Number(((operatingHours / availableHours) * 100).toFixed(1));
  }
}
