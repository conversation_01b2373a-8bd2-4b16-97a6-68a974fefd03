{"expo": {"name": "Mining Operations", "slug": "mining-operations-app", "version": "1.0.0", "orientation": "portrait", "main": "index.ts", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#1A365D"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "statusBarStyle": "light", "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to take profile pictures.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select profile pictures."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#1A365D"}, "edgeToEdgeEnabled": true, "statusBarStyle": "light", "statusBarBackgroundColor": "#1A365D", "navigationBarStyle": "light", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "navigationBarHidden": false, "softwareKeyboardLayoutMode": "pan"}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": [["expo-screen-orientation", {"initialOrientation": "PORTRAIT_UP"}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your team.", "cameraPermission": "The app accesses your camera to let you take profile pictures."}], "expo-sqlite"]}}