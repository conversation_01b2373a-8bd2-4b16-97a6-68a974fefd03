#!/usr/bin/env node

/**
 * Documentation Content Checker
 * Checks for broken links, missing content, and consistency
 */

const fs = require('fs');
const path = require('path');

const DOCS_DIR = path.join(__dirname, '..', 'docs');

console.log('🔍 CHECKING DOCUMENTATION CONTENT...\n');

let issues = [];
let suggestions = [];

// Helper function to find all .md files
const findMarkdownFiles = (dir, files = []) => {
  const items = fs.readdirSync(dir);
  
  items.forEach(item => {
    const itemPath = path.join(dir, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      findMarkdownFiles(itemPath, files);
    } else if (item.endsWith('.md')) {
      files.push(itemPath);
    }
  });
  
  return files;
};

// Check for broken internal links
const checkInternalLinks = (filePath, content) => {
  const relativePath = path.relative(DOCS_DIR, filePath);
  const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  let match;
  
  while ((match = linkRegex.exec(content)) !== null) {
    const linkText = match[1];
    const linkPath = match[2];
    
    // Skip external links
    if (linkPath.startsWith('http') || linkPath.startsWith('mailto:')) {
      continue;
    }
    
    // Skip anchors
    if (linkPath.startsWith('#')) {
      continue;
    }
    
    // Resolve relative path
    const fileDir = path.dirname(filePath);
    const targetPath = path.resolve(fileDir, linkPath);
    
    if (!fs.existsSync(targetPath)) {
      issues.push(`❌ Broken link in ${relativePath}: "${linkText}" → ${linkPath}`);
    }
  }
};

// Check for required sections in setup.md
const checkSetupGuide = (filePath, content) => {
  const requiredSections = [
    'Quick Start',
    'Prerequisites',
    'Installation',
    'Environment',
    'Database',
    'Testing',
    'Troubleshooting'
  ];
  
  const missingSections = requiredSections.filter(section => 
    !content.toLowerCase().includes(section.toLowerCase())
  );
  
  if (missingSections.length > 0) {
    suggestions.push(`💡 development/setup.md missing sections: ${missingSections.join(', ')}`);
  }
};

// Check for outdated content
const checkForOutdatedContent = (filePath, content) => {
  const relativePath = path.relative(DOCS_DIR, filePath);
  
  // Check for old dates
  const dateRegex = /202[0-3]/g;
  const dates = content.match(dateRegex) || [];
  const oldDates = dates.filter(date => parseInt(date) < 2024);
  
  if (oldDates.length > 0) {
    suggestions.push(`📅 ${relativePath} may have outdated dates: ${oldDates.join(', ')}`);
  }
  
  // Check for deprecated terms
  const deprecatedTerms = [
    'React Native CLI',
    'Metro bundler',
    'old formula',
    'previous version'
  ];
  
  deprecatedTerms.forEach(term => {
    if (content.toLowerCase().includes(term.toLowerCase())) {
      suggestions.push(`🔄 ${relativePath} mentions potentially outdated term: "${term}"`);
    }
  });
};

// Check index.md completeness
const checkIndexCompleteness = (content) => {
  const requiredSections = [
    'For Developers',
    'For Designers', 
    'For Project Managers',
    'Documentation Structure'
  ];
  
  const missingSections = requiredSections.filter(section => 
    !content.includes(section)
  );
  
  if (missingSections.length > 0) {
    issues.push(`❌ index.md missing sections: ${missingSections.join(', ')}`);
  }
};

// Main checking logic
const markdownFiles = findMarkdownFiles(DOCS_DIR);

console.log(`📄 Found ${markdownFiles.length} markdown files\n`);

markdownFiles.forEach(filePath => {
  const relativePath = path.relative(DOCS_DIR, filePath);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check for broken links
  checkInternalLinks(filePath, content);
  
  // Special checks for specific files
  if (relativePath === 'development/setup.md') {
    checkSetupGuide(filePath, content);
  }
  
  if (relativePath === 'index.md') {
    checkIndexCompleteness(content);
  }
  
  // Check for outdated content
  checkForOutdatedContent(filePath, content);
  
  // Check for empty files
  if (content.trim().length < 100) {
    issues.push(`❌ ${relativePath} appears to be empty or too short`);
  }
  
  // Check for proper headers
  if (!content.startsWith('#')) {
    suggestions.push(`💡 ${relativePath} should start with a header`);
  }
});

// Check for AI Agent Rules compliance
const rulesFile = path.join(DOCS_DIR, 'AI_AGENT_DOCUMENTATION_RULES.md');
if (!fs.existsSync(rulesFile)) {
  issues.push('❌ AI_AGENT_DOCUMENTATION_RULES.md not found');
} else {
  const rulesContent = fs.readFileSync(rulesFile, 'utf8');
  if (!rulesContent.includes('FOLDER STRUCTURE')) {
    issues.push('❌ AI_AGENT_DOCUMENTATION_RULES.md missing folder structure');
  }
}

// Report results
console.log('📊 DOCUMENTATION CHECK RESULTS:\n');

if (issues.length === 0 && suggestions.length === 0) {
  console.log('✅ DOCUMENTATION IS HEALTHY!');
  console.log('   No broken links found');
  console.log('   All required sections present');
  console.log('   Content appears up-to-date');
} else {
  if (issues.length > 0) {
    console.log('🚨 ISSUES FOUND:');
    issues.forEach(issue => console.log(`   ${issue}`));
    console.log('');
  }
  
  if (suggestions.length > 0) {
    console.log('💡 SUGGESTIONS:');
    suggestions.forEach(suggestion => console.log(`   ${suggestion}`));
    console.log('');
  }
}

console.log('📈 STATISTICS:');
console.log(`   Total files: ${markdownFiles.length}`);
console.log(`   Issues found: ${issues.length}`);
console.log(`   Suggestions: ${suggestions.length}`);

console.log('\n🎯 RECOMMENDATIONS:');
console.log('   - Fix broken links immediately');
console.log('   - Update outdated content regularly');
console.log('   - Keep index.md synchronized with structure');
console.log('   - Follow AI Agent Documentation Rules');

if (issues.length > 0) {
  console.log('\n❌ CHECK FAILED - Fix issues above');
  process.exit(1);
} else {
  console.log('\n✅ CHECK PASSED');
  process.exit(0);
}
