# Navigation Documentation

## Cortex 7 Metadata
- **Document Type**: Navigation Guide
- **Component**: Navigation System and Routing
- **Technology**: React Navigation 6, TypeScript, Tab/Stack Navigation
- **Tags**: `#navigation` `#routing` `#react-navigation` `#mobile-navigation`
- **Last Updated**: 2025-01-19
- **Status**: Active ✅
- **Source**: Consolidated from memory-bank/navigation

## Overview
Comprehensive navigation system documentation for the MiningOperationsApp, including tab navigation, stack navigation, and routing patterns.

## Navigation Architecture

### Navigation Structure
```
App
├── AuthNavigator (Stack)
│   ├── LoginScreen
│   ├── RegisterScreen
│   └── ForgotPasswordScreen
└── MainNavigator (Tab)
    ├── ProductionTab (Stack)
    │   ├── ProductionOverviewScreen
    │   ├── ProductionDetailsScreen
    │   └── ProductionReportsScreen
    ├── EquipmentTab (Stack)
    │   ├── EquipmentManagementScreen
    │   ├── EquipmentDetailsScreen
    │   └── MaintenanceScheduleScreen
    ├── SafetyTab (Stack)
    │   ├── SafetyReportingScreen
    │   ├── SafetyDashboardScreen
    │   └── SafetyTrainingScreen
    └── SettingsTab (Stack)
        ├── SettingsScreen
        ├── ProfileScreen
        └── NotificationScreen
```

## Navigation Implementation

### Root Navigator
```typescript
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuth } from '../contexts/AuthContext';

const Stack = createNativeStackNavigator();

const RootNavigator = () => {
  const { isAuthenticated } = useAuth();

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <Stack.Screen name="Main" component={MainNavigator} />
        ) : (
          <Stack.Screen name="Auth" component={AuthNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
```

### Tab Navigator
```typescript
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'ProductionTab':
              iconName = focused ? 'analytics' : 'analytics-outline';
              break;
            case 'EquipmentTab':
              iconName = focused ? 'construct' : 'construct-outline';
              break;
            case 'SafetyTab':
              iconName = focused ? 'shield-checkmark' : 'shield-checkmark-outline';
              break;
            case 'SettingsTab':
              iconName = focused ? 'settings' : 'settings-outline';
              break;
            default:
              iconName = 'circle';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textSecondary,
        tabBarStyle: {
          backgroundColor: Colors.surface,
          borderTopColor: Colors.border,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="ProductionTab" 
        component={ProductionNavigator}
        options={{ title: 'Production' }}
      />
      <Tab.Screen 
        name="EquipmentTab" 
        component={EquipmentNavigator}
        options={{ title: 'Equipment' }}
      />
      <Tab.Screen 
        name="SafetyTab" 
        component={SafetyNavigator}
        options={{ title: 'Safety' }}
      />
      <Tab.Screen 
        name="SettingsTab" 
        component={SettingsNavigator}
        options={{ title: 'Settings' }}
      />
    </Tab.Navigator>
  );
};
```

### Stack Navigators

#### Production Stack
```typescript
import { createNativeStackNavigator } from '@react-navigation/native-stack';

const Stack = createNativeStackNavigator<ProductionStackParamList>();

const ProductionNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.primary,
        },
        headerTintColor: Colors.textInverse,
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: Layout.fontSize.lg,
        },
        headerBackTitleVisible: false,
      }}
    >
      <Stack.Screen 
        name="ProductionOverview" 
        component={ProductionOverviewScreen}
        options={{ 
          title: 'Production Overview',
          headerRight: () => (
            <TouchableOpacity onPress={() => navigation.navigate('ProductionReports')}>
              <Ionicons name="document-text" size={24} color={Colors.textInverse} />
            </TouchableOpacity>
          ),
        }}
      />
      <Stack.Screen 
        name="ProductionDetails" 
        component={ProductionDetailsScreen}
        options={({ route }) => ({ 
          title: `Details - ${route.params?.date || 'Unknown'}`,
        })}
      />
      <Stack.Screen 
        name="ProductionReports" 
        component={ProductionReportsScreen}
        options={{ title: 'Production Reports' }}
      />
    </Stack.Navigator>
  );
};
```

## Type Definitions

### Navigation Types
```typescript
// Root navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

// Main tab navigation types
export type MainTabParamList = {
  ProductionTab: undefined;
  EquipmentTab: undefined;
  SafetyTab: undefined;
  SettingsTab: undefined;
};

// Production stack navigation types
export type ProductionStackParamList = {
  ProductionOverview: undefined;
  ProductionDetails: {
    date: string;
    metricId?: string;
  };
  ProductionReports: {
    period?: 'daily' | 'weekly' | 'monthly';
    startDate?: string;
    endDate?: string;
  };
};

// Equipment stack navigation types
export type EquipmentStackParamList = {
  EquipmentManagement: undefined;
  EquipmentDetails: {
    equipmentId: string;
  };
  MaintenanceSchedule: {
    equipmentId?: string;
  };
};

// Safety stack navigation types
export type SafetyStackParamList = {
  SafetyReporting: undefined;
  SafetyDashboard: undefined;
  SafetyTraining: undefined;
};

// Settings stack navigation types
export type SettingsStackParamList = {
  Settings: undefined;
  Profile: undefined;
  Notifications: undefined;
};
```

### Navigation Props
```typescript
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';

// Screen props types
export type ProductionOverviewScreenProps = NativeStackScreenProps<
  ProductionStackParamList,
  'ProductionOverview'
>;

export type ProductionDetailsScreenProps = NativeStackScreenProps<
  ProductionStackParamList,
  'ProductionDetails'
>;

// Tab screen props
export type ProductionTabProps = BottomTabScreenProps<
  MainTabParamList,
  'ProductionTab'
>;
```

## Navigation Hooks

### Custom Navigation Hooks
```typescript
import { useNavigation, useRoute } from '@react-navigation/native';

// Typed navigation hook
export const useProductionNavigation = () => {
  return useNavigation<NativeStackNavigationProp<ProductionStackParamList>>();
};

// Typed route hook
export const useProductionRoute = <T extends keyof ProductionStackParamList>() => {
  return useRoute<RouteProp<ProductionStackParamList, T>>();
};

// Navigation helper hook
export const useNavigationHelpers = () => {
  const navigation = useNavigation();

  const navigateToProductionDetails = useCallback((date: string, metricId?: string) => {
    navigation.navigate('ProductionTab', {
      screen: 'ProductionDetails',
      params: { date, metricId },
    });
  }, [navigation]);

  const navigateToEquipmentDetails = useCallback((equipmentId: string) => {
    navigation.navigate('EquipmentTab', {
      screen: 'EquipmentDetails',
      params: { equipmentId },
    });
  }, [navigation]);

  const goBack = useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.goBack();
    }
  }, [navigation]);

  return {
    navigateToProductionDetails,
    navigateToEquipmentDetails,
    goBack,
  };
};
```

## Navigation Patterns

### Deep Linking
```typescript
const linking = {
  prefixes: ['miningapp://'],
  config: {
    screens: {
      Main: {
        screens: {
          ProductionTab: {
            screens: {
              ProductionOverview: 'production',
              ProductionDetails: 'production/details/:date',
              ProductionReports: 'production/reports',
            },
          },
          EquipmentTab: {
            screens: {
              EquipmentManagement: 'equipment',
              EquipmentDetails: 'equipment/:equipmentId',
              MaintenanceSchedule: 'equipment/maintenance',
            },
          },
        },
      },
    },
  },
};

// Usage in NavigationContainer
<NavigationContainer linking={linking}>
  {/* Navigation structure */}
</NavigationContainer>
```

### Navigation Guards
```typescript
const useNavigationGuard = () => {
  const { isAuthenticated } = useAuth();
  const navigation = useNavigation();

  const navigateWithAuth = useCallback((screenName: string, params?: any) => {
    if (!isAuthenticated) {
      navigation.navigate('Auth', { screen: 'Login' });
      return;
    }
    navigation.navigate(screenName, params);
  }, [isAuthenticated, navigation]);

  return { navigateWithAuth };
};
```

### Navigation State Persistence
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

const PERSISTENCE_KEY = 'NAVIGATION_STATE';

const App = () => {
  const [isReady, setIsReady] = useState(false);
  const [initialState, setInitialState] = useState();

  useEffect(() => {
    const restoreState = async () => {
      try {
        const savedStateString = await AsyncStorage.getItem(PERSISTENCE_KEY);
        const state = savedStateString ? JSON.parse(savedStateString) : undefined;

        if (state !== undefined) {
          setInitialState(state);
        }
      } finally {
        setIsReady(true);
      }
    };

    if (!isReady) {
      restoreState();
    }
  }, [isReady]);

  if (!isReady) {
    return null;
  }

  return (
    <NavigationContainer
      initialState={initialState}
      onStateChange={(state) =>
        AsyncStorage.setItem(PERSISTENCE_KEY, JSON.stringify(state))
      }
    >
      {/* Navigation structure */}
    </NavigationContainer>
  );
};
```

## Navigation Animations

### Custom Transitions
```typescript
const customTransition = {
  gestureEnabled: true,
  gestureDirection: 'horizontal',
  transitionSpec: {
    open: {
      animation: 'timing',
      config: {
        duration: 300,
        easing: Easing.out(Easing.poly(4)),
      },
    },
    close: {
      animation: 'timing',
      config: {
        duration: 300,
        easing: Easing.in(Easing.poly(4)),
      },
    },
  },
  cardStyleInterpolator: ({ current, layouts }) => {
    return {
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
      },
    };
  },
};

// Apply to stack navigator
<Stack.Navigator screenOptions={customTransition}>
  {/* Screens */}
</Stack.Navigator>
```

### Tab Bar Animations
```typescript
const AnimatedTabBar = ({ state, descriptors, navigation }) => {
  const translateX = useSharedValue(0);

  useEffect(() => {
    translateX.value = withSpring(state.index * (screenWidth / state.routes.length));
  }, [state.index]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  return (
    <View style={styles.tabBar}>
      <Animated.View style={[styles.tabIndicator, animatedStyle]} />
      {state.routes.map((route, index) => (
        <TabBarButton
          key={route.key}
          route={route}
          index={index}
          navigation={navigation}
          descriptor={descriptors[route.key]}
        />
      ))}
    </View>
  );
};
```

## Navigation Testing

### Navigation Testing Patterns
```typescript
import { NavigationContainer } from '@react-navigation/native';
import { render, fireEvent } from '@testing-library/react-native';

describe('Navigation', () => {
  it('should navigate between tabs', () => {
    const { getByText } = render(
      <NavigationContainer>
        <MainNavigator />
      </NavigationContainer>
    );

    // Test tab navigation
    fireEvent.press(getByText('Equipment'));
    expect(getByText('Equipment Management')).toBeTruthy();

    fireEvent.press(getByText('Safety'));
    expect(getByText('Safety Reporting')).toBeTruthy();
  });

  it('should navigate to details screen with params', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <ProductionNavigator />
      </NavigationContainer>
    );

    fireEvent.press(getByTestId('production-item-2025-01-19'));
    expect(getByText('Details - 2025-01-19')).toBeTruthy();
  });
});
```

---
*Navigation documentation following Cortex 7 standards for comprehensive navigation system reference.*
