import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Animated } from 'react-native';
import AnimatedMenuItem from '../components/menu/AnimatedMenuItem';
import { Colors } from '../constants/colors';

// All mocks are handled in setup.ts

describe('AnimatedMenuItem with Transparency Effects', () => {
  const defaultProps = {
    id: 'test-item',
    title: 'Test Menu Item',
    icon: 'calendar',
    color: Colors.primary,
    onPress: jest.fn(),
    index: 0,
    hasNotification: false,
    notificationCount: 0,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock Animated.timing to return a controllable animation
    jest.spyOn(Animated, 'timing').mockImplementation(() => ({
      start: jest.fn(),
      stop: jest.fn(),
      reset: jest.fn(),
    }));
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders with transparent background styling', () => {
    const { getByTestId } = render(
      <AnimatedMenuItem {...defaultProps} />
    );

    // The component should render without crashing
    expect(getByTestId).toBeDefined();
  });

  it('applies transparency animation on press', () => {
    const { getByRole } = render(
      <AnimatedMenuItem {...defaultProps} />
    );

    const button = getByRole('button');
    
    // Simulate press in
    fireEvent(button, 'pressIn');
    
    // Verify that Animated.timing was called for transparency animation
    expect(Animated.timing).toHaveBeenCalledWith(
      expect.any(Object), // transparencyAnim
      expect.objectContaining({
        toValue: 0.7,
        duration: 150,
        useNativeDriver: true,
      })
    );
  });

  it('restores transparency on press out', () => {
    const { getByRole } = render(
      <AnimatedMenuItem {...defaultProps} />
    );

    const button = getByRole('button');
    
    // Simulate press out
    fireEvent(button, 'pressOut');
    
    // Verify that transparency is restored
    expect(Animated.timing).toHaveBeenCalledWith(
      expect.any(Object), // transparencyAnim
      expect.objectContaining({
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      })
    );
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByRole } = render(
      <AnimatedMenuItem {...defaultProps} onPress={mockOnPress} />
    );

    const button = getByRole('button');
    fireEvent.press(button);

    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('displays notification badge when hasNotification is true', () => {
    const { getByText } = render(
      <AnimatedMenuItem 
        {...defaultProps} 
        hasNotification={true} 
        notificationCount={3} 
      />
    );

    expect(getByText('3')).toBeTruthy();
  });

  it('displays 9+ for notification count greater than 9', () => {
    const { getByText } = render(
      <AnimatedMenuItem 
        {...defaultProps} 
        hasNotification={true} 
        notificationCount={15} 
      />
    );

    expect(getByText('9+')).toBeTruthy();
  });

  it('has proper accessibility attributes', () => {
    const { getByRole } = render(
      <AnimatedMenuItem {...defaultProps} />
    );

    const button = getByRole('button');
    expect(button.props.accessibilityLabel).toBe('Test Menu Item menu item');
    expect(button.props.accessible).toBe(true);
  });
});

// Test for transparency style values
describe('AnimatedMenuItem Transparency Styles', () => {
  it('should have transparent background in menuIconBackground style', () => {
    // This test verifies the styling constants used for transparency
    const transparentBg = 'rgba(255, 255, 255, 0.08)';
    const subtleBorder = 'rgba(255, 255, 255, 0.12)';
    const iconBg = 'rgba(255, 255, 255, 0.05)';
    
    // Verify transparency values are within expected ranges
    expect(transparentBg).toMatch(/rgba\(255, 255, 255, 0\.\d+\)/);
    expect(subtleBorder).toMatch(/rgba\(255, 255, 255, 0\.\d+\)/);
    expect(iconBg).toMatch(/rgba\(255, 255, 255, 0\.\d+\)/);
  });
});
