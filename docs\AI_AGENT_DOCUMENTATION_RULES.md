# 🤖 AI AGENT DOCUMENTATION RULES

> **🎯 Tujuan**: Memastikan konsistensi penempatan dan update dokumentasi oleh AI Agent  
> **📋 Scope**: Panduan wajib untuk semua AI agent yang bekerja pada project ini  
> **🔄 Status**: Mandatory - HARUS diikuti oleh semua AI agent

---

## 📁 STRUKTUR DOKUMENTASI WAJIB

### **🏗️ FOLDER STRUCTURE (TIDAK BOLEH DIUBAH)**
```
docs/
├── README.md                    # Main entry point - overview project
├── index.md                     # Navigation hub - quick links
├── implementation-progress.md   # Current status tracking
├── architecture/               # System design & architecture
├── design/                     # UI/UX design system
├── features/                   # Feature-specific documentation
├── development/                # Development guides & setup
├── api/                        # API documentation
├── user-guides/               # End-user documentation
└── resources/                 # Reports, changelogs, references
```

---

## 🚫 ATURAN WAJIB - JANGAN DILANGGAR

### **❌ DILARANG KERAS:**
1. **Membuat file .md di root docs/** (kecuali README.md, index.md, implementation-progress.md)
2. **Membuat folder baru** tanpa update struktur di file ini
3. **Duplikasi konten** di multiple files
4. **Membuat file dengan nama UPPERCASE** di luar resources/
5. **Mengabaikan update index.md** saat menambah dokumentasi baru

### **✅ WAJIB DILAKUKAN:**
1. **Selalu cek struktur** sebelum membuat file baru
2. **Update existing files** daripada membuat file baru
3. **Update index.md** setiap kali ada perubahan dokumentasi
4. **Follow naming convention** yang sudah ditetapkan
5. **Merge konten** ke file yang sudah ada jika relevan
6. **Gunakan format penulisan yang konsisten** dengan nomor, nama file, dan date

---

## � ATURAN PENULISAN FILE .MD (WAJIB)

### **🔢 Format Nama File**
```bash
# Format: [nomor]-[nama-file].md
# Contoh yang BENAR:
01-overview.md
02-database-design.md
03-navigation-system.md

# Contoh yang SALAH:
overview.md
Database-Design.md
navigation_system.md
```

### **📅 Format Header File (MANDATORY)**
```markdown
# [Nomor]. [Judul Lengkap]

> **📝 File**: `[nomor]-[nama-file].md`
> **📅 Created**: [DD Month YYYY]
> **🔄 Last Updated**: [DD Month YYYY]
> **👤 Author**: [AI Agent/Developer Name]
> **📋 Version**: [v1.0/v2.0/etc]
> **✅ Status**: [Draft/In Progress/Complete/Production Ready]
> **🎯 Purpose**: [Brief description of file purpose]

---

[Content starts here]
```

### **📋 Contoh Header yang Benar:**
```markdown
# 01. System Architecture Overview

> **📝 File**: `01-overview.md`
> **📅 Created**: 15 January 2025
> **🔄 Last Updated**: 15 January 2025
> **👤 Author**: Augment AI Agent
> **📋 Version**: v2.0
> **✅ Status**: Production Ready
> **🎯 Purpose**: Comprehensive system architecture documentation for Mining Operations App

---

## Table of Contents
[content continues...]
```

### **🔢 Sistem Penomoran per Folder:**
```bash
architecture/
├── 01-overview.md              # System overview
├── 02-database-design.md       # Database schema
├── 03-navigation.md            # Navigation patterns
├── 04-flow-diagrams.md         # System flows
└── 05-implementation-roadmap.md # Implementation plan

features/
├── 01-dashboard-screen.md      # Main dashboard
├── 02-production-overview.md   # Production analytics
├── 03-enhanced-menu-system.md  # Menu system
└── 04-svg-icons-system.md      # Icon system

development/
├── 01-setup.md                 # MASTER SETUP GUIDE
├── 02-shadow-system.md         # Cross-platform shadows
├── 03-testing-guide.md         # Testing framework
└── 04-deployment-guide.md      # Deployment process
```

### **📅 Format Tanggal yang Konsisten:**
```bash
✅ BENAR: 15 January 2025, 03 March 2025, 28 December 2024
❌ SALAH: Jan 15 2025, 2025-01-15, 15/01/2025, January 15th 2025
```

### **🏷️ Naming Convention Rules:**
```bash
✅ BENAR:
- Gunakan lowercase dengan dash (-)
- Nomor dengan leading zero (01, 02, 03...)
- Nama deskriptif dan jelas
- Maksimal 3-4 kata

❌ SALAH:
- CamelCase atau PascalCase
- Underscore (_)
- Spasi dalam nama file
- Nama terlalu panjang (>50 karakter)
```

---

## �📋 PANDUAN PENEMPATAN FILE

### **🏗️ architecture/** - System Design
**Untuk**: System architecture, database design, flow diagrams
```bash
✅ BOLEH: overview.md, database-design.md, navigation.md
❌ JANGAN: setup-guide.md, user-manual.md
```

### **🎨 design/** - UI/UX Design
**Untuk**: Design system, colors, typography, components
```bash
✅ BOLEH: design-system.md, component-library.md
❌ JANGAN: implementation-guide.md, api-docs.md
```

### **⭐ features/** - Feature Documentation
**Untuk**: Dokumentasi fitur-fitur aplikasi
```bash
✅ BOLEH: dashboard-screen.md, production-overview.md
❌ JANGAN: setup-guide.md, database-schema.md
```

### **🔧 development/** - Development Guides
**Untuk**: Setup, configuration, development workflow
```bash
✅ BOLEH: setup.md (MASTER GUIDE), shadow-system.md
❌ JANGAN: user-guide.md, feature-specs.md

⭐ KHUSUS: setup.md adalah MASTER GUIDE - merge semua setup content ke sini
```

### **🌐 api/** - API Documentation
**Untuk**: API endpoints, authentication, data models
```bash
✅ BOLEH: endpoints.md, authentication.md, data-models.md
❌ JANGAN: user-guide.md, setup-instructions.md
```

### **👥 user-guides/** - End-User Documentation
**Untuk**: Panduan untuk end-user aplikasi
```bash
✅ BOLEH: operator-guide.md, admin-guide.md
❌ JANGAN: development-setup.md, api-reference.md
```

### **📚 resources/** - Reports & References
**Untuk**: Changelogs, reports, implementation files, references
```bash
✅ BOLEH: changelog.md, roadmap.md, *-REPORT.md, *-SUMMARY.md
❌ JANGAN: setup-guide.md, feature-documentation.md
```

---

## 🔄 WORKFLOW WAJIB UNTUK AI AGENT

### **📝 Sebelum Membuat Dokumentasi Baru:**
```bash
1. ✅ Baca file AI_AGENT_DOCUMENTATION_RULES.md (file ini)
2. ✅ Cek struktur folder yang sudah ada
3. ✅ Tentukan folder yang tepat berdasarkan konten
4. ✅ Cek apakah sudah ada file yang relevan untuk di-update
5. ✅ Jika ada file relevan, UPDATE daripada buat baru
```

### **📄 Saat Membuat/Update File:**
```bash
1. ✅ Gunakan format nama file: [nomor]-[nama-file].md
2. ✅ Tambahkan header MANDATORY dengan semua field required
3. ✅ Update tanggal "Last Updated" setiap kali edit
4. ✅ Increment version number jika perubahan signifikan
5. ✅ Merge konten jika ada overlap dengan file lain
6. ✅ Update index.md dengan link ke file baru/updated
7. ✅ Update implementation-progress.md jika relevan
```

### **🔍 Setelah Membuat/Update Dokumentasi:**
```bash
1. ✅ Verifikasi file ada di folder yang benar
2. ✅ Pastikan index.md sudah di-update
3. ✅ Cek tidak ada duplikasi konten
4. ✅ Pastikan navigation links berfungsi
5. ✅ Update changelog.md jika perlu
```

---

## 📋 TEMPLATE WAJIB

### **📄 Header Template untuk Semua File (MANDATORY):**
```markdown
# [Nomor]. [Judul Lengkap]

> **� File**: `[nomor]-[nama-file].md`
> **📅 Created**: [DD Month YYYY]
> **🔄 Last Updated**: [DD Month YYYY]
> **👤 Author**: [AI Agent/Developer Name]
> **📋 Version**: [v1.0/v2.0/etc]
> **✅ Status**: [Draft/In Progress/Complete/Production Ready]
> **🎯 Purpose**: [Brief description of file purpose]

---

## Table of Contents
- [Section 1](#section-1)
- [Section 2](#section-2)

---

[Content here]
```

### **📄 Header Template untuk File Tanpa Nomor (Root Files):**
```markdown
# 📊 [TITLE]

> **📝 File**: `[nama-file].md`
> **📅 Created**: [DD Month YYYY]
> **🔄 Last Updated**: [DD Month YYYY]
> **👤 Author**: [AI Agent/Developer Name]
> **📋 Version**: [v1.0/v2.0/etc]
> **✅ Status**: [Draft/In Progress/Complete/Production Ready]
> **🎯 Purpose**: [Brief description of file purpose]

---

[Content here]
```

### **🔗 Link Template untuk index.md:**
```markdown
- **📊 [Title]**: [file-path.md] - [Brief description]
```

---

## 🎯 CONTOH DECISION TREE

### **🤔 "Saya ingin membuat dokumentasi tentang setup environment"**
```
❓ Apakah ini tentang development setup?
├── ✅ YA → Update development/setup.md (MASTER GUIDE)
└── ❌ TIDAK → Tentukan kategori yang tepat

❓ Apakah konten sudah ada di development/setup.md?
├── ✅ YA → Merge ke file yang sudah ada
└── ❌ TIDAK → Tambahkan section baru di setup.md
```

### **🤔 "Saya ingin membuat dokumentasi tentang fitur baru"**
```
❓ Apakah ini dokumentasi fitur aplikasi?
├── ✅ YA → Buat/update di features/
└── ❌ TIDAK → Tentukan kategori yang tepat

❓ Apakah sudah ada file untuk fitur ini?
├── ✅ YA → Update file yang sudah ada
└── ❌ TIDAK → Buat file baru di features/
```

### **🤔 "Saya ingin membuat report atau summary"**
```
❓ Apakah ini report/summary/changelog?
├── ✅ YA → Tempatkan di resources/
└── ❌ TIDAK → Tentukan kategori yang tepat

❓ Apakah ini update untuk changelog?
├── ✅ YA → Update resources/changelog.md
└── ❌ TIDAK → Buat file baru di resources/
```

---

## 🚨 EMERGENCY RULES

### **🔴 Jika Menemukan Struktur yang Berantakan:**
1. **JANGAN** langsung membuat file baru
2. **REORGANISASI** file yang ada ke struktur yang benar
3. **UPDATE** semua links dan references
4. **VERIFIKASI** navigation masih berfungsi
5. **DOKUMENTASIKAN** perubahan di changelog.md

### **⚠️ Jika Tidak Yakin Penempatan:**
1. **TANYA** user untuk konfirmasi
2. **JELASKAN** opsi penempatan yang tersedia
3. **IKUTI** struktur yang sudah ditetapkan
4. **JANGAN** buat struktur baru tanpa persetujuan

---

## 📊 MONITORING & COMPLIANCE

### **✅ Checklist Compliance:**
```bash
□ Semua file .md ada di folder yang tepat
□ Tidak ada file .md di root docs/ (kecuali 3 file utama)
□ index.md selalu up-to-date dengan struktur terbaru
□ Tidak ada duplikasi konten di multiple files
□ Semua links berfungsi dengan baik
□ Naming convention konsisten
□ Header template digunakan di semua file
```

### **🔍 Regular Audit:**
AI Agent harus melakukan audit dokumentasi setiap kali:
- Membuat file baru
- Update file existing
- Reorganisasi struktur
- User request dokumentasi

---

## 🎯 TUJUAN AKHIR

### **✅ Dokumentasi yang Konsisten:**
- Struktur folder yang stabil dan predictable
- Tidak ada ambiguitas penempatan file
- Easy navigation untuk semua user
- Maintenance yang mudah untuk jangka panjang

### **🚀 Benefit untuk User:**
- Selalu tahu dimana mencari dokumentasi
- Tidak ada confusion dengan multiple files
- Struktur yang sama di semua project
- AI agent yang konsisten dalam dokumentasi

---

**📅 Created**: January 2025  
**🔄 Status**: Mandatory for all AI agents  
**📋 Compliance**: Required for all documentation work  
**🎯 This file ensures consistent documentation structure across all AI agent interactions!**
