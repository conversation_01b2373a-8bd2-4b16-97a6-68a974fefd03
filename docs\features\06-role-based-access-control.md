# 06. Role-Based Access Control System

> **📝 File**: `06-role-based-access-control.md`
> **📅 Created**: 05 August 2025
> **🔄 Last Updated**: 05 August 2025
> **👤 Author**: Augment AI Agent
> **📋 Version**: v1.0
> **✅ Status**: Production Ready
> **🎯 Purpose**: Comprehensive documentation for role-based access control system implementation in Mining Operations App

---

## 📋 Table of Contents
- [Overview](#overview)
- [Role Hierarchy](#role-hierarchy)
- [Permission System](#permission-system)
- [TypeScript Integration](#typescript-integration)
- [React Context Implementation](#react-context-implementation)
- [Usage Examples](#usage-examples)
- [Demo Users](#demo-users)
- [Security Features](#security-features)

---

## 🎯 Overview

Sistem Role-Based Access Control (RBAC) yang komprehensif untuk mengatur akses pengguna dalam aplikasi Mining Operations berdasarkan level hierarki dan permission granular.

### Key Features
- ✅ **10 Level Hierarki**: Dari General Employee (Level 1) hingga Super Administrator (Level 10)
- ✅ **Granular Permissions**: Control per resource dan action (create, read, update, delete, export, sync)
- ✅ **TypeScript Integration**: Full type safety dengan interfaces dan enums
- ✅ **React Context**: Seamless integration dengan React components
- ✅ **Database Functions**: Server-side validation dan permission checking
- ✅ **Demo Users**: Complete testing users untuk setiap level
- ✅ **Security**: Database-level security dengan Row Level Security (RLS)

---

## 👥 Role Hierarchy

### Level Structure (1-10)
| Level | Role | Display Name | Department | Access Scope |
|-------|------|--------------|------------|--------------|
| 10 | `super_admin` | Super Administrator | IT Management | Full system access |
| 9 | `admin` | Administrator | IT Management | Wide administrative access |
| 8 | `mine_manager` | Mine Manager | Operations | Full operational access |
| 7 | `production_supervisor` | Production Supervisor | Production | Production management |
| 6 | `equipment_manager` | Equipment Manager | Maintenance | Equipment management |
| 5 | `safety_officer` | Safety Officer | Safety | Safety management |
| 4 | `shift_supervisor` | Shift Supervisor | Operations | Shift operations |
| 3 | `operator` | Equipment Operator | Operations | Equipment operation |
| 2 | `technician` | Maintenance Technician | Maintenance | Equipment maintenance |
| 1 | `employee` | General Employee | Administration | Limited access |

### Hierarchical Access Rules
- **Higher levels** dapat mengakses data dan fitur level di bawahnya
- **Same level** dapat berkolaborasi dengan permission yang sama
- **Lower levels** tidak dapat mengakses data level di atasnya

---

## 🔐 Permission System

### Resource Categories
```typescript
type AccessibleResource = 
  | 'users'        // User management
  | 'production'   // Production data
  | 'equipment'    // Equipment management
  | 'safety'       // Safety management
  | 'reports'      // Report generation
  | 'analytics'    // Analytics dashboard
  | 'sap'          // SAP integration
  | 'attendance'   // Attendance tracking
  | 'maintenance'  // Maintenance records
  | 'incidents'    // Incident management
  | 'profile';     // Profile management
```

### Permission Actions
```typescript
type PermissionAction = 
  | 'create'  // Create new records
  | 'read'    // View/read data
  | 'update'  // Modify existing data
  | 'delete'  // Remove data
  | 'export'  // Export data
  | 'sync';   // Synchronize data
```

### Permission Matrix Examples
```typescript
// Super Admin - Full Access
{
  all: true,
  users: ['create', 'read', 'update', 'delete'],
  production: ['create', 'read', 'update', 'delete'],
  equipment: ['create', 'read', 'update', 'delete'],
  // ... all resources with full permissions
}

// Production Supervisor - Production Focus
{
  production: ['create', 'read', 'update'],
  equipment: ['read', 'update'],
  safety: ['create', 'read'],
  reports: ['create', 'read']
}

// General Employee - Limited Access
{
  attendance: ['read', 'update'],
  safety: ['read'],
  profile: ['read', 'update']
}
```

---

## 💻 TypeScript Integration

### Core Interfaces
```typescript
// Role interface
interface Role {
  id: string;
  name: string;
  display_name: string;
  description: string;
  level: number;
  permissions: RolePermissions;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// User with role interface
interface UserWithRole {
  id: string;
  email: string;
  full_name: string;
  departemen?: string;
  jabatan?: string;
  role_id: string;
  role_name: string;
  role_display_name: string;
  role_level: number;
  role_permissions: RolePermissions;
}
```

### Enums for Type Safety
```typescript
enum RoleName {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MINE_MANAGER = 'mine_manager',
  PRODUCTION_SUPERVISOR = 'production_supervisor',
  EQUIPMENT_MANAGER = 'equipment_manager',
  SAFETY_OFFICER = 'safety_officer',
  SHIFT_SUPERVISOR = 'shift_supervisor',
  OPERATOR = 'operator',
  TECHNICIAN = 'technician',
  EMPLOYEE = 'employee'
}

enum RoleLevel {
  EMPLOYEE = 1,
  TECHNICIAN = 2,
  OPERATOR = 3,
  SHIFT_SUPERVISOR = 4,
  SAFETY_OFFICER = 5,
  EQUIPMENT_MANAGER = 6,
  PRODUCTION_SUPERVISOR = 7,
  MINE_MANAGER = 8,
  ADMIN = 9,
  SUPER_ADMIN = 10
}
```

---

## ⚛️ React Context Implementation

### RoleContext Setup
```typescript
// Context provider
export const RoleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [userRole, setUserRole] = useState<UserWithRole | null>(null);

  // Fetch user role from database
  const fetchUserRole = async (userId: string) => {
    const { data } = await supabase
      .from('user_roles')
      .select('*')
      .eq('id', userId)
      .single();
    
    setUserRole(data);
  };

  // Context value
  const contextValue = {
    userRole,
    hasPermission: (resource: string, action: PermissionAction) => 
      RoleUtils.hasPermission(userRole, resource, action),
    hasMinimumLevel: (requiredLevel: number) => 
      RoleUtils.hasMinimumLevel(userRole, requiredLevel),
    isAdmin: () => RoleUtils.isAdmin(userRole),
    isSuperAdmin: () => RoleUtils.isSuperAdmin(userRole),
    canAccessResource: (resource: string) => 
      canAccessResource(userRole, resource)
  };

  return (
    <RoleContext.Provider value={contextValue}>
      {children}
    </RoleContext.Provider>
  );
};
```

### Custom Hooks
```typescript
// Basic role hook
export const useRole = () => {
  const context = useContext(RoleContext);
  if (!context) {
    throw new Error('useRole must be used within a RoleProvider');
  }
  return context;
};

// Permission checking hook
export const usePermission = (resource: string, action: PermissionAction) => {
  const { userRole, hasPermission } = useRole();
  return {
    allowed: hasPermission(resource, action),
    loading: userRole === undefined,
    userRole
  };
};

// Role guard hook
export const useRoleGuard = (requiredLevel: number) => {
  const { userRole, hasMinimumLevel } = useRole();
  return {
    canAccess: hasMinimumLevel(requiredLevel),
    loading: userRole === undefined,
    currentLevel: userRole?.role_level || 0,
    requiredLevel
  };
};
```

---

## 🚀 Usage Examples

### Component-Level Access Control
```typescript
// Role-based component rendering
const AdminPanel = () => {
  const { isAdmin } = useRole();
  
  if (!isAdmin()) {
    return <AccessDenied />;
  }
  
  return <AdminDashboard />;
};

// Permission-based features
const DeleteButton = ({ itemId }: { itemId: string }) => {
  const { allowed } = usePermission('equipment', 'delete');
  
  if (!allowed) return null;
  
  return (
    <Button onPress={() => deleteItem(itemId)}>
      Delete Equipment
    </Button>
  );
};
```

### HOC for Access Control
```typescript
// WithRoleAccess component
<WithRoleAccess requiredLevel={RoleLevel.ADMIN}>
  <AdminPanel />
</WithRoleAccess>

<WithRoleAccess 
  requiredPermission={{ resource: 'equipment', action: 'delete' }}
>
  <DeleteButton />
</WithRoleAccess>
```

### Navigation Guards
```typescript
// Screen-level protection
const ProductionScreen = () => {
  const { canAccess, loading } = useRoleGuard(RoleLevel.OPERATOR);
  
  if (loading) return <LoadingScreen />;
  if (!canAccess) return <UnauthorizedScreen />;
  
  return <ProductionDashboard />;
};
```

---

## 👤 Demo Users

### Complete Testing Suite
| Email | Password | Role | Level | Department |
|-------|----------|------|-------|------------|
| `<EMAIL>` | `SuperAdmin123!` | Super Administrator | 10 | IT Management |
| `<EMAIL>` | `Admin123!` | Administrator | 9 | IT Management |
| `<EMAIL>` | `Manager123!` | Mine Manager | 8 | Operations |
| `<EMAIL>` | `ProdSup123!` | Production Supervisor | 7 | Production |
| `<EMAIL>` | `EquipMgr123!` | Equipment Manager | 6 | Maintenance |
| `<EMAIL>` | `Safety123!` | Safety Officer | 5 | Safety |
| `<EMAIL>` | `ShiftSup123!` | Shift Supervisor | 4 | Operations |
| `<EMAIL>` | `Operator123!` | Equipment Operator | 3 | Operations |
| `<EMAIL>` | `Technician123!` | Maintenance Technician | 2 | Maintenance |
| `<EMAIL>` | `Employee123!` | General Employee | 1 | Administration |

### Testing Scenarios
1. **Hierarchical Access**: Test level-based access control
2. **Permission Matrix**: Test granular permissions per resource
3. **Department Isolation**: Test department-based access
4. **Cross-Level Collaboration**: Test collaboration between levels
5. **Security Boundaries**: Test unauthorized access prevention

---

## 🔒 Security Features

### Database-Level Security
- **Row Level Security (RLS)**: Postgres-level access control
- **Function-Based Validation**: Server-side permission checking
- **Audit Trail**: Complete change tracking
- **Data Encryption**: Sensitive data protection

### Application-Level Security
- **Context Isolation**: Secure role context management
- **Type Safety**: Compile-time permission validation
- **Runtime Checks**: Dynamic permission verification
- **Session Management**: Secure user session handling

### Best Practices
- **Principle of Least Privilege**: Minimal required access
- **Defense in Depth**: Multiple security layers
- **Regular Audits**: Permission and access reviews
- **Secure Defaults**: Safe default configurations

---

**Next**: [Navigation System](../architecture/navigation.md) | [Database Design](../architecture/database-design.md) | [API Endpoints](../api/endpoints.md)
