# 🖼️ Header Image Feature - User Guide

## 🎯 **Overview**

Header Image feature memungkinkan pengguna untuk mengkustomisasi tampilan header profil mereka dengan gambar latar belakang yang personal. Fitur ini memberikan pengalaman yang lebih personal dan profesional.

## 🎨 **Fitur Header Image**

### **📱 Custom Header Component**
- **Dynamic Background**: Header yang dapat berubah sesuai gambar yang dipilih
- **Gradient Overlay**: Overlay gelap untuk keterbacaan teks yang lebih baik
- **User Information Display**: Menampilkan nama dan jabatan pengguna
- **Edit Button**: Tombol kamera untuk mengubah header image
- **Responsive Design**: Menyesuaikan dengan berbagai ukuran layar

### **🖼️ Header Image Options**

#### **1. 📷 Take Photo**
- **Camera Access**: Menggunakan kamera device untuk mengambil foto baru
- **Live Preview**: Preview langsung sebelum menyimpan
- **Aspect Ratio**: Otomatis crop ke rasio 16:9 untuk header
- **Quality Optimization**: Kompresi otomatis untuk performa optimal

#### **2. 🖼️ Choose from Gallery**
- **Gallery Access**: Akses ke photo library device
- **Image Selection**: Pilih dari foto yang sudah ada
- **Editing Tools**: Crop dan adjust sebelum menggunakan
- **Format Support**: Support berbagai format gambar (JPG, PNG, etc.)

#### **3. 🎨 Choose Preset**
- **Predefined Images**: 6 pilihan gambar preset berkualitas tinggi
- **Mining Themed**: Gambar-gambar yang sesuai dengan industri mining
- **Professional Look**: Gambar yang memberikan kesan profesional
- **Instant Apply**: Langsung terapkan tanpa upload

#### **4. 🗑️ Remove Header**
- **Reset to Default**: Kembali ke header gradient default
- **Clean Removal**: Hapus gambar dari storage untuk menghemat space
- **Confirmation Dialog**: Konfirmasi sebelum menghapus

### **🎨 Preset Header Collection**

#### **Mining Industry Themed:**
1. **Mining Site Sunset** - Pemandangan tambang saat sunset
2. **Heavy Equipment** - Alat berat mining dalam aksi
3. **Open Pit Mine** - Pemandangan tambang terbuka dari atas

#### **Industrial Themed:**
4. **Industrial Complex** - Kompleks industri modern
5. **Mountain Landscape** - Pemandangan pegunungan yang indah

#### **Abstract:**
6. **Abstract Gradient** - Gradient abstrak yang elegan

## 🔧 **Technical Features**

### **📁 Storage Management**
- **Supabase Storage**: Upload ke cloud storage yang aman
- **User Folders**: Setiap user memiliki folder terpisah
- **Auto Cleanup**: Otomatis hapus gambar lama saat upload baru
- **Local Caching**: Cache gambar di device untuk loading cepat

### **🖼️ Image Processing**
- **Aspect Ratio**: Otomatis crop ke 16:9 untuk konsistensi
- **Quality Control**: Kompresi optimal (80% quality)
- **Size Optimization**: Resize untuk performa terbaik
- **Format Standardization**: Konversi ke format yang optimal

### **🔒 Security & Privacy**
- **User Isolation**: Setiap user hanya bisa akses gambar sendiri
- **Secure Upload**: Upload melalui authenticated API
- **Permission Management**: Request permission sebelum akses camera/gallery
- **Data Protection**: Gambar tersimpan aman di cloud storage

## 🚀 **Cara Menggunakan**

### **📱 Mengubah Header Image:**

#### **Method 1: Dari Profile Screen**
1. **Buka Profile** → Tap tab Profile di bottom navigation
2. **Tap Camera Icon** → Tap ikon kamera di pojok kanan atas header
3. **Pilih Option** → Pilih Take Photo, Gallery, atau Preset
4. **Konfirmasi** → Gambar langsung terapkan setelah dipilih

#### **Method 2: Dari Settings Menu**
1. **Buka Profile** → Tap tab Profile
2. **Account Settings** → Scroll ke Account Settings
3. **Header Image** → Tap menu "Header Image"
4. **Pilih Option** → Pilih dari berbagai opsi yang tersedia

### **📷 Menggunakan Camera:**
1. **Tap "Take Photo"** → Pilih opsi ambil foto
2. **Permission** → Izinkan akses kamera jika diminta
3. **Ambil Foto** → Ambil foto dengan orientasi landscape
4. **Edit & Crop** → Adjust posisi dan crop sesuai keinginan
5. **Save** → Tap "Use Photo" untuk menyimpan

### **🖼️ Menggunakan Gallery:**
1. **Tap "Choose from Gallery"** → Pilih dari galeri
2. **Permission** → Izinkan akses photo library jika diminta
3. **Pilih Foto** → Browse dan pilih foto yang diinginkan
4. **Edit & Crop** → Adjust dan crop ke rasio 16:9
5. **Save** → Tap "Choose" untuk menggunakan foto

### **🎨 Menggunakan Preset:**
1. **Tap "Choose Preset"** → Pilih dari preset yang tersedia
2. **Browse Options** → Lihat 6 pilihan preset yang tersedia
3. **Preview** → Tap untuk melihat preview
4. **Apply** → Tap nama preset untuk langsung terapkan

### **🗑️ Menghapus Header:**
1. **Tap "Remove Header"** → Pilih opsi hapus
2. **Konfirmasi** → Konfirmasi penghapusan
3. **Reset** → Header kembali ke gradient default

## 💡 **Tips untuk Hasil Terbaik**

### **📸 Photography Tips:**
- **Orientasi Landscape**: Gunakan orientasi horizontal (16:9)
- **Resolusi Minimum**: 800x450 pixels untuk kualitas terbaik
- **Lighting**: Pastikan pencahayaan yang baik
- **Komposisi**: Hindari objek penting di area yang akan tertutup teks

### **🎨 Design Tips:**
- **Background Sederhana**: Hindari background yang terlalu ramai
- **Kontras**: Pilih gambar dengan kontras yang baik untuk keterbacaan teks
- **Warna**: Pertimbangkan warna yang sesuai dengan branding perusahaan
- **Profesional**: Pilih gambar yang memberikan kesan profesional

### **⚡ Performance Tips:**
- **Ukuran File**: Gunakan gambar dengan ukuran file yang wajar (<2MB)
- **Format**: JPG lebih baik untuk foto, PNG untuk gambar dengan transparansi
- **Cleanup**: Hapus header lama jika tidak digunakan lagi

## 🔧 **Troubleshooting**

### **❌ Masalah Umum:**

#### **Camera Tidak Bisa Dibuka:**
- Cek permission kamera di device settings
- Restart aplikasi jika perlu
- Pastikan kamera tidak digunakan aplikasi lain

#### **Gallery Tidak Bisa Diakses:**
- Cek permission photo library di device settings
- Pastikan ada foto di gallery
- Restart aplikasi jika perlu

#### **Upload Gagal:**
- Cek koneksi internet
- Pastikan ukuran file tidak terlalu besar
- Coba lagi setelah beberapa saat

#### **Gambar Tidak Muncul:**
- Tunggu beberapa saat untuk loading
- Cek koneksi internet
- Restart aplikasi jika perlu

### **🔄 Reset Header:**
Jika mengalami masalah, Anda bisa:
1. Hapus header image yang bermasalah
2. Restart aplikasi
3. Set header baru atau gunakan default

## 📊 **Storage Information**

### **📁 File Organization:**
```
profile-photos/
├── user_folder_name/
│   └── headers/
│       ├── header_timestamp1.jpg
│       └── header_timestamp2.jpg
```

### **🧹 Auto Cleanup:**
- Otomatis hapus gambar lama saat upload baru
- Hanya simpan 1 header image per user
- Cleanup dilakukan saat upload untuk menghemat storage

### **💾 Local Storage:**
- URL header image disimpan di AsyncStorage
- Cache untuk loading cepat
- Sync dengan cloud storage

## 🎯 **Benefits**

### **👤 Personal Branding:**
- **Unique Identity**: Setiap user bisa memiliki header yang unik
- **Professional Look**: Memberikan kesan profesional
- **Company Branding**: Bisa menggunakan gambar yang sesuai dengan perusahaan

### **📱 User Experience:**
- **Visual Appeal**: Tampilan yang lebih menarik dan personal
- **Easy Customization**: Mudah diubah kapan saja
- **Instant Preview**: Langsung terlihat setelah diubah
- **Responsive Design**: Tampil baik di berbagai device

### **⚡ Performance:**
- **Optimized Loading**: Gambar dioptimasi untuk loading cepat
- **Efficient Storage**: Auto cleanup untuk menghemat space
- **Cached Images**: Local caching untuk performa terbaik

---

**🎯 RESULT: Header Image feature memberikan personalisasi yang powerful untuk profile pengguna dengan interface yang user-friendly!** 🚀✨

**📱 Next Step: Test fitur header image untuk memastikan semua fungsi berjalan dengan baik!**
