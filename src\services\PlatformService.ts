import { Platform } from 'react-native';

export class PlatformService {
  static isWeb(): boolean {
    return Platform.OS === 'web';
  }

  static isMobile(): boolean {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  }

  static isNative(): boolean {
    return !this.isWeb();
  }

  static getStorageType(): 'web' | 'native' {
    return this.isWeb() ? 'web' : 'native';
  }

  static async getStorageInfo(): Promise<{
    available: boolean;
    type: string;
    size?: number;
  }> {
    if (this.isWeb()) {
      // Web storage info
      try {
        if (typeof navigator !== 'undefined' && navigator.storage?.estimate) {
          const estimate = await navigator.storage.estimate();
          return {
            available: true,
            type: 'localStorage',
            size: estimate?.usage || 0,
          };
        }
        return {
          available: true,
          type: 'localStorage',
          size: 0,
        };
      } catch (error) {
        return {
          available: true,
          type: 'localStorage',
          size: 0,
        };
      }
    } else {
      // Native storage info
      return {
        available: true,
        type: 'asyncStorage',
      };
    }
  }

  static async checkFileSystemAvailability(): Promise<boolean> {
    if (this.isWeb()) {
      // File system not available on web
      return false;
    }
    
    try {
      // Check if expo-file-system is available
      const FileSystem = require('expo-file-system');
      return !!FileSystem.getInfoAsync;
    } catch (error) {
      return false;
    }
  }

  static async checkSQLiteAvailability(): Promise<boolean> {
    if (this.isWeb()) {
      // Use IndexedDB or localStorage on web
      return false;
    }
    
    try {
      // Check if expo-sqlite is available
      const SQLite = require('expo-sqlite');
      return !!SQLite.openDatabase;
    } catch (error) {
      return false;
    }
  }

  static getNetworkAPI() {
    if (this.isWeb()) {
      // Check if web APIs are available
      if (typeof navigator === 'undefined' || typeof window === 'undefined') {
        return {
          isConnected: true,
          addEventListener: () => () => {},
        };
      }

      // Use navigator.onLine for web
      return {
        isConnected: navigator.onLine,
        addEventListener: (callback: (online: boolean) => void) => {
          const handleOnline = () => callback(true);
          const handleOffline = () => callback(false);

          window.addEventListener('online', handleOnline);
          window.addEventListener('offline', handleOffline);

          return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
          };
        }
      };
    } else {
      // Use @react-native-community/netinfo for native
      try {
        const NetInfo = require('@react-native-community/netinfo');
        return NetInfo;
      } catch (error) {
        console.warn('NetInfo not available, using fallback');
        return {
          isConnected: true,
          addEventListener: () => () => {},
        };
      }
    }
  }
}

export default PlatformService;
