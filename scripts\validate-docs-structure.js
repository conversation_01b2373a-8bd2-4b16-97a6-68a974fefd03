#!/usr/bin/env node

/**
 * Documentation Structure Validator
 * Ensures documentation follows established structure rules
 */

const fs = require('fs');
const path = require('path');

const DOCS_DIR = path.join(__dirname, '..', 'docs');
const ALLOWED_ROOT_FILES = ['README.md', 'index.md', 'implementation-progress.md', 'AI_AGENT_DOCUMENTATION_RULES.md'];
const REQUIRED_FOLDERS = ['architecture', 'design', 'features', 'development', 'api', 'user-guides', 'resources'];

console.log('🔍 VALIDATING DOCUMENTATION STRUCTURE...\n');

let errors = [];
let warnings = [];

// Check if docs directory exists
if (!fs.existsSync(DOCS_DIR)) {
  errors.push('❌ docs/ directory not found');
  process.exit(1);
}

// Get all items in docs directory
const docsItems = fs.readdirSync(DOCS_DIR);

// Check for unauthorized files in root
const rootFiles = docsItems.filter(item => {
  const itemPath = path.join(DOCS_DIR, item);
  return fs.statSync(itemPath).isFile() && item.endsWith('.md');
});

const unauthorizedRootFiles = rootFiles.filter(file => !ALLOWED_ROOT_FILES.includes(file));

if (unauthorizedRootFiles.length > 0) {
  errors.push(`❌ Unauthorized .md files in docs/ root: ${unauthorizedRootFiles.join(', ')}`);
  errors.push('   → Move these files to appropriate folders');
}

// Check for required folders
const folders = docsItems.filter(item => {
  const itemPath = path.join(DOCS_DIR, item);
  return fs.statSync(itemPath).isDirectory();
});

const missingFolders = REQUIRED_FOLDERS.filter(folder => !folders.includes(folder));

if (missingFolders.length > 0) {
  warnings.push(`⚠️  Missing recommended folders: ${missingFolders.join(', ')}`);
}

// Check for required root files
const missingRootFiles = ALLOWED_ROOT_FILES.filter(file => !rootFiles.includes(file));

if (missingRootFiles.length > 0) {
  warnings.push(`⚠️  Missing recommended root files: ${missingRootFiles.join(', ')}`);
}

// Check development/setup.md exists (master guide)
const setupFile = path.join(DOCS_DIR, 'development', 'setup.md');
if (!fs.existsSync(setupFile)) {
  errors.push('❌ development/setup.md not found (required master guide)');
}

// Check index.md exists and is updated
const indexFile = path.join(DOCS_DIR, 'index.md');
if (!fs.existsSync(indexFile)) {
  errors.push('❌ index.md not found (required navigation hub)');
} else {
  // Check if index.md mentions the folder structure
  const indexContent = fs.readFileSync(indexFile, 'utf8');
  if (!indexContent.includes('docs/')) {
    warnings.push('⚠️  index.md might need structure documentation update');
  }
}

// Check file naming convention
const checkNamingConvention = (dir, level = 0) => {
  if (level > 2) return;

  const items = fs.readdirSync(dir);
  const mdFiles = items.filter(item => item.endsWith('.md'));

  // Skip root files from naming convention check
  if (dir === DOCS_DIR) {
    return;
  }

  mdFiles.forEach(file => {
    const relativePath = path.relative(DOCS_DIR, path.join(dir, file));

    // Check if file follows [number]-[name].md format
    const numberPrefixRegex = /^\d{2}-[a-z0-9-]+\.md$/;

    if (!numberPrefixRegex.test(file)) {
      warnings.push(`⚠️  ${relativePath} doesn't follow naming convention: [number]-[name].md`);
      warnings.push(`   → Should be like: 01-${file.replace(/[^a-z0-9]/gi, '-').toLowerCase()}`);
    }

    // Check for proper header format
    const filePath = path.join(dir, file);
    const content = fs.readFileSync(filePath, 'utf8');

    // Check for required header fields
    const requiredFields = [
      'File:',
      'Created:',
      'Last Updated:',
      'Author:',
      'Version:',
      'Status:',
      'Purpose:'
    ];

    const missingFields = requiredFields.filter(field => !content.includes(field));

    if (missingFields.length > 0) {
      warnings.push(`⚠️  ${relativePath} missing header fields: ${missingFields.join(', ')}`);
    }

    // Check date format (DD Month YYYY)
    const dateRegex = /\d{1,2} (January|February|March|April|May|June|July|August|September|October|November|December) \d{4}/;
    if (content.includes('Created:') && !dateRegex.test(content)) {
      warnings.push(`⚠️  ${relativePath} has incorrect date format (should be: DD Month YYYY)`);
    }
  });

  // Recurse into subdirectories
  items.forEach(item => {
    const itemPath = path.join(dir, item);
    if (fs.statSync(itemPath).isDirectory()) {
      checkNamingConvention(itemPath, level + 1);
    }
  });
};

// Check for duplicate content indicators
const checkForDuplicates = (dir, level = 0) => {
  if (level > 2) return; // Limit recursion depth
  
  const items = fs.readdirSync(dir);
  const mdFiles = items.filter(item => item.endsWith('.md'));
  
  // Check for files with similar names that might indicate duplication
  const setupFiles = mdFiles.filter(file => 
    file.toLowerCase().includes('setup') || 
    file.toLowerCase().includes('guide') ||
    file.toLowerCase().includes('master')
  );
  
  if (setupFiles.length > 1 && dir.includes('development')) {
    warnings.push(`⚠️  Multiple setup-related files in ${path.relative(DOCS_DIR, dir)}: ${setupFiles.join(', ')}`);
  }
  
  // Recurse into subdirectories
  items.forEach(item => {
    const itemPath = path.join(dir, item);
    if (fs.statSync(itemPath).isDirectory()) {
      checkForDuplicates(itemPath, level + 1);
    }
  });
};

checkForDuplicates(DOCS_DIR);

// Check naming convention and headers
checkNamingConvention(DOCS_DIR);

// Report results
console.log('📊 VALIDATION RESULTS:\n');

if (errors.length === 0 && warnings.length === 0) {
  console.log('✅ DOCUMENTATION STRUCTURE IS VALID!');
  console.log('   All files are in correct locations');
  console.log('   Required files and folders exist');
  console.log('   No unauthorized files in root');
} else {
  if (errors.length > 0) {
    console.log('🚨 ERRORS FOUND:');
    errors.forEach(error => console.log(`   ${error}`));
    console.log('');
  }
  
  if (warnings.length > 0) {
    console.log('⚠️  WARNINGS:');
    warnings.forEach(warning => console.log(`   ${warning}`));
    console.log('');
  }
}

console.log('📁 CURRENT STRUCTURE:');
console.log(`   Root files: ${rootFiles.join(', ')}`);
console.log(`   Folders: ${folders.join(', ')}`);

console.log('\n📋 ALLOWED ROOT FILES:');
console.log(`   ${ALLOWED_ROOT_FILES.join(', ')}`);

console.log('\n📂 REQUIRED FOLDERS:');
console.log(`   ${REQUIRED_FOLDERS.join(', ')}`);

console.log('\n🎯 RULES:');
console.log('   - Only specific .md files allowed in docs/ root');
console.log('   - All other documentation must be in appropriate folders');
console.log('   - development/setup.md is the master setup guide');
console.log('   - index.md must be kept updated with structure changes');
console.log('   - File naming: [number]-[name].md (e.g., 01-overview.md)');
console.log('   - Required header fields: File, Created, Last Updated, Author, Version, Status, Purpose');
console.log('   - Date format: DD Month YYYY (e.g., 15 January 2025)');

if (errors.length > 0) {
  console.log('\n❌ VALIDATION FAILED - Fix errors above');
  process.exit(1);
} else {
  console.log('\n✅ VALIDATION PASSED');
  process.exit(0);
}
