// Database-specific types for Mining Operations Platform

export interface DatabaseConnection {
  url: string;
  apiKey: string;
  timeout: number;
  retries: number;
}

export interface QueryOptions {
  select?: string[];
  filters?: FilterCriteria[];
  sorting?: SortCriteria[];
  pagination?: PaginationOptions;
  joins?: JoinCriteria[];
}

export interface JoinCriteria {
  table: string;
  on: string;
  type: 'inner' | 'left' | 'right' | 'full';
}

export interface FilterCriteria {
  field: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'between' | 'is' | 'not';
  value: any;
}

export interface SortCriteria {
  field: string;
  direction: 'asc' | 'desc';
}

export interface PaginationOptions {
  page: number;
  limit: number;
  offset?: number;
}

export interface DatabaseTransaction {
  id: string;
  operations: DatabaseOperation[];
  status: 'pending' | 'committed' | 'rolled_back';
  timestamp: string;
}

export interface DatabaseOperation {
  type: 'insert' | 'update' | 'delete' | 'select';
  table: string;
  data?: any;
  conditions?: FilterCriteria[];
  returning?: string[];
}

export interface DatabaseMigration {
  version: string;
  name: string;
  up: string;
  down: string;
  timestamp: string;
}

export interface DatabaseSchema {
  tables: TableSchema[];
  views: ViewSchema[];
  functions: FunctionSchema[];
  triggers: TriggerSchema[];
}

export interface TableSchema {
  name: string;
  columns: ColumnSchema[];
  primaryKey: string[];
  foreignKeys: ForeignKeySchema[];
  indexes: IndexSchema[];
  constraints: ConstraintSchema[];
}

export interface ColumnSchema {
  name: string;
  type: string;
  nullable: boolean;
  defaultValue?: any;
  autoIncrement?: boolean;
  unique?: boolean;
}

export interface ForeignKeySchema {
  name: string;
  columns: string[];
  referencedTable: string;
  referencedColumns: string[];
  onUpdate: 'cascade' | 'restrict' | 'set_null' | 'set_default';
  onDelete: 'cascade' | 'restrict' | 'set_null' | 'set_default';
}

export interface IndexSchema {
  name: string;
  columns: string[];
  unique: boolean;
  type: 'btree' | 'hash' | 'gin' | 'gist';
}

export interface ConstraintSchema {
  name: string;
  type: 'check' | 'unique' | 'primary_key' | 'foreign_key';
  definition: string;
}

export interface ViewSchema {
  name: string;
  definition: string;
  columns: ColumnSchema[];
}

export interface FunctionSchema {
  name: string;
  parameters: ParameterSchema[];
  returnType: string;
  definition: string;
  language: string;
}

export interface ParameterSchema {
  name: string;
  type: string;
  mode: 'in' | 'out' | 'inout';
  defaultValue?: any;
}

export interface TriggerSchema {
  name: string;
  table: string;
  event: 'insert' | 'update' | 'delete';
  timing: 'before' | 'after' | 'instead_of';
  function: string;
}

// Supabase-specific types
export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
  schema?: string;
  autoRefreshToken?: boolean;
  persistSession?: boolean;
  detectSessionInUrl?: boolean;
}

export interface SupabaseResponse<T> {
  data: T | null;
  error: SupabaseError | null;
  count?: number | null;
  status: number;
  statusText: string;
}

export interface SupabaseError {
  message: string;
  details: string;
  hint: string;
  code: string;
}

export interface RealtimeSubscription {
  id: string;
  table: string;
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  callback: (payload: RealtimePayload) => void;
}

export interface RealtimePayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: Record<string, any>;
  old: Record<string, any>;
  schema: string;
  table: string;
  commit_timestamp: string;
}

// Local database types (SQLite)
export interface LocalDatabaseConfig {
  name: string;
  version: number;
  location: string;
  createFromLocation?: string;
}

export interface LocalTransaction {
  executeSql(
    statement: string,
    params?: any[],
    success?: (tx: LocalTransaction, result: LocalResultSet) => void,
    error?: (tx: LocalTransaction, error: LocalError) => boolean
  ): void;
}

export interface LocalResultSet {
  insertId: number;
  rowsAffected: number;
  rows: LocalResultSetRowList;
}

export interface LocalResultSetRowList {
  length: number;
  item(index: number): any;
}

export interface LocalError {
  code: number;
  message: string;
}

// Sync-related database types
export interface SyncQueue {
  id: string;
  operation: DatabaseOperation;
  timestamp: string;
  retryCount: number;
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  error?: string;
}

export interface ConflictResolution {
  strategy: 'client_wins' | 'server_wins' | 'merge' | 'manual';
  resolver?: (clientData: any, serverData: any) => any;
}

export interface SyncMetadata {
  table: string;
  lastSyncTimestamp: string;
  syncDirection: 'up' | 'down' | 'bidirectional';
  conflictResolution: ConflictResolution;
}

export interface DatabaseBackup {
  id: string;
  timestamp: string;
  size: number;
  tables: string[];
  location: string;
  checksum: string;
}

export interface DatabaseRestore {
  backupId: string;
  timestamp: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
  error?: string;
}
