// ===== TIPE DATA SISTEM ROLE =====
// Interface dan tipe untuk sistem manajemen role pengguna

// Interface untuk role/peran pengguna
export interface Role {
  id: string;
  name: string;                    // <PERSON>a role (snake_case)
  display_name: string;            // Nama tampilan role
  description: string;             // Deskripsi role
  level: number;                   // Level hierarki role (1-10)
  permissions: RolePermissions;    // Izin yang dimiliki role
  is_active: boolean;              // Status aktif role
  created_at: string;              // Tanggal dibuat
  updated_at: string;              // Tanggal diperbarui
}

// Interface untuk permissions/izin role
export interface RolePermissions {
  all?: boolean;                   // Akses penuh ke semua fitur
  users?: PermissionAction[];      // Izin manajemen pengguna
  production?: PermissionAction[]; // Izin manajemen produksi
  equipment?: PermissionAction[];  // Izin manajemen peralatan
  safety?: PermissionAction[];     // Izin manajemen keselamatan
  reports?: PermissionAction[];    // Izin manajemen laporan
  analytics?: PermissionAction[];  // Izin analitik
  sap?: PermissionAction[];        // Izin integrasi SAP
  attendance?: PermissionAction[]; // Izin manajemen kehadiran
  maintenance?: PermissionAction[]; // Izin manajemen perawatan
  incidents?: PermissionAction[];  // Izin manajemen insiden
  profile?: PermissionAction[];    // Izin manajemen profil
}

// Tipe untuk aksi permission
export type PermissionAction = 'create' | 'read' | 'update' | 'delete' | 'export' | 'sync';

// Interface untuk user dengan role
export interface UserWithRole {
  id: string;
  email: string;
  full_name: string;
  departemen?: string;
  jabatan?: string;
  role_id: string;
  role_name: string;
  role_display_name: string;
  role_level: number;
  role_permissions: RolePermissions;
}

// Enum untuk nama role yang tersedia
export enum RoleName {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MINE_MANAGER = 'mine_manager',
  PRODUCTION_SUPERVISOR = 'production_supervisor',
  EQUIPMENT_MANAGER = 'equipment_manager',
  SAFETY_OFFICER = 'safety_officer',
  SHIFT_SUPERVISOR = 'shift_supervisor',
  OPERATOR = 'operator',
  TECHNICIAN = 'technician',
  EMPLOYEE = 'employee'
}

// Enum untuk level role
export enum RoleLevel {
  EMPLOYEE = 1,
  TECHNICIAN = 2,
  OPERATOR = 3,
  SHIFT_SUPERVISOR = 4,
  SAFETY_OFFICER = 5,
  EQUIPMENT_MANAGER = 6,
  PRODUCTION_SUPERVISOR = 7,
  MINE_MANAGER = 8,
  ADMIN = 9,
  SUPER_ADMIN = 10
}

// Interface untuk context role
export interface RoleContext {
  userRole: UserWithRole | null;
  hasPermission: (resource: string, action: PermissionAction) => boolean;
  hasMinimumLevel: (requiredLevel: number) => boolean;
  isAdmin: () => boolean;
  isSuperAdmin: () => boolean;
  canAccessResource: (resource: string) => boolean;
}

// Tipe untuk resource yang dapat diakses
export type AccessibleResource = 
  | 'users' 
  | 'production' 
  | 'equipment' 
  | 'safety' 
  | 'reports' 
  | 'analytics' 
  | 'sap' 
  | 'attendance' 
  | 'maintenance' 
  | 'incidents' 
  | 'profile';

// Interface untuk permission check result
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  requiredLevel?: number;
  currentLevel?: number;
}

// Konstanta untuk role permissions
export const ROLE_PERMISSIONS = {
  // Super Admin - Akses penuh
  SUPER_ADMIN: {
    all: true,
    users: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    production: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    equipment: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    safety: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    reports: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    analytics: ['read', 'export'] as PermissionAction[],
    sap: ['read', 'sync'] as PermissionAction[]
  },
  
  // Admin - Akses luas
  ADMIN: {
    users: ['create', 'read', 'update'] as PermissionAction[],
    production: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    equipment: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    safety: ['create', 'read', 'update', 'delete'] as PermissionAction[],
    reports: ['create', 'read', 'update'] as PermissionAction[],
    analytics: ['read', 'export'] as PermissionAction[],
    sap: ['read'] as PermissionAction[]
  },
  
  // Employee - Akses terbatas
  EMPLOYEE: {
    attendance: ['read', 'update'] as PermissionAction[],
    safety: ['read'] as PermissionAction[],
    profile: ['read', 'update'] as PermissionAction[]
  }
} as const;

// Helper functions untuk role checking
export const RoleUtils = {
  // Cek apakah user memiliki permission tertentu
  hasPermission: (userRole: UserWithRole | null, resource: string, action: PermissionAction): boolean => {
    if (!userRole || !userRole.role_permissions) return false;
    
    // Cek akses penuh
    if (userRole.role_permissions.all) return true;
    
    // Cek permission spesifik resource
    const resourcePermissions = userRole.role_permissions[resource as keyof RolePermissions] as PermissionAction[];
    return resourcePermissions?.includes(action) || false;
  },
  
  // Cek apakah user memiliki level minimum
  hasMinimumLevel: (userRole: UserWithRole | null, requiredLevel: number): boolean => {
    if (!userRole) return false;
    return userRole.role_level >= requiredLevel;
  },
  
  // Cek apakah user adalah admin
  isAdmin: (userRole: UserWithRole | null): boolean => {
    if (!userRole) return false;
    return userRole.role_level >= RoleLevel.ADMIN;
  },
  
  // Cek apakah user adalah super admin
  isSuperAdmin: (userRole: UserWithRole | null): boolean => {
    if (!userRole) return false;
    return userRole.role_name === RoleName.SUPER_ADMIN;
  },
  
  // Dapatkan display name role berdasarkan level
  getRoleDisplayByLevel: (level: number): string => {
    const roleMap: Record<number, string> = {
      1: 'General Employee',
      2: 'Maintenance Technician',
      3: 'Equipment Operator',
      4: 'Shift Supervisor',
      5: 'Safety Officer',
      6: 'Equipment Manager',
      7: 'Production Supervisor',
      8: 'Mine Manager',
      9: 'Administrator',
      10: 'Super Administrator'
    };
    return roleMap[level] || 'Unknown Role';
  }
};

// Export default untuk kemudahan import
export default {
  Role,
  RolePermissions,
  UserWithRole,
  RoleName,
  RoleLevel,
  RoleContext,
  ROLE_PERMISSIONS,
  RoleUtils
};
