# 👨‍💼 Admin User Setup Guide for Dashboard Header Management

## 🎯 **Overview**

Untuk menggunakan Dashboard Header Management, Anda perlu membuat user dengan privileges admin. Berikut adalah panduan lengkap untuk setup admin users.

## 🔧 **Step 1: Run Database Setup**

1. **Buka Supabase Dashboard** → Go to your project
2. **SQL Editor** → <PERSON><PERSON> "SQL Editor" di sidebar
3. **Run Setup Script** → Copy dan paste script dari `setup_complete_database.sql`
4. **Execute** → Klik "Run" untuk menjalankan script

## 👤 **Step 2: Create Admin Users**

### **Method 1: Via Supabase Auth UI (Recommended)**

1. **Buka Supabase Dashboard** → Authentication → Users
2. **Add User** → Klik "Add user" button
3. **Fill Details**:
   ```
   Email: <EMAIL>
   Password: [Strong password]
   Email Confirm: true (check this)
   ```
4. **Create User** → Klik "Add user"
5. **Copy User ID** → Copy UUID dari user yang baru dibuat

### **Method 2: Via SQL (After Auth Signup)**

Jika user sudah signup via app, update profile mereka:

```sql
-- Update existing user to admin
UPDATE users 
SET 
    departemen = 'Administration',
    jabatan = 'System Administrator',
    full_name = 'Admin User',
    employee_id = 'ADMIN001',
    updated_at = NOW()
WHERE email = '<EMAIL>';
```

## 🏢 **Step 3: Setup Admin Profile**

Setelah user dibuat di Auth, tambahkan profile data:

```sql
-- Insert admin profile (replace USER_ID with actual UUID)
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    employee_id,
    nik,
    departemen,
    jabatan,
    is_active,
    created_at,
    updated_at
) VALUES (
    'REPLACE_WITH_ACTUAL_USER_ID', -- UUID from auth.users
    '<EMAIL>',
    'System Administrator',
    '+62-812-3456-7890',
    'ADMIN001',
    '3201234567890001',
    'Administration',
    'System Administrator',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    full_name = EXCLUDED.full_name,
    updated_at = NOW();
```

## 👥 **Step 4: Create Multiple Admin Users**

Untuk membuat beberapa admin users:

```sql
-- Create multiple admin profiles
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    employee_id,
    nik,
    departemen,
    jabatan,
    is_active
) VALUES 
-- Admin User 1
(
    'USER_ID_1', -- Replace with actual UUID
    '<EMAIL>',
    'Admin User 1',
    '+62-812-1111-1111',
    'ADMIN001',
    '3201111111111111',
    'Administration',
    'Administrator',
    true
),
-- Manager User
(
    'USER_ID_2', -- Replace with actual UUID
    '<EMAIL>',
    'Operations Manager',
    '+62-812-2222-2222',
    'MGR001',
    '3202222222222222',
    'Management',
    'Operations Manager',
    true
),
-- IT Admin
(
    'USER_ID_3', -- Replace with actual UUID
    '<EMAIL>',
    'IT Administrator',
    '+62-812-3333-3333',
    'IT001',
    '3203333333333333',
    'IT',
    'IT Administrator',
    true
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    updated_at = NOW();
```

## 🔍 **Step 5: Verify Admin Access**

Test apakah admin access sudah berfungsi:

```sql
-- Check admin users
SELECT * FROM get_admin_users();

-- Test admin function for specific user
SELECT is_user_admin('USER_ID_HERE');

-- Check dashboard header images access
SELECT * FROM get_dashboard_header_images();
```

## 🎯 **Admin Detection Rules**

User akan dianggap admin jika memenuhi salah satu kriteria:

### **By Departemen:**
- `Administration`
- `Management`

### **By Jabatan (case-insensitive):**
- Mengandung kata `admin`
- Mengandung kata `manager`

### **Examples:**
```sql
-- These will be detected as admin:
departemen = 'Administration' → ✅ Admin
departemen = 'Management' → ✅ Admin
jabatan = 'System Administrator' → ✅ Admin
jabatan = 'IT Admin' → ✅ Admin
jabatan = 'Operations Manager' → ✅ Admin
jabatan = 'Site Manager' → ✅ Admin

-- These will NOT be admin:
departemen = 'Production' → ❌ Not Admin
jabatan = 'Operator' → ❌ Not Admin
jabatan = 'Technician' → ❌ Not Admin
```

## 📱 **Step 6: Test in App**

1. **Login** dengan admin credentials
2. **Go to Profile** → Tap Profile tab
3. **Check Admin Section** → Should see "Admin Settings" section
4. **Access Dashboard Headers** → Tap "Dashboard Headers"
5. **Test Functionality** → Try adding/editing header images

## 🔧 **Troubleshooting**

### **Admin Menu Tidak Muncul:**

1. **Check Profile Data**:
```sql
SELECT id, email, full_name, departemen, jabatan, is_active 
FROM users 
WHERE email = '<EMAIL>';
```

2. **Check Admin Function**:
```sql
SELECT is_user_admin() AS is_admin;
```

3. **Update Profile if Needed**:
```sql
UPDATE users 
SET departemen = 'Administration', jabatan = 'Administrator'
WHERE email = '<EMAIL>';
```

### **Database Errors:**

1. **Check Table Exists**:
```sql
SELECT * FROM dashboard_header_images LIMIT 1;
```

2. **Check Functions Exist**:
```sql
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_name LIKE '%dashboard_header%';
```

3. **Re-run Setup Script** if needed

### **Permission Errors:**

1. **Check RLS Policies**:
```sql
SELECT * FROM pg_policies WHERE tablename = 'dashboard_header_images';
```

2. **Test Policy**:
```sql
-- This should work for admin users
SELECT * FROM dashboard_header_images;
```

## 🎯 **Quick Setup Commands**

For quick setup, run these in order:

```sql
-- 1. Run complete setup
\i setup_complete_database.sql

-- 2. Create admin user profile (after creating in Auth UI)
INSERT INTO users (id, email, full_name, departemen, jabatan, is_active) 
VALUES ('YOUR_USER_ID', '<EMAIL>', 'Admin User', 'Administration', 'Administrator', true);

-- 3. Verify setup
SELECT * FROM get_admin_users();
SELECT * FROM get_dashboard_header_images();
```

## ✅ **Success Checklist**

- [ ] Database setup script executed successfully
- [ ] Admin user created in Supabase Auth
- [ ] Admin profile added to users table
- [ ] Admin detection function returns true
- [ ] Admin menu appears in app profile
- [ ] Dashboard header management accessible
- [ ] Can add/edit/delete header images

---

**🎯 RESULT: Admin users siap untuk mengelola Dashboard Header Images!** 🚀✨
