#!/bin/bash

# Pre-commit hook for documentation validation
# This script runs before each commit to ensure documentation structure compliance

echo "🔍 Running documentation validation..."

# Run documentation structure validation
echo "📁 Checking documentation structure..."
node scripts/validate-docs-structure.js

if [ $? -ne 0 ]; then
    echo "❌ Documentation structure validation failed!"
    echo "   Please fix the structure issues before committing."
    echo "   See docs/AI_AGENT_DOCUMENTATION_RULES.md for guidelines."
    exit 1
fi

echo "✅ Documentation structure validation passed!"

# Check for common documentation issues
echo "📄 Checking for common documentation issues..."

# Check for files in wrong locations
if find docs/ -maxdepth 1 -name "*.md" -not -name "README.md" -not -name "index.md" -not -name "implementation-progress.md" -not -name "AI_AGENT_DOCUMENTATION_RULES.md" | grep -q .; then
    echo "❌ Found unauthorized .md files in docs/ root!"
    echo "   Move these files to appropriate folders:"
    find docs/ -maxdepth 1 -name "*.md" -not -name "README.md" -not -name "index.md" -not -name "implementation-progress.md" -not -name "AI_AGENT_DOCUMENTATION_RULES.md"
    exit 1
fi

# Check for duplicate setup files
if find docs/ -name "*setup*" -o -name "*SETUP*" -o -name "*guide*" -o -name "*GUIDE*" | grep -v "development/setup.md" | grep -q .; then
    echo "⚠️  Found potential duplicate setup/guide files:"
    find docs/ -name "*setup*" -o -name "*SETUP*" -o -name "*guide*" -o -name "*GUIDE*" | grep -v "development/setup.md"
    echo "   Consider merging content into development/setup.md"
fi

echo "✅ Documentation validation completed successfully!"
echo "🎯 Remember: development/setup.md is the master setup guide"
echo "📋 Follow docs/AI_AGENT_DOCUMENTATION_RULES.md for consistency"

exit 0
