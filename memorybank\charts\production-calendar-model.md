# Production Calendar Model Implementation

## Cortex 7 Metadata
- **Document Type**: Implementation Guide
- **Component**: Production Calendar Model for Daily Charts
- **Technology**: React Native, TypeScript, Production Calendar System
- **Tags**: `#production-calendar` `#daily-charts` `#monthly-filtering` `#variable-data-points`
- **Last Updated**: 2025-01-19
- **Status**: Implemented ✅

## Overview
Implementation of a production calendar model for daily chart data filtering, where production months may start on different dates than standard calendar months, providing variable data points based on the production calendar system.

## Problem Statement

### Business Requirement
- Mining operations follow a production calendar where months start on custom dates
- Example: July 2025 production month starts on June 30, 2025, not July 1
- Daily charts need to show data for the current production month period only
- Number of data points should vary based on how many days have passed in the production month

### Technical Challenge
- Standard calendar filtering doesn't align with production operations
- Need two-stage filtering: monthly field + date range
- Variable chart data count based on production calendar
- Maintain chronological ordering within production month periods

## Production Calendar Model

### Calendar Configuration
```typescript
interface ProductionMonth {
  name: string;           // "July 2025"
  startDate: string;      // "2025-06-30" (ISO date)
  endDate: string;        // "2025-07-29" (ISO date)
  year: number;           // 2025
  month: number;          // 7
}
```

### Example Production Calendar
```typescript
const PRODUCTION_CALENDAR_2025 = [
  {
    name: "July 2025",
    startDate: "2025-06-30",  // Starts June 30
    endDate: "2025-07-29",    // Ends July 29
    year: 2025,
    month: 7
  },
  {
    name: "August 2025", 
    startDate: "2025-07-30",  // Starts July 30
    endDate: "2025-08-29",    // Ends August 29
    year: 2025,
    month: 8
  }
];
```

## Two-Stage Filtering Process

### Stage 1: Primary Filter by Monthly Column
```typescript
// Filter by monthly field in database
const monthlyFiltered = dailyData.filter(item => item.monthly === monthlyField);
```

**Purpose**: Get all data records that belong to the specified production month

### Stage 2: Secondary Filter by Date Range
```typescript
// Filter by production month date range up to current date
const today = new Date().toISOString().split('T')[0];
const endDate = today <= productionMonth.endDate ? today : productionMonth.endDate;

const dateFiltered = monthlyFiltered.filter(item => {
  return item.date >= productionMonth.startDate && item.date <= endDate;
});
```

**Purpose**: Show only completed days within the current production month period

## Implementation Details

### Core Function: filterDailyDataForProductionMonth
```typescript
export const filterDailyDataForProductionMonth = (
  dailyData: any[], 
  monthlyField: string
): any[] => {
  // Step 1: Primary filter by monthly column
  const monthlyFiltered = dailyData.filter(item => item.monthly === monthlyField);
  
  // Step 2: Get production month configuration
  const productionMonth = getProductionMonth(monthlyField);
  if (!productionMonth) {
    return monthlyFiltered; // Fallback to monthly filter only
  }
  
  // Step 3: Secondary filter by date range (production month start to today)
  const today = new Date().toISOString().split('T')[0];
  const endDate = today <= productionMonth.endDate ? today : productionMonth.endDate;
  
  const dateFiltered = monthlyFiltered.filter(item => {
    return item.date >= productionMonth.startDate && item.date <= endDate;
  });
  
  // Step 4: Ensure chronological ordering
  return dateFiltered.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
};
```

### Integration in ProductionOverviewScreen
```typescript
if (selectedPeriod === 'Daily') {
  const currentProductionMonth = getCurrentProductionMonth();
  
  if (currentProductionMonth) {
    // Use production calendar filtering
    processedData = filterDailyDataForProductionMonth(processedData, currentProductionMonth.name);
    
    // Log production month info
    const dateRange = getProductionMonthDateRange(currentProductionMonth.name);
    console.log(`Production month: ${dateRange.startDate} to ${dateRange.currentEndDate} (${dateRange.daysCount} days)`);
  } else {
    // Fallback to standard filtering
    processedData = processedData
      .filter(item => new Date(item.date) <= today)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }
}
```

## Dynamic Chart Data Count

### Variable Data Points
Unlike fixed chart data counts, the production calendar model provides variable data points:

```typescript
// July 2025 example (starts June 30)
// If today is July 15, 2025:
Production Month: July 2025
Start Date: June 30, 2025
Current End Date: July 15, 2025
Data Points: 16 days (June 30 + 15 July days)
Chart Labels: [30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
```

### Benefits of Variable Count
1. **Accurate Representation**: Shows actual production days completed
2. **Real-time Updates**: Data points increase as days progress
3. **Production Alignment**: Matches operational calendar system
4. **Flexible Display**: Adapts to different production month lengths

## Database Query Enhancement

### Current Query Structure
```sql
SELECT * FROM daily_production_metrics 
WHERE monthly = 'July 2025'
  AND date >= '2025-06-30' 
  AND date <= '2025-07-15'
ORDER BY date ASC;
```

### Query Benefits
- **Efficient Filtering**: Database-level filtering reduces data transfer
- **Chronological Ordering**: Maintains proper date sequence
- **Production Alignment**: Respects production calendar boundaries

## Example Scenarios

### Scenario 1: Current Production Month (July 2025)
```
Today: July 15, 2025
Production Month: July 2025 (June 30 - July 29)
Filter Range: June 30, 2025 to July 15, 2025
Data Points: 16 days
Chart Labels: [30, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
```

### Scenario 2: Previous Production Month (June 2025)
```
Production Month: June 2025 (May 30 - June 29)
Filter Range: May 30, 2025 to June 29, 2025 (complete month)
Data Points: 31 days
Chart Labels: [30, 31, 1, 2, 3, ..., 28, 29]
```

### Scenario 3: Future Production Month (August 2025)
```
Production Month: August 2025 (July 30 - August 29)
Filter Range: July 30, 2025 to July 15, 2025 (no data - future month)
Data Points: 0 days
Chart Labels: []
```

## User Interface Enhancements

### Production Month Information Display
```typescript
{selectedPeriod === 'Daily' && (() => {
  const currentProductionMonth = getCurrentProductionMonth();
  if (currentProductionMonth) {
    const dateRange = getProductionMonthDateRange(currentProductionMonth.name);
    return `\n📅 Production Month: ${currentProductionMonth.name} (${dateRange?.startDate} to ${dateRange?.currentEndDate}, ${dateRange?.daysCount} days)`;
  }
  return '';
})()}
```

### Benefits for Users
1. **Clear Context**: Shows which production month is being displayed
2. **Date Range Visibility**: Users see the actual date range
3. **Progress Indication**: Shows how many days have passed in the production month
4. **Transparency**: Clear understanding of data scope

## Error Handling and Fallbacks

### Production Month Not Found
```typescript
if (!productionMonth) {
  console.warn(`Production month configuration not found for: ${monthlyField}`);
  // Fallback to monthly filter only
  return monthlyFiltered;
}
```

### No Current Production Month
```typescript
if (!currentProductionMonth) {
  console.warn('No current production month found, using standard date filtering');
  // Fallback to standard date filtering
  processedData = processedData
    .filter(item => new Date(item.date) <= today)
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}
```

## Testing and Verification

### Test Scenarios Covered
1. **Current Production Month**: Variable data points based on current date
2. **Previous Production Month**: Complete production month data
3. **Future Production Month**: No data (future dates excluded)
4. **Chronological Ordering**: Proper date sequence within production month
5. **Two-Stage Filtering**: Both monthly and date range filters working

### Test Results
```
June 2025: 4 data points (complete month)
July 2025: 5 data points (current month, partial)
August 2025: 0 data points (future month)
Chronological Order: ✅ CORRECT for all months
```

## Performance Considerations

### Efficient Processing
- **Database-Level Filtering**: Reduces data transfer
- **Single Pass Processing**: Minimal client-side operations
- **Cached Configuration**: Production calendar loaded once
- **Optimized Sorting**: Leverages database ordering

### Memory Usage
- **Variable Data Size**: Only loads relevant production month data
- **Efficient Filtering**: No unnecessary data processing
- **Mobile Optimized**: Suitable for mobile device constraints

## Future Enhancements

### Planned Improvements
1. **Dynamic Calendar Loading**: Load production calendar from database
2. **Multi-Year Support**: Extended calendar configuration
3. **Time Zone Handling**: Proper time zone support for global operations
4. **Calendar Validation**: Validate production calendar configuration

### Advanced Features
1. **Production Month Selection**: Allow users to select different production months
2. **Calendar Visualization**: Visual production calendar display
3. **Comparative Analysis**: Compare different production months
4. **Export Functionality**: Export production month data

---
*Production calendar model implementation following Cortex 7 standards for comprehensive production-aligned data filtering documentation.*
