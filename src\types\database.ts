// Database types for Mining Operations App

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      dashboard_header_images: {
        Row: {
          id: string
          title: string
          description: string | null
          image_url: string
          thumbnail_url: string | null
          display_order: number
          is_active: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          image_url: string
          thumbnail_url?: string | null
          display_order?: number
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          image_url?: string
          thumbnail_url?: string | null
          display_order?: number
          is_active?: boolean
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          phone: string | null
          location_id: string | null
          avatar_url: string | null
          employee_id: string | null
          hire_date: string | null
          nik: string | null
          departemen: string | null
          jabatan: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          phone?: string | null
          location_id?: string | null
          avatar_url?: string | null
          employee_id?: string | null
          hire_date?: string | null
          nik?: string | null
          departemen?: string | null
          jabatan?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          phone?: string | null
          location_id?: string | null
          avatar_url?: string | null
          employee_id?: string | null
          hire_date?: string | null
          nik?: string | null
          departemen?: string | null
          jabatan?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      locations: {
        Row: {
          id: string
          name: string
          description: string | null
          location_type: 'mine_site' | 'processing_plant' | 'office' | 'warehouse'
          coordinates: unknown | null
          address: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          location_type: 'mine_site' | 'processing_plant' | 'office' | 'warehouse'
          coordinates?: unknown | null
          address?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          location_type?: 'mine_site' | 'processing_plant' | 'office' | 'warehouse'
          coordinates?: unknown | null
          address?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      equipment: {
        Row: {
          id: string
          name: string
          model: string | null
          serial_number: string | null
          manufacturer: string | null
          equipment_type: 'excavator' | 'dump_truck' | 'drill' | 'conveyor' | 'crusher' | 'loader' | 'bulldozer' | 'grader'
          location_id: string | null
          status: 'operational' | 'maintenance' | 'down' | 'retired'
          purchase_date: string | null
          last_maintenance_date: string | null
          next_maintenance_due: string | null
          specifications: Json | null
          operating_hours: number
          fuel_capacity: number | null
          max_load_capacity: number | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          model?: string | null
          serial_number?: string | null
          manufacturer?: string | null
          equipment_type: 'excavator' | 'dump_truck' | 'drill' | 'conveyor' | 'crusher' | 'loader' | 'bulldozer' | 'grader'
          location_id?: string | null
          status?: 'operational' | 'maintenance' | 'down' | 'retired'
          purchase_date?: string | null
          last_maintenance_date?: string | null
          next_maintenance_due?: string | null
          specifications?: Json | null
          operating_hours?: number
          fuel_capacity?: number | null
          max_load_capacity?: number | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          model?: string | null
          serial_number?: string | null
          manufacturer?: string | null
          equipment_type?: 'excavator' | 'dump_truck' | 'drill' | 'conveyor' | 'crusher' | 'loader' | 'bulldozer' | 'grader'
          location_id?: string | null
          status?: 'operational' | 'maintenance' | 'down' | 'retired'
          purchase_date?: string | null
          last_maintenance_date?: string | null
          next_maintenance_due?: string | null
          specifications?: Json | null
          operating_hours?: number
          fuel_capacity?: number | null
          max_load_capacity?: number | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      safety_incidents: {
        Row: {
          id: string
          incident_number: string
          reported_by: string
          incident_date: string
          location_id: string
          severity: 'low' | 'medium' | 'high' | 'critical'
          incident_type: 'injury' | 'near_miss' | 'equipment_failure' | 'environmental' | 'security'
          title: string
          description: string
          corrective_actions: string | null
          equipment_id: string | null
          injured_person_name: string | null
          witnesses: string[] | null
          status: 'reported' | 'investigating' | 'resolved' | 'closed'
          investigation_notes: string | null
          resolved_date: string | null
          attachments: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          incident_number: string
          reported_by: string
          incident_date: string
          location_id: string
          severity: 'low' | 'medium' | 'high' | 'critical'
          incident_type: 'injury' | 'near_miss' | 'equipment_failure' | 'environmental' | 'security'
          title: string
          description: string
          corrective_actions?: string | null
          equipment_id?: string | null
          injured_person_name?: string | null
          witnesses?: string[] | null
          status?: 'reported' | 'investigating' | 'resolved' | 'closed'
          investigation_notes?: string | null
          resolved_date?: string | null
          attachments?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          incident_number?: string
          reported_by?: string
          incident_date?: string
          location_id?: string
          severity?: 'low' | 'medium' | 'high' | 'critical'
          incident_type?: 'injury' | 'near_miss' | 'equipment_failure' | 'environmental' | 'security'
          title?: string
          description?: string
          corrective_actions?: string | null
          equipment_id?: string | null
          injured_person_name?: string | null
          witnesses?: string[] | null
          status?: 'reported' | 'investigating' | 'resolved' | 'closed'
          investigation_notes?: string | null
          resolved_date?: string | null
          attachments?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      production_reports: {
        Row: {
          id: string
          report_number: string
          created_by: string
          report_date: string
          shift: 'day' | 'night' | 'swing'
          location_id: string
          production_metrics: Json
          equipment_used: string[] | null
          total_tonnage: number | null
          operating_hours: number | null
          downtime_hours: number | null
          fuel_consumed: number | null
          notes: string | null
          weather_conditions: string | null
          crew_size: number | null
          approved_by: string | null
          approved_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          report_number: string
          created_by: string
          report_date: string
          shift: 'day' | 'night' | 'swing'
          location_id: string
          production_metrics: Json
          equipment_used?: string[] | null
          total_tonnage?: number | null
          operating_hours?: number | null
          downtime_hours?: number | null
          fuel_consumed?: number | null
          notes?: string | null
          weather_conditions?: string | null
          crew_size?: number | null
          approved_by?: string | null
          approved_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          report_number?: string
          created_by?: string
          report_date?: string
          shift?: 'day' | 'night' | 'swing'
          location_id?: string
          production_metrics?: Json
          equipment_used?: string[] | null
          total_tonnage?: number | null
          operating_hours?: number | null
          downtime_hours?: number | null
          fuel_consumed?: number | null
          notes?: string | null
          weather_conditions?: string | null
          crew_size?: number | null
          approved_by?: string | null
          approved_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "production_reports_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "production_reports_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "production_reports_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          }
        ]
      }
      daily_production_metrics: {
        Row: {
          id: string
          date: string
          monthly: string
          week: number
          actual_ob: number
          plan_ob: number
          actual_ore: number
          plan_ore: number
          actual_rain: number
          plan_rain: number
          actual_slippery: number
          plan_slippery: number
          actual_fuel: number
          plan_fuel: number
          location_id: string
          created_by: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          date: string
          monthly: string
          week: number
          actual_ob?: number
          plan_ob?: number
          actual_ore?: number
          plan_ore?: number
          actual_rain?: number
          plan_rain?: number
          actual_slippery?: number
          plan_slippery?: number
          actual_fuel?: number
          plan_fuel?: number
          location_id: string
          created_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          date?: string
          monthly?: string
          week?: number
          actual_ob?: number
          plan_ob?: number
          actual_ore?: number
          plan_ore?: number
          actual_rain?: number
          plan_rain?: number
          actual_slippery?: number
          plan_slippery?: number
          actual_fuel?: number
          plan_fuel?: number
          location_id?: string
          created_by?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "daily_production_metrics_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "daily_production_metrics_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          }
        ]
      }
      shift_reports: {
        Row: {
          id: string
          report_number: string
          created_by: string
          report_date: string
          shift: 'day' | 'night' | 'swing'
          location_id: string
          production_metrics: Json
          equipment_used: string[] | null
          total_tonnage: number | null
          operating_hours: number | null
          downtime_hours: number | null
          fuel_consumed: number | null
          notes: string | null
          weather_conditions: string | null
          crew_size: number | null
          approved_by: string | null
          approved_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          report_number: string
          created_by: string
          report_date: string
          shift: 'day' | 'night' | 'swing'
          location_id: string
          production_metrics: Json
          equipment_used?: string[] | null
          total_tonnage?: number | null
          operating_hours?: number | null
          downtime_hours?: number | null
          fuel_consumed?: number | null
          notes?: string | null
          weather_conditions?: string | null
          crew_size?: number | null
          approved_by?: string | null
          approved_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          report_number?: string
          created_by?: string
          report_date?: string
          shift?: 'day' | 'night' | 'swing'
          location_id?: string
          production_metrics?: Json
          equipment_used?: string[] | null
          total_tonnage?: number | null
          operating_hours?: number | null
          downtime_hours?: number | null
          fuel_consumed?: number | null
          notes?: string | null
          weather_conditions?: string | null
          crew_size?: number | null
          approved_by?: string | null
          approved_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      maintenance_records: {
        Row: {
          id: string
          maintenance_number: string
          equipment_id: string
          performed_by: string
          maintenance_type: 'preventive' | 'corrective' | 'emergency' | 'inspection'
          scheduled_date: string
          completed_date: string | null
          description: string
          parts_used: Json | null
          labor_hours: number | null
          total_cost: number | null
          next_maintenance_due: string | null
          status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
          notes: string | null
          attachments: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          maintenance_number: string
          equipment_id: string
          performed_by: string
          maintenance_type: 'preventive' | 'corrective' | 'emergency' | 'inspection'
          scheduled_date: string
          completed_date?: string | null
          description: string
          parts_used?: Json | null
          labor_hours?: number | null
          total_cost?: number | null
          next_maintenance_due?: string | null
          status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
          notes?: string | null
          attachments?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          maintenance_number?: string
          equipment_id?: string
          performed_by?: string
          maintenance_type?: 'preventive' | 'corrective' | 'emergency' | 'inspection'
          scheduled_date?: string
          completed_date?: string | null
          description?: string
          parts_used?: Json | null
          labor_hours?: number | null
          total_cost?: number | null
          next_maintenance_due?: string | null
          status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
          notes?: string | null
          attachments?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
