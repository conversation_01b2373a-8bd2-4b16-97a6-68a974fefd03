# Testing & Deployment Guide - Mining Operations App

## 🧪 **Testing Strategy**

### **Testing Pyramid**
1. **Unit Tests** (70%) - Individual functions and components
2. **Integration Tests** (20%) - Module interactions and API calls
3. **E2E Tests** (10%) - Complete user workflows

### **Testing Tools**
- **Jest** - Unit testing framework
- **React Native Testing Library** - Component testing
- **Detox** - E2E testing for React Native
- **MSW (Mock Service Worker)** - API mocking
- **Flipper** - Debugging and testing tools

## 📁 **Testing Structure**

```
__tests__/
├── unit/
│   ├── components/
│   ├── services/
│   ├── utils/
│   └── hooks/
├── integration/
│   ├── auth/
│   ├── production/
│   ├── equipment/
│   └── safety/
├── e2e/
│   ├── auth.e2e.js
│   ├── production.e2e.js
│   ├── equipment.e2e.js
│   └── safety.e2e.js
├── mocks/
│   ├── api/
│   ├── data/
│   └── services/
└── setup/
    ├── jest.setup.js
    ├── detox.config.js
    └── test-utils.tsx
```

## 🔧 **Test Configuration**

### **Jest Configuration**
```javascript
// jest.config.js
module.exports = {
  preset: 'react-native',
  setupFilesAfterEnv: [
    '<rootDir>/__tests__/setup/jest.setup.js',
    '@testing-library/jest-native/extend-expect'
  ],
  testMatch: [
    '<rootDir>/__tests__/**/*.test.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}'
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/constants/**',
    '!src/assets/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': 'identity-obj-proxy'
  },
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|expo|@expo|@react-navigation|react-native-chart-kit)/)'
  ]
};
```

### **Test Setup**
```javascript
// __tests__/setup/jest.setup.js
import 'react-native-gesture-handler/jestSetup';
import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// Mock Expo modules
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn(),
  setItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

jest.mock('expo-local-authentication', () => ({
  hasHardwareAsync: jest.fn(() => Promise.resolve(true)),
  isEnrolledAsync: jest.fn(() => Promise.resolve(true)),
  authenticateAsync: jest.fn(() => Promise.resolve({ success: true })),
}));

jest.mock('expo-sqlite', () => ({
  openDatabase: jest.fn(() => ({
    transaction: jest.fn(),
    executeSql: jest.fn(),
  })),
}));

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    reset: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
  useFocusEffect: jest.fn(),
}));

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
      getSession: jest.fn(),
      refreshSession: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
  })),
}));

// Global test utilities
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock timers
jest.useFakeTimers();
```

## 🧪 **Unit Tests Examples**

### **Component Testing**
```typescript
// __tests__/unit/components/Button.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '@/components/base/Button';

describe('Button Component', () => {
  it('renders correctly with title', () => {
    const { getByText } = render(
      <Button title="Test Button" onPress={jest.fn()} />
    );
    
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('shows loading state', () => {
    const { getByTestId } = render(
      <Button 
        title="Test Button" 
        onPress={jest.fn()} 
        loading={true}
        testID="button"
      />
    );
    
    expect(getByTestId('button')).toHaveStyle({ opacity: 0.7 });
  });

  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button 
        title="Test Button" 
        onPress={mockOnPress} 
        disabled={true}
      />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('renders with different variants', () => {
    const variants = ['primary', 'secondary', 'danger', 'ghost'] as const;
    
    variants.forEach(variant => {
      const { getByText } = render(
        <Button 
          title={`${variant} Button`} 
          onPress={jest.fn()} 
          variant={variant}
        />
      );
      
      expect(getByText(`${variant} Button`)).toBeTruthy();
    });
  });
});
```

### **Service Testing**
```typescript
// __tests__/unit/services/AuthService.test.ts
import { AuthService } from '@/features/auth/services/AuthService';
import { supabase } from '@/services/supabase';

// Mock Supabase
jest.mock('@/services/supabase');
const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        created_at: '2024-01-01T00:00:00Z'
      };
      
      const mockSession = {
        access_token: 'token123',
        refresh_token: 'refresh123',
        expires_at: Date.now() + 3600000
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null
      });

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: '123',
            email: '<EMAIL>',
            first_name: 'John',
            last_name: 'Doe',
            role: 'Operator'
          },
          error: null
        })
      } as any);

      const result = await AuthService.login({
        email: '<EMAIL>',
        password: 'password123'
      });

      expect(result.user.email).toBe('<EMAIL>');
      expect(result.accessToken).toBe('token123');
      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    it('should throw error with invalid credentials', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid credentials' }
      });

      await expect(AuthService.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })).rejects.toThrow('Login failed: Invalid credentials');
    });
  });

  describe('register', () => {
    it('should register new user successfully', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        created_at: '2024-01-01T00:00:00Z'
      };

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser, session: null },
        error: null
      });

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: '123',
            email: '<EMAIL>',
            first_name: 'Jane',
            last_name: 'Doe',
            role: 'Observer'
          },
          error: null
        })
      } as any);

      const result = await AuthService.register({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Doe'
      });

      expect(result.user.email).toBe('<EMAIL>');
      expect(mockSupabase.auth.signUp).toHaveBeenCalled();
    });
  });
});
```

### **Hook Testing**
```typescript
// __tests__/unit/hooks/useAuth.test.tsx
import React from 'react';
import { renderHook, act } from '@testing-library/react-hooks';
import { AuthProvider, useAuth } from '@/features/auth/hooks/useAuth';
import { AuthService } from '@/features/auth/services/AuthService';

// Mock AuthService
jest.mock('@/features/auth/services/AuthService');
const mockAuthService = AuthService as jest.Mocked<typeof AuthService>;

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>{children}</AuthProvider>
);

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    expect(result.current.loading).toBe(true);
    expect(result.current.user).toBe(null);
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should login successfully', async () => {
    const mockSession = {
      user: {
        id: '123',
        email: '<EMAIL>',
        fullName: 'John Doe',
        role: 'Operator'
      },
      accessToken: 'token123',
      refreshToken: 'refresh123',
      expiresAt: Date.now() + 3600000
    };

    mockAuthService.login.mockResolvedValue(mockSession);

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.login({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    expect(result.current.user).toEqual(mockSession.user);
    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.loading).toBe(false);
  });

  it('should handle login error', async () => {
    mockAuthService.login.mockRejectedValue(new Error('Login failed'));

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      try {
        await result.current.login({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
      } catch (error) {
        // Expected error
      }
    });

    expect(result.current.error).toBe('Login failed');
    expect(result.current.user).toBe(null);
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should logout successfully', async () => {
    // First login
    const mockSession = {
      user: {
        id: '123',
        email: '<EMAIL>',
        fullName: 'John Doe',
        role: 'Operator'
      },
      accessToken: 'token123',
      refreshToken: 'refresh123',
      expiresAt: Date.now() + 3600000
    };

    mockAuthService.login.mockResolvedValue(mockSession);
    mockAuthService.logout.mockResolvedValue();

    const { result } = renderHook(() => useAuth(), { wrapper });

    // Login first
    await act(async () => {
      await result.current.login({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    expect(result.current.isAuthenticated).toBe(true);

    // Then logout
    await act(async () => {
      await result.current.logout();
    });

    expect(result.current.user).toBe(null);
    expect(result.current.isAuthenticated).toBe(false);
  });
});
```

## 🔗 **Integration Tests**

### **API Integration Testing**
```typescript
// __tests__/integration/production/ProductionService.test.ts
import { ProductionService } from '@/features/production/services/ProductionService';
import { database } from '@/services/offline/database/DatabaseService';

describe('ProductionService Integration', () => {
  beforeAll(async () => {
    await database.initialize();
  });

  beforeEach(async () => {
    // Clean up database
    await database.query('DELETE FROM production_records');
    await database.query('DELETE FROM sync_queue');
  });

  afterAll(async () => {
    await database.close();
  });

  it('should create production record and add to sync queue', async () => {
    const productionData = {
      locationId: 'loc-1',
      shiftId: 'shift-1',
      materialType: 'Coal' as const,
      quantity: 100,
      unit: 'tons',
      recordedAt: new Date().toISOString(),
      recordedBy: 'user-1',
      notes: 'Test production record'
    };

    const result = await ProductionService.createProductionRecord(productionData);

    expect(result.id).toBeDefined();
    expect(result.quantity).toBe(100);
    expect(result.materialType).toBe('Coal');

    // Check if record was added to sync queue
    const syncItems = await database.select(
      'sync_queue',
      ['*'],
      'table_name = ? AND record_id = ?',
      ['production_records', result.id]
    );

    expect(syncItems).toHaveLength(1);
    expect(syncItems[0].operation).toBe('CREATE');
  });

  it('should handle offline production summary calculation', async () => {
    // Insert test data
    const testRecords = [
      {
        id: 'prod-1',
        location_id: 'loc-1',
        material_type: 'Coal',
        quantity: 50,
        recorded_at: '2024-01-01T08:00:00Z',
        recorded_by: 'user-1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'prod-2',
        location_id: 'loc-1',
        material_type: 'Coal',
        quantity: 75,
        recorded_at: '2024-01-01T16:00:00Z',
        recorded_by: 'user-1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    for (const record of testRecords) {
      await database.insert('production_records', record);
    }

    const summary = await ProductionService.getProductionSummary(
      '2024-01-01',
      '2024-01-01',
      'loc-1'
    );

    expect(summary.totalProduction).toBe(125);
    expect(summary.materialBreakdown).toHaveLength(1);
    expect(summary.materialBreakdown[0].materialType).toBe('Coal');
    expect(summary.materialBreakdown[0].quantity).toBe(125);
  });
});
```

## 🎭 **E2E Tests**

### **Detox Configuration**
```javascript
// detox.config.js
module.exports = {
  testRunner: 'jest',
  runnerConfig: 'e2e/config.json',
  configurations: {
    'ios.sim.debug': {
      binaryPath: 'ios/build/Build/Products/Debug-iphonesimulator/MiningOperationsApp.app',
      build: 'xcodebuild -workspace ios/MiningOperationsApp.xcworkspace -scheme MiningOperationsApp -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build',
      type: 'ios.simulator',
      device: {
        type: 'iPhone 14'
      }
    },
    'android.emu.debug': {
      binaryPath: 'android/app/build/outputs/apk/debug/app-debug.apk',
      build: 'cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug',
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_4_API_30'
      }
    }
  }
};
```

### **E2E Test Example**
```javascript
// __tests__/e2e/auth.e2e.js
describe('Authentication Flow', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should show login screen on app launch', async () => {
    await expect(element(by.text('Mining Operations'))).toBeVisible();
    await expect(element(by.text('Sign In'))).toBeVisible();
  });

  it('should login with valid credentials', async () => {
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('password123');
    await element(by.text('Sign In')).tap();
    
    // Wait for navigation to dashboard
    await waitFor(element(by.text('Dashboard')))
      .toBeVisible()
      .withTimeout(5000);
  });

  it('should show error with invalid credentials', async () => {
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('wrongpassword');
    await element(by.text('Sign In')).tap();
    
    await expect(element(by.text('Login failed'))).toBeVisible();
  });

  it('should navigate to registration screen', async () => {
    await element(by.text('Sign Up')).tap();
    await expect(element(by.text('Create Account'))).toBeVisible();
  });

  it('should complete registration flow', async () => {
    await element(by.text('Sign Up')).tap();
    
    await element(by.id('first-name-input')).typeText('John');
    await element(by.id('last-name-input')).typeText('Doe');
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('password123');
    await element(by.id('confirm-password-input')).typeText('password123');
    
    await element(by.text('Create Account')).tap();
    
    await waitFor(element(by.text('Dashboard')))
      .toBeVisible()
      .withTimeout(5000);
  });
});
```

## 📱 **Performance Testing**

### **Performance Monitoring**
```typescript
// __tests__/performance/performance.test.ts
import { performance } from 'perf_hooks';

describe('Performance Tests', () => {
  it('should load dashboard within 2 seconds', async () => {
    const start = performance.now();
    
    // Simulate dashboard data loading
    await loadDashboardData();
    
    const end = performance.now();
    const loadTime = end - start;
    
    expect(loadTime).toBeLessThan(2000); // 2 seconds
  });

  it('should handle large production datasets efficiently', async () => {
    const largeDataset = generateProductionData(10000); // 10k records
    
    const start = performance.now();
    await processProductionData(largeDataset);
    const end = performance.now();
    
    expect(end - start).toBeLessThan(5000); // 5 seconds
  });
});
```

## 🚀 **Deployment Configuration**

### **Environment Configuration**
```typescript
// app.config.ts
import { ExpoConfig, ConfigContext } from '@expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'Mining Operations',
  slug: 'mining-operations-app',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'light',
  splash: {
    image: './assets/splash.png',
    resizeMode: 'contain',
    backgroundColor: '#2563EB'
  },
  updates: {
    fallbackToCacheTimeout: 0,
    url: 'https://u.expo.dev/your-project-id'
  },
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.yourcompany.miningoperations',
    buildNumber: '1.0.0'
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#2563EB'
    },
    package: 'com.yourcompany.miningoperations',
    versionCode: 1
  },
  web: {
    favicon: './assets/favicon.png'
  },
  extra: {
    eas: {
      projectId: 'your-project-id'
    },
    supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
    supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
  }
});
```

### **EAS Build Configuration**
```json
// eas.json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "resourceClass": "m1-medium"
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "resourceClass": "m1-medium"
      }
    },
    "production": {
      "ios": {
        "resourceClass": "m1-medium"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
```

### **Deployment Scripts**
```json
// package.json scripts
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "detox test",
    "test:e2e:build": "detox build",
    "lint": "eslint . --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix",
    "type-check": "tsc --noEmit",
    "build:ios": "eas build --platform ios",
    "build:android": "eas build --platform android",
    "build:all": "eas build --platform all",
    "submit:ios": "eas submit --platform ios",
    "submit:android": "eas submit --platform android",
    "update": "eas update",
    "preview": "eas build --profile preview"
  }
}
```

This completes the comprehensive testing and deployment guide for the Mining Operations App. The documentation now covers all aspects from project setup to production deployment.
