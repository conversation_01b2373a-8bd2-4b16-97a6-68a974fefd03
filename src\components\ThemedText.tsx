import React from 'react';
import { Text, TextStyle, TextProps } from 'react-native';
import { useThemeColors } from '../contexts/ThemeContext';
import { Layout } from '../constants/layout';

interface ThemedTextProps extends TextProps {
  variant?: 'primary' | 'secondary' | 'light' | 'inverse' | 'success' | 'warning' | 'error';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  align?: 'left' | 'center' | 'right';
  children: React.ReactNode;
}

const ThemedText: React.FC<ThemedTextProps> = ({
  variant = 'primary',
  size = 'md',
  weight = 'normal',
  align = 'left',
  style,
  children,
  ...props
}) => {
  const colors = useThemeColors();

  const getTextColor = () => {
    switch (variant) {
      case 'primary': return colors.textPrimary;
      case 'secondary': return colors.textSecondary;
      case 'light': return colors.textLight;
      case 'inverse': return colors.textInverse;
      case 'success': return colors.success;
      case 'warning': return colors.warning;
      case 'error': return colors.error;
      default: return colors.textPrimary;
    }
  };

  const getFontSize = () => {
    switch (size) {
      case 'xs': return Layout.fontSize.xs;
      case 'sm': return Layout.fontSize.sm;
      case 'md': return Layout.fontSize.md;
      case 'lg': return Layout.fontSize.lg;
      case 'xl': return Layout.fontSize.xl;
      case 'xxl': return Layout.fontSize.xxl;
      default: return Layout.fontSize.md;
    }
  };

  const getFontWeight = (): TextStyle['fontWeight'] => {
    switch (weight) {
      case 'normal': return '400';
      case 'medium': return '500';
      case 'semibold': return '600';
      case 'bold': return '700';
      default: return '400';
    }
  };

  const textStyle: TextStyle = {
    color: getTextColor(),
    fontSize: getFontSize(),
    fontWeight: getFontWeight(),
    textAlign: align,
  };

  return (
    <Text style={[textStyle, style]} {...props}>
      {children}
    </Text>
  );
};

export default ThemedText;
