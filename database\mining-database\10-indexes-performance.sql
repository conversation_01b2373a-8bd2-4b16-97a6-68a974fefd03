-- =====================================================
-- Mining Operations Database - Indexes & Performance
-- =====================================================
-- File: 10-indexes-performance.sql
-- Description: Additional indexes and performance optimizations
-- Dependencies: All previous schema files
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- Daily mining report analysis indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_daily_mining_report_location_date_achievement 
ON daily_mining_report(location, report_date, total_achievement_percent);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_daily_mining_report_date_status_location 
ON daily_mining_report(report_date, status, location);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_daily_mining_report_year_month_location 
ON daily_mining_report(year_number, month_number, location);

-- Equipment performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_equipment_metrics_equipment_date_utilization 
ON equipment_metrics(equipment_id, recorded_date, utilization_rate);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_equipment_metrics_location_health_score 
ON equipment_metrics(location, health_score, recorded_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_equipment_status_location_type 
ON equipment(status, current_location, type);

-- Safety analysis indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_safety_incidents_location_date_severity 
ON safety_incidents(location, occurred_at, severity);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_safety_incidents_type_status_investigator 
ON safety_incidents(incident_type, status, assigned_investigator);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_safety_training_date_type_status 
ON safety_training(scheduled_date, training_type, status);

-- Maintenance optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_equipment_status_priority 
ON work_orders(equipment_id, status, priority);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_assigned_scheduled_status 
ON work_orders(assigned_to, scheduled_start_date, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_maintenance_schedules_equipment_due_active 
ON maintenance_schedules(equipment_id, next_due_date, is_active);

-- =====================================================
-- PARTIAL INDEXES FOR FILTERED QUERIES
-- =====================================================

-- Indexes on active/current records only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_equipment_active_location 
ON equipment(current_location, type) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_profiles_active_role 
ON user_profiles(role, primary_location) 
WHERE is_active = true;

-- Indexes on pending/open items
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_open_priority 
ON work_orders(priority, scheduled_start_date) 
WHERE status IN ('Created', 'Assigned', 'In Progress');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_safety_incidents_open_severity 
ON safety_incidents(severity, occurred_at) 
WHERE status IN ('Reported', 'Under Investigation');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_daily_reports_pending_approval 
ON daily_mining_report(location, report_date) 
WHERE status IN ('Draft', 'Submitted');

-- Indexes on performance issues
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_equipment_low_health 
ON equipment_metrics(equipment_id, recorded_date, health_score) 
WHERE health_score < 80;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_daily_reports_low_achievement 
ON daily_mining_report(location, report_date, total_achievement_percent) 
WHERE total_achievement_percent < 90;

-- =====================================================
-- EXPRESSION INDEXES
-- =====================================================

-- Date-based expression indexes for time-series queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_daily_reports_year_month 
ON daily_mining_report(EXTRACT(YEAR FROM report_date), EXTRACT(MONTH FROM report_date));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_equipment_metrics_year_month 
ON equipment_metrics(EXTRACT(YEAR FROM recorded_date), EXTRACT(MONTH FROM recorded_date));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_safety_incidents_year_month 
ON safety_incidents(EXTRACT(YEAR FROM occurred_at), EXTRACT(MONTH FROM occurred_at));

-- Text search indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_equipment_search 
ON equipment USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_safety_incidents_search 
ON safety_incidents USING gin(to_tsvector('english', title || ' ' || description));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_work_orders_search
ON work_orders USING gin(to_tsvector('english', title || ' ' || description));

-- Mining Certifications Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mining_certifications_expiring
ON mining_certifications(certificate_type, valid_until, holder_id)
WHERE valid_until BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '60 days';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mining_certifications_greencard_level
ON mining_certifications(greencard_level, valid_until)
WHERE certificate_type = 'Greencard';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mining_certifications_iut_type
ON mining_certifications(iut_license_type, iut_area_hectares)
WHERE certificate_type = 'IUT';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_mining_certifications_ott_capacity
ON mining_certifications(ott_operation_type, ott_capacity_tons_per_day)
WHERE certificate_type = 'OTT';

-- Certificate Renewals Indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_certificate_renewals_pending
ON certificate_renewals(renewal_due_date, status)
WHERE status IN ('Initiated', 'In Progress');

-- =====================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =====================================================

-- Equipment utilization summary
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_equipment_utilization AS
SELECT 
    equipment_id,
    location,
    EXTRACT(YEAR FROM recorded_date) as year,
    EXTRACT(MONTH FROM recorded_date) as month,
    COUNT(*) as record_count,
    AVG(utilization_rate) as avg_utilization,
    AVG(availability_rate) as avg_availability,
    SUM(operating_hours) as total_operating_hours,
    SUM(maintenance_hours) as total_maintenance_hours,
    AVG(health_score) as avg_health_score,
    AVG(fuel_efficiency) as avg_fuel_efficiency
FROM equipment_metrics
GROUP BY equipment_id, location, EXTRACT(YEAR FROM recorded_date), EXTRACT(MONTH FROM recorded_date);

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_equipment_utilization 
ON mv_equipment_utilization(equipment_id, location, year, month);

-- Safety metrics summary
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_safety_summary AS
SELECT 
    location,
    EXTRACT(YEAR FROM metric_date) as year,
    EXTRACT(MONTH FROM metric_date) as month,
    COUNT(*) as record_count,
    SUM(total_incidents) as total_incidents,
    SUM(near_miss_count) as total_near_miss,
    SUM(lost_time_injury_count) as total_lti,
    AVG(safety_score) as avg_safety_score,
    AVG(days_without_incident) as avg_days_without_incident,
    SUM(training_sessions_conducted) as total_training_sessions,
    AVG(training_compliance_percentage) as avg_training_compliance
FROM safety_metrics
GROUP BY location, EXTRACT(YEAR FROM metric_date), EXTRACT(MONTH FROM metric_date);

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_safety_summary 
ON mv_safety_summary(location, year, month);

-- Production performance summary
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_production_performance AS
SELECT 
    location,
    EXTRACT(YEAR FROM report_date) as year,
    EXTRACT(MONTH FROM report_date) as month,
    COUNT(*) as reporting_days,
    SUM(total_actual_material) as total_material,
    AVG(total_achievement_percent) as avg_achievement,
    AVG(actual_sr) as avg_stripping_ratio,
    AVG(actual_fr) as avg_fuel_ratio,
    AVG(operational_efficiency) as avg_efficiency,
    COUNT(CASE WHEN weather_impact_level IN ('High', 'Critical') THEN 1 END) as high_weather_impact_days
FROM daily_mining_report
GROUP BY location, EXTRACT(YEAR FROM report_date), EXTRACT(MONTH FROM report_date);

CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_production_performance 
ON mv_production_performance(location, year, month);

-- =====================================================
-- PERFORMANCE MONITORING VIEWS
-- =====================================================

-- View for monitoring table sizes
CREATE OR REPLACE VIEW v_table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = schemaname AND table_name = tablename) as column_count
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- View for monitoring index usage
CREATE OR REPLACE VIEW v_index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
    CASE 
        WHEN idx_scan = 0 THEN 'Unused'
        WHEN idx_scan < 100 THEN 'Low Usage'
        WHEN idx_scan < 1000 THEN 'Medium Usage'
        ELSE 'High Usage'
    END as usage_category
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- View for monitoring slow queries
CREATE OR REPLACE VIEW v_slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
WHERE mean_time > 100 -- queries taking more than 100ms on average
ORDER BY mean_time DESC;

-- =====================================================
-- MAINTENANCE FUNCTIONS
-- =====================================================

-- Function to refresh all materialized views
CREATE OR REPLACE FUNCTION refresh_all_materialized_views()
RETURNS void AS $$
BEGIN
    -- Refresh equipment utilization
    IF EXISTS (SELECT 1 FROM pg_matviews WHERE matviewname = 'mv_equipment_utilization') THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY mv_equipment_utilization;
        RAISE NOTICE 'Refreshed materialized view: mv_equipment_utilization';
    END IF;
    
    -- Refresh safety summary
    IF EXISTS (SELECT 1 FROM pg_matviews WHERE matviewname = 'mv_safety_summary') THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY mv_safety_summary;
        RAISE NOTICE 'Refreshed materialized view: mv_safety_summary';
    END IF;
    
    -- Refresh production performance
    IF EXISTS (SELECT 1 FROM pg_matviews WHERE matviewname = 'mv_production_performance') THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY mv_production_performance;
        RAISE NOTICE 'Refreshed materialized view: mv_production_performance';
    END IF;
    
    RAISE NOTICE 'All materialized views refreshed successfully';
END;
$$ LANGUAGE plpgsql;

-- Function to analyze all tables
CREATE OR REPLACE FUNCTION analyze_all_tables()
RETURNS void AS $$
DECLARE
    table_name text;
BEGIN
    FOR table_name IN 
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ANALYZE ' || table_name;
    END LOOP;
    
    RAISE NOTICE 'All tables analyzed successfully';
END;
$$ LANGUAGE plpgsql;

-- Function to reindex all tables
CREATE OR REPLACE FUNCTION reindex_all_tables()
RETURNS void AS $$
DECLARE
    table_name text;
BEGIN
    FOR table_name IN 
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        EXECUTE 'REINDEX TABLE ' || table_name;
    END LOOP;
    
    RAISE NOTICE 'All tables reindexed successfully';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SCHEDULED MAINTENANCE JOBS
-- =====================================================

-- Create a simple job scheduling table
CREATE TABLE IF NOT EXISTS scheduled_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_name VARCHAR(200) NOT NULL UNIQUE,
    job_function VARCHAR(200) NOT NULL,
    schedule_expression VARCHAR(100) NOT NULL, -- Cron-like expression
    last_run_at TIMESTAMPTZ,
    next_run_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert maintenance jobs
INSERT INTO scheduled_jobs (job_name, job_function, schedule_expression, next_run_at) VALUES
('Refresh Materialized Views', 'refresh_all_materialized_views()', '0 2 * * *', NOW() + INTERVAL '1 day'),
('Analyze Tables', 'analyze_all_tables()', '0 6 * * 0', NOW() + INTERVAL '7 days'),
('Update Statistics', 'analyze_all_tables()', '0 3 * * *', NOW() + INTERVAL '1 day')
ON CONFLICT (job_name) DO NOTHING;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON MATERIALIZED VIEW mv_equipment_utilization IS 'Materialized view for equipment utilization metrics';
COMMENT ON MATERIALIZED VIEW mv_safety_summary IS 'Materialized view for safety metrics summary';
COMMENT ON MATERIALIZED VIEW mv_production_performance IS 'Materialized view for production performance metrics';

COMMENT ON VIEW v_table_sizes IS 'Enhanced view for monitoring table sizes and statistics';
COMMENT ON VIEW v_index_usage IS 'Enhanced view for monitoring index usage patterns';
COMMENT ON VIEW v_slow_queries IS 'View for monitoring slow query performance';

COMMENT ON FUNCTION refresh_all_materialized_views() IS 'Refresh all materialized views concurrently';
COMMENT ON FUNCTION analyze_all_tables() IS 'Update statistics for all tables';
COMMENT ON FUNCTION reindex_all_tables() IS 'Reindex all tables for maintenance';

COMMENT ON TABLE scheduled_jobs IS 'Simple job scheduling for maintenance tasks';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('010', 'Performance indexes and optimization for all modules')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    index_count INTEGER;
    matview_count INTEGER;
    job_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO index_count 
    FROM pg_indexes 
    WHERE schemaname = 'public';
    
    SELECT COUNT(*) INTO matview_count 
    FROM pg_matviews 
    WHERE schemaname = 'public';
    
    SELECT COUNT(*) INTO job_count 
    FROM scheduled_jobs;
    
    RAISE NOTICE 'Performance optimization completed successfully';
    RAISE NOTICE 'Total indexes: %', index_count;
    RAISE NOTICE 'Materialized views: %', matview_count;
    RAISE NOTICE 'Scheduled jobs: %', job_count;
    RAISE NOTICE 'Database optimized for production workload';
END $$;
