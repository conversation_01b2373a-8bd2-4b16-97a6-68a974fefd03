import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DashboardHeaderService, { DashboardHeaderImage } from '../services/DashboardHeaderService';
import { Colors, Layout } from '../constants';

interface DashboardHeaderManagementScreenProps {
  navigation: any;
  route?: any;
}

const { width: screenWidth } = Dimensions.get('window');
const imageHeight = (screenWidth - 40) * 9 / 16; // 16:9 aspect ratio

const DashboardHeaderManagementScreen: React.FC<DashboardHeaderManagementScreenProps> = ({ navigation, route }) => {
  const [headerImages, setHeaderImages] = useState<DashboardHeaderImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [newImageForm, setNewImageForm] = useState({
    title: '',
    description: '',
    selectedImageUri: null as string | null,
  });

  const dashboardHeaderService = DashboardHeaderService.getInstance();

  useEffect(() => {
    loadHeaderImages();
  }, []);

  const loadHeaderImages = async (forceRefresh: boolean = false) => {
    try {
      if (forceRefresh) {
        setRefreshing(true);
        await dashboardHeaderService.clearCache();
      } else {
        setLoading(true);
      }

      const images = await dashboardHeaderService.getAllDashboardHeaderImages();
      setHeaderImages(images);
    } catch (error) {
      console.error('Error loading header images:', error);
      Alert.alert('Error', 'Failed to load header images');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleSelectImage = async () => {
    try {
      // Open gallery to select image
      const pickerResult = await dashboardHeaderService.openGallery();

      if (pickerResult.success && pickerResult.imageUri) {
        setNewImageForm(prev => ({
          ...prev,
          selectedImageUri: pickerResult.imageUri
        }));
      } else if (pickerResult.error) {
        Alert.alert('Error', pickerResult.error);
      }
    } catch (error: any) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const handleAddImage = async () => {
    if (!newImageForm.title.trim()) {
      Alert.alert('Error', 'Please enter a title for the image');
      return;
    }

    if (!newImageForm.selectedImageUri) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    setUploading(true);
    try {
      // Add image to database using selected image
      const addResult = await dashboardHeaderService.addDashboardHeaderImage(
        newImageForm.title,
        newImageForm.description,
        newImageForm.selectedImageUri
      );

      if (addResult.success) {
        Alert.alert('Success', 'Header image added successfully!');
        setShowAddModal(false);
        setNewImageForm({ title: '', description: '', selectedImageUri: null });
        await loadHeaderImages(true);
      } else {
        Alert.alert('Error', addResult.error || 'Failed to add header image');
      }
    } catch (error: any) {
      console.error('Error adding header image:', error);
      Alert.alert('Error', 'Failed to add header image');
    } finally {
      setUploading(false);
    }
  };

  const handleToggleActive = async (image: DashboardHeaderImage) => {
    try {
      const newStatus = !image.is_active; // Toggle current active status
      const success = await dashboardHeaderService.toggleActiveStatus(image.id, newStatus);

      if (success) {
        const actionText = newStatus ? 'will now be displayed' : 'is now hidden';
        const iconText = newStatus ? '👁️ (eye open)' : '👁️‍🗨️ (eye with line)';
        Alert.alert(
          'Visibility Updated',
          `Image "${image.title}" ${actionText} in dashboard rotation.\n\nIcon shows: ${iconText}\n\nThe image remains saved in the database.`
        );
        await loadHeaderImages(true);
      } else {
        Alert.alert('Error', 'Failed to update image visibility status');
      }
    } catch (error) {
      console.error('Error toggling image status:', error);
      Alert.alert('Error', 'Failed to update image visibility status');
    }
  };

  const handleDeleteImage = (image: DashboardHeaderImage) => {
    Alert.alert(
      'Delete Header Image',
      `Are you sure you want to delete "${image.title}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await dashboardHeaderService.deleteHeaderImage(image.id, image.image_url);
              
              if (success) {
                Alert.alert('Success', 'Header image deleted successfully!');
                await loadHeaderImages(true);
              } else {
                Alert.alert('Error', 'Failed to delete header image');
              }
            } catch (error) {
              console.error('Error deleting header image:', error);
              Alert.alert('Error', 'Failed to delete header image');
            }
          },
        },
      ]
    );
  };

  const renderHeaderImage = (image: DashboardHeaderImage, index: number) => (
    <View key={image.id} style={styles.imageCard}>
      <Image
        source={{ uri: image.image_url }}
        style={[
          styles.headerImage,
          !image.is_active && styles.inactiveImage
        ]}
        resizeMode="cover"
      />

      {/* Status Badge */}
      <View style={[
        styles.statusBadge,
        { backgroundColor: image.is_active ? Colors.success : Colors.error }
      ]}>
        <Text style={styles.statusText}>
          {image.is_active ? 'VISIBLE' : 'HIDDEN'}
        </Text>
      </View>

      <View style={styles.imageOverlay}>
        <View style={styles.imageInfo}>
          <Text style={styles.imageTitle}>{image.title}</Text>
          {image.description && (
            <Text style={styles.imageDescription}>{image.description}</Text>
          )}
          <Text style={styles.imageOrder}>Order: {image.display_order}</Text>
          <Text style={styles.imageStatus}>
            Status: {image.is_active ? 'Shown in dashboard' : 'Hidden from dashboard'}
          </Text>
          {image.created_by && (
            <Text style={styles.imageCreator}>
              Created by: {image.created_by_name || 'Admin User'}
            </Text>
          )}
        </View>

        <View style={styles.imageActions}>
          <TouchableOpacity
            style={[
              styles.actionButton,
              image.is_active ? styles.visibleButton : styles.hiddenButton
            ]}
            onPress={() => handleToggleActive(image)}
          >
            <Ionicons
              name={image.is_active ? "eye" : "eye-off"}
              size={16}
              color={Colors.white}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeleteImage(image)}
          >
            <Ionicons name="trash" size={16} color={Colors.white} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading header images...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            // Smart navigation based on source
            const source = route?.params?.source;
            if (source === 'Dashboard') {
              // Navigate back to Dashboard
              navigation.reset({
                index: 0,
                routes: [{ name: 'Dashboard' }],
              });
            } else {
              // Default back navigation (Profile)
              navigation.goBack();
            }
          }}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Dashboard Headers</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowAddModal(true)}
        >
          <Ionicons name="add" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content} 
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        onRefresh={() => loadHeaderImages(true)}
      >
        {/* Current Images */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Header Images ({headerImages.length})</Text>
          
          {headerImages.map(renderHeaderImage)}
          
          {headerImages.length === 0 && (
            <View style={styles.emptyState}>
              <Ionicons name="image-outline" size={48} color={Colors.textLight} />
              <Text style={styles.emptyText}>No header images found</Text>
              <Text style={styles.emptySubtext}>Add some images to get started</Text>
            </View>
          )}
        </View>

        {/* Instructions */}
        <View style={styles.instructionsSection}>
          <Text style={styles.instructionsTitle}>Instructions</Text>
          <View style={styles.instructionItem}>
            <Ionicons name="add-circle" size={16} color={Colors.primary} />
            <Text style={styles.instructionText}>Tap + to add new images from gallery</Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
            <Text style={styles.instructionText}>Use landscape images (16:9 ratio)</Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
            <Text style={styles.instructionText}>Minimum resolution: 1920x1080 pixels</Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
            <Text style={styles.instructionText}>Images will rotate every 5 seconds</Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="eye" size={16} color={Colors.success} />
            <Text style={styles.instructionText}>Eye open = Image displayed in dashboard</Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="eye-off" size={16} color={Colors.error} />
            <Text style={styles.instructionText}>Eye with line = Image hidden from dashboard</Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.info} />
            <Text style={styles.instructionText}>Hidden images remain saved in database</Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="person-circle" size={16} color={Colors.primary} />
            <Text style={styles.instructionText}>Creator info tracked for each uploaded image</Text>
          </View>
        </View>
      </ScrollView>

      {/* Add Image Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowAddModal(false)}
              disabled={uploading}
            >
              <Text style={styles.modalCancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Add Header Image</Text>
            <TouchableOpacity
              onPress={handleAddImage}
              disabled={uploading || !newImageForm.title.trim() || !newImageForm.selectedImageUri}
            >
              <Text style={[
                styles.modalSaveButton,
                (uploading || !newImageForm.title.trim() || !newImageForm.selectedImageUri) && styles.disabledText
              ]}>
                {uploading ? 'Adding...' : 'Add'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Image Selection */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Header Image *</Text>
              <TouchableOpacity
                style={styles.imagePickerButton}
                onPress={handleSelectImage}
                disabled={uploading}
              >
                {newImageForm.selectedImageUri ? (
                  <View style={styles.selectedImageContainer}>
                    <Image
                      source={{ uri: newImageForm.selectedImageUri }}
                      style={styles.selectedImagePreview}
                      resizeMode="cover"
                    />
                    <View style={styles.imagePickerOverlay}>
                      <Ionicons name="checkmark-circle" size={24} color={Colors.success} />
                      <Text style={styles.imageSelectedText}>Image Selected</Text>
                      <Text style={styles.changeImageText}>Tap to change</Text>
                    </View>
                  </View>
                ) : (
                  <View style={styles.imagePickerPlaceholder}>
                    <Ionicons name="image-outline" size={48} color={Colors.textLight} />
                    <Text style={styles.imagePickerText}>Tap to select image</Text>
                    <Text style={styles.imagePickerSubtext}>Choose from gallery</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Title *</Text>
              <TextInput
                style={styles.input}
                value={newImageForm.title}
                onChangeText={(value) => setNewImageForm(prev => ({ ...prev, title: value }))}
                placeholder="Enter image title"
                placeholderTextColor={Colors.textLight}
                editable={!uploading}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={newImageForm.description}
                onChangeText={(value) => setNewImageForm(prev => ({ ...prev, description: value }))}
                placeholder="Enter image description (optional)"
                placeholderTextColor={Colors.textLight}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
                editable={!uploading}
              />
            </View>

            <View style={styles.noteSection}>
              <Text style={styles.noteTitle}>Note:</Text>
              <Text style={styles.noteText}>
                After clicking "Add", you'll be prompted to select an image from your gallery. 
                The image will be automatically uploaded and added to the dashboard header rotation.
              </Text>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  addButton: {
    padding: Layout.spacing.xs,
  },
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  section: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
    marginTop: Layout.spacing.lg,
  },
  imageCard: {
    marginBottom: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    overflow: 'hidden',
    backgroundColor: Colors.backgroundLight,
    elevation: 2,
    shadowColor: Colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerImage: {
    width: '100%',
    height: imageHeight,
  },
  inactiveImage: {
    opacity: 0.5,
  },
  statusBadge: {
    position: 'absolute',
    top: 10,
    left: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    zIndex: 1,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '700',
    color: Colors.white,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: Layout.spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  imageInfo: {
    flex: 1,
    marginRight: Layout.spacing.md,
  },
  imageTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.white,
    marginBottom: Layout.spacing.xs,
  },
  imageDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.white,
    opacity: 0.9,
    marginBottom: Layout.spacing.xs,
  },
  imageOrder: {
    fontSize: Layout.fontSize.sm,
    color: Colors.white,
    opacity: 0.7,
  },
  imageStatus: {
    fontSize: Layout.fontSize.xs,
    color: Colors.white,
    opacity: 0.8,
    marginTop: Layout.spacing.xs,
  },
  imageCreator: {
    fontSize: Layout.fontSize.xs,
    color: Colors.white,
    opacity: 0.7,
    marginTop: Layout.spacing.xs,
    fontStyle: 'italic',
  },
  imageActions: {
    flexDirection: 'row',
    gap: Layout.spacing.sm,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  visibleButton: {
    backgroundColor: Colors.success, // Green untuk mata terbuka (visible)
  },
  hiddenButton: {
    backgroundColor: Colors.error, // Red untuk mata tertutup (hidden)
  },
  deleteButton: {
    backgroundColor: Colors.error,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: Layout.spacing.xl,
  },
  emptyText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    marginTop: Layout.spacing.sm,
  },
  emptySubtext: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    marginTop: Layout.spacing.xs,
  },
  instructionsSection: {
    backgroundColor: Colors.backgroundLight,
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.xl,
  },
  instructionsTitle: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: Layout.spacing.md,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Layout.spacing.sm,
  },
  instructionText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    marginLeft: Layout.spacing.sm,
    flex: 1,
  },
  loadingText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    marginTop: Layout.spacing.sm,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.lg,
    paddingTop: Layout.spacing.xl,
    paddingBottom: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalCancelButton: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
  },
  modalTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.text,
  },
  modalSaveButton: {
    fontSize: Layout.fontSize.md,
    fontWeight: '600',
    color: Colors.primary,
  },
  disabledText: {
    color: Colors.textLight,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: Layout.spacing.lg,
  },
  inputGroup: {
    marginBottom: Layout.spacing.lg,
    marginTop: Layout.spacing.lg,
  },
  inputLabel: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: Layout.spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: Layout.borderRadius.md,
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    fontSize: Layout.fontSize.md,
    color: Colors.text,
    backgroundColor: Colors.background,
  },
  textArea: {
    height: 80,
    paddingTop: Layout.spacing.sm,
  },
  noteSection: {
    backgroundColor: Colors.info + '20',
    padding: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginTop: Layout.spacing.lg,
  },
  noteTitle: {
    fontSize: Layout.fontSize.sm,
    fontWeight: '600',
    color: Colors.info,
    marginBottom: Layout.spacing.xs,
  },
  noteText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.info,
    lineHeight: 18,
  },

  // Image Picker Styles
  imagePickerButton: {
    borderWidth: 2,
    borderColor: Colors.border,
    borderRadius: Layout.borderRadius.md,
    borderStyle: 'dashed',
    overflow: 'hidden',
  },
  imagePickerPlaceholder: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.cardBackground,
  },
  imagePickerText: {
    fontSize: Layout.fontSize.md,
    color: Colors.textLight,
    marginTop: Layout.spacing.sm,
    fontWeight: '600',
  },
  imagePickerSubtext: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textLight,
    opacity: 0.7,
    marginTop: Layout.spacing.xs,
  },
  selectedImageContainer: {
    position: 'relative',
    height: 120,
  },
  selectedImagePreview: {
    width: '100%',
    height: '100%',
  },
  imagePickerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageSelectedText: {
    fontSize: Layout.fontSize.md,
    color: Colors.white,
    fontWeight: '600',
    marginTop: Layout.spacing.xs,
  },
  changeImageText: {
    fontSize: Layout.fontSize.sm,
    color: Colors.white,
    opacity: 0.8,
    marginTop: Layout.spacing.xs,
  },
});

export default DashboardHeaderManagementScreen;
