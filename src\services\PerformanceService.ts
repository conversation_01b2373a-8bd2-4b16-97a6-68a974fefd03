import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface PerformanceMetrics {
  screenLoadTime: number;
  apiResponseTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  renderTime: number;
  networkLatency: number;
}

export interface PerformanceConfig {
  enableMetrics: boolean;
  sampleRate: number; // 0-1, percentage of operations to measure
  maxMetricsStorage: number; // max number of metrics to store
  alertThresholds: {
    screenLoadTime: number; // ms
    apiResponseTime: number; // ms
    memoryUsage: number; // MB
  };
}

export class PerformanceService {
  private static instance: PerformanceService;
  private config: PerformanceConfig;
  private metrics: Map<string, number[]> = new Map();
  private timers: Map<string, number> = new Map();

  private constructor() {
    this.config = {
      enableMetrics: __DEV__, // Only in development by default
      sampleRate: __DEV__ ? 1.0 : 0.1, // 100% in dev, 10% in prod
      maxMetricsStorage: 1000,
      alertThresholds: {
        screenLoadTime: 3000, // 3 seconds
        apiResponseTime: 5000, // 5 seconds
        memoryUsage: 100 // 100 MB
      }
    };
  }

  static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService();
    }
    return PerformanceService.instance;
  }

  // Start performance timer
  startTimer(key: string): void {
    if (!this.shouldMeasure()) return;
    
    this.timers.set(key, Date.now());
    if (__DEV__) {
      console.log(`⏱️ Started timer: ${key}`);
    }
  }

  // End performance timer and record metric
  endTimer(key: string, category: string = 'general'): number {
    if (!this.shouldMeasure()) return 0;

    const startTime = this.timers.get(key);
    if (!startTime) {
      console.warn(`⚠️ Timer ${key} was not started`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(key);

    // Record metric
    this.recordMetric(`${category}_${key}`, duration);

    // Check for performance alerts
    this.checkPerformanceAlert(key, duration, category);

    if (__DEV__) {
      console.log(`⏱️ ${key} completed in ${duration}ms`);
    }

    return duration;
  }

  // Record a performance metric
  recordMetric(key: string, value: number): void {
    if (!this.shouldMeasure()) return;

    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }

    const values = this.metrics.get(key)!;
    values.push(value);

    // Keep only recent metrics
    if (values.length > this.config.maxMetricsStorage) {
      values.shift();
    }

    // Store to persistent storage periodically
    if (values.length % 10 === 0) {
      this.persistMetrics();
    }
  }

  // Get performance statistics
  getMetricStats(key: string): {
    count: number;
    average: number;
    min: number;
    max: number;
    p95: number;
  } | null {
    const values = this.metrics.get(key);
    if (!values || values.length === 0) return null;

    const sorted = [...values].sort((a, b) => a - b);
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);
    const average = sum / count;
    const min = sorted[0];
    const max = sorted[count - 1];
    const p95Index = Math.floor(count * 0.95);
    const p95 = sorted[p95Index];

    return { count, average, min, max, p95 };
  }

  // Mining-specific performance tracking
  trackScreenLoad(screenName: string): void {
    this.startTimer(`screen_load_${screenName}`);
  }

  completeScreenLoad(screenName: string): void {
    const duration = this.endTimer(`screen_load_${screenName}`, 'screen');
    
    // Track specific mining screens
    if (screenName === 'Dashboard') {
      this.recordMetric('dashboard_load_time', duration);
    } else if (screenName === 'ProductionOverview') {
      this.recordMetric('production_load_time', duration);
    } else if (screenName === 'SafetyIncidents') {
      this.recordMetric('safety_load_time', duration);
    }
  }

  trackApiCall(endpoint: string): void {
    this.startTimer(`api_${endpoint}`);
  }

  completeApiCall(endpoint: string, success: boolean): void {
    const duration = this.endTimer(`api_${endpoint}`, 'api');
    
    this.recordMetric(`api_${success ? 'success' : 'error'}_time`, duration);
    
    // Track specific mining API calls
    if (endpoint.includes('production')) {
      this.recordMetric('production_api_time', duration);
    } else if (endpoint.includes('safety')) {
      this.recordMetric('safety_api_time', duration);
    } else if (endpoint.includes('equipment')) {
      this.recordMetric('equipment_api_time', duration);
    }
  }

  trackCacheOperation(operation: 'hit' | 'miss' | 'set', key: string): void {
    this.recordMetric(`cache_${operation}`, 1);
    
    // Calculate cache hit rate
    const hits = this.metrics.get('cache_hit')?.length || 0;
    const misses = this.metrics.get('cache_miss')?.length || 0;
    const total = hits + misses;
    
    if (total > 0) {
      const hitRate = (hits / total) * 100;
      this.recordMetric('cache_hit_rate', hitRate);
    }
  }

  trackRenderTime(componentName: string, renderTime: number): void {
    this.recordMetric(`render_${componentName}`, renderTime);
    
    // Alert for slow renders
    if (renderTime > 16) { // 60fps = 16ms per frame
      console.warn(`🐌 Slow render detected: ${componentName} took ${renderTime}ms`);
    }
  }

  // Memory usage tracking
  async trackMemoryUsage(): Promise<void> {
    if (Platform.OS === 'web') {
      // Web memory tracking
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        this.recordMetric('memory_usage_mb', usedMB);
        
        if (usedMB > this.config.alertThresholds.memoryUsage) {
          console.warn(`🚨 High memory usage: ${usedMB.toFixed(2)}MB`);
        }
      }
    } else {
      // Mobile memory tracking would require native modules
      // For now, we'll track AsyncStorage usage as a proxy
      try {
        const keys = await AsyncStorage.getAllKeys();
        let totalSize = 0;
        
        for (const key of keys.slice(0, 10)) { // Sample first 10 keys
          const value = await AsyncStorage.getItem(key);
          if (value) {
            totalSize += value.length;
          }
        }
        
        const estimatedTotalMB = (totalSize * keys.length / 10) / 1024 / 1024;
        this.recordMetric('storage_usage_mb', estimatedTotalMB);
      } catch (error) {
        console.error('Failed to track memory usage:', error);
      }
    }
  }

  // Network performance tracking
  trackNetworkLatency(url: string, latency: number): void {
    this.recordMetric('network_latency', latency);
    this.recordMetric(`network_${this.getDomainFromUrl(url)}`, latency);
  }

  // Performance optimization suggestions
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = [];
    
    // Check screen load times
    const dashboardStats = this.getMetricStats('dashboard_load_time');
    if (dashboardStats && dashboardStats.average > 2000) {
      suggestions.push('Dashboard loading is slow. Consider implementing skeleton screens or reducing initial data load.');
    }

    // Check API response times
    const apiStats = this.getMetricStats('production_api_time');
    if (apiStats && apiStats.average > 3000) {
      suggestions.push('Production API calls are slow. Consider implementing pagination or data caching.');
    }

    // Check cache hit rate
    const cacheHitRate = this.getMetricStats('cache_hit_rate');
    if (cacheHitRate && cacheHitRate.average < 70) {
      suggestions.push('Cache hit rate is low. Review caching strategy and TTL settings.');
    }

    // Check memory usage
    const memoryStats = this.getMetricStats('memory_usage_mb');
    if (memoryStats && memoryStats.average > 80) {
      suggestions.push('Memory usage is high. Consider implementing memory cleanup and reducing cached data.');
    }

    return suggestions;
  }

  // Generate performance report
  generatePerformanceReport(): {
    summary: PerformanceMetrics;
    details: { [key: string]: any };
    suggestions: string[];
  } {
    const dashboardLoad = this.getMetricStats('dashboard_load_time');
    const apiResponse = this.getMetricStats('production_api_time');
    const cacheHitRate = this.getMetricStats('cache_hit_rate');
    const memoryUsage = this.getMetricStats('memory_usage_mb');
    const renderTime = this.getMetricStats('render_ProductionChart');
    const networkLatency = this.getMetricStats('network_latency');

    const summary: PerformanceMetrics = {
      screenLoadTime: dashboardLoad?.average || 0,
      apiResponseTime: apiResponse?.average || 0,
      cacheHitRate: cacheHitRate?.average || 0,
      memoryUsage: memoryUsage?.average || 0,
      renderTime: renderTime?.average || 0,
      networkLatency: networkLatency?.average || 0
    };

    const details: { [key: string]: any } = {};
    for (const [key, values] of this.metrics.entries()) {
      if (values.length > 0) {
        details[key] = this.getMetricStats(key);
      }
    }

    const suggestions = this.getOptimizationSuggestions();

    return { summary, details, suggestions };
  }

  // Private helper methods
  private shouldMeasure(): boolean {
    if (!this.config.enableMetrics) return false;
    return Math.random() < this.config.sampleRate;
  }

  private checkPerformanceAlert(key: string, duration: number, category: string): void {
    let threshold = 0;
    
    switch (category) {
      case 'screen':
        threshold = this.config.alertThresholds.screenLoadTime;
        break;
      case 'api':
        threshold = this.config.alertThresholds.apiResponseTime;
        break;
      default:
        return;
    }

    if (duration > threshold) {
      console.warn(`🚨 Performance alert: ${key} took ${duration}ms (threshold: ${threshold}ms)`);
    }
  }

  private getDomainFromUrl(url: string): string {
    try {
      return new URL(url).hostname.replace(/\./g, '_');
    } catch {
      return 'unknown';
    }
  }

  private async persistMetrics(): Promise<void> {
    try {
      const metricsData: { [key: string]: number[] } = {};
      for (const [key, values] of this.metrics.entries()) {
        metricsData[key] = values.slice(-100); // Keep last 100 values
      }
      
      await AsyncStorage.setItem('performance_metrics', JSON.stringify(metricsData));
    } catch (error) {
      console.error('Failed to persist performance metrics:', error);
    }
  }

  private async loadPersistedMetrics(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem('performance_metrics');
      if (stored) {
        const metricsData = JSON.parse(stored);
        for (const [key, values] of Object.entries(metricsData)) {
          this.metrics.set(key, values as number[]);
        }
      }
    } catch (error) {
      console.error('Failed to load persisted metrics:', error);
    }
  }

  // Initialize performance monitoring
  async initialize(): Promise<void> {
    await this.loadPersistedMetrics();
    
    // Start periodic memory tracking
    setInterval(() => {
      this.trackMemoryUsage();
    }, 30000); // Every 30 seconds

    console.log('📊 Performance monitoring initialized');
  }

  // Cleanup
  cleanup(): void {
    this.persistMetrics();
    this.metrics.clear();
    this.timers.clear();
  }
}

export default PerformanceService;
