{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "noImplicitAny": false, "strictNullChecks": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "allowJs": true}, "exclude": ["packages/core/src/**/*", "scripts/**/*", "src/utils/test*.ts", "src/utils/*ProductionCalendar*.ts"]}