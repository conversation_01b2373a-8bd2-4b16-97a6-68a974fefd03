import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import HeaderImageService from '../services/HeaderImageService';
import { Colors, Layout } from '../constants';

interface CustomHeaderComponentProps {
  title?: string;
  subtitle?: string;
  showUserInfo?: boolean;
  showEditButton?: boolean;
  onEditPress?: () => void;
  height?: number;
  style?: any;
}

const { width: screenWidth } = Dimensions.get('window');

const CustomHeaderComponent: React.FC<CustomHeaderComponentProps> = ({
  title,
  subtitle,
  showUserInfo = true,
  showEditButton = false,
  onEditPress,
  height = (screenWidth * 9) / 16, // Default 16:9 aspect ratio
  style,
}) => {
  const { user, profile } = useAuth();
  const [headerImageUrl, setHeaderImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const headerImageService = HeaderImageService.getInstance();

  useEffect(() => {
    loadHeaderImage();
  }, [user]);

  const loadHeaderImage = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      const savedHeaderUrl = await headerImageService.getHeaderImageUrl(user.id);
      setHeaderImageUrl(savedHeaderUrl);
    } catch (error) {
      console.error('Error loading header image:', error);
    } finally {
      setLoading(false);
    }
  };

  const DefaultHeader = () => (
    <LinearGradient
      colors={[Colors.primary, Colors.primary + 'CC']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={[styles.headerContainer, { height }, style]}
    >
      <View style={styles.headerContent}>
        {showUserInfo && (
          <>
            <Text style={styles.headerTitle}>
              {title || profile?.full_name || 'Welcome'}
            </Text>
            <Text style={styles.headerSubtitle}>
              {subtitle || profile?.jabatan || profile?.departemen || 'Mining Operations'}
            </Text>
          </>
        )}
        
        {!showUserInfo && title && (
          <Text style={styles.headerTitle}>{title}</Text>
        )}
        
        {!showUserInfo && subtitle && (
          <Text style={styles.headerSubtitle}>{subtitle}</Text>
        )}
      </View>

      {showEditButton && onEditPress && (
        <TouchableOpacity style={styles.editButton} onPress={onEditPress}>
          <Ionicons name="camera" size={20} color={Colors.white} />
        </TouchableOpacity>
      )}
    </LinearGradient>
  );

  const CustomHeader = () => (
    <ImageBackground
      source={{ uri: headerImageUrl! }}
      style={[styles.headerContainer, { height }, style]}
      imageStyle={styles.headerImage}
    >
      {/* Dark overlay for better text readability */}
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)']}
        style={styles.overlay}
      />
      
      <View style={styles.headerContent}>
        {showUserInfo && (
          <>
            <Text style={styles.headerTitle}>
              {title || profile?.full_name || 'Welcome'}
            </Text>
            <Text style={styles.headerSubtitle}>
              {subtitle || profile?.jabatan || profile?.departemen || 'Mining Operations'}
            </Text>
          </>
        )}
        
        {!showUserInfo && title && (
          <Text style={styles.headerTitle}>{title}</Text>
        )}
        
        {!showUserInfo && subtitle && (
          <Text style={styles.headerSubtitle}>{subtitle}</Text>
        )}
      </View>

      {showEditButton && onEditPress && (
        <TouchableOpacity style={styles.editButton} onPress={onEditPress}>
          <Ionicons name="camera" size={20} color={Colors.white} />
        </TouchableOpacity>
      )}
    </ImageBackground>
  );

  if (loading) {
    return <DefaultHeader />;
  }

  return headerImageUrl ? <CustomHeader /> : <DefaultHeader />;
};

const styles = StyleSheet.create({
  headerContainer: {
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    position: 'relative',
  },
  headerImage: {
    resizeMode: 'cover',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  headerContent: {
    padding: Layout.spacing.lg,
    paddingBottom: Layout.spacing.xl,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: Layout.fontSize.xxl,
    fontWeight: '700',
    color: Colors.white,
    marginBottom: Layout.spacing.xs,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  headerSubtitle: {
    fontSize: Layout.fontSize.lg,
    color: Colors.white,
    opacity: 0.9,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  editButton: {
    position: 'absolute',
    top: Layout.spacing.md,
    right: Layout.spacing.md,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
});

export default CustomHeaderComponent;
