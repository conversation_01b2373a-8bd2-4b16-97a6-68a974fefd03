# Authentication Implementation - Mining Operations App

## 🔐 **Authentication Architecture**

### **Technology Stack**
- **Primary Auth**: Supabase Authentication
- **Local Storage**: Expo SecureStore
- **Biometric Auth**: Expo Local Authentication
- **Session Management**: JWT tokens with refresh
- **Security**: Row Level Security (RLS) policies

### **Authentication Flow**
```
User Input → Validation → Supabase Auth → Profile Fetch → Role Check → Navigation
```

## 📁 **File Structure**

```
src/features/auth/
├── components/
│   ├── AuthBackground.tsx
│   ├── BiometricPrompt.tsx
│   ├── LoginForm.tsx
│   └── RegistrationForm.tsx
├── hooks/
│   ├── useAuth.tsx
│   ├── useBiometric.ts
│   └── useAuthValidation.ts
├── screens/
│   ├── LoginScreen.tsx
│   ├── RegisterScreen.tsx
│   ├── ForgotPasswordScreen.tsx
│   └── ProfileScreen.tsx
├── services/
│   ├── AuthService.ts
│   ├── BiometricService.ts
│   └── TokenService.ts
└── types/
    └── auth.types.ts
```

## 🎯 **Domain Models**

### **src/models/Auth.ts**
```typescript
export interface User {
  id: string;
  email: string;
  fullName: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  department: string;
  locationId?: string;
  phoneNumber?: string;
  profilePhotoUrl?: string;
  employeeId?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type UserRole = 
  | 'Super Admin'
  | 'Site Manager' 
  | 'Shift Supervisor'
  | 'Equipment Operator'
  | 'Safety Officer'
  | 'Maintenance Technician'
  | 'Quality Controller'
  | 'Observer';

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  department?: string;
  employeeId?: string;
}

export interface AuthSession {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface BiometricSettings {
  isEnabled: boolean;
  type: 'fingerprint' | 'face' | 'iris' | null;
  lastUsed?: string;
}

// Role-based permissions
export const ROLE_PERMISSIONS = {
  'Super Admin': ['*'],
  'Site Manager': [
    'dashboard.view', 'production.manage', 'equipment.manage',
    'safety.manage', 'maintenance.manage', 'reports.view', 'users.manage'
  ],
  'Shift Supervisor': [
    'dashboard.view', 'production.view', 'production.create',
    'equipment.view', 'safety.report', 'maintenance.view'
  ],
  'Equipment Operator': [
    'dashboard.view', 'equipment.operate', 'equipment.report',
    'safety.report', 'maintenance.report'
  ],
  'Safety Officer': [
    'dashboard.view', 'safety.manage', 'incidents.manage',
    'safety.reports', 'training.manage'
  ],
  'Maintenance Technician': [
    'dashboard.view', 'equipment.view', 'maintenance.manage',
    'work_orders.manage', 'parts.view'
  ],
  'Quality Controller': [
    'dashboard.view', 'production.view', 'quality.manage',
    'reports.view'
  ],
  'Observer': [
    'dashboard.view'
  ]
} as const;

// Validation functions
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
```

## 🔧 **Authentication Service**

### **src/features/auth/services/AuthService.ts**
```typescript
import { createClient } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';
import { User, LoginCredentials, RegisterData, AuthSession } from '../../../models/Auth';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    storage: {
      getItem: (key: string) => SecureStore.getItemAsync(key),
      setItem: (key: string, value: string) => SecureStore.setItemAsync(key, value),
      removeItem: (key: string) => SecureStore.deleteItemAsync(key),
    },
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

export class AuthService {
  // Login with email and password
  static async login(credentials: LoginCredentials): Promise<AuthSession> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) throw error;
      if (!data.user || !data.session) throw new Error('Login failed');

      // Fetch user profile
      const user = await this.fetchUserProfile(data.user.id);
      
      // Store session info
      if (credentials.rememberMe) {
        await SecureStore.setItemAsync('rememberMe', 'true');
        await SecureStore.setItemAsync('userEmail', credentials.email);
      }

      return {
        user,
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresAt: data.session.expires_at || 0,
      };
    } catch (error) {
      throw new Error(`Login failed: ${error.message}`);
    }
  }

  // Register new user
  static async register(userData: RegisterData): Promise<AuthSession> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
            phone: userData.phoneNumber,
          }
        }
      });

      if (error) throw error;
      if (!data.user) throw new Error('Registration failed');

      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert([{
          id: data.user.id,
          email: userData.email,
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone_number: userData.phoneNumber,
          department: userData.department,
          employee_id: userData.employeeId,
          role: 'Observer', // Default role
        }]);

      if (profileError) throw profileError;

      // If email confirmation is required, return early
      if (!data.session) {
        throw new Error('Please check your email to confirm your account');
      }

      const user = await this.fetchUserProfile(data.user.id);

      return {
        user,
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresAt: data.session.expires_at || 0,
      };
    } catch (error) {
      throw new Error(`Registration failed: ${error.message}`);
    }
  }

  // Logout
  static async logout(): Promise<void> {
    try {
      await supabase.auth.signOut();
      await SecureStore.deleteItemAsync('rememberMe');
      await SecureStore.deleteItemAsync('biometricEnabled');
    } catch (error) {
      throw new Error(`Logout failed: ${error.message}`);
    }
  }

  // Get current session
  static async getCurrentSession(): Promise<AuthSession | null> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) return null;

      const user = await this.fetchUserProfile(session.user.id);

      return {
        user,
        accessToken: session.access_token,
        refreshToken: session.refresh_token,
        expiresAt: session.expires_at || 0,
      };
    } catch (error) {
      console.error('Failed to get current session:', error);
      return null;
    }
  }

  // Refresh token
  static async refreshToken(): Promise<AuthSession> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error || !data.session) throw error || new Error('Token refresh failed');

      const user = await this.fetchUserProfile(data.user.id);

      return {
        user,
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        expiresAt: data.session.expires_at || 0,
      };
    } catch (error) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }
  }

  // Fetch user profile from database
  private static async fetchUserProfile(userId: string): Promise<User> {
    const { data, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        location:locations(name)
      `)
      .eq('id', userId)
      .single();

    if (error) throw new Error('Failed to fetch user profile');

    return {
      id: data.id,
      email: data.email,
      fullName: `${data.first_name} ${data.last_name}`,
      firstName: data.first_name,
      lastName: data.last_name,
      role: data.role,
      department: data.department,
      locationId: data.location_id,
      phoneNumber: data.phone_number,
      profilePhotoUrl: data.profile_photo_url,
      employeeId: data.employee_id,
      isActive: data.is_active,
      lastLoginAt: data.last_login_at,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  }

  // Reset password
  static async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'miningapp://reset-password',
      });
      
      if (error) throw error;
    } catch (error) {
      throw new Error(`Password reset failed: ${error.message}`);
    }
  }

  // Update password
  static async updatePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });
      
      if (error) throw error;
    } catch (error) {
      throw new Error(`Password update failed: ${error.message}`);
    }
  }

  // Update profile
  static async updateProfile(updates: Partial<User>): Promise<User> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No authenticated user');

      const { error } = await supabase
        .from('user_profiles')
        .update({
          first_name: updates.firstName,
          last_name: updates.lastName,
          phone_number: updates.phoneNumber,
          department: updates.department,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) throw error;

      return await this.fetchUserProfile(user.id);
    } catch (error) {
      throw new Error(`Profile update failed: ${error.message}`);
    }
  }
}
```

## 📱 **Biometric Authentication**

### **src/features/auth/services/BiometricService.ts**
```typescript
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { BiometricSettings } from '../../../models/Auth';

export class BiometricService {
  // Check if biometric authentication is available
  static async isAvailable(): Promise<boolean> {
    const hasHardware = await LocalAuthentication.hasHardwareAsync();
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    return hasHardware && isEnrolled;
  }

  // Get supported biometric types
  static async getSupportedTypes(): Promise<LocalAuthentication.AuthenticationType[]> {
    return await LocalAuthentication.supportedAuthenticationTypesAsync();
  }

  // Authenticate with biometrics
  static async authenticate(reason: string = 'Authenticate to access the app'): Promise<boolean> {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: reason,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
        disableDeviceFallback: false,
      });

      return result.success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  }

  // Enable biometric authentication
  static async enableBiometric(userId: string): Promise<void> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        throw new Error('Biometric authentication is not available');
      }

      const success = await this.authenticate('Enable biometric authentication');
      if (!success) {
        throw new Error('Biometric authentication failed');
      }

      const supportedTypes = await this.getSupportedTypes();
      const primaryType = supportedTypes[0];

      const settings: BiometricSettings = {
        isEnabled: true,
        type: this.mapBiometricType(primaryType),
        lastUsed: new Date().toISOString(),
      };

      await SecureStore.setItemAsync(
        `biometric_${userId}`, 
        JSON.stringify(settings)
      );
    } catch (error) {
      throw new Error(`Failed to enable biometric: ${error.message}`);
    }
  }

  // Disable biometric authentication
  static async disableBiometric(userId: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(`biometric_${userId}`);
    } catch (error) {
      throw new Error(`Failed to disable biometric: ${error.message}`);
    }
  }

  // Get biometric settings
  static async getBiometricSettings(userId: string): Promise<BiometricSettings | null> {
    try {
      const settingsJson = await SecureStore.getItemAsync(`biometric_${userId}`);
      return settingsJson ? JSON.parse(settingsJson) : null;
    } catch (error) {
      console.error('Failed to get biometric settings:', error);
      return null;
    }
  }

  // Check if biometric is enabled for user
  static async isBiometricEnabled(userId: string): Promise<boolean> {
    const settings = await this.getBiometricSettings(userId);
    return settings?.isEnabled || false;
  }

  private static mapBiometricType(
    type: LocalAuthentication.AuthenticationType
  ): 'fingerprint' | 'face' | 'iris' | null {
    switch (type) {
      case LocalAuthentication.AuthenticationType.FINGERPRINT:
        return 'fingerprint';
      case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
        return 'face';
      case LocalAuthentication.AuthenticationType.IRIS:
        return 'iris';
      default:
        return null;
    }
  }
}
```

## 🎣 **Authentication Hook**

### **src/features/auth/hooks/useAuth.tsx**
```typescript
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { AuthService } from '../services/AuthService';
import { BiometricService } from '../services/BiometricService';
import { User, LoginCredentials, RegisterData, AuthSession } from '../../../models/Auth';

interface AuthContextType {
  // State
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  
  // Biometric
  enableBiometric: () => Promise<void>;
  disableBiometric: () => Promise<void>;
  loginWithBiometric: () => Promise<void>;
  isBiometricEnabled: boolean;
  
  // Permissions
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);

  const isAuthenticated = !!user;

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  // Check biometric status when user changes
  useEffect(() => {
    if (user) {
      checkBiometricStatus();
    } else {
      setIsBiometricEnabled(false);
    }
  }, [user]);

  const initializeAuth = async () => {
    try {
      setLoading(true);
      const session = await AuthService.getCurrentSession();
      
      if (session) {
        setUser(session.user);
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      setError('Failed to initialize authentication');
    } finally {
      setLoading(false);
    }
  };

  const checkBiometricStatus = async () => {
    if (!user) return;
    
    try {
      const enabled = await BiometricService.isBiometricEnabled(user.id);
      setIsBiometricEnabled(enabled);
    } catch (error) {
      console.error('Failed to check biometric status:', error);
    }
  };

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      setLoading(true);
      setError(null);
      
      const session = await AuthService.login(credentials);
      setUser(session.user);
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const register = useCallback(async (userData: RegisterData) => {
    try {
      setLoading(true);
      setError(null);
      
      const session = await AuthService.register(userData);
      setUser(session.user);
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setLoading(true);
      await AuthService.logout();
      setUser(null);
      setIsBiometricEnabled(false);
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshToken = useCallback(async () => {
    try {
      const session = await AuthService.refreshToken();
      setUser(session.user);
    } catch (error) {
      setError(error.message);
      // If refresh fails, logout user
      await logout();
      throw error;
    }
  }, [logout]);

  const enableBiometric = useCallback(async () => {
    if (!user) throw new Error('No authenticated user');
    
    try {
      await BiometricService.enableBiometric(user.id);
      setIsBiometricEnabled(true);
    } catch (error) {
      setError(error.message);
      throw error;
    }
  }, [user]);

  const disableBiometric = useCallback(async () => {
    if (!user) throw new Error('No authenticated user');
    
    try {
      await BiometricService.disableBiometric(user.id);
      setIsBiometricEnabled(false);
    } catch (error) {
      setError(error.message);
      throw error;
    }
  }, [user]);

  const loginWithBiometric = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const success = await BiometricService.authenticate('Login with biometric');
      
      if (success) {
        // Get stored session or refresh current session
        const session = await AuthService.getCurrentSession();
        if (session) {
          setUser(session.user);
        } else {
          throw new Error('No valid session found');
        }
      } else {
        throw new Error('Biometric authentication failed');
      }
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false;
    
    const rolePermissions = ROLE_PERMISSIONS[user.role] || [];
    return rolePermissions.includes('*') || rolePermissions.includes(permission);
  }, [user]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    loading,
    error,
    login,
    register,
    logout,
    refreshToken,
    clearError,
    enableBiometric,
    disableBiometric,
    loginWithBiometric,
    isBiometricEnabled,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

## 🎨 **Authentication UI Components**

### **src/features/auth/components/AuthBackground.tsx**
```typescript
import React from 'react';
import { View, StyleSheet, ImageBackground, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '../../../constants/colors';

const { width, height } = Dimensions.get('window');

interface AuthBackgroundProps {
  children: React.ReactNode;
}

export const AuthBackground: React.FC<AuthBackgroundProps> = ({ children }) => {
  return (
    <View style={styles.container}>
      <ImageBackground
        source={require('../../../assets/images/mining-background.jpg')}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(37, 99, 235, 0.8)', 'rgba(29, 78, 216, 0.9)']}
          style={styles.gradient}
        >
          {children}
        </LinearGradient>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
});
```

### **src/features/auth/screens/LoginScreen.tsx**
```typescript
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { AuthBackground } from '../components/AuthBackground';
import { useAuth } from '../hooks/useAuth';
import { Colors, Typography, Spacing } from '../../../constants';
import { validateEmail } from '../../../models/Auth';

export const LoginScreen: React.FC = ({ navigation }) => {
  const { login, loginWithBiometric, loading, error, clearError, isBiometricEnabled } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  useEffect(() => {
    if (error) {
      Alert.alert('Login Error', error);
      clearError();
    }
  }, [error, clearError]);

  const validateForm = (): boolean => {
    let isValid = true;

    // Reset errors
    setEmailError('');
    setPasswordError('');

    // Validate email
    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    }

    // Validate password
    if (!password.trim()) {
      setPasswordError('Password is required');
      isValid = false;
    }

    return isValid;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    try {
      await login({ email, password, rememberMe });
    } catch (error) {
      // Error is handled by useAuth hook
    }
  };

  const handleBiometricLogin = async () => {
    try {
      await loginWithBiometric();
    } catch (error) {
      Alert.alert('Biometric Login Failed', error.message);
    }
  };

  return (
    <AuthBackground>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../../../assets/images/logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.appTitle}>Mining Operations</Text>
          <Text style={styles.appSubtitle}>Professional Mining Management</Text>
        </View>

        {/* Login Form */}
        <View style={styles.formContainer}>
          {/* Email Input */}
          <View style={styles.inputContainer}>
            <View style={[styles.inputWrapper, emailError && styles.inputError]}>
              <Ionicons name="mail-outline" size={20} color={Colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.textInput}
                placeholder="Email Address"
                placeholderTextColor={Colors.textSecondary}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
          </View>

          {/* Password Input */}
          <View style={styles.inputContainer}>
            <View style={[styles.inputWrapper, passwordError && styles.inputError]}>
              <Ionicons name="lock-closed-outline" size={20} color={Colors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.textInput}
                placeholder="Password"
                placeholderTextColor={Colors.textSecondary}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={styles.eyeIcon}
              >
                <Ionicons
                  name={showPassword ? "eye-outline" : "eye-off-outline"}
                  size={20}
                  color={Colors.textSecondary}
                />
              </TouchableOpacity>
            </View>
            {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
          </View>

          {/* Remember Me & Forgot Password */}
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={styles.rememberMeContainer}
              onPress={() => setRememberMe(!rememberMe)}
            >
              <Ionicons
                name={rememberMe ? "checkbox" : "square-outline"}
                size={20}
                color={Colors.primary}
              />
              <Text style={styles.rememberMeText}>Remember me</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => navigation.navigate('ForgotPassword')}>
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>
          </View>

          {/* Login Button */}
          <TouchableOpacity
            style={[styles.loginButton, loading && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={loading}
          >
            <Text style={styles.loginButtonText}>
              {loading ? 'Signing In...' : 'Sign In'}
            </Text>
          </TouchableOpacity>

          {/* Biometric Login */}
          {isBiometricEnabled && (
            <TouchableOpacity
              style={styles.biometricButton}
              onPress={handleBiometricLogin}
            >
              <Ionicons name="finger-print" size={24} color={Colors.primary} />
              <Text style={styles.biometricText}>Use Biometric Login</Text>
            </TouchableOpacity>
          )}

          {/* Register Link */}
          <View style={styles.registerContainer}>
            <Text style={styles.registerText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Register')}>
              <Text style={styles.registerLink}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>Mining Operations App v1.0.0</Text>
          <Text style={styles.footerText}>© 2024 Your Company Name</Text>
        </View>
      </KeyboardAvoidingView>
    </AuthBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: Spacing['2xl'],
  },
  logo: {
    width: 80,
    height: 80,
    marginBottom: Spacing.md,
  },
  appTitle: {
    fontSize: Typography['2xl'],
    fontWeight: Typography.bold,
    color: Colors.textInverse,
    marginBottom: Spacing.xs,
  },
  appSubtitle: {
    fontSize: Typography.base,
    color: Colors.textInverse,
    opacity: 0.8,
  },
  formContainer: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 16,
    padding: Spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  inputContainer: {
    marginBottom: Spacing.md,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.lightGray,
    borderRadius: 8,
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.background,
  },
  inputError: {
    borderColor: Colors.error,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  textInput: {
    flex: 1,
    height: 48,
    fontSize: Typography.base,
    color: Colors.textPrimary,
  },
  eyeIcon: {
    padding: Spacing.xs,
  },
  errorText: {
    fontSize: Typography.sm,
    color: Colors.error,
    marginTop: Spacing.xs,
    marginLeft: Spacing.sm,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rememberMeText: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
    marginLeft: Spacing.xs,
  },
  forgotPasswordText: {
    fontSize: Typography.sm,
    color: Colors.primary,
    fontWeight: Typography.medium,
  },
  loginButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: Spacing.md,
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  loginButtonDisabled: {
    backgroundColor: Colors.secondary,
  },
  loginButtonText: {
    fontSize: Typography.base,
    fontWeight: Typography.semibold,
    color: Colors.textInverse,
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.md,
    marginBottom: Spacing.md,
  },
  biometricText: {
    fontSize: Typography.base,
    color: Colors.primary,
    marginLeft: Spacing.sm,
    fontWeight: Typography.medium,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  registerText: {
    fontSize: Typography.sm,
    color: Colors.textSecondary,
  },
  registerLink: {
    fontSize: Typography.sm,
    color: Colors.primary,
    fontWeight: Typography.semibold,
  },
  footer: {
    alignItems: 'center',
    marginTop: Spacing.xl,
  },
  footerText: {
    fontSize: Typography.xs,
    color: Colors.textInverse,
    opacity: 0.7,
    marginBottom: Spacing.xs,
  },
});
```

This completes the authentication implementation with detailed UI components. The next section will cover the dashboard module implementation.
