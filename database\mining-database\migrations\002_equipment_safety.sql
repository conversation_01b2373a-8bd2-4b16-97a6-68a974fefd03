-- =====================================================
-- Mining Operations Database - Migration 002
-- =====================================================
-- Migration: 002_equipment_safety.sql
-- Description: Equipment management and safety modules
-- Version: 1.0
-- Date: 2024-01-20
-- Dependencies: 001_initial_schema.sql
-- =====================================================

-- Check if previous migration was applied
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM schema_migrations WHERE version = '001') THEN
        RAISE EXCEPTION 'Migration 001 must be applied before running migration 002';
    END IF;
END $$;

-- Record this migration start
INSERT INTO schema_migrations (version, description) 
VALUES ('002', 'Equipment management and safety modules with IUT/OTT')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- EXECUTE EQUIPMENT AND SAFETY MODULES
-- =====================================================

DO $$
DECLARE
    start_time TIMESTAMPTZ := NOW();
    end_time TIMESTAMPTZ;
    execution_time INTEGER;
BEGIN
    RAISE NOTICE 'Starting Migration 002: Equipment and Safety Modules';
    RAISE NOTICE 'Start time: %', start_time;
    
    -- 1. Equipment Management
    RAISE NOTICE 'Executing 07-equipment-management.sql...';
    \i ../07-equipment-management.sql
    
    -- 2. Safety Management (with corrected IUT/OTT definitions)
    RAISE NOTICE 'Executing 08-safety-management.sql...';
    \i ../08-safety-management.sql
    
    -- Calculate execution time
    end_time := NOW();
    execution_time := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    -- Update migration record
    UPDATE schema_migrations 
    SET 
        applied_at = end_time,
        checksum = md5(random()::text),
        execution_time_ms = execution_time
    WHERE version = '002';
    
    RAISE NOTICE 'Migration 002 completed successfully';
    RAISE NOTICE 'End time: %', end_time;
    RAISE NOTICE 'Execution time: % ms', execution_time;
END $$;

-- =====================================================
-- POST-MIGRATION CORRECTIONS FOR IUT/OTT
-- =====================================================

-- Update training types to reflect correct IUT/OTT definitions
DO $$
BEGIN
    -- Update training_type enum if it exists
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'training_type') THEN
        -- Drop and recreate enum with correct values
        ALTER TYPE training_type RENAME TO training_type_old;
        
        CREATE TYPE training_type AS ENUM (
            'Safety Induction',
            'Equipment Training',
            'Emergency Response',
            'First Aid',
            'Fire Safety',
            'Environmental',
            'Hazmat',
            'Refresher',
            'Certification',
            'Greencard',
            'IUT Training',  -- Inspeksi Umum Terencana
            'OTT Training'   -- Observasi Tugas Terencana
        );
        
        -- Update existing columns
        ALTER TABLE safety_training ALTER COLUMN training_type TYPE training_type USING training_type::text::training_type;
        
        -- Drop old enum
        DROP TYPE training_type_old;
        
        RAISE NOTICE 'Updated training_type enum with correct IUT/OTT definitions';
    END IF;
END $$;

-- Update certification types to reflect correct definitions
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'certification_type') THEN
        -- Update certification_type enum
        ALTER TYPE certification_type RENAME TO certification_type_old;
        
        CREATE TYPE certification_type AS ENUM (
            'Greencard',
            'IUT',  -- Inspeksi Umum Terencana
            'OTT',  -- Observasi Tugas Terencana
            'First Aid',
            'Fire Safety',
            'Equipment Operator',
            'Safety Officer',
            'Mine Rescue',
            'Blasting',
            'Environmental'
        );
        
        -- Update existing columns
        ALTER TABLE mining_certifications ALTER COLUMN certificate_type TYPE certification_type USING certificate_type::text::certification_type;
        
        -- Drop old enum
        DROP TYPE certification_type_old;
        
        RAISE NOTICE 'Updated certification_type enum with correct IUT/OTT definitions';
    END IF;
END $$;

-- Update existing sample data with correct IUT/OTT information
UPDATE mining_certifications 
SET 
    certificate_name = 'Inspeksi Umum Terencana Certification',
    iut_license_type = NULL,
    iut_commodity = NULL,
    iut_area_hectares = NULL,
    iut_coordinates = NULL
WHERE certificate_type = 'IUT';

UPDATE mining_certifications 
SET 
    certificate_name = 'Observasi Tugas Terencana Certification',
    ott_operation_type = NULL,
    ott_capacity_tons_per_day = NULL,
    ott_environmental_approval = NULL,
    ott_safety_plan_approved = NULL
WHERE certificate_type = 'OTT';

-- Add new fields specific to IUT (Inspeksi Umum Terencana)
ALTER TABLE mining_certifications 
ADD COLUMN IF NOT EXISTS iut_inspection_scope TEXT[], -- Areas/equipment to inspect
ADD COLUMN IF NOT EXISTS iut_inspection_frequency VARCHAR(50), -- 'Daily', 'Weekly', 'Monthly'
ADD COLUMN IF NOT EXISTS iut_checklist_template VARCHAR(200), -- Reference to checklist
ADD COLUMN IF NOT EXISTS iut_competency_level VARCHAR(50); -- 'Basic', 'Advanced', 'Expert'

-- Add new fields specific to OTT (Observasi Tugas Terencana)
ALTER TABLE mining_certifications 
ADD COLUMN IF NOT EXISTS ott_observation_areas TEXT[], -- Work areas to observe
ADD COLUMN IF NOT EXISTS ott_behavioral_focus TEXT[], -- Safety behaviors to observe
ADD COLUMN IF NOT EXISTS ott_observation_frequency VARCHAR(50), -- 'Daily', 'Weekly'
ADD COLUMN IF NOT EXISTS ott_feedback_authority BOOLEAN DEFAULT false; -- Can provide feedback

-- Update sample data with correct IUT/OTT fields
UPDATE mining_certifications 
SET 
    iut_inspection_scope = ARRAY['Equipment', 'Work Area', 'Safety Procedures'],
    iut_inspection_frequency = 'Weekly',
    iut_checklist_template = 'IUT-CHECKLIST-001',
    iut_competency_level = 'Advanced'
WHERE certificate_type = 'IUT';

UPDATE mining_certifications 
SET 
    ott_observation_areas = ARRAY['Mining Operations', 'Equipment Operation', 'Safety Compliance'],
    ott_behavioral_focus = ARRAY['PPE Usage', 'Safe Work Procedures', 'Hazard Recognition'],
    ott_observation_frequency = 'Daily',
    ott_feedback_authority = true
WHERE certificate_type = 'OTT';

-- =====================================================
-- CREATE IUT/OTT SPECIFIC TABLES
-- =====================================================

-- IUT Inspection Records
CREATE TABLE IF NOT EXISTS iut_inspections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Inspector Information
    inspector_id UUID NOT NULL REFERENCES user_profiles(id),
    iut_certificate_id UUID REFERENCES mining_certifications(id),
    
    -- Inspection Details
    inspection_date DATE NOT NULL,
    inspection_time TIME NOT NULL,
    location VARCHAR(200) NOT NULL,
    equipment_id UUID REFERENCES equipment(id),
    
    -- Inspection Scope
    inspection_type VARCHAR(100) NOT NULL, -- 'Routine', 'Follow-up', 'Incident-related'
    inspection_areas TEXT[] NOT NULL,
    checklist_used VARCHAR(200),
    
    -- Findings
    total_items_checked INTEGER DEFAULT 0,
    compliant_items INTEGER DEFAULT 0,
    non_compliant_items INTEGER DEFAULT 0,
    critical_findings INTEGER DEFAULT 0,
    
    -- Results
    overall_compliance_rate DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN total_items_checked > 0 
        THEN (compliant_items::DECIMAL / total_items_checked * 100)
        ELSE 0 END
    ) STORED,
    
    -- Detailed Findings
    findings_summary TEXT,
    non_compliance_details JSONB, -- Detailed non-compliance items
    recommendations TEXT,
    immediate_actions_required TEXT,
    
    -- Follow-up
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    follow_up_assigned_to UUID REFERENCES user_profiles(id),
    
    -- Status
    status VARCHAR(50) DEFAULT 'Completed',
    reviewed_by UUID REFERENCES user_profiles(id),
    reviewed_at TIMESTAMPTZ,
    
    -- Documentation
    photos TEXT[],
    documents TEXT[],
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (total_items_checked >= 0),
    CHECK (compliant_items >= 0),
    CHECK (non_compliant_items >= 0),
    CHECK (critical_findings >= 0),
    CHECK (compliant_items + non_compliant_items <= total_items_checked)
);

-- OTT Observation Records
CREATE TABLE IF NOT EXISTS ott_observations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Observer Information
    observer_id UUID NOT NULL REFERENCES user_profiles(id),
    ott_certificate_id UUID REFERENCES mining_certifications(id),
    
    -- Observation Details
    observation_date DATE NOT NULL,
    observation_time TIME NOT NULL,
    location VARCHAR(200) NOT NULL,
    work_area VARCHAR(200),
    
    -- Observed Personnel
    observed_person_id UUID REFERENCES user_profiles(id),
    observed_person_name VARCHAR(200), -- If not in system
    observed_task VARCHAR(200) NOT NULL,
    task_duration_minutes INTEGER,
    
    -- Observation Focus
    behavioral_categories TEXT[] NOT NULL,
    safety_behaviors_observed JSONB, -- Detailed behavior observations
    
    -- Assessment
    safe_behaviors_count INTEGER DEFAULT 0,
    unsafe_behaviors_count INTEGER DEFAULT 0,
    total_behaviors_observed INTEGER DEFAULT 0,
    safety_compliance_rate DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE WHEN total_behaviors_observed > 0 
        THEN (safe_behaviors_count::DECIMAL / total_behaviors_observed * 100)
        ELSE 0 END
    ) STORED,
    
    -- Findings
    positive_observations TEXT,
    areas_for_improvement TEXT,
    unsafe_conditions_noted TEXT,
    immediate_interventions TEXT,
    
    -- Feedback
    feedback_provided BOOLEAN DEFAULT false,
    feedback_method VARCHAR(50), -- 'Verbal', 'Written', 'Demonstration'
    feedback_summary TEXT,
    worker_response TEXT,
    
    -- Follow-up
    coaching_required BOOLEAN DEFAULT false,
    training_recommended BOOLEAN DEFAULT false,
    follow_up_date DATE,
    
    -- Status
    status VARCHAR(50) DEFAULT 'Completed',
    reviewed_by UUID REFERENCES user_profiles(id),
    reviewed_at TIMESTAMPTZ,
    
    -- Documentation
    photos TEXT[],
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CHECK (safe_behaviors_count >= 0),
    CHECK (unsafe_behaviors_count >= 0),
    CHECK (total_behaviors_observed >= 0),
    CHECK (safe_behaviors_count + unsafe_behaviors_count <= total_behaviors_observed),
    CHECK (task_duration_minutes > 0)
);

-- =====================================================
-- CREATE INDEXES FOR NEW TABLES
-- =====================================================

-- IUT Inspections Indexes
CREATE INDEX IF NOT EXISTS idx_iut_inspections_inspector ON iut_inspections(inspector_id);
CREATE INDEX IF NOT EXISTS idx_iut_inspections_date ON iut_inspections(inspection_date);
CREATE INDEX IF NOT EXISTS idx_iut_inspections_location ON iut_inspections(location);
CREATE INDEX IF NOT EXISTS idx_iut_inspections_equipment ON iut_inspections(equipment_id);
CREATE INDEX IF NOT EXISTS idx_iut_inspections_compliance ON iut_inspections(overall_compliance_rate);

-- OTT Observations Indexes
CREATE INDEX IF NOT EXISTS idx_ott_observations_observer ON ott_observations(observer_id);
CREATE INDEX IF NOT EXISTS idx_ott_observations_date ON ott_observations(observation_date);
CREATE INDEX IF NOT EXISTS idx_ott_observations_location ON ott_observations(location);
CREATE INDEX IF NOT EXISTS idx_ott_observations_observed_person ON ott_observations(observed_person_id);
CREATE INDEX IF NOT EXISTS idx_ott_observations_compliance ON ott_observations(safety_compliance_rate);

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    equipment_count INTEGER;
    safety_count INTEGER;
    iut_count INTEGER;
    ott_count INTEGER;
    certification_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO equipment_count FROM equipment;
    SELECT COUNT(*) INTO safety_count FROM safety_incidents;
    SELECT COUNT(*) INTO iut_count FROM iut_inspections;
    SELECT COUNT(*) INTO ott_count FROM ott_observations;
    SELECT COUNT(*) INTO certification_count FROM mining_certifications;
    
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MIGRATION 002 VERIFICATION';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Equipment records: %', equipment_count;
    RAISE NOTICE 'Safety incidents: %', safety_count;
    RAISE NOTICE 'Mining certifications: %', certification_count;
    RAISE NOTICE 'IUT inspections table created: %', 
        CASE WHEN EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'iut_inspections') 
        THEN 'Yes' ELSE 'No' END;
    RAISE NOTICE 'OTT observations table created: %', 
        CASE WHEN EXISTS(SELECT 1 FROM information_schema.tables WHERE table_name = 'ott_observations') 
        THEN 'Yes' ELSE 'No' END;
    RAISE NOTICE '================================================';
    RAISE NOTICE 'IUT = Inspeksi Umum Terencana (Planned General Inspection)';
    RAISE NOTICE 'OTT = Observasi Tugas Terencana (Planned Task Observation)';
    RAISE NOTICE '================================================';
END $$;
