import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AnimatedMenuItem from '../menu/AnimatedMenuItem';
import { Colors } from '../../constants/colors';
import { Layout } from '../../constants/layout';

/**
 * Demo component to showcase the transparent menu item effects
 * This component demonstrates how the transparent menu items look
 * against different backgrounds and with various color schemes.
 */
const TransparencyDemo: React.FC = () => {
  const demoMenuItems = [
    {
      id: 'attendance',
      title: 'Attendance Recording',
      icon: 'calendar',
      color: Colors.primary,
      hasNotification: true,
      notificationCount: 3,
    },
    {
      id: 'atr',
      title: 'ATR Pribadi',
      icon: 'time',
      color: Colors.info,
    },
    {
      id: 'position',
      title: 'Attendance Position',
      icon: 'location',
      color: Colors.warning,
    },
    {
      id: 'bakomsel',
      title: 'Bakomsel',
      icon: 'phone-portrait',
      color: Colors.accent,
      hasNotification: true,
      notificationCount: 1,
    },
    {
      id: 'info',
      title: 'Info Bantuan',
      icon: 'information-circle',
      color: Colors.secondary,
    },
    {
      id: 'karyawan',
      title: 'Karyawan WFO',
      icon: 'people',
      color: Colors.primaryLight,
    },
    {
      id: 'presensi',
      title: 'Presensi Meeting',
      icon: 'happy',
      color: Colors.accentLight,
    },
    {
      id: 'profil',
      title: 'Profil Karyawan',
      icon: 'person',
      color: Colors.infoLight,
    },
  ];

  const handleMenuPress = (id: string) => {
    console.log(`Menu item pressed: ${id}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Transparency Effects Demo</Text>
          <Text style={styles.headerSubtitle}>
            Showcasing transparent menu items with modern UI design
          </Text>
        </View>

        {/* Demo Section 1: Background Image Menu Design */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Background Image Menu Items</Text>
          <Text style={styles.sectionDescription}>
            Menu items menggunakan background image (menubg.png) untuk implementasi yang sederhana dan efisien
          </Text>
          <View style={styles.whiteBackground}>
            <View style={styles.menuGrid}>
              {demoMenuItems.slice(0, 4).map((item, index) => (
                <AnimatedMenuItem
                  key={item.id}
                  id={item.id}
                  title={item.title}
                  icon={item.icon}
                  color={item.color}
                  onPress={() => handleMenuPress(item.id)}
                  index={index}
                  hasNotification={item.hasNotification}
                  notificationCount={item.notificationCount}
                />
              ))}
            </View>
          </View>
        </View>

        {/* Demo Section 2: Against Mining Background */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Against Mining Background</Text>
          <ImageBackground
            source={{
              uri: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'
            }}
            style={styles.backgroundDemo}
            imageStyle={styles.backgroundImage}
          >
            <View style={styles.overlay}>
              <View style={styles.menuGrid}>
                {demoMenuItems.slice(4, 8).map((item, index) => (
                  <AnimatedMenuItem
                    key={item.id}
                    id={item.id}
                    title={item.title}
                    icon={item.icon}
                    color={item.color}
                    onPress={() => handleMenuPress(item.id)}
                    index={index}
                    hasNotification={item.hasNotification}
                    notificationCount={item.notificationCount}
                  />
                ))}
              </View>
            </View>
          </ImageBackground>
        </View>

        {/* Demo Section 2: Against Gradient Background */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Against Gradient Background</Text>
          <View style={styles.gradientDemo}>
            <View style={styles.menuGrid}>
              {demoMenuItems.slice(4, 8).map((item, index) => (
                <AnimatedMenuItem
                  key={item.id}
                  id={item.id}
                  title={item.title}
                  icon={item.icon}
                  color={item.color}
                  onPress={() => handleMenuPress(item.id)}
                  index={index}
                  hasNotification={item.hasNotification}
                  notificationCount={item.notificationCount}
                />
              ))}
            </View>
          </View>
        </View>

        {/* Demo Section 3: Against Light Background */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Against Light Background</Text>
          <View style={styles.lightDemo}>
            <View style={styles.menuGrid}>
              {demoMenuItems.slice(0, 4).map((item, index) => (
                <AnimatedMenuItem
                  key={item.id}
                  id={`light-${item.id}`}
                  title={item.title}
                  icon={item.icon}
                  color={item.color}
                  onPress={() => handleMenuPress(item.id)}
                  index={index}
                  hasNotification={item.hasNotification}
                  notificationCount={item.notificationCount}
                />
              ))}
            </View>
          </View>
        </View>

        {/* Features List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Background Image Design Features</Text>
          <View style={styles.featuresList}>
            <Text style={styles.featureItem}>• Background image menggunakan menubg.png asset</Text>
            <Text style={styles.featureItem}>• Implementasi yang sederhana dan mudah maintain</Text>
            <Text style={styles.featureItem}>• Performance yang optimal dibanding SVG</Text>
            <Text style={styles.featureItem}>• Konsistensi bentuk di semua platform</Text>
            <Text style={styles.featureItem}>• Mudah update design dengan ganti file image</Text>
            <Text style={styles.featureItem}>• Efek bayangan yang mendalam</Text>
            <Text style={styles.featureItem}>• Animasi interaktif saat ditekan</Text>
            <Text style={styles.featureItem}>• Icon dengan warna primary untuk kontras</Text>
            <Text style={styles.featureItem}>• Shimmer effect yang smooth</Text>
            <Text style={styles.featureItem}>• Notification badge di pojok kanan atas</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: Layout.spacing.lg,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: Layout.fontSize.xxl,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.sm,
  },
  headerSubtitle: {
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  section: {
    marginBottom: Layout.spacing.xl,
  },
  sectionTitle: {
    fontSize: Layout.fontSize.lg,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.lg,
  },
  sectionDescription: {
    fontSize: Layout.fontSize.sm,
    color: Colors.textSecondary,
    marginBottom: Layout.spacing.md,
    paddingHorizontal: Layout.spacing.lg,
    fontStyle: 'italic',
  },
  whiteBackground: {
    backgroundColor: Colors.white,
    marginHorizontal: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.lg,
    minHeight: 150,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  backgroundDemo: {
    height: 200,
    marginHorizontal: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    overflow: 'hidden',
  },
  backgroundImage: {
    borderRadius: Layout.borderRadius.lg,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    padding: Layout.spacing.md,
  },
  gradientDemo: {
    height: 200,
    marginHorizontal: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    backgroundColor: Colors.primary, // Fallback for React Native
    justifyContent: 'center',
    padding: Layout.spacing.md,
  },
  lightDemo: {
    height: 200,
    marginHorizontal: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    padding: Layout.spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featuresList: {
    paddingHorizontal: Layout.spacing.lg,
  },
  featureItem: {
    fontSize: Layout.fontSize.md,
    color: Colors.textSecondary,
    marginBottom: Layout.spacing.sm,
    lineHeight: 20,
  },
});

export default TransparencyDemo;
