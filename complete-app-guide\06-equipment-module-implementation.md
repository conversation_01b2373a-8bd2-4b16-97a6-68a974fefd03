# Equipment Module Implementation - Mining Operations App

## 🚜 **Equipment Module Architecture**

### **Overview**
The equipment module manages all mining equipment including tracking, monitoring, maintenance scheduling, performance analysis, and real-time status updates. It supports various equipment types and provides comprehensive equipment lifecycle management.

### **Key Features**
- **Real-time equipment monitoring** with GPS tracking
- **Equipment performance analytics** and KPIs
- **Maintenance scheduling** and work order management
- **Fuel consumption tracking** and optimization
- **Equipment utilization** analysis
- **Alert management** for equipment issues
- **Operator assignment** and tracking
- **Equipment history** and audit trails
- **Parts inventory** integration
- **Cost tracking** and analysis

## 📁 **File Structure**

```
src/features/equipment/
├── components/
│   ├── EquipmentCard.tsx
│   ├── EquipmentStatusIndicator.tsx
│   ├── EquipmentMetricsChart.tsx
│   ├── MaintenanceScheduleCard.tsx
│   ├── FuelGauge.tsx
│   ├── UtilizationChart.tsx
│   ├── EquipmentForm.tsx
│   └── EquipmentQRScanner.tsx
├── hooks/
│   ├── useEquipmentData.ts
│   ├── useEquipmentMetrics.ts
│   ├── useMaintenanceSchedule.ts
│   └── useEquipmentAlerts.ts
├── screens/
│   ├── EquipmentOverviewScreen.tsx
│   ├── EquipmentListScreen.tsx
│   ├── EquipmentDetailScreen.tsx
│   ├── MaintenanceScheduleScreen.tsx
│   ├── EquipmentPerformanceScreen.tsx
│   └── EquipmentHistoryScreen.tsx
├── services/
│   ├── EquipmentService.ts
│   ├── EquipmentMetricsService.ts
│   ├── MaintenanceService.ts
│   └── EquipmentAlertService.ts
└── types/
    └── equipment.types.ts
```

## 🎯 **Domain Models**

### **src/models/Equipment.ts**
```typescript
export interface Equipment {
  id: string;
  equipmentNumber: string;
  name: string;
  type: EquipmentType;
  category: EquipmentCategory;
  manufacturer: string;
  model: string;
  yearManufactured: number;
  serialNumber: string;
  purchaseDate: string;
  purchaseCost: number;
  currentLocationId: string;
  currentLocationName?: string;
  status: EquipmentStatus;
  fuelType: FuelType;
  fuelCapacity: number;
  maxLoadCapacity: number;
  operatingWeight: number;
  currentOperatorId?: string;
  currentOperatorName?: string;
  qrCode: string;
  photoUrl?: string;
  specifications: EquipmentSpecifications;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface EquipmentMetric {
  id: string;
  equipmentId: string;
  equipmentName?: string;
  recordedDate: string;
  shiftId?: string;
  shiftName?: string;
  operatingHours: number;
  plannedHours: number;
  maintenanceHours: number;
  downtimeHours: number;
  fuelConsumed: number;
  distanceTraveled: number;
  loadCycles: number;
  operatorId?: string;
  operatorName?: string;
  locationId: string;
  locationName?: string;
  efficiencyScore: number;
  utilizationRate: number;
  healthScore: number;
  recordedBy: string;
  createdAt: string;
}

export interface EquipmentAlert {
  id: string;
  equipmentId: string;
  equipmentName?: string;
  alertType: AlertType;
  severity: AlertSeverity;
  title: string;
  message: string;
  triggeredAt: string;
  acknowledgedAt?: string;
  acknowledgedBy?: string;
  resolvedAt?: string;
  resolvedBy?: string;
  resolutionNotes?: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

export interface MaintenanceSchedule {
  id: string;
  equipmentId: string;
  equipmentName?: string;
  maintenanceType: MaintenanceType;
  title: string;
  description: string;
  frequencyType: FrequencyType;
  frequencyValue: number;
  lastCompletedAt?: string;
  nextDueDate: string;
  estimatedDurationHours: number;
  priority: MaintenancePriority;
  assignedTechnicianId?: string;
  assignedTechnicianName?: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkOrder {
  id: string;
  workOrderNumber: string;
  equipmentId: string;
  equipmentName?: string;
  maintenanceScheduleId?: string;
  title: string;
  description: string;
  workType: WorkType;
  priority: MaintenancePriority;
  status: WorkOrderStatus;
  requestedBy: string;
  requestedByName?: string;
  assignedTo?: string;
  assignedToName?: string;
  estimatedHours: number;
  actualHours?: number;
  estimatedCost: number;
  actualCost?: number;
  scheduledStart?: string;
  scheduledEnd?: string;
  actualStart?: string;
  actualEnd?: string;
  completionNotes?: string;
  safetyRequirements?: string;
  partsRequired: PartRequirement[];
  attachments: string[];
  createdAt: string;
  updatedAt: string;
}

export interface PartRequirement {
  id: string;
  partNumber: string;
  partName: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  isAvailable: boolean;
  supplierName?: string;
  estimatedDelivery?: string;
}

export interface EquipmentSpecifications {
  enginePower: number; // HP or kW
  engineModel: string;
  transmissionType: string;
  hydraulicPressure: number; // PSI
  bucketCapacity?: number; // cubic meters
  dumpBodyCapacity?: number; // cubic meters
  maxSpeed: number; // km/h
  gradeability: number; // percentage
  turningRadius: number; // meters
  groundClearance: number; // mm
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  tireSize?: string;
  trackWidth?: number;
}

export interface EquipmentPerformance {
  equipmentId: string;
  equipmentName: string;
  period: string;
  totalOperatingHours: number;
  totalPlannedHours: number;
  utilizationRate: number;
  efficiency: number;
  healthScore: number;
  fuelEfficiency: number; // liters per hour
  productivityScore: number;
  maintenanceCost: number;
  downtimeHours: number;
  alertsCount: number;
  ranking: number;
  trends: {
    utilization: TrendDirection;
    efficiency: TrendDirection;
    health: TrendDirection;
    fuel: TrendDirection;
  };
}

export interface EquipmentSummary {
  totalEquipment: number;
  activeEquipment: number;
  maintenanceEquipment: number;
  inactiveEquipment: number;
  averageUtilization: number;
  averageEfficiency: number;
  averageHealthScore: number;
  totalOperatingHours: number;
  totalFuelConsumption: number;
  totalMaintenanceCost: number;
  alertsCount: number;
  overdueMaintenanceCount: number;
  lastUpdated: string;
}

// Enums and Types
export type EquipmentType = 
  | 'Excavator'
  | 'Dump Truck'
  | 'Wheel Loader'
  | 'Bulldozer'
  | 'Motor Grader'
  | 'Drill Rig'
  | 'Crusher'
  | 'Conveyor'
  | 'Generator'
  | 'Water Pump'
  | 'Compressor'
  | 'Crane'
  | 'Forklift'
  | 'Other';

export type EquipmentCategory = 
  | 'Heavy Machinery'
  | 'Light Vehicle'
  | 'Processing Equipment'
  | 'Support Equipment'
  | 'Safety Equipment'
  | 'Material Handling';

export type EquipmentStatus = 
  | 'Active'
  | 'Maintenance'
  | 'Repair'
  | 'Inactive'
  | 'Retired'
  | 'Standby';

export type FuelType = 
  | 'Diesel'
  | 'Gasoline'
  | 'Electric'
  | 'Hybrid'
  | 'Natural Gas'
  | 'Hydraulic';

export type AlertType = 
  | 'Maintenance Due'
  | 'Performance Issue'
  | 'Safety Concern'
  | 'Fuel Low'
  | 'Overheating'
  | 'Mechanical Failure'
  | 'Operational'
  | 'GPS Lost'
  | 'Unauthorized Use';

export type AlertSeverity = 
  | 'Low'
  | 'Medium'
  | 'High'
  | 'Critical';

export type MaintenanceType = 
  | 'Preventive'
  | 'Predictive'
  | 'Corrective'
  | 'Emergency'
  | 'Inspection'
  | 'Calibration';

export type FrequencyType = 
  | 'Hours'
  | 'Days'
  | 'Weeks'
  | 'Months'
  | 'Kilometers'
  | 'Cycles';

export type MaintenancePriority = 
  | 'Low'
  | 'Medium'
  | 'High'
  | 'Emergency';

export type WorkType = 
  | 'Preventive Maintenance'
  | 'Corrective Maintenance'
  | 'Emergency Repair'
  | 'Inspection'
  | 'Calibration'
  | 'Upgrade'
  | 'Installation'
  | 'Replacement';

export type WorkOrderStatus = 
  | 'Open'
  | 'Assigned'
  | 'In Progress'
  | 'On Hold'
  | 'Completed'
  | 'Cancelled'
  | 'Rejected';

export type TrendDirection = 'up' | 'down' | 'stable';

export interface EquipmentFilters {
  type?: EquipmentType;
  category?: EquipmentCategory;
  status?: EquipmentStatus;
  locationId?: string;
  manufacturer?: string;
  operatorId?: string;
  searchTerm?: string;
}

export interface EquipmentMetricsFilters {
  equipmentId?: string;
  startDate?: string;
  endDate?: string;
  locationId?: string;
  shiftId?: string;
  operatorId?: string;
}

// Utility functions
export const calculateUtilizationRate = (
  operatingHours: number, 
  plannedHours: number
): number => {
  return plannedHours > 0 ? (operatingHours / plannedHours) * 100 : 0;
};

export const calculateEfficiency = (
  actualOutput: number, 
  expectedOutput: number
): number => {
  return expectedOutput > 0 ? (actualOutput / expectedOutput) * 100 : 0;
};

export const calculateHealthScore = (
  maintenanceScore: number,
  performanceScore: number,
  alertsScore: number
): number => {
  return (maintenanceScore * 0.4 + performanceScore * 0.4 + alertsScore * 0.2);
};

export const calculateFuelEfficiency = (
  fuelConsumed: number, 
  operatingHours: number
): number => {
  return operatingHours > 0 ? fuelConsumed / operatingHours : 0;
};

export const getEquipmentStatusColor = (status: EquipmentStatus): string => {
  const colors = {
    'Active': '#10B981',
    'Maintenance': '#F59E0B',
    'Repair': '#EF4444',
    'Inactive': '#6B7280',
    'Retired': '#374151',
    'Standby': '#3B82F6'
  };
  return colors[status] || '#6B7280';
};

export const getAlertSeverityColor = (severity: AlertSeverity): string => {
  const colors = {
    'Low': '#10B981',
    'Medium': '#F59E0B',
    'High': '#EF4444',
    'Critical': '#DC2626'
  };
  return colors[severity] || '#6B7280';
};

export const formatOperatingHours = (hours: number): string => {
  if (hours < 1) {
    return `${Math.round(hours * 60)}m`;
  } else if (hours < 24) {
    return `${hours.toFixed(1)}h`;
  } else {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours.toFixed(1)}h`;
  }
};

export const getMaintenanceStatusText = (nextDueDate: string): {
  status: 'overdue' | 'due_soon' | 'scheduled' | 'completed';
  text: string;
  color: string;
} => {
  const now = new Date();
  const dueDate = new Date(nextDueDate);
  const diffDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return {
      status: 'overdue',
      text: `Overdue by ${Math.abs(diffDays)} days`,
      color: '#EF4444'
    };
  } else if (diffDays <= 7) {
    return {
      status: 'due_soon',
      text: `Due in ${diffDays} days`,
      color: '#F59E0B'
    };
  } else {
    return {
      status: 'scheduled',
      text: `Due in ${diffDays} days`,
      color: '#10B981'
    };
  }
};

// Equipment type configurations
export const EQUIPMENT_TYPE_CONFIGS = {
  'Excavator': {
    icon: 'construct',
    color: '#F59E0B',
    defaultSpecs: {
      enginePower: 200,
      bucketCapacity: 1.5,
      operatingWeight: 20000
    }
  },
  'Dump Truck': {
    icon: 'car',
    color: '#3B82F6',
    defaultSpecs: {
      enginePower: 400,
      dumpBodyCapacity: 25,
      operatingWeight: 35000
    }
  },
  'Wheel Loader': {
    icon: 'hardware-chip',
    color: '#10B981',
    defaultSpecs: {
      enginePower: 150,
      bucketCapacity: 2.0,
      operatingWeight: 15000
    }
  },
  'Bulldozer': {
    icon: 'layers',
    color: '#EF4444',
    defaultSpecs: {
      enginePower: 300,
      operatingWeight: 25000
    }
  },
  'Drill Rig': {
    icon: 'flash',
    color: '#8B5CF6',
    defaultSpecs: {
      enginePower: 250,
      operatingWeight: 30000
    }
  }
} as const;

// Validation functions
export const validateEquipment = (equipment: Partial<Equipment>): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (!equipment.equipmentNumber?.trim()) {
    errors.push('Equipment number is required');
  }
  if (!equipment.name?.trim()) {
    errors.push('Equipment name is required');
  }
  if (!equipment.type) {
    errors.push('Equipment type is required');
  }
  if (!equipment.manufacturer?.trim()) {
    errors.push('Manufacturer is required');
  }
  if (!equipment.model?.trim()) {
    errors.push('Model is required');
  }
  if (!equipment.serialNumber?.trim()) {
    errors.push('Serial number is required');
  }
  if (!equipment.currentLocationId) {
    errors.push('Current location is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateEquipmentMetric = (metric: Partial<EquipmentMetric>): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (!metric.equipmentId) {
    errors.push('Equipment ID is required');
  }
  if (!metric.recordedDate) {
    errors.push('Recorded date is required');
  }
  if (metric.operatingHours === undefined || metric.operatingHours < 0) {
    errors.push('Operating hours must be 0 or greater');
  }
  if (metric.plannedHours === undefined || metric.plannedHours < 0) {
    errors.push('Planned hours must be 0 or greater');
  }
  if (metric.fuelConsumed === undefined || metric.fuelConsumed < 0) {
    errors.push('Fuel consumed must be 0 or greater');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
```

This completes the equipment domain models. The next section will cover the equipment service implementation with detailed CRUD operations and business logic.
