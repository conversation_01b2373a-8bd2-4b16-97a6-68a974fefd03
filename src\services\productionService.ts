import { supabase } from '../config/supabase';

export interface ProductionSite {
  id: string;
  site_code: string;
  site_name: string;
  site_type: string;
  status: string;
  area_hectares: number;
  supervisor_id: string;
  description: string;
}

export interface ProductionRecord {
  id: string;
  record_code: string;
  site_id: string;
  production_date: string;
  shift_type: string;
  ob_actual: number;
  ore_actual: number;
  fuel_actual: number;
  ob_plan: number;
  ore_plan: number;
  fuel_plan: number;
  stripping_ratio_actual: number;
  stripping_ratio_target: number;
  weather_condition: string;
  working_hours: number;
  downtime_hours: number;
  status: string;
  notes: string;
}

export interface ProductionSummary {
  production_date: string;
  site_id: string;
  total_ob_actual: number;
  total_ore_actual: number;
  total_fuel_actual: number;
  ob_achievement_percent: number;
  ore_achievement_percent: number;
  shift_count: number;
}

export interface DashboardStats {
  totalProduction: number;
  totalSites: number;
  activeEquipment: number;
  averageEfficiency: number;
  todayProduction: number;
  weeklyProduction: number;
  monthlyProduction: number;
}

export interface ProductionOverview {
  // OB (Overburden) metrics
  ob_plan_today: number;
  ob_actual_today: number;
  ob_actual_yesterday: number;
  ob_achievement_percent: number;

  // ORE Production metrics
  ore_plan_today: number;
  ore_actual_today: number;
  ore_actual_yesterday: number;
  ore_achievement_percent: number;

  // SR (Stripping Ratio) metrics
  sr_plan_today: number;
  sr_actual_today: number;
  sr_actual_yesterday: number;
  sr_performance_percent: number;

  // FR (Fuel Ratio) metrics
  fr_plan_today: number;
  fr_actual_today: number;
  fr_actual_yesterday: number;
  fr_performance_percent: number;

  // Equipment & Efficiency
  active_equipment: number;
  total_equipment: number;
  overall_efficiency: number;
}

class ProductionService {
  // Get all production sites
  async getProductionSites(): Promise<ProductionSite[]> {
    try {
      const { data, error } = await supabase
        .from('production_sites')
        .select('*')
        .eq('is_active', true)
        .order('site_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching production sites:', error);
      return [];
    }
  }

  // Get recent production records
  async getRecentProductionRecords(limit: number = 10): Promise<ProductionRecord[]> {
    try {
      const { data, error } = await supabase
        .from('production_records')
        .select(`
          *,
          production_sites(site_name, site_code)
        `)
        .eq('status', 'approved')
        .order('production_date', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching production records:', error);
      return [];
    }
  }

  // Get production summary for dashboard
  async getProductionSummary(days: number = 7): Promise<ProductionSummary[]> {
    try {
      const { data, error } = await supabase
        .from('production_daily_summary')
        .select('*')
        .gte('production_date', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
        .order('production_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching production summary:', error);
      return [];
    }
  }

  // Get dashboard statistics
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // Get today's production
      const { data: todayData } = await supabase
        .from('production_daily_summary')
        .select('total_ore_actual, ore_achievement_percent')
        .eq('production_date', today);

      // Get weekly production
      const { data: weeklyData } = await supabase
        .from('production_daily_summary')
        .select('total_ore_actual, ore_achievement_percent')
        .gte('production_date', weekAgo);

      // Get monthly production
      const { data: monthlyData } = await supabase
        .from('production_daily_summary')
        .select('total_ore_actual, ore_achievement_percent')
        .gte('production_date', monthAgo);

      // Get active sites count
      const { data: sitesData } = await supabase
        .from('production_sites')
        .select('id')
        .eq('is_active', true)
        .eq('status', 'operational');

      // Get active equipment count
      const { data: equipmentData } = await supabase
        .from('equipment')
        .select('id')
        .eq('status', 'operational');

      const todayProduction = todayData?.reduce((sum, record) => sum + (record.total_ore_actual || 0), 0) || 0;
      const weeklyProduction = weeklyData?.reduce((sum, record) => sum + (record.total_ore_actual || 0), 0) || 0;
      const monthlyProduction = monthlyData?.reduce((sum, record) => sum + (record.total_ore_actual || 0), 0) || 0;
      const averageEfficiency = weeklyData?.reduce((sum, record) => sum + (record.ore_achievement_percent || 0), 0) / (weeklyData?.length || 1) || 0;

      return {
        totalProduction: monthlyProduction,
        totalSites: sitesData?.length || 0,
        activeEquipment: equipmentData?.length || 0,
        averageEfficiency: Math.round(averageEfficiency),
        todayProduction,
        weeklyProduction,
        monthlyProduction
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        totalProduction: 0,
        totalSites: 0,
        activeEquipment: 0,
        averageEfficiency: 0,
        todayProduction: 0,
        weeklyProduction: 0,
        monthlyProduction: 0
      };
    }
  }

  // Get production trends for charts
  async getProductionTrends(days: number = 30): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('production_daily_summary')
        .select('production_date, total_ore_actual, total_ob_actual, ore_achievement_percent')
        .gte('production_date', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
        .order('production_date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching production trends:', error);
      return [];
    }
  }

  // Get production overview for dashboard
  async getProductionOverview(): Promise<ProductionOverview> {
    try {
      console.log('🗓️ Using sample data for dashboard (database connection issues)');

      // Use sample data while database connection is being fixed
      const todayRecords = [
        { ob_plan: '2100', ob_actual: '2260', ore_plan: '1035', ore_actual: '1115', fuel_plan: '1300', fuel_actual: '1405' },
        { ob_plan: '2110', ob_actual: '2280', ore_plan: '1040', ore_actual: '1120', fuel_plan: '1310', fuel_actual: '1410' },
        { ob_plan: '2105', ob_actual: '2270', ore_plan: '1038', ore_actual: '1118', fuel_plan: '1305', fuel_actual: '1408' },
        { ob_plan: '2115', ob_actual: '2290', ore_plan: '1042', ore_actual: '1122', fuel_plan: '1315', fuel_actual: '1412' },
        { ob_plan: '2120', ob_actual: '2220', ore_plan: '1045', ore_actual: '1100', fuel_plan: '1320', fuel_actual: '1390' },
      ];

      const yesterdayRecords = [
        { ob_actual: '2050', ore_actual: '1005', fuel_actual: '1280' },
        { ob_actual: '2060', ore_actual: '1010', fuel_actual: '1285' },
        { ob_actual: '2055', ore_actual: '1008', fuel_actual: '1283' },
        { ob_actual: '2065', ore_actual: '1012', fuel_actual: '1287' },
        { ob_actual: '2070', ore_actual: '1030', fuel_actual: '1295' },
      ];

      console.log('📊 Using sample today records:', todayRecords.length, 'records');
      console.log('📊 Using sample yesterday records:', yesterdayRecords.length, 'records');

      // Aggregate today's data from production_records
      const todayData = todayRecords?.reduce((acc, record) => {
        return {
          total_ob_plan: acc.total_ob_plan + parseFloat(record.ob_plan || '0'),
          total_ob_actual: acc.total_ob_actual + parseFloat(record.ob_actual || '0'),
          total_ore_plan: acc.total_ore_plan + parseFloat(record.ore_plan || '0'),
          total_ore_actual: acc.total_ore_actual + parseFloat(record.ore_actual || '0'),
          total_fuel_plan: acc.total_fuel_plan + parseFloat(record.fuel_plan || '0'),
          total_fuel_actual: acc.total_fuel_actual + parseFloat(record.fuel_actual || '0'),
        };
      }, {
        total_ob_plan: 0,
        total_ob_actual: 0,
        total_ore_plan: 0,
        total_ore_actual: 0,
        total_fuel_plan: 0,
        total_fuel_actual: 0,
      });

      // Aggregate yesterday's data from production_records
      const yesterdayData = yesterdayRecords?.reduce((acc, record) => {
        return {
          total_ob_actual: acc.total_ob_actual + parseFloat(record.ob_actual || '0'),
          total_ore_actual: acc.total_ore_actual + parseFloat(record.ore_actual || '0'),
          total_fuel_actual: acc.total_fuel_actual + parseFloat(record.fuel_actual || '0'),
        };
      }, {
        total_ob_actual: 0,
        total_ore_actual: 0,
        total_fuel_actual: 0,
      });

      // Calculate ratios for today
      const todayOreActual = todayData?.total_ore_actual || 0;
      const todayObActual = todayData?.total_ob_actual || 0;
      const todayFuelActual = todayData?.total_fuel_actual || 0;

      const avg_stripping_ratio_actual = todayOreActual > 0 ? todayObActual / todayOreActual : 0;
      const avg_fuel_ratio_actual = (todayObActual + todayOreActual/3.39) > 0 ?
        todayFuelActual / (todayObActual + todayOreActual/3.39) : 0;

      const ore_achievement_percent = (todayData?.total_ore_plan || 0) > 0 ?
        (todayOreActual / (todayData?.total_ore_plan || 1)) * 100 : 0;

      // Calculate ratios for yesterday
      const yesterdayOreActual = yesterdayData?.total_ore_actual || 0;
      const yesterdayObActual = yesterdayData?.total_ob_actual || 0;
      const yesterdayFuelActual = yesterdayData?.total_fuel_actual || 0;

      const yesterday_stripping_ratio = yesterdayOreActual > 0 ? yesterdayObActual / yesterdayOreActual : 0;
      const yesterday_fuel_ratio = (yesterdayObActual + yesterdayOreActual/3.39) > 0 ?
        yesterdayFuelActual / (yesterdayObActual + yesterdayOreActual/3.39) : 0;

      console.log('📊 Aggregated today data:', {
        ob_actual: todayObActual,
        ore_actual: todayOreActual,
        sr_actual: avg_stripping_ratio_actual,
        fr_actual: avg_fuel_ratio_actual,
        achievement: ore_achievement_percent
      });

      // Get equipment counts
      const { data: equipmentData, error: equipmentError } = await supabase
        .from('equipment')
        .select('status');

      if (equipmentError) {
        console.error('❌ Error fetching equipment data:', equipmentError);
      } else {
        console.log('⚙️ Equipment data:', equipmentData);
      }

      const activeEquipment = equipmentData?.filter(eq => eq.status === 'operational').length || 0;
      const totalEquipment = equipmentData?.length || 0;

      console.log('⚙️ Equipment summary:', { activeEquipment, totalEquipment });

      // Calculate metrics using the aggregated data
      const ob_plan_today = todayData?.total_ob_plan || 0;
      const ob_actual_today = todayData?.total_ob_actual || 0;
      const ob_actual_yesterday = yesterdayData?.total_ob_actual || 0;

      const ore_plan_today = todayData?.total_ore_plan || 0;
      const ore_actual_today = todayData?.total_ore_actual || 0;
      const ore_actual_yesterday = yesterdayData?.total_ore_actual || 0;

      // Use calculated ratios
      const sr_plan_today = ore_plan_today > 0 ? ob_plan_today / ore_plan_today : 0;
      const sr_actual_today = avg_stripping_ratio_actual;
      const sr_actual_yesterday = yesterday_stripping_ratio;

      const fr_plan_today = (ob_plan_today + ore_plan_today/3.39) > 0 ?
        (todayData?.total_fuel_plan || 0) / (ob_plan_today + ore_plan_today/3.39) : 0;
      const fr_actual_today = avg_fuel_ratio_actual;
      const fr_actual_yesterday = yesterday_fuel_ratio;

      return {
        ob_plan_today,
        ob_actual_today,
        ob_actual_yesterday,
        ob_achievement_percent: ob_plan_today > 0 ? (ob_actual_today / ob_plan_today) * 100 : 0,

        ore_plan_today,
        ore_actual_today,
        ore_actual_yesterday,
        ore_achievement_percent: ore_plan_today > 0 ? (ore_actual_today / ore_plan_today) * 100 : 0,

        sr_plan_today,
        sr_actual_today,
        sr_actual_yesterday,
        sr_performance_percent: sr_plan_today > 0 ? (sr_actual_today / sr_plan_today) * 100 : 0,

        fr_plan_today,
        fr_actual_today,
        fr_actual_yesterday,
        fr_performance_percent: fr_plan_today > 0 ? (fr_actual_today / fr_plan_today) * 100 : 0,

        active_equipment: activeEquipment,
        total_equipment: totalEquipment,
        overall_efficiency: ore_achievement_percent
      };
    } catch (error) {
      console.error('Error fetching production overview:', error);
      return {
        ob_plan_today: 0, ob_actual_today: 0, ob_actual_yesterday: 0, ob_achievement_percent: 0,
        ore_plan_today: 0, ore_actual_today: 0, ore_actual_yesterday: 0, ore_achievement_percent: 0,
        sr_plan_today: 0, sr_actual_today: 0, sr_actual_yesterday: 0, sr_performance_percent: 0,
        fr_plan_today: 0, fr_actual_today: 0, fr_actual_yesterday: 0, fr_performance_percent: 0,
        active_equipment: 0, total_equipment: 0, overall_efficiency: 0
      };
    }
  }

  // Refresh materialized view (for admin use)
  async refreshProductionSummary(): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('refresh_materialized_view', {
        view_name: 'production_daily_summary'
      });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error refreshing production summary:', error);
      return false;
    }
  }
}

export const productionService = new ProductionService();
