import { Easing } from 'react-native';

/**
 * Test file to verify that all easing functions used in the app are available
 * This helps prevent "easing is not a function" errors
 */

export const testEasingFunctions = () => {
  try {
    // Test all easing functions used in the app
    const easingFunctions = {
      // Used in AnimatedMenuItem.tsx
      cubicOut: Easing.out(Easing.cubic),
      quadOut: Easing.out(Easing.quad),
      
      // Used in MenuGridSkeleton.tsx
      quadInOut: Easing.inOut(Easing.quad),
      
      // Used in ParticleEffect.tsx
      quadIn: Easing.in(Easing.quad),
      cubicOut2: Easing.out(Easing.cubic),
    };

    // Verify all functions are defined
    Object.entries(easingFunctions).forEach(([name, easingFn]) => {
      if (typeof easingFn !== 'function') {
        throw new Error(`Easing function ${name} is not a function`);
      }
    });

    console.log('✅ All easing functions are available and working');
    return true;
  } catch (error) {
    console.error('❌ Easing function test failed:', error);
    return false;
  }
};

// Available easing functions in React Native (for reference)
export const availableEasingFunctions = {
  // Basic easing functions
  linear: Easing.linear,
  quad: Easing.quad,
  cubic: Easing.cubic,
  
  // Composite easing functions
  in: (easing: any) => Easing.in(easing),
  out: (easing: any) => Easing.out(easing),
  inOut: (easing: any) => Easing.inOut(easing),
  
  // Note: Easing.back() and Easing.sine might not be available in all versions
  // Use Easing.cubic or Easing.quad as alternatives
};
