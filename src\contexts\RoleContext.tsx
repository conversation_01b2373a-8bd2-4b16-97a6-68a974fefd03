// ===== KONTEKS SISTEM ROLE =====
// Context untuk manajemen role dan permission pengguna

import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../config/supabase';
import { useAuth } from './AuthContext';
import { 
  UserWithRole, 
  RoleContext as IRoleContext, 
  PermissionAction, 
  RoleUtils,
  RoleLevel,
  RoleName 
} from '../types/roles';

// Interface untuk RoleProvider props
interface RoleProviderProps {
  children: React.ReactNode;
}

// Buat context dengan nilai default
const RoleContext = createContext<IRoleContext>({
  userRole: null,
  hasPermission: () => false,
  hasMinimumLevel: () => false,
  isAdmin: () => false,
  isSuperAdmin: () => false,
  canAccessResource: () => false,
});

// Provider component untuk role context
export const RoleProvider: React.FC<RoleProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [userRole, setUserRole] = useState<UserWithRole | null>(null);
  const [loading, setLoading] = useState(true);

  // Fungsi untuk mengambil data role user dari database
  const fetchUserRole = async (userId: string) => {
    try {
      setLoading(true);
      
      // Query menggunakan view user_roles yang sudah dibuat
      const { data, error } = await supabase
        .from('user_roles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user role:', error);
        return;
      }

      if (data) {
        setUserRole(data as UserWithRole);
      }
    } catch (error) {
      console.error('Error in fetchUserRole:', error);
    } finally {
      setLoading(false);
    }
  };

  // Effect untuk mengambil role ketika user berubah
  useEffect(() => {
    if (user?.id) {
      fetchUserRole(user.id);
    } else {
      setUserRole(null);
      setLoading(false);
    }
  }, [user]);

  // Fungsi untuk cek permission
  const hasPermission = (resource: string, action: PermissionAction): boolean => {
    return RoleUtils.hasPermission(userRole, resource, action);
  };

  // Fungsi untuk cek level minimum
  const hasMinimumLevel = (requiredLevel: number): boolean => {
    return RoleUtils.hasMinimumLevel(userRole, requiredLevel);
  };

  // Fungsi untuk cek apakah user adalah admin
  const isAdmin = (): boolean => {
    return RoleUtils.isAdmin(userRole);
  };

  // Fungsi untuk cek apakah user adalah super admin
  const isSuperAdmin = (): boolean => {
    return RoleUtils.isSuperAdmin(userRole);
  };

  // Fungsi untuk cek apakah user dapat mengakses resource
  const canAccessResource = (resource: string): boolean => {
    if (!userRole) return false;
    
    // Super admin dapat mengakses semua resource
    if (userRole.role_permissions.all) return true;
    
    // Cek apakah resource ada dalam permissions
    const resourcePermissions = userRole.role_permissions[resource as keyof typeof userRole.role_permissions];
    return Array.isArray(resourcePermissions) && resourcePermissions.length > 0;
  };

  // Nilai context yang akan disediakan
  const contextValue: IRoleContext = {
    userRole,
    hasPermission,
    hasMinimumLevel,
    isAdmin,
    isSuperAdmin,
    canAccessResource,
  };

  return (
    <RoleContext.Provider value={contextValue}>
      {children}
    </RoleContext.Provider>
  );
};

// Hook untuk menggunakan role context
export const useRole = (): IRoleContext => {
  const context = useContext(RoleContext);
  
  if (!context) {
    throw new Error('useRole must be used within a RoleProvider');
  }
  
  return context;
};

// Hook khusus untuk permission checking dengan loading state
export const usePermission = (resource: string, action: PermissionAction) => {
  const { userRole, hasPermission } = useRole();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set loading false setelah userRole tersedia atau null
    if (userRole !== undefined) {
      setLoading(false);
    }
  }, [userRole]);

  return {
    allowed: hasPermission(resource, action),
    loading,
    userRole
  };
};

// Hook untuk role-based navigation guard
export const useRoleGuard = (requiredLevel: number) => {
  const { userRole, hasMinimumLevel } = useRole();
  const [canAccess, setCanAccess] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (userRole) {
      setCanAccess(hasMinimumLevel(requiredLevel));
      setLoading(false);
    } else if (userRole === null) {
      setCanAccess(false);
      setLoading(false);
    }
  }, [userRole, requiredLevel, hasMinimumLevel]);

  return {
    canAccess,
    loading,
    userRole,
    currentLevel: userRole?.role_level || 0,
    requiredLevel
  };
};

// Komponen HOC untuk role-based access control
interface WithRoleAccessProps {
  children: React.ReactNode;
  requiredLevel?: number;
  requiredPermission?: {
    resource: string;
    action: PermissionAction;
  };
  fallback?: React.ReactNode;
}

export const WithRoleAccess: React.FC<WithRoleAccessProps> = ({
  children,
  requiredLevel,
  requiredPermission,
  fallback = null
}) => {
  const { userRole, hasMinimumLevel, hasPermission } = useRole();

  // Jika belum ada data user role, tampilkan loading atau fallback
  if (!userRole) {
    return <>{fallback}</>;
  }

  // Cek level requirement
  if (requiredLevel && !hasMinimumLevel(requiredLevel)) {
    return <>{fallback}</>;
  }

  // Cek permission requirement
  if (requiredPermission && !hasPermission(requiredPermission.resource, requiredPermission.action)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Utility functions untuk digunakan di luar component
export const RoleContextUtils = {
  // Fungsi untuk mendapatkan role berdasarkan level
  getRoleByLevel: (level: number): string => {
    return RoleUtils.getRoleDisplayByLevel(level);
  },

  // Fungsi untuk mendapatkan warna badge berdasarkan level
  getRoleBadgeColor: (level: number): string => {
    if (level >= RoleLevel.SUPER_ADMIN) return '#DC2626'; // Red
    if (level >= RoleLevel.ADMIN) return '#EA580C'; // Orange
    if (level >= RoleLevel.MINE_MANAGER) return '#D97706'; // Amber
    if (level >= RoleLevel.PRODUCTION_SUPERVISOR) return '#059669'; // Emerald
    if (level >= RoleLevel.EQUIPMENT_MANAGER) return '#0891B2'; // Cyan
    if (level >= RoleLevel.SAFETY_OFFICER) return '#7C3AED'; // Violet
    if (level >= RoleLevel.SHIFT_SUPERVISOR) return '#C2410C'; // Orange-600
    if (level >= RoleLevel.OPERATOR) return '#0D9488'; // Teal
    if (level >= RoleLevel.TECHNICIAN) return '#7C2D12'; // Orange-900
    return '#6B7280'; // Gray
  },

  // Fungsi untuk mendapatkan deskripsi singkat role
  getRoleDescription: (roleName: string): string => {
    const descriptions: Record<string, string> = {
      [RoleName.SUPER_ADMIN]: 'Akses penuh sistem',
      [RoleName.ADMIN]: 'Administrator sistem',
      [RoleName.MINE_MANAGER]: 'Manajer operasi tambang',
      [RoleName.PRODUCTION_SUPERVISOR]: 'Supervisor produksi',
      [RoleName.EQUIPMENT_MANAGER]: 'Manajer peralatan',
      [RoleName.SAFETY_OFFICER]: 'Petugas keselamatan',
      [RoleName.SHIFT_SUPERVISOR]: 'Supervisor shift',
      [RoleName.OPERATOR]: 'Operator peralatan',
      [RoleName.TECHNICIAN]: 'Teknisi perawatan',
      [RoleName.EMPLOYEE]: 'Karyawan umum'
    };
    return descriptions[roleName] || 'Role tidak dikenal';
  }
};

export default RoleContext;
