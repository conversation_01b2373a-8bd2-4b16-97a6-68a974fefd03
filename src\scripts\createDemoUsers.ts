// ===== SCRIPT PEMBUATAN USER DEMO =====
// Script untuk membuat user demo untuk setiap level role dalam sistem

import { supabase } from '../config/supabase';

// Interface untuk data user demo
interface DemoUser {
  email: string;
  password: string;
  full_name: string;
  phone: string;
  employee_id: string;
  nik: string;
  departemen: string;
  jabatan: string;
  hire_date: string;
  location_id: string;
  role_name: string;
}

// Data user demo untuk setiap level role
const demoUsers: DemoUser[] = [
  // Level 10 - Super Admin (sudah ada, akan diupdate)
  {
    email: '<EMAIL>',
    password: 'SuperAdmin123!',
    full_name: 'Super Administrator',
    phone: '+62812-1000-0010',
    employee_id: 'SA001',
    nik: '1234567890000010',
    departemen: 'IT_MANAGEMENT',
    jabatan: 'Super Administrator',
    hire_date: '2020-01-01',
    location_id: '550e8400-e29b-41d4-a716-446655440004', // Central Office
    role_name: 'super_admin'
  },
  
  // Level 9 - Admin
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    full_name: 'John Administrator',
    phone: '+62812-1000-0009',
    employee_id: 'ADM001',
    nik: '1234567890000009',
    departemen: 'IT_MANAGEMENT',
    jabatan: 'System Administrator',
    hire_date: '2020-02-01',
    location_id: '550e8400-e29b-41d4-a716-446655440004', // Central Office
    role_name: 'admin'
  },
  
  // Level 8 - Mine Manager
  {
    email: '<EMAIL>',
    password: 'Manager123!',
    full_name: 'Robert Mine Manager',
    phone: '+62812-1000-0008',
    employee_id: 'MM001',
    nik: '1234567890000008',
    departemen: 'OPERATIONS',
    jabatan: 'Mine Manager',
    hire_date: '2020-03-01',
    location_id: '550e8400-e29b-41d4-a716-446655440001', // North Mine Site
    role_name: 'mine_manager'
  },
  
  // Level 7 - Production Supervisor
  {
    email: '<EMAIL>',
    password: 'ProdSup123!',
    full_name: 'Sarah Production Supervisor',
    phone: '+62812-1000-0007',
    employee_id: 'PS001',
    nik: '1234567890000007',
    departemen: 'PRODUCTION',
    jabatan: 'Production Supervisor',
    hire_date: '2020-04-01',
    location_id: '550e8400-e29b-41d4-a716-446655440001', // North Mine Site
    role_name: 'production_supervisor'
  },
  
  // Level 6 - Equipment Manager
  {
    email: '<EMAIL>',
    password: 'EquipMgr123!',
    full_name: 'Michael Equipment Manager',
    phone: '+62812-1000-0006',
    employee_id: 'EM001',
    nik: '1234567890000006',
    departemen: 'MAINTENANCE',
    jabatan: 'Equipment Manager',
    hire_date: '2020-05-01',
    location_id: '550e8400-e29b-41d4-a716-446655440005', // Equipment Warehouse
    role_name: 'equipment_manager'
  },
  
  // Level 5 - Safety Officer
  {
    email: '<EMAIL>',
    password: 'Safety123!',
    full_name: 'Lisa Safety Officer',
    phone: '+62812-1000-0005',
    employee_id: 'SO001',
    nik: '1234567890000005',
    departemen: 'SAFETY',
    jabatan: 'Safety Officer',
    hire_date: '2020-06-01',
    location_id: '550e8400-e29b-41d4-a716-446655440004', // Central Office
    role_name: 'safety_officer'
  },
  
  // Level 4 - Shift Supervisor
  {
    email: '<EMAIL>',
    password: 'ShiftSup123!',
    full_name: 'David Shift Supervisor',
    phone: '+62812-1000-0004',
    employee_id: 'SS001',
    nik: '1234567890000004',
    departemen: 'OPERATIONS',
    jabatan: 'Shift Supervisor',
    hire_date: '2020-07-01',
    location_id: '550e8400-e29b-41d4-a716-446655440003', // South Mine Site
    role_name: 'shift_supervisor'
  },
  
  // Level 3 - Operator
  {
    email: '<EMAIL>',
    password: 'Operator123!',
    full_name: 'James Equipment Operator',
    phone: '+62812-1000-0003',
    employee_id: 'OP001',
    nik: '1234567890000003',
    departemen: 'OPERATIONS',
    jabatan: 'Equipment Operator',
    hire_date: '2020-08-01',
    location_id: '550e8400-e29b-41d4-a716-446655440001', // North Mine Site
    role_name: 'operator'
  },
  
  // Level 2 - Technician
  {
    email: '<EMAIL>',
    password: 'Technician123!',
    full_name: 'Mark Maintenance Technician',
    phone: '+62812-1000-0002',
    employee_id: 'TC001',
    nik: '1234567890000002',
    departemen: 'MAINTENANCE',
    jabatan: 'Maintenance Technician',
    hire_date: '2020-09-01',
    location_id: '550e8400-e29b-41d4-a716-446655440005', // Equipment Warehouse
    role_name: 'technician'
  },
  
  // Level 1 - Employee
  {
    email: '<EMAIL>',
    password: 'Employee123!',
    full_name: 'Anna General Employee',
    phone: '+62812-1000-0001',
    employee_id: 'EMP001',
    nik: '1234567890000001',
    departemen: 'ADMINISTRATION',
    jabatan: 'General Employee',
    hire_date: '2020-10-01',
    location_id: '550e8400-e29b-41d4-a716-446655440004', // Central Office
    role_name: 'employee'
  }
];

// Fungsi untuk membuat user demo
export const createDemoUsers = async () => {
  console.log('🚀 Memulai pembuatan user demo...');
  
  for (const userData of demoUsers) {
    try {
      console.log(`📝 Membuat user: ${userData.email}`);
      
      // 1. Buat user di Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
      });

      if (authError) {
        console.error(`❌ Error membuat auth user ${userData.email}:`, authError.message);
        continue;
      }

      if (!authData.user) {
        console.error(`❌ Tidak ada user data untuk ${userData.email}`);
        continue;
      }

      // 2. Dapatkan role_id
      const { data: roleData, error: roleError } = await supabase
        .from('roles')
        .select('id')
        .eq('name', userData.role_name)
        .single();

      if (roleError || !roleData) {
        console.error(`❌ Error mendapatkan role ${userData.role_name}:`, roleError?.message);
        continue;
      }

      // 3. Update atau insert profile user
      const { error: profileError } = await supabase
        .from('users')
        .upsert({
          id: authData.user.id,
          email: userData.email,
          full_name: userData.full_name,
          phone: userData.phone,
          employee_id: userData.employee_id,
          nik: userData.nik,
          departemen: userData.departemen,
          jabatan: userData.jabatan,
          hire_date: userData.hire_date,
          location_id: userData.location_id,
          role_id: roleData.id,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.error(`❌ Error membuat profile ${userData.email}:`, profileError.message);
        continue;
      }

      console.log(`✅ Berhasil membuat user: ${userData.email} (${userData.role_name})`);
      
      // Delay untuk menghindari rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Error umum untuk ${userData.email}:`, error);
    }
  }
  
  console.log('🎉 Selesai membuat user demo!');
};

// Fungsi untuk menampilkan informasi login user demo
export const displayDemoUserCredentials = () => {
  console.log('\n📋 KREDENSIAL USER DEMO UNTUK DEVELOPMENT:');
  console.log('=' .repeat(60));
  
  demoUsers.forEach((user, index) => {
    console.log(`\n${index + 1}. ${user.full_name} (Level ${10 - index})`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Password: ${user.password}`);
    console.log(`   Role: ${user.role_name}`);
    console.log(`   Department: ${user.departemen}`);
    console.log(`   Position: ${user.jabatan}`);
  });
  
  console.log('\n' + '=' .repeat(60));
  console.log('💡 Gunakan kredensial di atas untuk testing berbagai level akses');
  console.log('🔒 Password semua user mengikuti format: [Role]123!');
};

// Export untuk digunakan di tempat lain
export { demoUsers };

// Jika file ini dijalankan langsung
if (require.main === module) {
  createDemoUsers()
    .then(() => {
      displayDemoUserCredentials();
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error menjalankan script:', error);
      process.exit(1);
    });
}
