{"timestamp": "2025-07-26T23:01:09.438Z", "application": {"name": "mining-operations-platform", "version": "1.0.0", "status": "unknown", "dependencies": 29, "devDependencies": 4}, "database": {"provider": "Supabase", "url": "https://ohqbaimnhwvdfrmxvhxv.supabase.co", "connection": "success", "tables": {"users": {"status": "accessible", "sampleRecords": 0}, "locations": {"status": "accessible", "sampleRecords": 0}, "equipment": {"status": "accessible", "sampleRecords": 0}, "production_reports": {"status": "accessible", "sampleRecords": 0}, "safety_incidents": {"status": "accessible", "sampleRecords": 0}, "maintenance_records": {"status": "accessible", "sampleRecords": 0}, "shifts": {"status": "accessible", "sampleRecords": 0}, "user_shifts": {"status": "accessible", "sampleRecords": 0}, "activity_documentation": {"status": "accessible", "sampleRecords": 1}}, "dataCount": {"users": 0, "locations": 0, "equipment": 0, "production_reports": 0, "safety_incidents": 0}, "responseTime": 1568}, "system": {"platform": "win32", "nodeVersion": "v22.15.0", "architecture": "x64"}, "tests": [{"name": "Database Connection", "status": "PASS", "message": "Connected successfully (1568ms)", "details": null}, {"name": "Table: users", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: locations", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: equipment", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: production_reports", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: safety_incidents", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: maintenance_records", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: shifts", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: user_shifts", "status": "PASS", "message": "Accessible (0 sample records)", "details": null}, {"name": "Table: activity_documentation", "status": "PASS", "message": "Accessible (1 sample records)", "details": null}, {"name": "Authentication", "status": "WARNING", "message": "No active session (not logged in)", "details": null}], "recommendations": [{"type": "data", "priority": "medium", "title": "No Sample Data Found", "description": "Database tables are empty. Consider running the sample data population script.", "action": "Run: node scripts/populate-sample-data.js"}, {"type": "auth", "priority": "low", "title": "No Active Session", "description": "No user is currently logged in. This is normal for initial setup.", "action": "Create a user account through the app or admin panel"}], "authentication": {"status": "not_authenticated"}}