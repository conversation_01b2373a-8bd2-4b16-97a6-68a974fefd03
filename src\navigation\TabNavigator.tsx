// @ts-nocheck
import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text, StyleSheet, Platform, Animated, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

// Import screens and navigators
import DashboardStackNavigator from './DashboardStackNavigator';
import ProfileStackNavigator from './ProfileStackNavigator';
import EquipmentScreen from '../screens/EquipmentScreen';
import SafetyScreen from '../screens/SafetyScreen';
import ReportsScreen from '../screens/ReportsScreen';


import { Colors } from '../constants/colors';
import { Layout } from '../constants/layout';

const Tab = createBottomTabNavigator();

interface TabBarIconProps {
  focused: boolean;
  color: string;
  size: number;
}

// Custom animated tab bar button component
const AnimatedTabBarButton: React.FC<any> = (props) => {
  const { children, onPress, onLongPress, accessibilityLabel, testID, ...otherProps } = props;
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.timing(scaleValue, {
      toValue: 0.9,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.timing(scaleValue, {
      toValue: 1,
      duration: 150,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    // Ensure animation completes before navigation
    if (onPress) {
      onPress();
    }
  };

  return (
    <TouchableOpacity
      {...otherProps}
      onPress={handlePress}
      onLongPress={onLongPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      accessibilityLabel={accessibilityLabel}
      testID={testID}
      style={[styles.tabButton, otherProps.style]}
      activeOpacity={1}
    >
      <Animated.View style={[
        styles.animatedContainer,
        { transform: [{ scale: scaleValue }] }
      ]}>
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
};

const TabNavigator: React.FC = () => {
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: Colors.surface,
          borderTopWidth: 1,
          borderTopColor: Colors.border,
          height: Layout.tabBarHeight + (Platform.OS === 'android' ? insets.bottom : 0),
          paddingBottom: Platform.OS === 'ios' ? 20 : Math.max(insets.bottom, 10),
          paddingTop: 10,
          ...Layout.shadow,
        },
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.textLight,
        tabBarLabelStyle: {
          fontSize: Layout.fontSize.xs,
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarButton: (props) => <AnimatedTabBarButton {...props} />,
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardStackNavigator}
        options={{
          tabBarIcon: ({ focused, color, size }: TabBarIconProps) => (
            <View style={[styles.iconContainer, focused && styles.iconContainerFocused]}>
              <Ionicons
                name={focused ? 'home' : 'home-outline'}
                size={size}
                color={color}
              />
            </View>
          ),
          tabBarLabel: 'Dashboard',
        }}
      />
      
      <Tab.Screen
        name="Equipment"
        component={EquipmentScreen}
        options={{
          tabBarIcon: ({ focused, color, size }: TabBarIconProps) => (
            <View style={[styles.iconContainer, focused && styles.iconContainerFocused]}>
              <Ionicons 
                name={focused ? 'construct' : 'construct-outline'} 
                size={size} 
                color={color} 
              />
            </View>
          ),
          tabBarLabel: 'Equipment',
        }}
      />
      
      <Tab.Screen
        name="Safety"
        component={SafetyScreen}
        options={{
          tabBarIcon: ({ focused, color, size }: TabBarIconProps) => (
            <View style={[styles.iconContainer, focused && styles.iconContainerFocused]}>
              <Ionicons 
                name={focused ? 'shield-checkmark' : 'shield-checkmark-outline'} 
                size={size} 
                color={color} 
              />
            </View>
          ),
          tabBarLabel: 'Safety',
        }}
      />
      
      <Tab.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          tabBarIcon: ({ focused, color, size }: TabBarIconProps) => (
            <View style={[styles.iconContainer, focused && styles.iconContainerFocused]}>
              <Ionicons
                name={focused ? 'document-text' : 'document-text-outline'}
                size={size}
                color={color}
              />
            </View>
          ),
          tabBarLabel: 'Reports',
        }}
      />



      <Tab.Screen
        name="Profile"
        component={ProfileStackNavigator}
        options={{
          tabBarIcon: ({ focused, color, size }: TabBarIconProps) => (
            <View style={[styles.iconContainer, focused && styles.iconContainerFocused]}>
              <Ionicons
                name={focused ? 'person' : 'person-outline'}
                size={size}
                color={color}
              />
            </View>
          ),
          tabBarLabel: 'Profile',
        }}
      />


    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 32,
    height: 32,
    borderRadius: Layout.borderRadius.md,
  },
  iconContainerFocused: {
    backgroundColor: Colors.primaryLight + '20',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 2,
    minHeight: 50, // Ensure minimum touch target size
  },
  animatedContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
});

export default TabNavigator;
