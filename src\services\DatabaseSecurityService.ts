import { supabase } from '../config/supabase';

export interface TableSecurityInfo {
  table_name: string;
  rls_enabled: boolean;
  policies: PolicyInfo[];
  access_level: 'unrestricted' | 'restricted' | 'user_specific';
}

export interface PolicyInfo {
  policy_name: string;
  command: string; // SELECT, INSERT, UPDATE, DELETE
  roles: string[];
  using_expression: string;
  check_expression?: string;
}

export class DatabaseSecurityService {
  private static instance: DatabaseSecurityService;

  private constructor() {}

  static getInstance(): DatabaseSecurityService {
    if (!DatabaseSecurityService.instance) {
      DatabaseSecurityService.instance = new DatabaseSecurityService();
    }
    return DatabaseSecurityService.instance;
  }

  // Check RLS status for all tables
  async checkTableSecurity(): Promise<TableSecurityInfo[]> {
    try {
      console.log('🔒 Checking database security policies...');

      // Get all user tables (excluding system tables)
      const { data: tables, error: tablesError } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .not('table_name', 'like', 'pg_%')
        .not('table_name', 'like', 'spatial_%')
        .not('table_name', 'like', 'geography_%')
        .not('table_name', 'like', 'geometry_%');

      if (tablesError) {
        console.error('❌ Failed to get tables:', tablesError);
        return [];
      }

      const securityInfo: TableSecurityInfo[] = [];

      for (const table of tables || []) {
        const info = await this.getTableSecurityInfo(table.table_name);
        securityInfo.push(info);
      }

      console.log(`🔒 Security analysis completed for ${securityInfo.length} tables`);
      return securityInfo;
    } catch (error) {
      console.error('❌ Security check error:', error);
      return [];
    }
  }

  // Get security info for specific table
  private async getTableSecurityInfo(tableName: string): Promise<TableSecurityInfo> {
    try {
      // Check if RLS is enabled
      const { data: rlsData, error: rlsError } = await supabase
        .rpc('check_rls_enabled', { table_name: tableName });

      const rls_enabled = !rlsError && rlsData;

      // Get policies for the table
      const policies = await this.getTablePolicies(tableName);

      // Determine access level
      let access_level: 'unrestricted' | 'restricted' | 'user_specific' = 'restricted';
      
      if (!rls_enabled || policies.length === 0) {
        access_level = 'unrestricted';
      } else if (policies.some(p => p.using_expression.includes('auth.uid()'))) {
        access_level = 'user_specific';
      }

      return {
        table_name: tableName,
        rls_enabled,
        policies,
        access_level
      };
    } catch (error) {
      console.error(`❌ Failed to get security info for ${tableName}:`, error);
      return {
        table_name: tableName,
        rls_enabled: false,
        policies: [],
        access_level: 'unrestricted'
      };
    }
  }

  // Get policies for a table
  private async getTablePolicies(tableName: string): Promise<PolicyInfo[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_table_policies', { table_name: tableName });

      if (error) {
        console.warn(`⚠️ Could not get policies for ${tableName}:`, error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.warn(`⚠️ Policy query error for ${tableName}:`, error);
      return [];
    }
  }

  // Test table access with current user
  async testTableAccess(tableName: string): Promise<{
    can_select: boolean;
    can_insert: boolean;
    can_update: boolean;
    can_delete: boolean;
    error_message?: string;
  }> {
    try {
      console.log(`🧪 Testing access to table: ${tableName}`);

      const results = {
        can_select: false,
        can_insert: false,
        can_update: false,
        can_delete: false,
        error_message: undefined as string | undefined
      };

      // Test SELECT
      try {
        const { error: selectError } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        results.can_select = !selectError;
      } catch (e) {
        results.error_message = `SELECT failed: ${e}`;
      }

      // Test INSERT (with dummy data)
      try {
        const { error: insertError } = await supabase
          .from(tableName)
          .insert({ test_field: 'test_value' })
          .select()
          .single();
        results.can_insert = !insertError;
      } catch (e) {
        // Expected to fail for most tables due to required fields
      }

      // Test UPDATE
      try {
        const { error: updateError } = await supabase
          .from(tableName)
          .update({ updated_at: new Date().toISOString() })
          .eq('id', 'non-existent-id');
        results.can_update = !updateError;
      } catch (e) {
        // Expected to fail if no matching records
      }

      // Test DELETE
      try {
        const { error: deleteError } = await supabase
          .from(tableName)
          .delete()
          .eq('id', 'non-existent-id');
        results.can_delete = !deleteError;
      } catch (e) {
        // Expected to fail if no matching records
      }

      console.log(`🧪 Access test results for ${tableName}:`, results);
      return results;
    } catch (error) {
      console.error(`❌ Access test error for ${tableName}:`, error);
      return {
        can_select: false,
        can_insert: false,
        can_update: false,
        can_delete: false,
        error_message: `Test failed: ${error}`
      };
    }
  }

  // Generate security report
  async generateSecurityReport(): Promise<{
    summary: {
      total_tables: number;
      unrestricted_tables: number;
      restricted_tables: number;
      user_specific_tables: number;
    };
    tables: TableSecurityInfo[];
    recommendations: string[];
  }> {
    try {
      console.log('📊 Generating comprehensive security report...');

      const tables = await this.checkTableSecurity();
      
      const summary = {
        total_tables: tables.length,
        unrestricted_tables: tables.filter(t => t.access_level === 'unrestricted').length,
        restricted_tables: tables.filter(t => t.access_level === 'restricted').length,
        user_specific_tables: tables.filter(t => t.access_level === 'user_specific').length
      };

      const recommendations: string[] = [];

      // Generate recommendations
      if (summary.unrestricted_tables > 2) {
        recommendations.push('Consider enabling RLS on unrestricted tables for better security');
      }

      if (summary.user_specific_tables === 0) {
        recommendations.push('Consider implementing user-specific policies for sensitive data');
      }

      const unprotectedTables = tables.filter(t => 
        t.access_level === 'unrestricted' && 
        !['spatial_ref_sys', 'geography_columns', 'geometry_columns'].includes(t.table_name)
      );

      if (unprotectedTables.length > 0) {
        recommendations.push(`Review security for: ${unprotectedTables.map(t => t.table_name).join(', ')}`);
      }

      console.log('📊 Security report generated successfully');
      return { summary, tables, recommendations };
    } catch (error) {
      console.error('❌ Security report generation error:', error);
      return {
        summary: { total_tables: 0, unrestricted_tables: 0, restricted_tables: 0, user_specific_tables: 0 },
        tables: [],
        recommendations: ['Failed to generate security report']
      };
    }
  }

  // Check current user permissions
  async getCurrentUserPermissions(): Promise<{
    user_id: string | null;
    email: string | null;
    role: string | null;
    permissions: string[];
  }> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        return {
          user_id: null,
          email: null,
          role: null,
          permissions: ['anonymous']
        };
      }

      // Get user role from database
      const { data: userRole, error: roleError } = await supabase
        .from('users')
        .select('role, permissions')
        .eq('id', user.id)
        .single();

      return {
        user_id: user.id,
        email: user.email || null,
        role: userRole?.role || 'user',
        permissions: userRole?.permissions || ['basic_access']
      };
    } catch (error) {
      console.error('❌ Failed to get user permissions:', error);
      return {
        user_id: null,
        email: null,
        role: null,
        permissions: ['error']
      };
    }
  }

  // Audit database access
  async auditDatabaseAccess(): Promise<void> {
    try {
      console.log('🔍 Starting database access audit...');

      const securityReport = await this.generateSecurityReport();
      const userPermissions = await this.getCurrentUserPermissions();

      console.log('🔒 SECURITY AUDIT REPORT');
      console.log('========================');
      console.log(`📊 Total Tables: ${securityReport.summary.total_tables}`);
      console.log(`🔓 Unrestricted: ${securityReport.summary.unrestricted_tables}`);
      console.log(`🔒 Restricted: ${securityReport.summary.restricted_tables}`);
      console.log(`👤 User-Specific: ${securityReport.summary.user_specific_tables}`);
      console.log('');
      console.log(`👤 Current User: ${userPermissions.email || 'Anonymous'}`);
      console.log(`🎭 Role: ${userPermissions.role || 'None'}`);
      console.log(`🔑 Permissions: ${userPermissions.permissions.join(', ')}`);
      console.log('');
      
      if (securityReport.recommendations.length > 0) {
        console.log('💡 RECOMMENDATIONS:');
        securityReport.recommendations.forEach((rec, index) => {
          console.log(`${index + 1}. ${rec}`);
        });
      } else {
        console.log('✅ No security recommendations at this time');
      }

      console.log('🔍 Database access audit completed');
    } catch (error) {
      console.error('❌ Database audit error:', error);
    }
  }
}

export default DatabaseSecurityService;
