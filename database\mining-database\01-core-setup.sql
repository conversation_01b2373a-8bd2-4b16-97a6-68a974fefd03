-- =====================================================
-- Mining Operations Database - Core Setup
-- =====================================================
-- File: 01-core-setup.sql
-- Description: Core database setup with extensions and basic types
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- CORE ENUMS AND TYPES
-- =====================================================

-- Status types for reports
CREATE TYPE report_status AS ENUM (
    'Draft',
    'Submitted', 
    'Approved',
    'Rejected'
);

-- Weather impact levels
CREATE TYPE weather_impact AS ENUM (
    'None',
    'Low',
    'Medium', 
    'High',
    'Critical'
);

-- Period types for targets and analysis
CREATE TYPE period_type AS ENUM (
    'Daily',
    'Weekly',
    'Monthly',
    'Quarterly',
    'Yearly'
);

-- User roles for the system
CREATE TYPE user_role AS ENUM (
    'Super Admin',
    'Site Manager',
    'Shift Supervisor',
    'Equipment Operator',
    'Safety Officer',
    'Observer'
);

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate report number
CREATE OR REPLACE FUNCTION generate_report_number(location_name TEXT, report_date DATE)
RETURNS TEXT AS $$
BEGIN
    RETURN UPPER(LEFT(location_name, 3)) || '-' || TO_CHAR(report_date, 'YYYYMMDD') || '-' || LPAD(EXTRACT(DOY FROM report_date)::TEXT, 3, '0');
END;
$$ LANGUAGE plpgsql;

-- Function to calculate working days in period
CREATE OR REPLACE FUNCTION calculate_working_days(start_date DATE, end_date DATE)
RETURNS INTEGER AS $$
DECLARE
    working_days INTEGER := 0;
    current_date DATE := start_date;
BEGIN
    WHILE current_date <= end_date LOOP
        -- Count all days as working days for mining operations (24/7)
        -- Exclude only major holidays if needed
        IF NOT EXISTS (
            SELECT 1 FROM production_calendar 
            WHERE calendar_date = current_date 
            AND is_holiday = true
        ) THEN
            working_days := working_days + 1;
        END IF;
        current_date := current_date + 1;
    END LOOP;
    
    RETURN working_days;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- MIGRATION TRACKING
-- =====================================================

-- Create migration tracking table
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(50) PRIMARY KEY,
    description TEXT,
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    applied_by VARCHAR(100) DEFAULT current_user,
    checksum VARCHAR(64)
);

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('001', 'Core setup with extensions and utility functions')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TYPE report_status IS 'Status values for daily mining reports';
COMMENT ON TYPE weather_impact IS 'Weather impact levels on mining operations';
COMMENT ON TYPE period_type IS 'Time period types for analysis and targets';
COMMENT ON TYPE user_role IS 'User roles in the mining operations system';

COMMENT ON FUNCTION update_updated_at_column() IS 'Trigger function to automatically update updated_at timestamp';
COMMENT ON FUNCTION generate_report_number(TEXT, DATE) IS 'Generate unique report number based on location and date';
COMMENT ON FUNCTION calculate_working_days(DATE, DATE) IS 'Calculate working days between two dates excluding holidays';

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Core setup completed successfully';
    RAISE NOTICE 'Extensions enabled: uuid-ossp, pg_stat_statements';
    RAISE NOTICE 'Enums created: report_status, weather_impact, period_type, user_role';
    RAISE NOTICE 'Utility functions created: update_updated_at_column, generate_report_number, calculate_working_days';
END $$;
