-- =====================================================
-- Mining Operations Database - Safety Management
-- =====================================================
-- File: 08-safety-management.sql
-- Description: Safety incidents, training, inspections, and compliance
-- Dependencies: 01-core-setup.sql, 06-user-management.sql, 07-equipment-management.sql
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- ADDITIONAL ENUMS FOR SAFETY
-- =====================================================

-- Incident types
CREATE TYPE incident_type AS ENUM (
    'Near Miss',
    'First Aid',
    'Medical Treatment',
    'Lost Time Injury',
    'Property Damage',
    'Environmental',
    'Security',
    'Fire',
    'Fatality'
);

-- Severity levels
CREATE TYPE severity_level AS ENUM (
    'Low',
    'Medium',
    'High',
    'Critical'
);

-- Incident status
CREATE TYPE incident_status AS ENUM (
    'Reported',
    'Under Investigation',
    'Investigation Complete',
    'Closed',
    'Cancelled'
);

-- Training types
CREATE TYPE training_type AS ENUM (
    'Safety Induction',
    'Equipment Training',
    'Emergency Response',
    'First Aid',
    'Fire Safety',
    'Environmental',
    'Hazmat',
    'Refresher',
    'Certification',
    'Greencard',
    'IUT Training',  -- Inspeksi Umum Terencana
    'OTT Training'   -- Observasi Tugas Terencana
);

-- Certification types
CREATE TYPE certification_type AS ENUM (
    'Greencard',
    'IUT',  -- Inspeksi Umum Terencana
    'OTT',  -- Observasi Tugas Terencana
    'First Aid',
    'Fire Safety',
    'Equipment Operator',
    'Safety Officer',
    'Mine Rescue',
    'Blasting',
    'Environmental'
);

-- Document status
CREATE TYPE document_status AS ENUM (
    'Valid',
    'Expired',
    'Expiring Soon',
    'Suspended',
    'Revoked',
    'Pending Renewal'
);

-- =====================================================
-- SAFETY INCIDENTS TABLE
-- =====================================================
CREATE TABLE safety_incidents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Incident Identification
    incident_number VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    
    -- Incident Classification
    incident_type incident_type NOT NULL,
    severity severity_level NOT NULL,
    category VARCHAR(100), -- 'Workplace Injury', 'Equipment Failure', 'Environmental'
    
    -- Location and Context
    location VARCHAR(200),
    exact_location TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    
    -- Timing
    occurred_at TIMESTAMPTZ NOT NULL,
    discovered_at TIMESTAMPTZ,
    reported_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- People Involved
    people_involved INTEGER DEFAULT 0,
    injuries_count INTEGER DEFAULT 0,
    fatalities_count INTEGER DEFAULT 0,
    witnesses_count INTEGER DEFAULT 0,
    
    -- Equipment and Assets
    equipment_involved UUID REFERENCES equipment(id),
    assets_damaged TEXT,
    estimated_damage_cost DECIMAL(12, 2),
    
    -- Environmental Conditions
    weather_conditions VARCHAR(100),
    temperature DECIMAL(5, 2),
    visibility VARCHAR(50), -- 'Good', 'Poor', 'Limited'
    lighting_conditions VARCHAR(50), -- 'Good', 'Poor', 'Dark'
    
    -- Incident Details
    immediate_cause TEXT,
    root_cause TEXT,
    contributing_factors TEXT,
    immediate_actions TEXT,
    
    -- Investigation
    investigation_required BOOLEAN DEFAULT true,
    investigation_started_at TIMESTAMPTZ,
    investigation_completed_at TIMESTAMPTZ,
    investigation_findings TEXT,
    
    -- Corrective Actions
    corrective_actions TEXT,
    preventive_actions TEXT,
    action_plan TEXT,
    target_completion_date DATE,
    actual_completion_date DATE,
    
    -- Status and Workflow
    status incident_status DEFAULT 'Reported',
    priority priority_level DEFAULT 'Medium',
    
    -- Personnel Assignment
    reported_by UUID NOT NULL REFERENCES user_profiles(id),
    assigned_investigator UUID REFERENCES user_profiles(id),
    safety_officer_id UUID REFERENCES user_profiles(id),
    supervisor_id UUID REFERENCES user_profiles(id),
    
    -- Approval and Closure
    investigation_approved_by UUID REFERENCES user_profiles(id),
    investigation_approved_at TIMESTAMPTZ,
    closed_by UUID REFERENCES user_profiles(id),
    closed_at TIMESTAMPTZ,
    closure_notes TEXT,
    
    -- Documentation
    photos TEXT[], -- Array of photo URLs
    documents TEXT[], -- Array of document URLs
    witness_statements JSONB, -- Array of witness statement details
    
    -- Regulatory and Compliance
    regulatory_notification_required BOOLEAN DEFAULT false,
    regulatory_notified_at TIMESTAMPTZ,
    regulatory_reference VARCHAR(100),
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    updated_by UUID REFERENCES user_profiles(id),
    
    -- Constraints
    CHECK (people_involved >= 0),
    CHECK (injuries_count >= 0),
    CHECK (fatalities_count >= 0),
    CHECK (witnesses_count >= 0),
    CHECK (injuries_count <= people_involved),
    CHECK (fatalities_count <= people_involved),
    CHECK (estimated_damage_cost >= 0),
    CHECK (discovered_at IS NULL OR discovered_at >= occurred_at),
    CHECK (reported_at >= occurred_at),
    CHECK (investigation_completed_at IS NULL OR investigation_completed_at >= investigation_started_at)
);

-- =====================================================
-- SAFETY INSPECTIONS TABLE
-- =====================================================
CREATE TABLE safety_inspections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Inspection Details
    inspection_number VARCHAR(50) UNIQUE,
    inspection_name VARCHAR(200) NOT NULL,
    inspection_type VARCHAR(100) NOT NULL, -- 'Routine', 'Scheduled', 'Surprise', 'Follow-up'
    
    -- Scope and Location
    location VARCHAR(200),
    equipment_id UUID REFERENCES equipment(id), -- If equipment-specific
    
    -- Timing
    scheduled_date DATE,
    actual_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    duration_hours DECIMAL(4, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN start_time IS NOT NULL AND end_time IS NOT NULL THEN
                EXTRACT(EPOCH FROM (end_time - start_time)) / 3600
            ELSE NULL
        END
    ) STORED,
    
    -- Inspector Information
    lead_inspector_id UUID NOT NULL REFERENCES user_profiles(id),
    inspector_team UUID[], -- Array of inspector user IDs
    
    -- Inspection Checklist
    checklist_template VARCHAR(100), -- Reference to checklist template
    checklist_items JSONB NOT NULL, -- Inspection checklist with results
    
    -- Results
    total_items INTEGER DEFAULT 0,
    compliant_items INTEGER DEFAULT 0,
    non_compliant_items INTEGER DEFAULT 0,
    critical_issues INTEGER DEFAULT 0,
    compliance_percentage DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN total_items > 0 THEN (compliant_items::DECIMAL / total_items * 100)
            ELSE 0 
        END
    ) STORED,
    
    -- Overall Assessment
    overall_rating VARCHAR(50), -- 'Excellent', 'Good', 'Satisfactory', 'Poor', 'Critical'
    overall_score DECIMAL(5, 2), -- Score out of 100
    passed BOOLEAN,
    
    -- Issues and Recommendations
    issues_identified TEXT,
    recommendations TEXT,
    immediate_actions_required TEXT,
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    
    -- Documentation
    photos TEXT[], -- Array of photo URLs
    documents TEXT[], -- Array of document URLs
    notes TEXT,
    
    -- Status and Approval
    status VARCHAR(50) DEFAULT 'Completed', -- 'Scheduled', 'In Progress', 'Completed', 'Cancelled'
    approved_by UUID REFERENCES user_profiles(id),
    approved_at TIMESTAMPTZ,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),
    
    -- Constraints
    CHECK (total_items >= 0),
    CHECK (compliant_items >= 0),
    CHECK (non_compliant_items >= 0),
    CHECK (critical_issues >= 0),
    CHECK (compliant_items + non_compliant_items <= total_items),
    CHECK (critical_issues <= non_compliant_items),
    CHECK (overall_score IS NULL OR overall_score BETWEEN 0 AND 100),
    CHECK (end_time IS NULL OR end_time > start_time)
);

-- =====================================================
-- SAFETY TRAINING TABLE
-- =====================================================
CREATE TABLE safety_training (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Training Details
    training_name VARCHAR(200) NOT NULL,
    training_code VARCHAR(50),
    training_type training_type NOT NULL,
    description TEXT,
    
    -- Training Content
    duration_hours DECIMAL(4, 2) NOT NULL,
    training_materials TEXT[], -- Array of training material URLs
    certification_required BOOLEAN DEFAULT false,
    certification_validity_months INTEGER, -- Certification validity period
    
    -- Training Schedule
    scheduled_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    location VARCHAR(200),
    training_room VARCHAR(100),
    
    -- Trainer Information
    trainer_id UUID REFERENCES user_profiles(id),
    external_trainer_name VARCHAR(200),
    external_trainer_company VARCHAR(200),
    
    -- Attendees
    max_attendees INTEGER,
    registered_attendees INTEGER DEFAULT 0,
    actual_attendees INTEGER DEFAULT 0,
    
    -- Training Status
    status VARCHAR(50) DEFAULT 'Scheduled', -- 'Scheduled', 'In Progress', 'Completed', 'Cancelled'
    completion_rate DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN registered_attendees > 0 THEN (actual_attendees::DECIMAL / registered_attendees * 100)
            ELSE 0 
        END
    ) STORED,
    
    -- Assessment
    assessment_required BOOLEAN DEFAULT false,
    passing_score DECIMAL(5, 2) DEFAULT 70,
    average_score DECIMAL(5, 2),
    pass_rate DECIMAL(5, 2),
    
    -- Documentation
    training_notes TEXT,
    feedback_summary TEXT,
    photos TEXT[],
    certificates_issued INTEGER DEFAULT 0,
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),
    
    -- Constraints
    CHECK (duration_hours > 0),
    CHECK (max_attendees > 0),
    CHECK (registered_attendees >= 0),
    CHECK (actual_attendees >= 0),
    CHECK (actual_attendees <= registered_attendees),
    CHECK (passing_score BETWEEN 0 AND 100),
    CHECK (average_score IS NULL OR average_score BETWEEN 0 AND 100),
    CHECK (pass_rate IS NULL OR pass_rate BETWEEN 0 AND 100),
    CHECK (certificates_issued >= 0),
    CHECK (certificates_issued <= actual_attendees)
);

-- =====================================================
-- SAFETY TRAINING ATTENDEES TABLE
-- =====================================================
CREATE TABLE safety_training_attendees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    training_id UUID NOT NULL REFERENCES safety_training(id) ON DELETE CASCADE,
    attendee_id UUID NOT NULL REFERENCES user_profiles(id),
    
    -- Attendance Details
    registered_at TIMESTAMPTZ DEFAULT NOW(),
    attended BOOLEAN DEFAULT false,
    attendance_time TIMESTAMPTZ,
    completion_time TIMESTAMPTZ,
    
    -- Assessment Results
    assessment_score DECIMAL(5, 2),
    passed BOOLEAN,
    attempts INTEGER DEFAULT 1,
    
    -- Certification
    certificate_issued BOOLEAN DEFAULT false,
    certificate_number VARCHAR(100),
    certificate_issued_at TIMESTAMPTZ,
    certificate_expires_at TIMESTAMPTZ,
    certificate_url TEXT,
    
    -- Feedback
    feedback_rating INTEGER, -- 1-5 rating
    feedback_comments TEXT,
    
    -- Status
    status VARCHAR(50) DEFAULT 'Registered', -- 'Registered', 'Attended', 'Completed', 'Failed', 'Cancelled'
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(training_id, attendee_id),
    CHECK (assessment_score IS NULL OR assessment_score BETWEEN 0 AND 100),
    CHECK (attempts > 0),
    CHECK (feedback_rating IS NULL OR feedback_rating BETWEEN 1 AND 5),
    CHECK (completion_time IS NULL OR completion_time >= attendance_time)
);

-- =====================================================
-- GREENCARD, IUT, OTT MANAGEMENT TABLE
-- =====================================================
CREATE TABLE mining_certifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Certificate Holder
    holder_id UUID NOT NULL REFERENCES user_profiles(id),

    -- Certificate Details
    certificate_type certification_type NOT NULL,
    certificate_number VARCHAR(100) UNIQUE NOT NULL,
    certificate_name VARCHAR(200) NOT NULL,

    -- Issuing Authority
    issuing_authority VARCHAR(200) NOT NULL,
    issuing_authority_code VARCHAR(50),
    issuer_contact_info JSONB,

    -- Certificate Validity
    issued_date DATE NOT NULL,
    valid_from DATE NOT NULL,
    valid_until DATE NOT NULL,
    validity_period_months INTEGER,

    -- Status and Conditions
    status document_status DEFAULT 'Valid',
    conditions_restrictions TEXT,
    scope_of_work TEXT, -- What work is authorized
    location_restrictions TEXT[], -- Specific locations where valid

    -- Renewal Information
    renewal_required BOOLEAN DEFAULT true,
    renewal_notice_days INTEGER DEFAULT 30,
    renewal_started_at TIMESTAMPTZ,
    renewal_completed_at TIMESTAMPTZ,

    -- Training Requirements
    required_training_hours DECIMAL(6, 2),
    completed_training_hours DECIMAL(6, 2) DEFAULT 0,
    last_training_date DATE,
    next_training_due DATE,

    -- Medical Requirements (for certain certificates)
    medical_exam_required BOOLEAN DEFAULT false,
    last_medical_exam_date DATE,
    next_medical_exam_due DATE,
    medical_restrictions TEXT,

    -- Competency Assessment
    competency_assessment_required BOOLEAN DEFAULT false,
    last_assessment_date DATE,
    next_assessment_due DATE,
    assessment_score DECIMAL(5, 2),
    assessor_id UUID REFERENCES user_profiles(id),

    -- Specific Fields for Different Certificate Types
    -- Greencard specific
    greencard_level VARCHAR(50), -- 'Basic', 'Advanced', 'Supervisor'
    greencard_categories TEXT[], -- Array of authorized categories

    -- IUT specific (Inspeksi Umum Terencana - Planned General Inspection)
    iut_inspection_scope TEXT[], -- Areas/equipment to inspect
    iut_inspection_frequency VARCHAR(50), -- 'Daily', 'Weekly', 'Monthly'
    iut_checklist_template VARCHAR(200), -- Reference to checklist
    iut_competency_level VARCHAR(50), -- 'Basic', 'Advanced', 'Expert'

    -- OTT specific (Observasi Tugas Terencana - Planned Task Observation)
    ott_observation_areas TEXT[], -- Work areas to observe
    ott_behavioral_focus TEXT[], -- Safety behaviors to observe
    ott_observation_frequency VARCHAR(50), -- 'Daily', 'Weekly'
    ott_feedback_authority BOOLEAN DEFAULT false, -- Can provide feedback

    -- Documentation
    certificate_file_url TEXT,
    supporting_documents TEXT[], -- Array of document URLs
    photos TEXT[], -- Array of photo URLs

    -- Verification
    verified_by UUID REFERENCES user_profiles(id),
    verified_at TIMESTAMPTZ,
    verification_notes TEXT,

    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),

    -- Constraints
    CHECK (valid_until > valid_from),
    CHECK (issued_date <= valid_from),
    CHECK (completed_training_hours >= 0),
    CHECK (required_training_hours >= 0),
    CHECK (assessment_score IS NULL OR assessment_score BETWEEN 0 AND 100),
    -- No specific constraints for IUT/OTT fields as they are now text arrays and booleans
);

-- =====================================================
-- CERTIFICATE RENEWAL TRACKING TABLE
-- =====================================================
CREATE TABLE certificate_renewals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    certificate_id UUID NOT NULL REFERENCES mining_certifications(id) ON DELETE CASCADE,

    -- Renewal Details
    renewal_type VARCHAR(50) NOT NULL, -- 'Regular', 'Emergency', 'Extension'
    renewal_reason TEXT,

    -- Timeline
    renewal_initiated_date DATE NOT NULL,
    renewal_due_date DATE NOT NULL,
    renewal_completed_date DATE,

    -- Requirements
    training_required BOOLEAN DEFAULT false,
    training_completed BOOLEAN DEFAULT false,
    training_completion_date DATE,

    medical_exam_required BOOLEAN DEFAULT false,
    medical_exam_completed BOOLEAN DEFAULT false,
    medical_exam_date DATE,

    competency_test_required BOOLEAN DEFAULT false,
    competency_test_completed BOOLEAN DEFAULT false,
    competency_test_date DATE,
    competency_test_score DECIMAL(5, 2),

    -- Documentation
    application_submitted BOOLEAN DEFAULT false,
    application_submitted_date DATE,
    supporting_documents_complete BOOLEAN DEFAULT false,

    -- Approval Process
    reviewed_by UUID REFERENCES user_profiles(id),
    reviewed_at TIMESTAMPTZ,
    approved_by UUID REFERENCES user_profiles(id),
    approved_at TIMESTAMPTZ,

    -- Status
    status VARCHAR(50) DEFAULT 'Initiated', -- 'Initiated', 'In Progress', 'Completed', 'Rejected', 'Expired'
    rejection_reason TEXT,

    -- Costs
    renewal_fee DECIMAL(10, 2),
    training_cost DECIMAL(10, 2),
    medical_exam_cost DECIMAL(10, 2),
    total_cost DECIMAL(10, 2) GENERATED ALWAYS AS (
        COALESCE(renewal_fee, 0) + COALESCE(training_cost, 0) + COALESCE(medical_exam_cost, 0)
    ) STORED,

    -- Notes
    notes TEXT,

    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),

    -- Constraints
    CHECK (renewal_completed_date IS NULL OR renewal_completed_date >= renewal_initiated_date),
    CHECK (competency_test_score IS NULL OR competency_test_score BETWEEN 0 AND 100),
    CHECK (renewal_fee >= 0),
    CHECK (training_cost >= 0),
    CHECK (medical_exam_cost >= 0)
);

-- =====================================================
-- SAFETY ACTIVITY PLANS TABLE
-- =====================================================
CREATE TABLE safety_activity_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Plan Period
    location VARCHAR(200) NOT NULL,
    plan_year INTEGER NOT NULL,
    plan_month INTEGER NOT NULL,
    plan_week INTEGER, -- Optional for weekly plans

    -- Plan Details
    plan_name VARCHAR(200) NOT NULL,
    plan_description TEXT,
    plan_type VARCHAR(50) NOT NULL, -- 'Monthly', 'Weekly', 'Quarterly'

    -- Greencard Plan Targets by Role
    greencard_group_leader_plan INTEGER DEFAULT 0,
    greencard_supervisor_plan INTEGER DEFAULT 0,
    greencard_department_head_plan INTEGER DEFAULT 0,
    greencard_total_plan INTEGER GENERATED ALWAYS AS (
        greencard_group_leader_plan + greencard_supervisor_plan + greencard_department_head_plan
    ) STORED,

    -- IUT Plan Targets by Role
    iut_group_leader_plan INTEGER DEFAULT 0,
    iut_supervisor_plan INTEGER DEFAULT 0,
    iut_department_head_plan INTEGER DEFAULT 0,
    iut_total_plan INTEGER GENERATED ALWAYS AS (
        iut_group_leader_plan + iut_supervisor_plan + iut_department_head_plan
    ) STORED,

    -- OTT Plan Targets by Role
    ott_group_leader_plan INTEGER DEFAULT 0,
    ott_supervisor_plan INTEGER DEFAULT 0,
    ott_department_head_plan INTEGER DEFAULT 0,
    ott_total_plan INTEGER GENERATED ALWAYS AS (
        ott_group_leader_plan + ott_supervisor_plan + ott_department_head_plan
    ) STORED,

    -- Plan Status
    is_active BOOLEAN DEFAULT true,
    approved_by UUID REFERENCES user_profiles(id),
    approved_at TIMESTAMPTZ,

    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES user_profiles(id),
    updated_by UUID REFERENCES user_profiles(id),

    -- Constraints
    UNIQUE(location, plan_year, plan_month, plan_type),
    CHECK (plan_year >= 2020 AND plan_year <= 2050),
    CHECK (plan_month >= 1 AND plan_month <= 12),
    CHECK (plan_week IS NULL OR (plan_week >= 1 AND plan_week <= 53)),
    CHECK (greencard_group_leader_plan >= 0),
    CHECK (greencard_supervisor_plan >= 0),
    CHECK (greencard_department_head_plan >= 0),
    CHECK (iut_group_leader_plan >= 0),
    CHECK (iut_supervisor_plan >= 0),
    CHECK (iut_department_head_plan >= 0),
    CHECK (ott_group_leader_plan >= 0),
    CHECK (ott_supervisor_plan >= 0),
    CHECK (ott_department_head_plan >= 0)
);

-- =====================================================
-- SAFETY ACTIVITY ACTUALS TABLE
-- =====================================================
CREATE TABLE safety_activity_actuals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Reference to Plan
    plan_id UUID NOT NULL REFERENCES safety_activity_plans(id) ON DELETE CASCADE,

    -- Actual Period (can be daily tracking)
    actual_date DATE NOT NULL,
    location VARCHAR(200) NOT NULL,

    -- Greencard Actual by Role
    greencard_group_leader_actual INTEGER DEFAULT 0,
    greencard_supervisor_actual INTEGER DEFAULT 0,
    greencard_department_head_actual INTEGER DEFAULT 0,
    greencard_total_actual INTEGER GENERATED ALWAYS AS (
        greencard_group_leader_actual + greencard_supervisor_actual + greencard_department_head_actual
    ) STORED,

    -- IUT Actual by Role
    iut_group_leader_actual INTEGER DEFAULT 0,
    iut_supervisor_actual INTEGER DEFAULT 0,
    iut_department_head_actual INTEGER DEFAULT 0,
    iut_total_actual INTEGER GENERATED ALWAYS AS (
        iut_group_leader_actual + iut_supervisor_actual + iut_department_head_actual
    ) STORED,

    -- OTT Actual by Role
    ott_group_leader_actual INTEGER DEFAULT 0,
    ott_supervisor_actual INTEGER DEFAULT 0,
    ott_department_head_actual INTEGER DEFAULT 0,
    ott_total_actual INTEGER GENERATED ALWAYS AS (
        ott_group_leader_actual + ott_supervisor_actual + ott_department_head_actual
    ) STORED,

    -- Performance Metrics
    notes TEXT,
    issues_encountered TEXT,

    -- Data Entry
    recorded_by UUID NOT NULL REFERENCES user_profiles(id),

    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Constraints
    UNIQUE(plan_id, actual_date),
    CHECK (greencard_group_leader_actual >= 0),
    CHECK (greencard_supervisor_actual >= 0),
    CHECK (greencard_department_head_actual >= 0),
    CHECK (iut_group_leader_actual >= 0),
    CHECK (iut_supervisor_actual >= 0),
    CHECK (iut_department_head_actual >= 0),
    CHECK (ott_group_leader_actual >= 0),
    CHECK (ott_supervisor_actual >= 0),
    CHECK (ott_department_head_actual >= 0)
);

-- =====================================================
-- SAFETY METRICS TABLE
-- =====================================================
CREATE TABLE safety_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Metric Period
    location VARCHAR(200) NOT NULL,
    metric_date DATE NOT NULL,
    
    -- Incident Metrics
    total_incidents INTEGER DEFAULT 0,
    near_miss_count INTEGER DEFAULT 0,
    first_aid_count INTEGER DEFAULT 0,
    medical_treatment_count INTEGER DEFAULT 0,
    lost_time_injury_count INTEGER DEFAULT 0,
    fatality_count INTEGER DEFAULT 0,
    
    -- Safety Performance
    days_without_incident INTEGER DEFAULT 0,
    safety_score DECIMAL(5, 2) DEFAULT 100, -- Overall safety score (0-100)
    
    -- Training Metrics
    training_sessions_conducted INTEGER DEFAULT 0,
    personnel_trained INTEGER DEFAULT 0,
    training_compliance_rate DECIMAL(5, 2) DEFAULT 0,
    
    -- Inspection Metrics
    inspections_completed INTEGER DEFAULT 0,
    inspections_passed INTEGER DEFAULT 0,
    critical_issues_found INTEGER DEFAULT 0,
    issues_resolved INTEGER DEFAULT 0,
    
    -- Personnel Metrics
    total_personnel INTEGER DEFAULT 0,
    personnel_with_valid_training INTEGER DEFAULT 0,
    training_compliance_percentage DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN total_personnel > 0 THEN (personnel_with_valid_training::DECIMAL / total_personnel * 100)
            ELSE 0 
        END
    ) STORED,
    
    -- Environmental Metrics
    environmental_incidents INTEGER DEFAULT 0,
    air_quality_violations INTEGER DEFAULT 0,
    noise_violations INTEGER DEFAULT 0,

    -- Certification Metrics
    total_greencard_holders INTEGER DEFAULT 0,
    greencard_expiring_30_days INTEGER DEFAULT 0,
    total_iut_certificates INTEGER DEFAULT 0,
    iut_expiring_30_days INTEGER DEFAULT 0,
    total_ott_certificates INTEGER DEFAULT 0,
    ott_expiring_30_days INTEGER DEFAULT 0,
    certification_compliance_rate DECIMAL(5, 2) DEFAULT 100,
    
    -- Calculated Fields
    calculated_at TIMESTAMPTZ DEFAULT NOW(),
    calculated_by UUID REFERENCES user_profiles(id),
    
    -- Audit Fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(location, metric_date),
    CHECK (total_incidents >= 0),
    CHECK (near_miss_count >= 0),
    CHECK (first_aid_count >= 0),
    CHECK (medical_treatment_count >= 0),
    CHECK (lost_time_injury_count >= 0),
    CHECK (fatality_count >= 0),
    CHECK (days_without_incident >= 0),
    CHECK (safety_score BETWEEN 0 AND 100),
    CHECK (training_sessions_conducted >= 0),
    CHECK (personnel_trained >= 0),
    CHECK (inspections_completed >= 0),
    CHECK (inspections_passed <= inspections_completed),
    CHECK (total_personnel >= 0),
    CHECK (personnel_with_valid_training <= total_personnel)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Safety Incidents Indexes
CREATE INDEX idx_safety_incidents_number ON safety_incidents(incident_number);
CREATE INDEX idx_safety_incidents_location ON safety_incidents(location);
CREATE INDEX idx_safety_incidents_type ON safety_incidents(incident_type);
CREATE INDEX idx_safety_incidents_severity ON safety_incidents(severity);
CREATE INDEX idx_safety_incidents_status ON safety_incidents(status);
CREATE INDEX idx_safety_incidents_occurred ON safety_incidents(occurred_at);
CREATE INDEX idx_safety_incidents_reported_by ON safety_incidents(reported_by);
CREATE INDEX idx_safety_incidents_investigator ON safety_incidents(assigned_investigator);
CREATE INDEX idx_safety_incidents_equipment ON safety_incidents(equipment_involved);

-- Safety Inspections Indexes
CREATE INDEX idx_safety_inspections_location ON safety_inspections(location);
CREATE INDEX idx_safety_inspections_date ON safety_inspections(actual_date);
CREATE INDEX idx_safety_inspections_inspector ON safety_inspections(lead_inspector_id);
CREATE INDEX idx_safety_inspections_type ON safety_inspections(inspection_type);
CREATE INDEX idx_safety_inspections_status ON safety_inspections(status);
CREATE INDEX idx_safety_inspections_rating ON safety_inspections(overall_rating);

-- Safety Training Indexes
CREATE INDEX idx_safety_training_date ON safety_training(scheduled_date);
CREATE INDEX idx_safety_training_type ON safety_training(training_type);
CREATE INDEX idx_safety_training_trainer ON safety_training(trainer_id);
CREATE INDEX idx_safety_training_location ON safety_training(location);
CREATE INDEX idx_safety_training_status ON safety_training(status);

-- Training Attendees Indexes
CREATE INDEX idx_training_attendees_training ON safety_training_attendees(training_id);
CREATE INDEX idx_training_attendees_attendee ON safety_training_attendees(attendee_id);
CREATE INDEX idx_training_attendees_status ON safety_training_attendees(status);
CREATE INDEX idx_training_attendees_certificate ON safety_training_attendees(certificate_expires_at);

-- Safety Activity Plans Indexes
CREATE INDEX idx_safety_activity_plans_location ON safety_activity_plans(location);
CREATE INDEX idx_safety_activity_plans_year_month ON safety_activity_plans(plan_year, plan_month);
CREATE INDEX idx_safety_activity_plans_location_year_month ON safety_activity_plans(location, plan_year, plan_month);
CREATE INDEX idx_safety_activity_plans_type ON safety_activity_plans(plan_type);
CREATE INDEX idx_safety_activity_plans_active ON safety_activity_plans(is_active);

-- Safety Activity Actuals Indexes
CREATE INDEX idx_safety_activity_actuals_plan ON safety_activity_actuals(plan_id);
CREATE INDEX idx_safety_activity_actuals_date ON safety_activity_actuals(actual_date);
CREATE INDEX idx_safety_activity_actuals_location ON safety_activity_actuals(location);
CREATE INDEX idx_safety_activity_actuals_plan_date ON safety_activity_actuals(plan_id, actual_date);

-- Safety Metrics Indexes
CREATE INDEX idx_safety_metrics_location ON safety_metrics(location);
CREATE INDEX idx_safety_metrics_date ON safety_metrics(metric_date);
CREATE INDEX idx_safety_metrics_location_date ON safety_metrics(location, metric_date);

-- Mining Certifications Indexes
CREATE INDEX idx_mining_certifications_holder ON mining_certifications(holder_id);
CREATE INDEX idx_mining_certifications_type ON mining_certifications(certificate_type);
CREATE INDEX idx_mining_certifications_number ON mining_certifications(certificate_number);
CREATE INDEX idx_mining_certifications_status ON mining_certifications(status);
CREATE INDEX idx_mining_certifications_validity ON mining_certifications(valid_until);
CREATE INDEX idx_mining_certifications_expiring ON mining_certifications(valid_until)
WHERE valid_until BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days';
CREATE INDEX idx_mining_certifications_greencard ON mining_certifications(certificate_type, greencard_level)
WHERE certificate_type = 'Greencard';
CREATE INDEX idx_mining_certifications_iut ON mining_certifications(certificate_type, iut_license_type)
WHERE certificate_type = 'IUT';
CREATE INDEX idx_mining_certifications_ott ON mining_certifications(certificate_type, ott_operation_type)
WHERE certificate_type = 'OTT';

-- Certificate Renewals Indexes
CREATE INDEX idx_certificate_renewals_certificate ON certificate_renewals(certificate_id);
CREATE INDEX idx_certificate_renewals_due_date ON certificate_renewals(renewal_due_date);
CREATE INDEX idx_certificate_renewals_status ON certificate_renewals(status);
CREATE INDEX idx_certificate_renewals_pending ON certificate_renewals(status, renewal_due_date)
WHERE status IN ('Initiated', 'In Progress');

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update updated_at timestamp
CREATE TRIGGER update_safety_incidents_updated_at 
    BEFORE UPDATE ON safety_incidents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_inspections_updated_at 
    BEFORE UPDATE ON safety_inspections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_training_updated_at 
    BEFORE UPDATE ON safety_training 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_training_attendees_updated_at 
    BEFORE UPDATE ON safety_training_attendees 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_activity_plans_updated_at
    BEFORE UPDATE ON safety_activity_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_activity_actuals_updated_at
    BEFORE UPDATE ON safety_activity_actuals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_safety_metrics_updated_at
    BEFORE UPDATE ON safety_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mining_certifications_updated_at
    BEFORE UPDATE ON mining_certifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_certificate_renewals_updated_at
    BEFORE UPDATE ON certificate_renewals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- UTILITY FUNCTIONS FOR CERTIFICATIONS
-- =====================================================

-- Function to check certificate expiry status
CREATE OR REPLACE FUNCTION get_certificate_status(cert_id UUID)
RETURNS document_status AS $$
DECLARE
    cert_record mining_certifications%ROWTYPE;
    days_to_expiry INTEGER;
BEGIN
    SELECT * INTO cert_record FROM mining_certifications WHERE id = cert_id;

    IF NOT FOUND THEN
        RETURN 'Expired'::document_status;
    END IF;

    days_to_expiry := cert_record.valid_until - CURRENT_DATE;

    IF days_to_expiry < 0 THEN
        RETURN 'Expired'::document_status;
    ELSIF days_to_expiry <= 30 THEN
        RETURN 'Expiring Soon'::document_status;
    ELSE
        RETURN 'Valid'::document_status;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get certificates expiring soon
CREATE OR REPLACE FUNCTION get_expiring_certificates(days_ahead INTEGER DEFAULT 30)
RETURNS TABLE (
    certificate_id UUID,
    holder_name TEXT,
    certificate_type certification_type,
    certificate_number VARCHAR(100),
    valid_until DATE,
    days_remaining INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        mc.id,
        up.full_name,
        mc.certificate_type,
        mc.certificate_number,
        mc.valid_until,
        (mc.valid_until - CURRENT_DATE)::INTEGER
    FROM mining_certifications mc
    JOIN user_profiles up ON mc.holder_id = up.id
    WHERE mc.valid_until BETWEEN CURRENT_DATE AND CURRENT_DATE + days_ahead
    AND mc.status = 'Valid'
    ORDER BY mc.valid_until ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to update certificate status based on expiry
CREATE OR REPLACE FUNCTION update_certificate_statuses()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
BEGIN
    -- Update expired certificates
    UPDATE mining_certifications
    SET status = 'Expired'
    WHERE valid_until < CURRENT_DATE
    AND status != 'Expired';

    GET DIAGNOSTICS updated_count = ROW_COUNT;

    -- Update expiring soon certificates
    UPDATE mining_certifications
    SET status = 'Expiring Soon'
    WHERE valid_until BETWEEN CURRENT_DATE AND CURRENT_DATE + 30
    AND status = 'Valid';

    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- Function to create default safety activity plans
CREATE OR REPLACE FUNCTION create_default_safety_plans(
    location_param VARCHAR(200),
    year_param INTEGER,
    month_param INTEGER DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    plans_created INTEGER := 0;
    current_month INTEGER;
    month_start INTEGER;
    month_end INTEGER;
BEGIN
    -- If month not specified, create for all months
    IF month_param IS NULL THEN
        month_start := 1;
        month_end := 12;
    ELSE
        month_start := month_param;
        month_end := month_param;
    END IF;

    -- Create monthly plans
    FOR current_month IN month_start..month_end LOOP
        INSERT INTO safety_activity_plans (
            location, plan_year, plan_month, plan_name, plan_type,
            -- Default Greencard targets by role
            greencard_group_leader_plan, greencard_supervisor_plan, greencard_department_head_plan,
            -- Default IUT targets by role
            iut_group_leader_plan, iut_supervisor_plan, iut_department_head_plan,
            -- Default OTT targets by role
            ott_group_leader_plan, ott_supervisor_plan, ott_department_head_plan,
            created_by
        ) VALUES (
            location_param, year_param, current_month,
            format('Safety Activities Plan - %s %s/%s', location_param, current_month, year_param),
            'Monthly',
            -- Greencard Plan: Group Leader=8, Supervisor=6, Department Head=3
            8, 6, 3,
            -- IUT Plan: Group Leader=6, Supervisor=3, Department Head=2
            6, 3, 2,
            -- OTT Plan: Group Leader=6, Supervisor=3, Department Head=2
            6, 3, 2,
            (SELECT id FROM user_profiles WHERE role = 'Super Admin' LIMIT 1)
        ) ON CONFLICT (location, plan_year, plan_month, plan_type) DO NOTHING;

        plans_created := plans_created + 1;
    END LOOP;

    RETURN plans_created;
END;
$$ LANGUAGE plpgsql;

-- Function to record daily safety activities
CREATE OR REPLACE FUNCTION record_daily_safety_activities(
    location_param VARCHAR(200),
    date_param DATE,
    greencard_gl INTEGER DEFAULT 0,
    greencard_sv INTEGER DEFAULT 0,
    greencard_dh INTEGER DEFAULT 0,
    iut_gl INTEGER DEFAULT 0,
    iut_sv INTEGER DEFAULT 0,
    iut_dh INTEGER DEFAULT 0,
    ott_gl INTEGER DEFAULT 0,
    ott_sv INTEGER DEFAULT 0,
    ott_dh INTEGER DEFAULT 0,
    notes_param TEXT DEFAULT NULL,
    recorded_by_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    plan_record safety_activity_plans%ROWTYPE;
    recorder_id UUID;
BEGIN
    -- Get the plan for this month
    SELECT * INTO plan_record
    FROM safety_activity_plans
    WHERE location = location_param
    AND plan_year = EXTRACT(YEAR FROM date_param)
    AND plan_month = EXTRACT(MONTH FROM date_param)
    AND plan_type = 'Monthly'
    AND is_active = true
    LIMIT 1;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'No active monthly plan found for location % in %/%',
            location_param, EXTRACT(MONTH FROM date_param), EXTRACT(YEAR FROM date_param);
    END IF;

    -- Set recorder ID
    IF recorded_by_id IS NULL THEN
        SELECT id INTO recorder_id FROM user_profiles WHERE role = 'Safety Officer' LIMIT 1;
    ELSE
        recorder_id := recorded_by_id;
    END IF;

    -- Insert or update daily actuals
    INSERT INTO safety_activity_actuals (
        plan_id, actual_date, location,
        greencard_group_leader_actual, greencard_supervisor_actual, greencard_department_head_actual,
        iut_group_leader_actual, iut_supervisor_actual, iut_department_head_actual,
        ott_group_leader_actual, ott_supervisor_actual, ott_department_head_actual,
        notes, recorded_by
    ) VALUES (
        plan_record.id, date_param, location_param,
        greencard_gl, greencard_sv, greencard_dh,
        iut_gl, iut_sv, iut_dh,
        ott_gl, ott_sv, ott_dh,
        notes_param, recorder_id
    ) ON CONFLICT (plan_id, actual_date) DO UPDATE SET
        greencard_group_leader_actual = EXCLUDED.greencard_group_leader_actual,
        greencard_supervisor_actual = EXCLUDED.greencard_supervisor_actual,
        greencard_department_head_actual = EXCLUDED.greencard_department_head_actual,
        iut_group_leader_actual = EXCLUDED.iut_group_leader_actual,
        iut_supervisor_actual = EXCLUDED.iut_supervisor_actual,
        iut_department_head_actual = EXCLUDED.iut_department_head_actual,
        ott_group_leader_actual = EXCLUDED.ott_group_leader_actual,
        ott_supervisor_actual = EXCLUDED.ott_supervisor_actual,
        ott_department_head_actual = EXCLUDED.ott_department_head_actual,
        notes = EXCLUDED.notes,
        updated_at = NOW();

    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CERTIFICATION VIEWS
-- =====================================================

-- View for certificate dashboard
CREATE VIEW v_certificate_dashboard AS
SELECT
    mc.id,
    up.full_name as holder_name,
    up.employee_id,
    up.department,
    mc.certificate_type,
    mc.certificate_number,
    mc.certificate_name,
    mc.issued_date,
    mc.valid_until,
    (mc.valid_until - CURRENT_DATE) as days_remaining,
    mc.status,
    CASE
        WHEN mc.valid_until < CURRENT_DATE THEN 'Expired'
        WHEN mc.valid_until <= CURRENT_DATE + 30 THEN 'Expiring Soon'
        ELSE 'Valid'
    END as calculated_status,
    mc.greencard_level,
    mc.iut_license_type,
    mc.ott_operation_type,
    mc.renewal_required,
    cr.status as renewal_status
FROM mining_certifications mc
JOIN user_profiles up ON mc.holder_id = up.id
LEFT JOIN certificate_renewals cr ON mc.id = cr.certificate_id
    AND cr.status IN ('Initiated', 'In Progress')
ORDER BY mc.valid_until ASC;

-- View for Greencard summary
CREATE VIEW v_greencard_summary AS
SELECT
    up.full_name as holder_name,
    up.employee_id,
    up.primary_location,
    mc.certificate_number,
    mc.greencard_level,
    mc.greencard_categories,
    mc.valid_until,
    mc.status,
    mc.last_training_date,
    mc.next_training_due,
    mc.completed_training_hours,
    mc.required_training_hours
FROM mining_certifications mc
JOIN user_profiles up ON mc.holder_id = up.id
WHERE mc.certificate_type = 'Greencard'
ORDER BY mc.valid_until ASC;

-- View for IUT summary
CREATE VIEW v_iut_summary AS
SELECT
    up.full_name as holder_name,
    up.employee_id,
    mc.certificate_number,
    mc.iut_license_type,
    mc.iut_commodity,
    mc.iut_area_hectares,
    mc.valid_until,
    mc.status,
    mc.issuing_authority,
    mc.conditions_restrictions
FROM mining_certifications mc
JOIN user_profiles up ON mc.holder_id = up.id
WHERE mc.certificate_type = 'IUT'
ORDER BY mc.valid_until ASC;

-- View for OTT summary
CREATE VIEW v_ott_summary AS
SELECT
    up.full_name as holder_name,
    up.employee_id,
    mc.certificate_number,
    mc.ott_observation_areas,
    mc.ott_behavioral_focus,
    mc.ott_observation_frequency,
    mc.ott_feedback_authority,
    mc.valid_until,
    mc.status,
    mc.scope_of_work,
    mc.location_restrictions
FROM mining_certifications mc
JOIN user_profiles up ON mc.holder_id = up.id
WHERE mc.certificate_type = 'OTT'
ORDER BY mc.valid_until ASC;

-- =====================================================
-- PLAN VS ACTUAL VIEWS
-- =====================================================

-- Monthly Plan vs Actual Summary
CREATE VIEW v_monthly_safety_plan_actual AS
SELECT
    sap.location,
    sap.plan_year,
    sap.plan_month,
    sap.plan_name,

    -- Greencard Plan vs Actual
    sap.greencard_group_leader_plan,
    sap.greencard_supervisor_plan,
    sap.greencard_department_head_plan,
    sap.greencard_total_plan,

    COALESCE(SUM(saa.greencard_group_leader_actual), 0) as greencard_group_leader_actual,
    COALESCE(SUM(saa.greencard_supervisor_actual), 0) as greencard_supervisor_actual,
    COALESCE(SUM(saa.greencard_department_head_actual), 0) as greencard_department_head_actual,
    COALESCE(SUM(saa.greencard_total_actual), 0) as greencard_total_actual,

    -- Greencard Achievement %
    CASE WHEN sap.greencard_group_leader_plan > 0
         THEN ROUND((COALESCE(SUM(saa.greencard_group_leader_actual), 0)::DECIMAL / sap.greencard_group_leader_plan * 100), 2)
         ELSE 0 END as greencard_group_leader_achievement,
    CASE WHEN sap.greencard_supervisor_plan > 0
         THEN ROUND((COALESCE(SUM(saa.greencard_supervisor_actual), 0)::DECIMAL / sap.greencard_supervisor_plan * 100), 2)
         ELSE 0 END as greencard_supervisor_achievement,
    CASE WHEN sap.greencard_department_head_plan > 0
         THEN ROUND((COALESCE(SUM(saa.greencard_department_head_actual), 0)::DECIMAL / sap.greencard_department_head_plan * 100), 2)
         ELSE 0 END as greencard_department_head_achievement,
    CASE WHEN sap.greencard_total_plan > 0
         THEN ROUND((COALESCE(SUM(saa.greencard_total_actual), 0)::DECIMAL / sap.greencard_total_plan * 100), 2)
         ELSE 0 END as greencard_total_achievement,

    -- IUT Plan vs Actual
    sap.iut_group_leader_plan,
    sap.iut_supervisor_plan,
    sap.iut_department_head_plan,
    sap.iut_total_plan,

    COALESCE(SUM(saa.iut_group_leader_actual), 0) as iut_group_leader_actual,
    COALESCE(SUM(saa.iut_supervisor_actual), 0) as iut_supervisor_actual,
    COALESCE(SUM(saa.iut_department_head_actual), 0) as iut_department_head_actual,
    COALESCE(SUM(saa.iut_total_actual), 0) as iut_total_actual,

    -- IUT Achievement %
    CASE WHEN sap.iut_group_leader_plan > 0
         THEN ROUND((COALESCE(SUM(saa.iut_group_leader_actual), 0)::DECIMAL / sap.iut_group_leader_plan * 100), 2)
         ELSE 0 END as iut_group_leader_achievement,
    CASE WHEN sap.iut_supervisor_plan > 0
         THEN ROUND((COALESCE(SUM(saa.iut_supervisor_actual), 0)::DECIMAL / sap.iut_supervisor_plan * 100), 2)
         ELSE 0 END as iut_supervisor_achievement,
    CASE WHEN sap.iut_department_head_plan > 0
         THEN ROUND((COALESCE(SUM(saa.iut_department_head_actual), 0)::DECIMAL / sap.iut_department_head_plan * 100), 2)
         ELSE 0 END as iut_department_head_achievement,
    CASE WHEN sap.iut_total_plan > 0
         THEN ROUND((COALESCE(SUM(saa.iut_total_actual), 0)::DECIMAL / sap.iut_total_plan * 100), 2)
         ELSE 0 END as iut_total_achievement,

    -- OTT Plan vs Actual
    sap.ott_group_leader_plan,
    sap.ott_supervisor_plan,
    sap.ott_department_head_plan,
    sap.ott_total_plan,

    COALESCE(SUM(saa.ott_group_leader_actual), 0) as ott_group_leader_actual,
    COALESCE(SUM(saa.ott_supervisor_actual), 0) as ott_supervisor_actual,
    COALESCE(SUM(saa.ott_department_head_actual), 0) as ott_department_head_actual,
    COALESCE(SUM(saa.ott_total_actual), 0) as ott_total_actual,

    -- OTT Achievement %
    CASE WHEN sap.ott_group_leader_plan > 0
         THEN ROUND((COALESCE(SUM(saa.ott_group_leader_actual), 0)::DECIMAL / sap.ott_group_leader_plan * 100), 2)
         ELSE 0 END as ott_group_leader_achievement,
    CASE WHEN sap.ott_supervisor_plan > 0
         THEN ROUND((COALESCE(SUM(saa.ott_supervisor_actual), 0)::DECIMAL / sap.ott_supervisor_plan * 100), 2)
         ELSE 0 END as ott_supervisor_achievement,
    CASE WHEN sap.ott_department_head_plan > 0
         THEN ROUND((COALESCE(SUM(saa.ott_department_head_actual), 0)::DECIMAL / sap.ott_department_head_plan * 100), 2)
         ELSE 0 END as ott_department_head_achievement,
    CASE WHEN sap.ott_total_plan > 0
         THEN ROUND((COALESCE(SUM(saa.ott_total_actual), 0)::DECIMAL / sap.ott_total_plan * 100), 2)
         ELSE 0 END as ott_total_achievement,

    -- Plan Status
    sap.is_active,
    sap.approved_at

FROM safety_activity_plans sap
LEFT JOIN safety_activity_actuals saa ON sap.id = saa.plan_id
WHERE sap.plan_type = 'Monthly'
GROUP BY sap.id, sap.location, sap.plan_year, sap.plan_month, sap.plan_name,
         sap.greencard_group_leader_plan, sap.greencard_supervisor_plan, sap.greencard_department_head_plan,
         sap.iut_group_leader_plan, sap.iut_supervisor_plan, sap.iut_department_head_plan,
         sap.ott_group_leader_plan, sap.ott_supervisor_plan, sap.ott_department_head_plan,
         sap.is_active, sap.approved_at
ORDER BY sap.plan_year DESC, sap.plan_month DESC, sap.location;

-- Daily Safety Activity Dashboard
CREATE VIEW v_daily_safety_dashboard AS
SELECT
    saa.actual_date,
    saa.location,
    sap.plan_name,

    -- Daily Actuals
    saa.greencard_group_leader_actual,
    saa.greencard_supervisor_actual,
    saa.greencard_department_head_actual,
    saa.greencard_total_actual,

    saa.iut_group_leader_actual,
    saa.iut_supervisor_actual,
    saa.iut_department_head_actual,
    saa.iut_total_actual,

    saa.ott_group_leader_actual,
    saa.ott_supervisor_actual,
    saa.ott_department_head_actual,
    saa.ott_total_actual,

    -- Monthly Targets (for reference)
    sap.greencard_total_plan as monthly_greencard_target,
    sap.iut_total_plan as monthly_iut_target,
    sap.ott_total_plan as monthly_ott_target,

    -- Notes
    saa.notes,
    saa.issues_encountered,

    -- Data Entry Info
    up.full_name as recorded_by_name,
    saa.created_at

FROM safety_activity_actuals saa
JOIN safety_activity_plans sap ON saa.plan_id = sap.id
JOIN user_profiles up ON saa.recorded_by = up.id
ORDER BY saa.actual_date DESC, saa.location;

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Sample Safety Incidents
INSERT INTO safety_incidents (
    incident_number, title, description, incident_type, severity, location,
    occurred_at, people_involved, injuries_count, reported_by, status, created_by
) VALUES
('INC-2024-001', 'Near Miss - Equipment Collision', 
 'Two dump trucks nearly collided at intersection near Pit A', 
 'Near Miss', 'Medium', 'Pit A', CURRENT_TIMESTAMP - INTERVAL '2 days', 2, 0,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 'Under Investigation',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('INC-2024-002', 'Minor Injury - Slip and Fall',
 'Worker slipped on wet surface near processing area',
 'First Aid', 'Low', 'Pit B', CURRENT_TIMESTAMP - INTERVAL '5 days', 1, 1,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 'Investigation Complete',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Sample Safety Training
INSERT INTO safety_training (
    training_name, training_type, duration_hours, scheduled_date, location,
    max_attendees, trainer_id, certification_required, created_by
) VALUES
('Monthly Safety Induction', 'Safety Induction', 4.0, CURRENT_DATE + 7, 'Main Office', 20,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'), true,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('Equipment Safety Training', 'Equipment Training', 6.0, CURRENT_DATE + 14, 'Training Center', 15,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'), true,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Sample Mining Certifications
INSERT INTO mining_certifications (
    holder_id, certificate_type, certificate_number, certificate_name,
    issuing_authority, issued_date, valid_from, valid_until,
    greencard_level, greencard_categories, status, created_by
) VALUES
((SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 'Greencard', 'GC-2024-001', 'Mining Safety Greencard',
 'Kementerian ESDM', '2024-01-15', '2024-01-15', '2026-01-15',
 'Advanced', ARRAY['Surface Mining', 'Equipment Operation', 'Safety Supervision'], 'Valid',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),

((SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 'IUT', 'IUT-2024-001', 'Inspeksi Umum Terencana Certification',
 'Internal Safety Department', '2024-01-01', '2024-01-01', '2025-01-01',
 NULL, NULL, 'Valid',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),

((SELECT id FROM user_profiles WHERE email = '<EMAIL>'),
 'OTT', 'OTT-2024-001', 'Observasi Tugas Terencana Certification',
 'Internal Safety Department', '2024-01-10', '2024-01-10', '2025-01-10',
 NULL, NULL, 'Valid',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Update IUT specific fields (Inspeksi Umum Terencana)
UPDATE mining_certifications SET
    iut_inspection_scope = ARRAY['Equipment Condition', 'Work Area Safety', 'Safety Procedures', 'PPE Compliance'],
    iut_inspection_frequency = 'Weekly',
    iut_checklist_template = 'IUT-CHECKLIST-GENERAL-001',
    iut_competency_level = 'Advanced'
WHERE certificate_type = 'IUT';

-- Update OTT specific fields (Observasi Tugas Terencana)
UPDATE mining_certifications SET
    ott_observation_areas = ARRAY['Mining Operations', 'Equipment Operation', 'Material Handling', 'Maintenance Work'],
    ott_behavioral_focus = ARRAY['PPE Usage', 'Safe Work Procedures', 'Hazard Recognition', 'Communication'],
    ott_observation_frequency = 'Daily',
    ott_feedback_authority = true
WHERE certificate_type = 'OTT';

-- Sample Certificate Renewals
INSERT INTO certificate_renewals (
    certificate_id, renewal_type, renewal_initiated_date, renewal_due_date,
    training_required, medical_exam_required, status, created_by
) VALUES
((SELECT id FROM mining_certifications WHERE certificate_number = 'GC-2024-001'),
 'Regular', '2025-10-15', '2026-01-15',
 true, true, 'Initiated',
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- Create default safety activity plans for current year
SELECT create_default_safety_plans('Pit A', EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER);
SELECT create_default_safety_plans('Pit B', EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER);
SELECT create_default_safety_plans('Pit C', EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER);

-- Sample daily safety activities for the past week
DO $$
DECLARE
    i INTEGER;
    current_date_iter DATE;
BEGIN
    -- Record activities for past 7 days
    FOR i IN 0..6 LOOP
        current_date_iter := CURRENT_DATE - i;

        -- Pit A activities (good performance)
        PERFORM record_daily_safety_activities(
            'Pit A', current_date_iter,
            -- Greencard: GL=2, SV=1, DH=1 (daily targets ~25% of monthly)
            2, 1, 1,
            -- IUT: GL=2, SV=1, DH=0 (daily targets ~25% of monthly)
            2, 1, 0,
            -- OTT: GL=2, SV=1, DH=1 (daily targets ~25% of monthly)
            2, 1, 1,
            'Good safety performance today',
            (SELECT id FROM user_profiles WHERE email = '<EMAIL>')
        );

        -- Pit B activities (average performance)
        PERFORM record_daily_safety_activities(
            'Pit B', current_date_iter,
            -- Greencard: GL=1, SV=1, DH=0
            1, 1, 0,
            -- IUT: GL=1, SV=0, DH=0
            1, 0, 0,
            -- OTT: GL=1, SV=1, DH=0
            1, 1, 0,
            'Average safety performance',
            (SELECT id FROM user_profiles WHERE email = '<EMAIL>')
        );

        -- Pit C activities (excellent performance)
        PERFORM record_daily_safety_activities(
            'Pit C', current_date_iter,
            -- Greencard: GL=3, SV=2, DH=1
            3, 2, 1,
            -- IUT: GL=2, SV=1, DH=1
            2, 1, 1,
            -- OTT: GL=3, SV=1, DH=1
            3, 1, 1,
            'Excellent safety performance - exceeded targets',
            (SELECT id FROM user_profiles WHERE email = '<EMAIL>')
        );
    END LOOP;
END $$;

-- Sample Safety Metrics
INSERT INTO safety_metrics (
    location, metric_date, total_incidents, near_miss_count, first_aid_count,
    days_without_incident, safety_score, total_personnel,
    total_greencard_holders, total_iut_certificates, total_ott_certificates,
    certification_compliance_rate, calculated_by
) VALUES
('Pit A', CURRENT_DATE - 1, 1, 1, 0, 15, 95.5, 25, 20, 1, 1, 88.0,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('Pit B', CURRENT_DATE - 1, 1, 0, 1, 8, 92.0, 20, 15, 0, 1, 80.0,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>')),
('Pit C', CURRENT_DATE - 1, 0, 0, 0, 30, 100.0, 18, 18, 0, 0, 100.0,
 (SELECT id FROM user_profiles WHERE email = '<EMAIL>'));

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE safety_incidents IS 'Safety incident reporting and investigation tracking';
COMMENT ON TABLE safety_inspections IS 'Safety inspections and audit records';
COMMENT ON TABLE safety_training IS 'Safety training sessions and programs';
COMMENT ON TABLE safety_training_attendees IS 'Individual training attendance and certification records';
COMMENT ON TABLE safety_metrics IS 'Daily safety metrics and KPIs by location';
COMMENT ON TABLE mining_certifications IS 'Mining certifications including Greencard, IUT, and OTT';
COMMENT ON TABLE certificate_renewals IS 'Certificate renewal tracking and management';
COMMENT ON TABLE safety_activity_plans IS 'Monthly safety activity plans with targets by role';
COMMENT ON TABLE safety_activity_actuals IS 'Daily safety activity actuals tracking';

COMMENT ON COLUMN safety_incidents.incident_number IS 'Unique incident identifier';
COMMENT ON COLUMN safety_incidents.regulatory_notification_required IS 'Whether incident must be reported to authorities';
COMMENT ON COLUMN safety_inspections.compliance_percentage IS 'Auto-calculated compliance percentage';
COMMENT ON COLUMN safety_training.completion_rate IS 'Auto-calculated training completion rate';
COMMENT ON COLUMN safety_metrics.training_compliance_percentage IS 'Auto-calculated training compliance percentage';
COMMENT ON COLUMN mining_certifications.certificate_number IS 'Unique certificate identifier';
COMMENT ON COLUMN mining_certifications.greencard_level IS 'Greencard competency level (Basic, Advanced, Supervisor)';
COMMENT ON COLUMN mining_certifications.iut_inspection_scope IS 'IUT inspection scope areas (Inspeksi Umum Terencana)';
COMMENT ON COLUMN mining_certifications.ott_observation_areas IS 'OTT observation areas (Observasi Tugas Terencana)';
COMMENT ON COLUMN certificate_renewals.total_cost IS 'Auto-calculated total renewal cost';
COMMENT ON COLUMN safety_activity_plans.greencard_total_plan IS 'Auto-calculated total Greencard plan (GL+SV+DH)';
COMMENT ON COLUMN safety_activity_plans.iut_total_plan IS 'Auto-calculated total IUT plan (GL+SV+DH)';
COMMENT ON COLUMN safety_activity_plans.ott_total_plan IS 'Auto-calculated total OTT plan (GL+SV+DH)';
COMMENT ON COLUMN safety_activity_actuals.greencard_total_actual IS 'Auto-calculated total Greencard actual (GL+SV+DH)';
COMMENT ON COLUMN safety_activity_actuals.iut_total_actual IS 'Auto-calculated total IUT actual (GL+SV+DH)';
COMMENT ON COLUMN safety_activity_actuals.ott_total_actual IS 'Auto-calculated total OTT actual (GL+SV+DH)';

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('008', 'Safety management with incidents, training, and compliance')
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
DECLARE
    incident_count INTEGER;
    training_count INTEGER;
    metric_count INTEGER;
    certification_count INTEGER;
    renewal_count INTEGER;
    plan_count INTEGER;
    actual_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO incident_count FROM safety_incidents;
    SELECT COUNT(*) INTO training_count FROM safety_training;
    SELECT COUNT(*) INTO metric_count FROM safety_metrics;
    SELECT COUNT(*) INTO certification_count FROM mining_certifications;
    SELECT COUNT(*) INTO renewal_count FROM certificate_renewals;
    SELECT COUNT(*) INTO plan_count FROM safety_activity_plans;
    SELECT COUNT(*) INTO actual_count FROM safety_activity_actuals;

    RAISE NOTICE 'Safety management setup completed successfully';
    RAISE NOTICE 'Sample incidents created: %', incident_count;
    RAISE NOTICE 'Training sessions: %', training_count;
    RAISE NOTICE 'Safety metrics: %', metric_count;
    RAISE NOTICE 'Mining certifications: %', certification_count;
    RAISE NOTICE 'Certificate renewals: %', renewal_count;
    RAISE NOTICE 'Safety activity plans: %', plan_count;
    RAISE NOTICE 'Safety activity actuals: %', actual_count;
    RAISE NOTICE 'Safety compliance, incident tracking, and certification management ready';
    RAISE NOTICE 'Greencard, IUT (Inspeksi Umum Terencana), and OTT (Observasi Tugas Terencana) management integrated';
    RAISE NOTICE '';
    RAISE NOTICE 'Plan Targets by Role:';
    RAISE NOTICE 'Greencard - Group Leader: 8, Supervisor: 6, Department Head: 3';
    RAISE NOTICE 'IUT - Group Leader: 6, Supervisor: 3, Department Head: 2';
    RAISE NOTICE 'OTT - Group Leader: 6, Supervisor: 3, Department Head: 2';
END $$;
