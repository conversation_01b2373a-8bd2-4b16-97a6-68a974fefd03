import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { Layout } from '../constants/layout';

interface AnalyticsScreenProps {
  navigation: any;
}

interface KPIMetric {
  id: string;
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
}

interface ChartData {
  label: string;
  value: number;
  color: string;
}

const { width } = Dimensions.get('window');

const AnalyticsScreen: React.FC<AnalyticsScreenProps> = ({ navigation }) => {
  const { isDarkMode } = useTheme();
  const colors = useThemeColors();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly');

  const [kpiMetrics] = useState<KPIMetric[]>([
    {
      id: '1',
      title: 'Total Production',
      value: '2,847 tons',
      change: '+12.5%',
      changeType: 'increase',
      icon: 'trending-up',
      color: '#10B981'
    },
    {
      id: '2',
      title: 'Equipment Efficiency',
      value: '94.2%',
      change: '+2.1%',
      changeType: 'increase',
      icon: 'speedometer',
      color: '#3B82F6'
    },
    {
      id: '3',
      title: 'Safety Score',
      value: '98.7%',
      change: '-0.3%',
      changeType: 'decrease',
      icon: 'shield-checkmark',
      color: '#F59E0B'
    },
    {
      id: '4',
      title: 'Cost per Ton',
      value: '$45.20',
      change: '-5.8%',
      changeType: 'decrease',
      icon: 'cash',
      color: '#EF4444'
    }
  ]);

  const [productionData] = useState<ChartData[]>([
    { label: 'Coal', value: 45, color: '#1F2937' },
    { label: 'Iron Ore', value: 30, color: '#DC2626' },
    { label: 'Copper', value: 15, color: '#D97706' },
    { label: 'Gold', value: 10, color: '#F59E0B' }
  ]);

  const [monthlyTrends] = useState([
    { month: 'Jan', production: 2400, efficiency: 92, safety: 98 },
    { month: 'Feb', production: 2600, efficiency: 94, safety: 97 },
    { month: 'Mar', production: 2800, efficiency: 96, safety: 99 },
    { month: 'Apr', production: 2700, efficiency: 93, safety: 98 },
    { month: 'May', production: 2900, efficiency: 95, safety: 98 },
    { month: 'Jun', production: 2847, efficiency: 94, safety: 99 }
  ]);

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Layout.spacing.lg,
      paddingVertical: Layout.spacing.md,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: Layout.fontSize.xl,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginLeft: Layout.spacing.md,
    },
    content: {
      flex: 1,
      paddingHorizontal: Layout.spacing.lg,
      paddingTop: Layout.spacing.lg,
    },
    periodSelector: {
      flexDirection: 'row',
      backgroundColor: colors.surfaceSecondary,
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.xs,
      marginBottom: Layout.spacing.lg,
    },
    periodButton: {
      flex: 1,
      paddingVertical: Layout.spacing.sm,
      alignItems: 'center',
      borderRadius: Layout.borderRadius.md,
    },
    periodButtonActive: {
      backgroundColor: colors.primary,
    },
    periodButtonText: {
      fontSize: Layout.fontSize.sm,
      fontWeight: '600',
      color: colors.textSecondary,
    },
    periodButtonTextActive: {
      color: colors.textInverse,
    },
    sectionTitle: {
      fontSize: Layout.fontSize.lg,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.md,
      marginTop: Layout.spacing.sm,
    },
    kpiGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: Layout.spacing.lg,
    },
    kpiCard: {
      width: '48%',
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.md,
      marginBottom: Layout.spacing.md,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    kpiHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Layout.spacing.sm,
    },
    kpiIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: Layout.spacing.sm,
    },
    kpiTitle: {
      fontSize: Layout.fontSize.sm,
      color: colors.textSecondary,
      flex: 1,
    },
    kpiValue: {
      fontSize: Layout.fontSize.xl,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.xs,
    },
    kpiChange: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    kpiChangeText: {
      fontSize: Layout.fontSize.sm,
      fontWeight: '600',
      marginLeft: Layout.spacing.xs,
    },
    chartCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.lg,
      marginBottom: Layout.spacing.lg,
      borderWidth: 1,
      borderColor: colors.cardBorder,
      shadowColor: colors.cardShadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    chartTitle: {
      fontSize: Layout.fontSize.lg,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.lg,
      textAlign: 'center',
    },
    pieChartContainer: {
      alignItems: 'center',
      marginBottom: Layout.spacing.lg,
    },
    pieChart: {
      width: 200,
      height: 200,
      borderRadius: 100,
      marginBottom: Layout.spacing.lg,
    },
    legendContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'center',
    },
    legendItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginHorizontal: Layout.spacing.sm,
      marginVertical: Layout.spacing.xs,
    },
    legendColor: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: Layout.spacing.xs,
    },
    legendText: {
      fontSize: Layout.fontSize.sm,
      color: colors.textSecondary,
    },
    trendContainer: {
      marginBottom: Layout.spacing.lg,
    },
    trendItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Layout.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    trendMonth: {
      width: 50,
      fontSize: Layout.fontSize.sm,
      fontWeight: '600',
      color: colors.textPrimary,
    },
    trendBar: {
      flex: 1,
      height: 8,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 4,
      marginHorizontal: Layout.spacing.sm,
      overflow: 'hidden',
    },
    trendProgress: {
      height: '100%',
      backgroundColor: colors.primary,
      borderRadius: 4,
    },
    trendValue: {
      width: 60,
      fontSize: Layout.fontSize.sm,
      fontWeight: '600',
      color: colors.textPrimary,
      textAlign: 'right',
    },
    insightCard: {
      backgroundColor: colors.primary + '10',
      borderRadius: Layout.borderRadius.lg,
      padding: Layout.spacing.lg,
      marginBottom: Layout.spacing.lg,
      borderLeftWidth: 4,
      borderLeftColor: colors.primary,
    },
    insightTitle: {
      fontSize: Layout.fontSize.md,
      fontWeight: 'bold',
      color: colors.primary,
      marginBottom: Layout.spacing.sm,
    },
    insightText: {
      fontSize: Layout.fontSize.sm,
      color: colors.textSecondary,
      lineHeight: 20,
    },
  });

  const getChangeColor = (changeType: string) => {
    switch (changeType) {
      case 'increase':
        return colors.success;
      case 'decrease':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getChangeIcon = (changeType: string) => {
    switch (changeType) {
      case 'increase':
        return 'trending-up';
      case 'decrease':
        return 'trending-down';
      default:
        return 'remove';
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const renderPieChart = () => {
    const total = productionData.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;

    return (
      <View style={dynamicStyles.pieChartContainer}>
        <View style={dynamicStyles.pieChart}>
          {/* Simple pie chart representation - in real app, use a chart library */}
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            borderRadius: 100,
            backgroundColor: colors.surfaceSecondary,
          }} />
          {productionData.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const angle = (item.value / total) * 360;
            const rotation = currentAngle;
            currentAngle += angle;
            
            return (
              <View
                key={index}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  borderRadius: 100,
                  backgroundColor: item.color,
                  transform: [{ rotate: `${rotation}deg` }],
                  opacity: 0.8,
                }}
              />
            );
          })}
        </View>
        <View style={dynamicStyles.legendContainer}>
          {productionData.map((item, index) => (
            <View key={index} style={dynamicStyles.legendItem}>
              <View style={[dynamicStyles.legendColor, { backgroundColor: item.color }]} />
              <Text style={dynamicStyles.legendText}>{item.label} ({item.value}%)</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={dynamicStyles.container}>
      <View style={dynamicStyles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.textPrimary} />
        </TouchableOpacity>
        <Text style={dynamicStyles.headerTitle}>Analytics</Text>
      </View>

      <ScrollView 
        style={dynamicStyles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Period Selector */}
        <View style={dynamicStyles.periodSelector}>
          {(['daily', 'weekly', 'monthly', 'yearly'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                dynamicStyles.periodButton,
                selectedPeriod === period && dynamicStyles.periodButtonActive
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <Text style={[
                dynamicStyles.periodButtonText,
                selectedPeriod === period && dynamicStyles.periodButtonTextActive
              ]}>
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* KPI Metrics */}
        <Text style={dynamicStyles.sectionTitle}>Key Performance Indicators</Text>
        <View style={dynamicStyles.kpiGrid}>
          {kpiMetrics.map((metric) => (
            <View key={metric.id} style={dynamicStyles.kpiCard}>
              <View style={dynamicStyles.kpiHeader}>
                <View style={[dynamicStyles.kpiIcon, { backgroundColor: metric.color + '20' }]}>
                  <Ionicons name={metric.icon} size={20} color={metric.color} />
                </View>
                <Text style={dynamicStyles.kpiTitle}>{metric.title}</Text>
              </View>
              <Text style={dynamicStyles.kpiValue}>{metric.value}</Text>
              <View style={dynamicStyles.kpiChange}>
                <Ionicons 
                  name={getChangeIcon(metric.changeType)} 
                  size={16} 
                  color={getChangeColor(metric.changeType)} 
                />
                <Text style={[
                  dynamicStyles.kpiChangeText, 
                  { color: getChangeColor(metric.changeType) }
                ]}>
                  {metric.change}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Production Distribution */}
        <View style={dynamicStyles.chartCard}>
          <Text style={dynamicStyles.chartTitle}>Production Distribution</Text>
          {renderPieChart()}
        </View>

        {/* Monthly Trends */}
        <View style={dynamicStyles.chartCard}>
          <Text style={dynamicStyles.chartTitle}>Monthly Production Trends</Text>
          <View style={dynamicStyles.trendContainer}>
            {monthlyTrends.map((trend, index) => (
              <View key={index} style={dynamicStyles.trendItem}>
                <Text style={dynamicStyles.trendMonth}>{trend.month}</Text>
                <View style={dynamicStyles.trendBar}>
                  <View 
                    style={[
                      dynamicStyles.trendProgress, 
                      { width: `${(trend.production / 3000) * 100}%` }
                    ]} 
                  />
                </View>
                <Text style={dynamicStyles.trendValue}>{trend.production}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* AI Insights */}
        <Text style={dynamicStyles.sectionTitle}>AI Insights</Text>
        <View style={dynamicStyles.insightCard}>
          <Text style={dynamicStyles.insightTitle}>Production Optimization</Text>
          <Text style={dynamicStyles.insightText}>
            Based on current trends, increasing equipment maintenance frequency by 15% 
            could improve overall efficiency by 3.2% and reduce operational costs by $12,000 monthly.
          </Text>
        </View>

        <View style={dynamicStyles.insightCard}>
          <Text style={dynamicStyles.insightTitle}>Safety Recommendation</Text>
          <Text style={dynamicStyles.insightText}>
            Weather patterns suggest increased safety protocols needed for next week. 
            Consider adjusting shift schedules and equipment deployment accordingly.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AnalyticsScreen;
