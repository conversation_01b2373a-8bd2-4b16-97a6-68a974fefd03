#!/usr/bin/env node

/**
 * Setup New Supabase Project Helper
 * 
 * This script helps configure a new Supabase project for the Mining Operations App
 * after the original project becomes unavailable.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🚀 Mining Operations App - Supabase Setup Helper\n');
  console.log('This script will help you configure a new Supabase project.\n');

  try {
    // Get new project details
    console.log('📋 Please provide your new Supabase project details:\n');
    
    const projectUrl = await question('🌐 Project URL (e.g., https://abc123.supabase.co): ');
    const anonKey = await question('🔑 Anon Key: ');
    const serviceKey = await question('🔐 Service Role Key (optional): ');
    
    // Extract project ID from URL
    const projectId = projectUrl.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];
    
    if (!projectId) {
      throw new Error('Invalid project URL format. Expected: https://[project-id].supabase.co');
    }

    console.log(`\n✅ Project ID extracted: ${projectId}\n`);

    // Update .env file
    console.log('📝 Updating .env file...');
    const envContent = `# Supabase Configuration for Mining Operations App
EXPO_PUBLIC_SUPABASE_URL=${projectUrl}
EXPO_PUBLIC_SUPABASE_ANON_KEY=${anonKey}

# API Configuration
EXPO_PUBLIC_API_URL=${projectUrl}

# App Configuration
EXPO_PUBLIC_APP_NAME=Mining Operations App
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_ENVIRONMENT=development

# Project Information
PROJECT_ID=${projectId}
PROJECT_NAME=mining-operations-app
REGION=us-east-1
`;

    fs.writeFileSync('.env', envContent);
    console.log('✅ .env file updated');

    // Update supabase config
    console.log('📝 Updating Supabase configuration...');
    const supabaseConfigPath = 'src/config/supabase.ts';
    
    if (fs.existsSync(supabaseConfigPath)) {
      let configContent = fs.readFileSync(supabaseConfigPath, 'utf8');
      
      // Update the fallback URLs in the config
      configContent = configContent.replace(
        /const supabaseUrl = process\.env\.EXPO_PUBLIC_SUPABASE_URL \|\| '[^']+';/,
        `const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || '${projectUrl}';`
      );
      
      configContent = configContent.replace(
        /const supabaseAnonKey = process\.env\.EXPO_PUBLIC_SUPABASE_ANON_KEY \|\| '[^']+';/,
        `const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '${anonKey}';`
      );
      
      fs.writeFileSync(supabaseConfigPath, configContent);
      console.log('✅ Supabase config updated');
    }

    // Update check scripts
    console.log('📝 Updating check scripts...');
    const checkScriptPath = 'check-supabase.js';
    
    if (fs.existsSync(checkScriptPath)) {
      let scriptContent = fs.readFileSync(checkScriptPath, 'utf8');
      
      scriptContent = scriptContent.replace(
        /const supabaseUrl = '[^']+';/,
        `const supabaseUrl = '${projectUrl}';`
      );
      
      scriptContent = scriptContent.replace(
        /const supabaseAnonKey = '[^']+';/,
        `const supabaseAnonKey = '${anonKey}';`
      );
      
      fs.writeFileSync(checkScriptPath, scriptContent);
      console.log('✅ Check script updated');
    }

    // Update scripts folder
    const scriptsCheckPath = 'scripts/check-supabase.js';
    if (fs.existsSync(scriptsCheckPath)) {
      let scriptContent = fs.readFileSync(scriptsCheckPath, 'utf8');
      
      scriptContent = scriptContent.replace(
        /const supabaseUrl = '[^']+';/,
        `const supabaseUrl = '${projectUrl}';`
      );
      
      scriptContent = scriptContent.replace(
        /const supabaseAnonKey = '[^']+';/,
        `const supabaseAnonKey = '${anonKey}';`
      );
      
      fs.writeFileSync(scriptsCheckPath, scriptContent);
      console.log('✅ Scripts check file updated');
    }

    console.log('\n🎉 Configuration updated successfully!\n');

    // Test connection
    console.log('🔍 Testing connection to new project...');
    
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(projectUrl, anonKey);
    
    try {
      const { data, error } = await supabase.from('users').select('count').limit(1);
      
      if (error) {
        console.log('⚠️  Connection test failed:', error.message);
        console.log('📋 This is normal for a new project. You need to set up the database schema.');
      } else {
        console.log('✅ Connection test successful!');
      }
    } catch (err) {
      console.log('⚠️  Connection test error:', err.message);
      console.log('📋 This might be normal for a new project.');
    }

    console.log('\n📋 Next Steps:');
    console.log('1. 🗄️  Set up database schema:');
    console.log('   - Go to your Supabase dashboard');
    console.log('   - Navigate to SQL Editor');
    console.log('   - Run the SQL files from the database/ folder');
    console.log('');
    console.log('2. 🧪 Test the connection:');
    console.log('   node check-supabase.js');
    console.log('');
    console.log('3. 🚀 Start the application:');
    console.log('   npx expo start');
    console.log('');
    console.log('4. 📚 Refer to database/README.md for detailed setup instructions');

    if (serviceKey) {
      console.log('\n🔐 Service Role Key provided. You can use this for:');
      console.log('- Database migrations');
      console.log('- Admin operations');
      console.log('- Bypassing RLS policies');
      console.log('\n⚠️  Keep the service key secure and never expose it in client-side code!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
