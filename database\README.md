# Mining Operations App - Supabase Database Documentation

## 📋 Overview

This document provides comprehensive information about the Supabase database schema designed for the Mining Operations App. The database supports multi-user mining operations with proper security, relationships, and real-time capabilities.

## 🏗️ Database Structure

### Core Tables

1. **locations** - Mining sites, processing plants, offices, warehouses
2. **users** - User profiles extending Supabase auth
3. **equipment** - Mining equipment and machinery
4. **safety_incidents** - Safety reports and incident tracking
5. **production_reports** - Daily production metrics and reports
6. **maintenance_records** - Equipment maintenance tracking
7. **shifts** - Work shift scheduling
8. **user_shifts** - User-shift assignments

### Key Features

- **Row Level Security (RLS)** - Multi-tenant security based on user roles and locations
- **Auto-numbering** - Automatic generation of incident, maintenance, and report numbers
- **JSONB Storage** - Flexible storage for equipment specifications and production metrics
- **Real-time Subscriptions** - Live updates for safety incidents and equipment status
- **Comprehensive Indexing** - Optimized for performance

## 🔐 Security Model

### User Roles

- **admin** - Full system access
- **supervisor** - Location-based management access
- **safety_officer** - Safety incident management
- **maintenance_tech** - Equipment and maintenance management
- **operator** - Basic operational access

### RLS Policies

- Users can only access data from their assigned location
- Role-based permissions for different data types
- Supervisors have elevated access within their location
- Admins have system-wide access

## 📊 Database Schema

### Locations Table
```sql
CREATE TABLE locations (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location_type location_type NOT NULL,
    coordinates POINT,
    address TEXT,
    is_active BOOLEAN DEFAULT true
);
```

### Users Table
```sql
CREATE TABLE users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'operator',
    location_id UUID REFERENCES locations(id),
    employee_id VARCHAR(50) UNIQUE
);
```

### Equipment Table
```sql
CREATE TABLE equipment (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    equipment_type equipment_type NOT NULL,
    location_id UUID REFERENCES locations(id),
    status equipment_status DEFAULT 'operational',
    specifications JSONB,
    operating_hours INTEGER DEFAULT 0
);
```

### Safety Incidents Table
```sql
CREATE TABLE safety_incidents (
    id UUID PRIMARY KEY,
    incident_number VARCHAR(50) UNIQUE NOT NULL,
    reported_by UUID REFERENCES users(id) NOT NULL,
    severity incident_severity NOT NULL,
    incident_type incident_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status incident_status DEFAULT 'reported'
);
```

## 🚀 Setup Instructions

### 1. Create Supabase Project
```bash
# The project has already been created with ID: ohqbaimnhwvdfrmxvhxv
# URL: https://ohqbaimnhwvdfrmxvhxv.supabase.co
```

### 2. Install Dependencies
```bash
npm install @supabase/supabase-js @react-native-async-storage/async-storage
```

### 3. Configure Environment
Create a `.env` file with your Supabase credentials:
```env
SUPABASE_URL=https://ohqbaimnhwvdfrmxvhxv.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
```

### 4. Initialize Supabase Client
```typescript
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
```

## 💻 Usage Examples

### Authentication
```typescript
// Sign up new user
const { data, error } = await DatabaseService.signUp(
  '<EMAIL>',
  'password',
  {
    full_name: 'John Doe',
    role: 'operator',
    location_id: 'location-uuid',
    employee_id: 'EMP001'
  }
);

// Sign in
const { data, error } = await DatabaseService.signIn(
  '<EMAIL>',
  'password'
);
```

### Equipment Management
```typescript
// Get all equipment
const equipment = await DatabaseService.getEquipment();

// Update equipment status
await DatabaseService.updateEquipmentStatus(
  'equipment-id',
  'maintenance'
);
```

### Safety Incidents
```typescript
// Create safety incident
const incident = await DatabaseService.createSafetyIncident({
  incident_date: new Date().toISOString(),
  location_id: 'location-id',
  severity: 'medium',
  incident_type: 'near_miss',
  title: 'Near miss incident',
  description: 'Description of the incident'
});

// Get all incidents
const incidents = await DatabaseService.getSafetyIncidents();
```

### Production Reports
```typescript
// Create production report
const report = await DatabaseService.createProductionReport({
  report_date: '2024-01-15',
  shift: 'day',
  location_id: 'location-id',
  production_metrics: {
    ore_extracted: 1500,
    waste_removed: 900
  },
  total_tonnage: 2400,
  crew_size: 15
});
```

### Real-time Subscriptions
```typescript
// Subscribe to safety incidents
const subscription = DatabaseService.subscribeToSafetyIncidents(
  (payload) => {
    console.log('New incident:', payload);
    // Update UI with new incident
  }
);

// Subscribe to equipment status changes
const equipmentSub = DatabaseService.subscribeToEquipmentStatus(
  (payload) => {
    console.log('Equipment status changed:', payload);
    // Update UI with equipment status
  }
);
```

## 📈 Performance Optimization

### Indexes Created
- User location and role indexes
- Equipment type and status indexes
- Safety incident date and severity indexes
- Production report date indexes
- Maintenance record equipment indexes

### Query Optimization Tips
1. Use specific column selection instead of `SELECT *`
2. Leverage indexes for filtering and sorting
3. Use pagination for large datasets
4. Implement proper caching strategies

## 🔧 Maintenance

### Regular Tasks
1. Monitor database performance
2. Update RLS policies as needed
3. Backup critical data regularly
4. Review and optimize slow queries

### Monitoring
- Set up alerts for failed queries
- Monitor connection pool usage
- Track database size growth
- Review security logs regularly

## 🚨 Troubleshooting

### Common Issues
1. **RLS Policy Errors** - Check user role and location assignments
2. **Connection Issues** - Verify Supabase URL and API keys
3. **Permission Denied** - Review RLS policies for the affected table
4. **Slow Queries** - Check if proper indexes are in place

### Debug Tips
- Enable query logging in development
- Use Supabase dashboard for real-time monitoring
- Test RLS policies with different user roles
- Validate data types and constraints

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Real-time Subscriptions](https://supabase.com/docs/guides/realtime)
