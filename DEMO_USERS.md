# 👥 USER DEMO UNTUK DEVELOPMENT & TESTING

Dokumen ini berisi informasi lengkap tentang user demo yang telah dibuat untuk keperluan development dan testing sistem role-based access control dalam aplikasi Mining Operations.

## 📋 DAFTAR USER DEMO

Berikut adalah 10 user demo yang mewakili setiap level role dalam sistem:

### 🔴 Level 10 - Super Administrator
- **Email**: `<EMAIL>`
- **Password**: `SuperAdmin123!`
- **Nama**: Super Administrator
- **Employee ID**: SA001
- **Departemen**: IT Management
- **Jabatan**: Super Administrator
- **Lokasi**: Central Office
- **Akses**: Penuh ke semua fitur sistem

### 🟠 Level 9 - Administrator
- **Email**: `<EMAIL>`
- **Password**: `Admin123!`
- **Nama**: John Administrator
- **Employee ID**: ADM001
- **Departemen**: IT Management
- **Jabatan**: System Administrator
- **Lokasi**: Central Office
- **Akses**: Administrator dengan akses luas

### 🟡 Level 8 - Mine Manager
- **Email**: `<EMAIL>`
- **Password**: `Manager123!`
- **Nama**: Robert Mine Manager
- **Employee ID**: MM001
- **Departemen**: Operations
- **Jabatan**: Mine Manager
- **Lokasi**: North Mine Site
- **Akses**: Manajer tambang dengan akses operasional penuh

### 🟢 Level 7 - Production Supervisor
- **Email**: `<EMAIL>`
- **Password**: `ProdSup123!`
- **Nama**: Sarah Production Supervisor
- **Employee ID**: PS001
- **Departemen**: Production
- **Jabatan**: Production Supervisor
- **Lokasi**: North Mine Site
- **Akses**: Supervisor produksi tambang

### 🔵 Level 6 - Equipment Manager
- **Email**: `<EMAIL>`
- **Password**: `EquipMgr123!`
- **Nama**: Michael Equipment Manager
- **Employee ID**: EM001
- **Departemen**: Maintenance
- **Jabatan**: Equipment Manager
- **Lokasi**: Equipment Warehouse
- **Akses**: Manajer peralatan tambang

### 🟣 Level 5 - Safety Officer
- **Email**: `<EMAIL>`
- **Password**: `Safety123!`
- **Nama**: Lisa Safety Officer
- **Employee ID**: SO001
- **Departemen**: Safety
- **Jabatan**: Safety Officer
- **Lokasi**: Central Office
- **Akses**: Petugas keselamatan tambang

### 🟤 Level 4 - Shift Supervisor
- **Email**: `<EMAIL>`
- **Password**: `ShiftSup123!`
- **Nama**: David Shift Supervisor
- **Employee ID**: SS001
- **Departemen**: Operations
- **Jabatan**: Shift Supervisor
- **Lokasi**: South Mine Site
- **Akses**: Supervisor shift operasional

### ⚫ Level 3 - Equipment Operator
- **Email**: `<EMAIL>`
- **Password**: `Operator123!`
- **Nama**: James Equipment Operator
- **Employee ID**: OP001
- **Departemen**: Operations
- **Jabatan**: Equipment Operator
- **Lokasi**: North Mine Site
- **Akses**: Operator peralatan tambang

### ⚪ Level 2 - Maintenance Technician
- **Email**: `<EMAIL>`
- **Password**: `Technician123!`
- **Nama**: Mark Maintenance Technician
- **Employee ID**: TC001
- **Departemen**: Maintenance
- **Jabatan**: Maintenance Technician
- **Lokasi**: Equipment Warehouse
- **Akses**: Teknisi perawatan peralatan

### 🔘 Level 1 - General Employee
- **Email**: `<EMAIL>`
- **Password**: `Employee123!`
- **Nama**: Anna General Employee
- **Employee ID**: EMP001
- **Departemen**: Administration
- **Jabatan**: General Employee
- **Lokasi**: Central Office
- **Akses**: Karyawan umum dengan akses terbatas

## 🔐 POLA PASSWORD

Semua password mengikuti pola yang konsisten:
- Format: `[RoleName]123!`
- Contoh: `SuperAdmin123!`, `Admin123!`, `Manager123!`
- Menggunakan kombinasi huruf besar, kecil, angka, dan simbol
- Panjang minimal 8 karakter

## 🏢 LOKASI KERJA

User demo tersebar di berbagai lokasi:
- **Central Office**: Super Admin, Admin, Safety Officer, Employee
- **North Mine Site**: Mine Manager, Production Supervisor, Operator
- **South Mine Site**: Shift Supervisor
- **Equipment Warehouse**: Equipment Manager, Technician

## 📊 TESTING SCENARIOS

### Scenario 1: Hierarchical Access Testing
```
Super Admin (Level 10) → Dapat mengakses semua fitur
Admin (Level 9) → Tidak dapat mengelola Super Admin
Mine Manager (Level 8) → Tidak dapat mengakses admin functions
... dan seterusnya
```

### Scenario 2: Permission-Based Testing
```
Equipment Manager → Dapat CRUD equipment, tidak dapat manage users
Safety Officer → Dapat CRUD safety data, tidak dapat manage production
Production Supervisor → Dapat manage production, tidak dapat delete equipment
```

### Scenario 3: Department-Based Testing
```
IT Management → Super Admin, Admin
Operations → Mine Manager, Shift Supervisor, Operator
Production → Production Supervisor
Maintenance → Equipment Manager, Technician
Safety → Safety Officer
Administration → General Employee
```

## 🛠️ CARA MENGGUNAKAN

### 1. Login Testing
```typescript
// Contoh login dengan user demo
const loginCredentials = {
  email: '<EMAIL>',
  password: 'Admin123!'
};
```

### 2. Role Testing
```typescript
// Cek role user setelah login
const { userRole, hasPermission } = useRole();
console.log('User Role:', userRole?.role_name);
console.log('Can manage users:', hasPermission('users', 'create'));
```

### 3. Permission Testing
```typescript
// Test permission untuk berbagai resource
const permissions = [
  { resource: 'production', action: 'create' },
  { resource: 'equipment', action: 'delete' },
  { resource: 'safety', action: 'update' },
  { resource: 'reports', action: 'export' }
];

permissions.forEach(({ resource, action }) => {
  console.log(`${resource}.${action}:`, hasPermission(resource, action));
});
```

## 📝 CATATAN PENTING

1. **Development Only**: User demo ini hanya untuk development dan testing
2. **Database Demo**: Data disimpan di tabel `demo_users`, bukan `users` utama
3. **No Auth Integration**: User demo tidak terintegrasi dengan Supabase Auth
4. **Manual Testing**: Gunakan untuk testing manual fitur role-based access
5. **Production Warning**: Jangan gunakan di production environment

## 🔍 QUERY DATABASE

### Melihat Semua User Demo
```sql
SELECT * FROM demo_user_roles ORDER BY role_level DESC;
```

### Melihat User Berdasarkan Level
```sql
SELECT * FROM demo_user_roles WHERE role_level >= 8; -- Manager level ke atas
```

### Melihat User Berdasarkan Departemen
```sql
SELECT * FROM demo_user_roles WHERE departemen = 'OPERATIONS';
```

### Test Permission Function
```sql
SELECT check_user_permission(
  (SELECT id FROM demo_users WHERE email = '<EMAIL>'),
  'users',
  'create'
);
```

## 🚀 NEXT STEPS

1. **Integration Testing**: Test integrasi dengan komponen React
2. **UI Testing**: Test tampilan role-based UI components
3. **Navigation Testing**: Test role-based navigation guards
4. **Permission Testing**: Test granular permissions
5. **Performance Testing**: Test query performance dengan banyak user

---

**Happy Testing! 🎉**

Gunakan user demo ini untuk menguji semua aspek sistem role-based access control dalam aplikasi Mining Operations.
