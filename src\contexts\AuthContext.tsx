import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../config/supabase';
import { Database } from '../types/database';

// Types
type UserProfile = Database['public']['Tables']['users']['Row'];

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  isTestingMode: boolean;
  isAdmin: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, userData: {
    full_name: string;
    phone?: string;
    departemen?: string;
    jabatan?: string;
    location_id: string;
    employee_id: string;
  }) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  enableTestingMode: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth Provider Component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isTestingMode, setIsTestingMode] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        loadUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      // Auth state changed - handle silently
      
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        await loadUserProfile(session.user.id);
      } else {
        setProfile(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAdminStatus = (userProfile: UserProfile | null): boolean => {
    if (!userProfile || !userProfile.is_active) return false;

    const { departemen, jabatan } = userProfile;

    // Check by departemen
    if (departemen && ['Administration', 'Management'].includes(departemen)) {
      return true;
    }

    // Check by jabatan (case-insensitive)
    if (jabatan) {
      const jabatanLower = jabatan.toLowerCase();
      if (jabatanLower.includes('admin') || jabatanLower.includes('manager')) {
        return true;
      }
    }

    return false;
  };

  const loadUserProfile = async (userId: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        // If profile doesn't exist, user might need to complete registration
        setProfile(null);
        setIsAdmin(false);
      } else {
        setProfile(data);
        setIsAdmin(checkAdminStatus(data));
      }
    } catch (error) {
      setProfile(null);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Profile will be loaded automatically by the auth state change listener
    } catch (error) {
      // Don't log to console to avoid showing error notifications
      // Let the LoginScreen handle the error display
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, userData: {
    full_name: string;
    phone?: string;
    departemen?: string;
    jabatan?: string;
    location_id: string;
    employee_id: string;
  }) => {
    try {
      setLoading(true);
      
      // Sign up with Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) throw error;

      // Create user profile
      if (data.user) {
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email,
            ...userData,
          });

        if (profileError) throw profileError;
      }

      // Profile will be loaded automatically by the auth state change listener
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Clear local state
      setSession(null);
      setUser(null);
      setProfile(null);
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Handle JWT expired by auto-logout
  const handleJWTExpired = async () => {
    await signOut();
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    try {
      if (!user) throw new Error('No user logged in');

      setLoading(true);

      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;

      setProfile(data);
      setIsAdmin(checkAdminStatus(data)); // Update admin status
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const enableTestingMode = () => {
    setIsTestingMode(true);
    setLoading(false);

    // Create a mock user for testing
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      aud: 'authenticated',
      role: 'authenticated',
      app_metadata: {},
      user_metadata: { full_name: 'Demo Supervisor' }
    } as User;

    const mockProfile = {
      id: 'test-user-id',
      email: '<EMAIL>',
      full_name: 'Demo Supervisor',
      phone: '******-0199',
      location_id: '550e8400-e29b-41d4-a716-446655440001',
      avatar_url: null,
      employee_id: 'DEMO001',
      hire_date: '2024-01-01',
      nik: '3201234567890001',
      departemen: 'Produksi',
      jabatan: 'Supervisor Shift',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as UserProfile;

    setUser(mockUser);
    setProfile(mockProfile);
    setIsAdmin(checkAdminStatus(mockProfile)); // Set admin status for mock profile
  };

  const value: AuthContextType = {
    session,
    user,
    profile,
    loading,
    isTestingMode,
    isAdmin,
    signIn,
    signUp,
    signOut,
    updateProfile,
    enableTestingMode,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Helper hooks for common auth checks
export const useRequireAuth = () => {
  const { user, loading } = useAuth();
  return { user, loading, isAuthenticated: !!user };
};

export const useUserRole = () => {
  const { profile } = useAuth();
  return profile?.departemen || null;
};

export const useUserLocation = () => {
  const { profile } = useAuth();
  return profile?.location_id || null;
};
