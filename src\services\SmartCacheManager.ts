import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

export interface CacheStrategy {
  key: string;
  ttl: number;
  priority: 'high' | 'medium' | 'low';
  refreshOnExpiry?: boolean;
  networkDependent?: boolean;
}

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  cacheSize: number;
  lastCleanup: number;
}

export class SmartCacheManager {
  private static instance: SmartCacheManager;
  private metrics: CacheMetrics;
  private isOnline: boolean = true;
  private maxCacheSize: number = 50 * 1024 * 1024; // 50MB
  private cleanupThreshold: number = 0.8; // Cleanup when 80% full

  private constructor() {
    this.metrics = {
      hitRate: 0,
      missRate: 0,
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0,
      cacheSize: 0,
      lastCleanup: Date.now()
    };
    this.initializeNetworkMonitoring();
  }

  static getInstance(): SmartCacheManager {
    if (!SmartCacheManager.instance) {
      SmartCacheManager.instance = new SmartCacheManager();
    }
    return SmartCacheManager.instance;
  }

  private async initializeNetworkMonitoring(): Promise<void> {
    try {
      const state = await NetInfo.fetch();
      this.isOnline = state.isConnected ?? false;

      NetInfo.addEventListener(state => {
        this.isOnline = state.isConnected ?? false;
        console.log(`🌐 Network status: ${this.isOnline ? 'ONLINE' : 'OFFLINE'}`);
      });
    } catch (error) {
      console.error('❌ Failed to initialize network monitoring:', error);
    }
  }

  // Smart caching with strategy
  async get<T>(key: string, strategy: CacheStrategy): Promise<T | null> {
    this.metrics.totalRequests++;
    
    try {
      const cacheKey = `smart_cache_${key}`;
      const cached = await AsyncStorage.getItem(cacheKey);
      
      if (!cached) {
        this.metrics.totalMisses++;
        this.updateMetrics();
        return null;
      }

      const cacheItem = JSON.parse(cached);
      const now = Date.now();
      
      // Check if cache is expired
      if (now - cacheItem.timestamp > strategy.ttl) {
        console.log(`⏰ Cache expired for key: ${key}`);
        
        // If offline and cache exists, return stale data
        if (!this.isOnline && strategy.networkDependent) {
          console.log(`📱 Returning stale data (offline mode): ${key}`);
          this.metrics.totalHits++;
          this.updateMetrics();
          return cacheItem.data;
        }
        
        // Remove expired cache
        await AsyncStorage.removeItem(cacheKey);
        this.metrics.totalMisses++;
        this.updateMetrics();
        return null;
      }

      // Cache hit
      console.log(`✅ Cache hit: ${key}`);
      this.metrics.totalHits++;
      this.updateMetrics();
      return cacheItem.data;
      
    } catch (error) {
      console.error(`❌ Cache get error for key ${key}:`, error);
      this.metrics.totalMisses++;
      this.updateMetrics();
      return null;
    }
  }

  async set<T>(key: string, data: T, strategy: CacheStrategy): Promise<void> {
    try {
      const cacheKey = `smart_cache_${key}`;
      const cacheItem = {
        data,
        timestamp: Date.now(),
        strategy,
        size: JSON.stringify(data).length
      };

      await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheItem));
      console.log(`💾 Cached: ${key} (Priority: ${strategy.priority}, TTL: ${strategy.ttl/1000/60}min)`);
      
      // Update cache size and check if cleanup needed
      await this.updateCacheSize();
      
    } catch (error) {
      console.error(`❌ Cache set error for key ${key}:`, error);
    }
  }

  // Intelligent cache with fallback
  async getOrFetch<T>(
    key: string, 
    fetchFunction: () => Promise<T>, 
    strategy: CacheStrategy
  ): Promise<T> {
    // Try cache first
    const cached = await this.get<T>(key, strategy);
    if (cached !== null) {
      return cached;
    }

    // Cache miss - fetch data
    console.log(`🔄 Cache miss - fetching data: ${key}`);
    
    try {
      const data = await fetchFunction();
      
      // Cache the result
      await this.set(key, data, strategy);
      
      return data;
    } catch (error) {
      console.error(`❌ Fetch failed for key ${key}:`, error);
      
      // If offline, try to return any stale data
      if (!this.isOnline) {
        const staleData = await this.getStaleData<T>(key);
        if (staleData) {
          console.log(`📱 Returning stale data due to fetch failure: ${key}`);
          return staleData;
        }
      }
      
      throw error;
    }
  }

  private async getStaleData<T>(key: string): Promise<T | null> {
    try {
      const cacheKey = `smart_cache_${key}`;
      const cached = await AsyncStorage.getItem(cacheKey);
      
      if (cached) {
        const cacheItem = JSON.parse(cached);
        return cacheItem.data;
      }
      
      return null;
    } catch (error) {
      console.error(`❌ Failed to get stale data for key ${key}:`, error);
      return null;
    }
  }

  // Cache cleanup and optimization
  async cleanup(): Promise<void> {
    console.log('🧹 Starting cache cleanup...');
    
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('smart_cache_'));
      
      const itemsToRemove: string[] = [];
      const now = Date.now();
      
      for (const key of cacheKeys) {
        try {
          const cached = await AsyncStorage.getItem(key);
          if (!cached) continue;
          
          const cacheItem = JSON.parse(cached);
          
          // Remove expired items
          if (now - cacheItem.timestamp > cacheItem.strategy.ttl) {
            itemsToRemove.push(key);
          }
        } catch (error) {
          // Remove corrupted cache items
          itemsToRemove.push(key);
        }
      }
      
      if (itemsToRemove.length > 0) {
        await AsyncStorage.multiRemove(itemsToRemove);
        console.log(`🗑️ Removed ${itemsToRemove.length} expired/corrupted cache items`);
      }
      
      // If still over threshold, remove low priority items
      await this.updateCacheSize();
      if (this.metrics.cacheSize > this.maxCacheSize * this.cleanupThreshold) {
        await this.cleanupByPriority();
      }
      
      this.metrics.lastCleanup = now;
      
    } catch (error) {
      console.error('❌ Cache cleanup failed:', error);
    }
  }

  private async cleanupByPriority(): Promise<void> {
    console.log('🎯 Cleaning up by priority...');
    
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('smart_cache_'));
      
      const lowPriorityItems: string[] = [];
      
      for (const key of cacheKeys) {
        try {
          const cached = await AsyncStorage.getItem(key);
          if (!cached) continue;
          
          const cacheItem = JSON.parse(cached);
          
          if (cacheItem.strategy.priority === 'low') {
            lowPriorityItems.push(key);
          }
        } catch (error) {
          // Remove corrupted items
          lowPriorityItems.push(key);
        }
      }
      
      if (lowPriorityItems.length > 0) {
        await AsyncStorage.multiRemove(lowPriorityItems);
        console.log(`🗑️ Removed ${lowPriorityItems.length} low priority cache items`);
      }
      
    } catch (error) {
      console.error('❌ Priority cleanup failed:', error);
    }
  }

  private async updateCacheSize(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('smart_cache_'));
      
      let totalSize = 0;
      for (const key of cacheKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      }
      
      this.metrics.cacheSize = totalSize;
      
      // Auto cleanup if over threshold
      if (totalSize > this.maxCacheSize * this.cleanupThreshold) {
        console.log(`⚠️ Cache size (${(totalSize/1024/1024).toFixed(2)}MB) over threshold, triggering cleanup`);
        await this.cleanup();
      }
      
    } catch (error) {
      console.error('❌ Failed to update cache size:', error);
    }
  }

  private updateMetrics(): void {
    this.metrics.hitRate = this.metrics.totalRequests > 0 
      ? (this.metrics.totalHits / this.metrics.totalRequests) * 100 
      : 0;
    this.metrics.missRate = 100 - this.metrics.hitRate;
  }

  // Public methods for cache management
  async invalidate(pattern: string): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => 
        key.startsWith('smart_cache_') && key.includes(pattern)
      );
      
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
        console.log(`🗑️ Invalidated ${cacheKeys.length} cache entries matching: ${pattern}`);
      }
    } catch (error) {
      console.error(`❌ Failed to invalidate cache pattern ${pattern}:`, error);
    }
  }

  async clear(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('smart_cache_'));
      
      await AsyncStorage.multiRemove(cacheKeys);
      console.log(`🗑️ Cleared all ${cacheKeys.length} cache entries`);
      
      // Reset metrics
      this.metrics = {
        hitRate: 0,
        missRate: 0,
        totalRequests: 0,
        totalHits: 0,
        totalMisses: 0,
        cacheSize: 0,
        lastCleanup: Date.now()
      };
    } catch (error) {
      console.error('❌ Failed to clear cache:', error);
    }
  }

  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  // Preload strategies for mining operations
  async preloadCriticalData(): Promise<void> {
    console.log('🚀 Preloading critical mining data...');
    
    // This would be called during app initialization
    // Implementation depends on your specific data needs
  }
}

export default SmartCacheManager;
