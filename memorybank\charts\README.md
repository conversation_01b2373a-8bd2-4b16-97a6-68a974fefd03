# Chart System Documentation

## Overview
Comprehensive documentation for the chart system implementation in MiningOperationsApp, including horizontal scrolling, label optimization, and visual improvements.

## Chart System Architecture

### Core Components
1. **ScrollableChart Component** - Main chart wrapper with scrolling capability
2. **Dynamic Width Calculation** - Automatic sizing based on data points
3. **Label Optimization System** - Prevents text overlap on mobile devices
4. **Chart Configuration** - Type-specific styling and behavior

## Implementation Files
- [Scrollable Charts Implementation](./scrollable-charts.md)
- [Chart Label Optimization](./chart-labels.md)
- [Daily Chart Sorting](./daily-chart-sorting.md)
- [Production Calendar Model](./production-calendar-model.md)
- [Chart Appearance Configuration](./chart-appearance.md)
- [Testing and Verification](./chart-testing.md)

## Key Features Implemented

### 1. Horizontal Scrolling ✅
- **Dynamic Width Calculation**: `dataPoints × minWidthPerPoint`
- **Automatic Scrolling**: Enabled when content exceeds screen width
- **Smooth Scrolling**: Native ScrollView with proper indicators
- **Responsive Design**: Works on all screen sizes

### 2. Label Optimization ✅
- **Daily Labels**: "12", "13", "14" (day numbers only)
- **Weekly Labels**: "28", "29", "30" (week numbers only)
- **Monthly Labels**: "Jan", "Feb", "Mar" (3-letter abbreviations)
- **Minimum Spacing**: 50px between labels to prevent overlap

### 3. Daily Chart Chronological Sorting ✅
- **Proper Date Ordering**: Daily data sorted chronologically (oldest to newest)
- **Database Alignment**: Consistent with Supabase date field ordering
- **Date Filtering**: Only displays data up to current date
- **Simplified Logic**: Streamlined data processing without unnecessary reversals

### 4. Production Calendar Model ✅
- **Custom Month Start Dates**: Production months start on custom dates (e.g., July starts June 30)
- **Two-Stage Filtering**: Primary filter by monthly field + secondary filter by date range
- **Variable Data Points**: Chart data count varies based on production calendar and current date
- **Production Alignment**: Aligns with operational calendar system rather than standard calendar

### 4. Visual Improvements ✅
- **Removed Bezier Curves**: Straight lines for better readability
- **Eliminated Grid Lines**: Clean background without distractions
- **Maintained Data Points**: Visible dots on all chart lines
- **Professional Appearance**: Minimal, clean design

### 5. Chart Types Supported ✅
- **Trends Chart**: Production trends with blue color scheme and chronological ordering
- **Impact Chart**: Rain impact data with orange color scheme
- **Fuel Chart**: Fuel consumption with info color scheme
- **Daily Charts**: Proper chronological sorting for all chart types in daily view

## Technical Specifications

### Chart Width Calculation
```typescript
const calculateChartWidth = (dataPoints: number, maxLabelLength: number = 3): number => {
  const baseWidth = screenWidth - CHART_CONTAINER_PADDING;
  const minWidthPerPoint = Math.max(MIN_LABEL_SPACING, maxLabelLength * 8 + 20);
  const calculatedWidth = dataPoints * minWidthPerPoint;
  
  return Math.max(baseWidth, calculatedWidth);
};
```

### Constants
- `CHART_CONTAINER_PADDING`: 48px (total container padding)
- `MIN_LABEL_SPACING`: 50px (minimum space between labels)
- `SCROLL_INDICATOR_HEIGHT`: 4px (scroll indicator height)

### Chart Behavior Matrix

| Dataset Size | Data Points | Scrolling | Example Use Case |
|-------------|-------------|-----------|------------------|
| Small | ≤8 points | No | 7 daily data points |
| Medium | 9-15 points | Maybe | 12 weekly data points |
| Large | >15 points | Yes | 24 monthly data points |
| Very Large | >30 points | Yes | 30+ daily data points |

## Chart Configuration

### Trends Chart
```typescript
{
  backgroundColor: Colors.surface,
  color: () => Colors.primary,
  bezier: false,
  withDots: true,
  withShadow: false,
  withInnerLines: false,
  withOuterLines: false,
}
```

### Impact Chart
```typescript
{
  backgroundColor: Colors.surface,
  color: () => Colors.warning,
  yAxisSuffix: "h",
  bezier: false,
  withDots: true,
  withShadow: false,
  withInnerLines: false,
  withOuterLines: false,
}
```

### Fuel Chart
```typescript
{
  backgroundColor: Colors.surface,
  color: () => Colors.info,
  bezier: false,
  withDots: true,
  withShadow: false,
  withInnerLines: false,
  withOuterLines: false,
}
```

## Data Format Requirements

### Expected Input Format
```typescript
{
  labels: string[],        // X-axis labels
  datasets: [{
    data: number[],        // Y-axis data points
    color: () => string,   // Line color function
  }],
  legend: string[]         // Legend labels
}
```

### Label Processing
- **Daily**: Extract day number from date string
- **Weekly**: Extract week number from database field
- **Monthly**: Convert full month name to 3-letter abbreviation

## Performance Optimizations

### Rendering Efficiency
- Only enable scrolling when necessary
- Lazy loading for large datasets
- Optimized re-rendering with React.memo
- Efficient width calculations

### Memory Management
- Proper cleanup of scroll listeners
- Optimized chart data processing
- Minimal re-renders on data updates

## Testing Coverage

### Test Scenarios
1. **Small Dataset (7 points)**: No scrolling, fits screen
2. **Medium Dataset (12 points)**: Conditional scrolling
3. **Large Dataset (24 points)**: Always scrollable
4. **Very Large Dataset (30+ points)**: Extended scrolling

### Verification Points
- ✅ Dynamic width calculation accuracy
- ✅ Label spacing prevents overlap
- ✅ Scroll indicators appear correctly
- ✅ Chart appearance matches specifications
- ✅ Performance remains smooth with large datasets

## Integration Points

### ProductionOverviewScreen.tsx
- Main implementation file
- ScrollableChart component integration
- Chart type switching logic
- Data processing and formatting

### Database Integration
- Real production metrics data
- Chart data aggregation
- Period-based data filtering
- Performance optimization for large datasets

## Future Enhancements

### Planned Features
1. **Zoom Functionality**: Pinch-to-zoom for detailed inspection
2. **Data Point Interaction**: Tap to highlight specific points
3. **Animated Scrolling**: Auto-scroll to latest data
4. **Scroll Position Memory**: Remember position when switching charts
5. **Export Functionality**: Save charts as images
6. **Advanced Filtering**: Date range and data type filters

### Performance Improvements
1. **Virtual Scrolling**: For extremely large datasets
2. **Chart Caching**: Cache rendered charts for faster switching
3. **Progressive Loading**: Load data in chunks
4. **Background Processing**: Process chart data in background threads

---
*Chart system documentation following Cortex 7 standards for comprehensive development reference.*
