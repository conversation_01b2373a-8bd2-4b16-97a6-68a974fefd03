const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Disable TypeScript checking during development
config.transformer.enableBabelRCLookup = false;
config.transformer.enableBabelRuntime = false;

// Add resolver for packages
config.resolver.nodeModulesPaths = [
  './node_modules',
  './packages/core/node_modules'
];

// Ignore TypeScript errors during bundling
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Handle SQLite web compatibility issues
config.resolver.alias = {
  ...config.resolver.alias,
};

// Add asset extensions for web
config.resolver.assetExts = [
  ...config.resolver.assetExts,
  'wasm'
];

module.exports = config;
