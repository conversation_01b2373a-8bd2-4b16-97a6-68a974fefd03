-- Fix Database Formulas for FR and SR
-- This script updates the database to use correct mining industry formulas

-- =====================================================
-- 1. Fix daily_production_metrics table if it exists
-- =====================================================

-- Check if we need to update existing calculated columns
DO $$
BEGIN
    -- Update fuel_ratio calculation to use correct formula
    -- FR = Fuel / (OB + (Ore / 3.39))
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'daily_production_metrics' 
        AND column_name = 'fuel_ratio'
    ) THEN
        -- Drop existing fuel_ratio if it's a generated column
        BEGIN
            ALTER TABLE daily_production_metrics DROP COLUMN IF EXISTS fuel_ratio;
        EXCEPTION WHEN OTHERS THEN
            -- Column might not exist or might not be droppable
            NULL;
        END;
        
        -- Add corrected fuel_ratio column
        ALTER TABLE daily_production_metrics 
        ADD COLUMN fuel_ratio DECIMAL(8,4) GENERATED ALWAYS AS (
            CASE WHEN (actual_ob + (actual_ore / 3.39)) > 0 
            THEN ROUND(actual_fuel / (actual_ob + (actual_ore / 3.39)), 4)
            ELSE 0 END
        ) STORED;
        
        RAISE NOTICE 'Updated fuel_ratio formula in daily_production_metrics';
    END IF;

    -- Update stripping_ratio calculation (should already be correct, but ensure consistency)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'daily_production_metrics' 
        AND column_name = 'stripping_ratio'
    ) THEN
        -- Drop existing stripping_ratio if it's a generated column
        BEGIN
            ALTER TABLE daily_production_metrics DROP COLUMN IF EXISTS stripping_ratio;
        EXCEPTION WHEN OTHERS THEN
            NULL;
        END;
        
        -- Add corrected stripping_ratio column
        ALTER TABLE daily_production_metrics 
        ADD COLUMN stripping_ratio DECIMAL(8,4) GENERATED ALWAYS AS (
            CASE WHEN actual_ore > 0 
            THEN ROUND(actual_ob / actual_ore, 4)
            ELSE 0 END
        ) STORED;
        
        RAISE NOTICE 'Updated stripping_ratio formula in daily_production_metrics';
    END IF;

    -- Add total_material column with correct calculation
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'daily_production_metrics' 
        AND column_name = 'total_material'
    ) THEN
        ALTER TABLE daily_production_metrics 
        ADD COLUMN total_material DECIMAL(12,2) GENERATED ALWAYS AS (
            ROUND(actual_ob + (actual_ore / 3.39), 2)
        ) STORED;
        
        RAISE NOTICE 'Added total_material column to daily_production_metrics';
    END IF;
END $$;

-- =====================================================
-- 2. Create/Update Mining Calculation Functions
-- =====================================================

-- Function to calculate Strip Ratio
CREATE OR REPLACE FUNCTION calculate_strip_ratio(
    overburden DECIMAL,
    ore DECIMAL
) RETURNS DECIMAL AS $$
BEGIN
    IF ore <= 0 THEN
        RETURN 0;
    END IF;
    RETURN ROUND(overburden / ore, 4);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to calculate Fuel Ratio (corrected formula)
CREATE OR REPLACE FUNCTION calculate_fuel_ratio(
    fuel DECIMAL,
    overburden DECIMAL,
    ore DECIMAL
) RETURNS DECIMAL AS $$
DECLARE
    total_material DECIMAL;
BEGIN
    total_material := overburden + (ore / 3.39);
    IF total_material <= 0 THEN
        RETURN 0;
    END IF;
    RETURN ROUND(fuel / total_material, 4);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to calculate Total Material
CREATE OR REPLACE FUNCTION calculate_total_material(
    overburden DECIMAL,
    ore DECIMAL
) RETURNS DECIMAL AS $$
BEGIN
    RETURN ROUND(overburden + (ore / 3.39), 2);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to calculate Achievement Percentage
CREATE OR REPLACE FUNCTION calculate_achievement(
    actual_value DECIMAL,
    plan_value DECIMAL
) RETURNS DECIMAL AS $$
BEGIN
    IF plan_value <= 0 THEN
        RETURN 0;
    END IF;
    RETURN ROUND((actual_value / plan_value) * 100, 1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =====================================================
-- 3. Create View for Standardized Mining Metrics
-- =====================================================

CREATE OR REPLACE VIEW mining_metrics_standardized AS
SELECT 
    id,
    date,
    location_id,
    
    -- Raw data
    actual_ob,
    plan_ob,
    actual_ore,
    plan_ore,
    actual_fuel,
    plan_fuel,
    
    -- Standardized calculations using correct formulas
    calculate_strip_ratio(actual_ob, actual_ore) AS strip_ratio_actual,
    calculate_strip_ratio(plan_ob, plan_ore) AS strip_ratio_plan,
    
    calculate_fuel_ratio(actual_fuel, actual_ob, actual_ore) AS fuel_ratio_actual,
    calculate_fuel_ratio(plan_fuel, plan_ob, plan_ore) AS fuel_ratio_plan,
    
    calculate_total_material(actual_ob, actual_ore) AS total_material_actual,
    calculate_total_material(plan_ob, plan_ore) AS total_material_plan,
    
    -- Achievement percentages
    calculate_achievement(actual_ob, plan_ob) AS ob_achievement_percent,
    calculate_achievement(actual_ore, plan_ore) AS ore_achievement_percent,
    calculate_achievement(actual_fuel, plan_fuel) AS fuel_achievement_percent,
    
    -- Variances
    calculate_strip_ratio(actual_ob, actual_ore) - calculate_strip_ratio(plan_ob, plan_ore) AS sr_variance,
    calculate_fuel_ratio(actual_fuel, actual_ob, actual_ore) - calculate_fuel_ratio(plan_fuel, plan_ob, plan_ore) AS fr_variance,
    
    created_at,
    updated_at
FROM daily_production_metrics
WHERE actual_ob IS NOT NULL 
  AND actual_ore IS NOT NULL 
  AND actual_fuel IS NOT NULL;

-- =====================================================
-- 4. Create Trigger to Validate Data
-- =====================================================

CREATE OR REPLACE FUNCTION validate_mining_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate Strip Ratio range (0.5 - 15.0 is typical for mining)
    IF NEW.actual_ore > 0 THEN
        DECLARE
            sr DECIMAL := NEW.actual_ob / NEW.actual_ore;
        BEGIN
            IF sr < 0.1 OR sr > 20.0 THEN
                RAISE WARNING 'Strip Ratio %.2f is outside typical range (0.1-20.0) for record %', sr, NEW.id;
            END IF;
        END;
    END IF;
    
    -- Validate Fuel Ratio range (0.5 - 5.0 L/Bcm is typical)
    DECLARE
        total_mat DECIMAL := NEW.actual_ob + (NEW.actual_ore / 3.39);
        fr DECIMAL;
    BEGIN
        IF total_mat > 0 THEN
            fr := NEW.actual_fuel / total_mat;
            IF fr > 10.0 THEN
                RAISE WARNING 'Fuel Ratio %.2f L/Bcm seems high (typical: 0.5-5.0) for record %', fr, NEW.id;
            END IF;
        END IF;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to daily_production_metrics if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'daily_production_metrics') THEN
        DROP TRIGGER IF EXISTS validate_mining_data_trigger ON daily_production_metrics;
        CREATE TRIGGER validate_mining_data_trigger
            BEFORE INSERT OR UPDATE ON daily_production_metrics
            FOR EACH ROW EXECUTE FUNCTION validate_mining_data();
        RAISE NOTICE 'Created validation trigger for daily_production_metrics';
    END IF;
END $$;

-- =====================================================
-- 5. Comments and Documentation
-- =====================================================

COMMENT ON FUNCTION calculate_strip_ratio IS 'Calculate Strip Ratio: SR = Overburden (Bcm) / Ore (tons)';
COMMENT ON FUNCTION calculate_fuel_ratio IS 'Calculate Fuel Ratio: FR = Fuel (L) / (OB (Bcm) + (Ore (tons) / 3.39)) - Result in L/Bcm';
COMMENT ON FUNCTION calculate_total_material IS 'Calculate Total Material: OB (Bcm) + (Ore (tons) / 3.39) - Result in Bcm';
COMMENT ON FUNCTION calculate_achievement IS 'Calculate Achievement: (Actual / Plan) * 100';

COMMENT ON VIEW mining_metrics_standardized IS 'Standardized mining metrics using correct industry formulas for SR and FR';

-- =====================================================
-- 6. Final Status Report
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=== MINING FORMULAS UPDATE COMPLETED ===';
    RAISE NOTICE 'Strip Ratio (SR): Overburden (Bcm) / Ore (tons)';
    RAISE NOTICE 'Fuel Ratio (FR): Fuel (L) / (OB (Bcm) + (Ore (tons) / 3.39))';
    RAISE NOTICE 'Total Material: OB (Bcm) + (Ore (tons) / 3.39)';
    RAISE NOTICE 'Ore Density Factor: 3.39 (tons to Bcm conversion)';
    RAISE NOTICE '============================================';
END $$;
