#!/usr/bin/env ts-node

import { DatabaseService } from '../src/services/supabase';

// Generate comprehensive dummy data for 60 days
const generateDummyData = (): any[] => {
  const data: any[] = [];
  
  // Generate data from 60 days ago to today (July 23, 2025)
  const today = new Date('2025-07-23');
  const sixtyDaysAgo = new Date(today);
  sixtyDaysAgo.setDate(today.getDate() - 60);
  
  console.log(`Generating dummy data from ${sixtyDaysAgo.toISOString().split('T')[0]} to ${today.toISOString().split('T')[0]}`);

  for (let d = new Date(sixtyDaysAgo); d <= today; d.setDate(d.getDate() + 1)) {
    const currentDate = new Date(d);
    const dateStr = currentDate.toISOString().split('T')[0];
    
    // Determine production month based on production calendar logic
    let monthlyField = '';
    let weekNumber = 0;
    
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    
    // Production calendar logic for 2025
    if (year === 2025) {
      if ((month === 5 && day >= 30) || (month === 6 && day <= 29)) {
        monthlyField = 'June 2025';
        weekNumber = Math.floor((day + 30) / 7) + 21; // Week 22-25
      } else if ((month === 6 && day >= 30) || (month === 7 && day <= 29)) {
        monthlyField = 'July 2025';
        weekNumber = Math.floor((day + (month === 6 ? 0 : 30)) / 7) + 26; // Week 26-30
      } else if ((month === 7 && day >= 30) || (month === 8 && day <= 29)) {
        monthlyField = 'August 2025';
        weekNumber = Math.floor((day + (month === 7 ? 0 : 30)) / 7) + 30; // Week 31-34
      } else {
        // Fallback
        monthlyField = `${currentDate.toLocaleDateString('en-US', { month: 'long' })} ${year}`;
        const startOfYear = new Date(year, 0, 1);
        const pastDaysOfYear = (currentDate.getTime() - startOfYear.getTime()) / 86400000;
        weekNumber = Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
      }
    }

    // Generate realistic production data with patterns
    const dayOfYear = Math.floor((currentDate.getTime() - new Date(currentDate.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
    const dayOfWeek = currentDate.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    // Base production values
    let baseOb = 6000;
    let baseOre = 3500;
    let baseFuel = 550;
    
    // Weekend reduction
    if (isWeekend) {
      baseOb *= 0.7;
      baseOre *= 0.7;
      baseFuel *= 0.8;
    }
    
    // Seasonal variation
    const seasonalFactor = 1 + 0.2 * Math.sin((dayOfYear / 365) * 2 * Math.PI + Math.PI);
    baseOb *= seasonalFactor;
    baseOre *= seasonalFactor;
    
    // Weekly trend
    const weekOfYear = Math.floor(dayOfYear / 7);
    const trendFactor = 1 + (weekOfYear * 0.002);
    baseOb *= trendFactor;
    baseOre *= trendFactor;
    
    // Daily variation
    const obVariation = (Math.random() - 0.5) * 0.3;
    const oreVariation = (Math.random() - 0.5) * 0.3;
    const fuelVariation = (Math.random() - 0.5) * 0.25;
    
    const actualOb = Math.round(baseOb * (1 + obVariation));
    const actualOre = Math.round(baseOre * (1 + oreVariation));
    const actualFuel = Math.round(baseFuel * (1 + fuelVariation));

    data.push({
      date: dateStr,
      monthly: monthlyField,
      week: weekNumber,
      actual_ob: actualOb,
      actual_ore: actualOre,
      plan_ob: 8000,
      plan_ore: 5000,
      actual_fuel: actualFuel,
      plan_fuel: 600,
      actual_rain: Math.floor(Math.random() * 5),
      plan_rain: 2,
      actual_slippery: Math.floor(Math.random() * 3),
      plan_slippery: 1,
      location_id: '550e8400-e29b-41d4-a716-446655440001',
      notes: `Production data for ${dateStr}`
    });
  }

  // Add specific recent data points for consistency
  const recentDataPoints = [
    {
      date: '2025-07-20',
      monthly: 'July 2025',
      week: 29,
      actual_ob: 6200,
      actual_ore: 3600,
      plan_ob: 8000,
      plan_ore: 5000,
      actual_fuel: 580,
      plan_fuel: 600,
      actual_rain: 1,
      plan_rain: 2,
      actual_slippery: 0,
      plan_slippery: 1,
      location_id: '550e8400-e29b-41d4-a716-446655440001',
      notes: 'Good production day - clear weather'
    },
    {
      date: '2025-07-21',
      monthly: 'July 2025',
      week: 29,
      actual_ob: 5800,
      actual_ore: 3400,
      plan_ob: 8000,
      plan_ore: 5000,
      actual_fuel: 560,
      plan_fuel: 600,
      actual_rain: 2,
      plan_rain: 2,
      actual_slippery: 1,
      plan_slippery: 1,
      location_id: '550e8400-e29b-41d4-a716-446655440001',
      notes: 'Light rain affected operations'
    },
    {
      date: '2025-07-22',
      monthly: 'July 2025',
      week: 30,
      actual_ob: 6500,
      actual_ore: 3800,
      plan_ob: 8000,
      plan_ore: 5000,
      actual_fuel: 620,
      plan_fuel: 600,
      actual_rain: 0,
      plan_rain: 2,
      actual_slippery: 0,
      plan_slippery: 1,
      location_id: '550e8400-e29b-41d4-a716-446655440001',
      notes: 'Excellent conditions - exceeded fuel plan'
    },
    {
      date: '2025-07-23',
      monthly: 'July 2025',
      week: 30,
      actual_ob: 6100,
      actual_ore: 3550,
      plan_ob: 8000,
      plan_ore: 5000,
      actual_fuel: 590,
      plan_fuel: 600,
      actual_rain: 0,
      plan_rain: 2,
      actual_slippery: 0,
      plan_slippery: 1,
      location_id: '550e8400-e29b-41d4-a716-446655440001',
      notes: 'Current day production - on track'
    }
  ];

  // Replace recent data points
  recentDataPoints.forEach(recentPoint => {
    const existingIndex = data.findIndex(item => item.date === recentPoint.date);
    if (existingIndex >= 0) {
      data[existingIndex] = recentPoint;
    } else {
      data.push(recentPoint);
    }
  });

  // Sort by date
  data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  return data;
};

// Main function to populate data
async function populateDummyData() {
  try {
    console.log('🚀 Starting dummy data population to Supabase...');
    
    // Generate dummy data
    const dummyData = generateDummyData();
    console.log(`📊 Generated ${dummyData.length} data points`);
    
    // Show sample of data
    console.log('\n📋 Sample data points:');
    dummyData.slice(-5).forEach(item => {
      const stripRatio = (item.actual_ob / item.actual_ore).toFixed(2);
      console.log(`  ${item.date}: OB=${item.actual_ob}, Ore=${item.actual_ore}, Strip Ratio=${stripRatio}, Fuel=${item.actual_fuel}L`);
    });

    console.log('\n⏳ Uploading to Supabase...');
    
    // Upload in batches to avoid timeout
    const batchSize = 50;
    let totalUploaded = 0;
    
    for (let i = 0; i < dummyData.length; i += batchSize) {
      const batch = dummyData.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(dummyData.length / batchSize);
      
      console.log(`📤 Uploading batch ${batchNumber}/${totalBatches} (${batch.length} records)...`);
      
      try {
        const result = await DatabaseService.bulkCreateDailyProductionMetrics(batch);
        totalUploaded += batch.length;
        console.log(`✅ Batch ${batchNumber} uploaded successfully`);
      } catch (error) {
        console.error(`❌ Batch ${batchNumber} failed:`, error);
        // Continue with next batch
      }
    }
    
    console.log(`\n🎉 Upload completed! Total records uploaded: ${totalUploaded}/${dummyData.length}`);
    
  } catch (error) {
    console.error('❌ Error populating dummy data:', error);
    throw error;
  }
}

// Function to check database contents
async function checkDatabase() {
  try {
    console.log('\n🔍 Checking Supabase database contents...');
    
    // Get recent data
    const endDate = '2025-07-23';
    const startDate = '2025-06-01';
    
    console.log(`📅 Querying data from ${startDate} to ${endDate}...`);
    
    const data = await DatabaseService.getDailyProductionMetrics(
      startDate,
      endDate,
      '550e8400-e29b-41d4-a716-446655440001'
    );
    
    console.log(`📊 Found ${data.length} records in database`);
    
    if (data.length > 0) {
      console.log('\n📋 Recent 5 records:');
      data.slice(-5).forEach((item: any) => {
        const stripRatio = item.actual_ore > 0 ? (item.actual_ob / item.actual_ore).toFixed(2) : '0.00';
        console.log(`  ${item.date}: OB=${item.actual_ob}, Ore=${item.actual_ore}, Strip Ratio=${stripRatio}, Week=${item.week}`);
      });
      
      // Calculate some statistics
      const totalOb = data.reduce((sum: number, item: any) => sum + (item.actual_ob || 0), 0);
      const totalOre = data.reduce((sum: number, item: any) => sum + (item.actual_ore || 0), 0);
      const avgStripRatio = totalOre > 0 ? (totalOb / totalOre).toFixed(2) : '0.00';
      
      console.log('\n📈 Statistics:');
      console.log(`  Total Overburden: ${totalOb.toLocaleString()} Bcm`);
      console.log(`  Total Ore: ${totalOre.toLocaleString()} tons`);
      console.log(`  Average Strip Ratio: ${avgStripRatio}`);
      console.log(`  Date Range: ${data[0].date} to ${data[data.length - 1].date}`);
    } else {
      console.log('⚠️  No data found in database');
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
    throw error;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  try {
    if (args.includes('--check-only')) {
      await checkDatabase();
    } else if (args.includes('--populate-only')) {
      await populateDummyData();
    } else {
      // Default: populate then check
      await populateDummyData();
      await checkDatabase();
    }
    
    console.log('\n✅ Script completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Script failed:', error);
    process.exit(1);
  }
}

// Export functions for use in other scripts
export { populateDummyData, checkDatabase, generateDummyData };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
