---
type: "agent_requested"
description: "Example description"
---
General Rule
when a task requires building or modifying a user interface, you must use tools available in the shadcn-ui mcp server
Planning Rule
when planning a UI build using shadcn :
1.	discover assets: first, use list_components() and list_blocks() to see all available assets in the MCP Server
2.	Map request to assets : Analyze the user's request and map the requires UI elemts to the available components and blocks
3.	prioritize blocks : you should prioritize using blocks ( get_blocks ) wherever possible for common, complex UI patterns (e.g., login pages, calenders,dashboards). Blocks provide more
structure and accelerate development. use individual components (get_components) for smaller, more specific needs.
Implementation rule
when implementing the UI :
1.	get a demo first : before using a component, you must call the get_component_demo(component_name) tool. this is critical for understanding how the component is used, its required props and its structure
2.	retrieve the code :
⦁	for a single component,call get_component(component_name)
⦁	for a composite block, call get_block(block_name)
3.	implement correctly : integragte the retrieved code into application, customizing it with the necessary props and logic to fulfill the user's request