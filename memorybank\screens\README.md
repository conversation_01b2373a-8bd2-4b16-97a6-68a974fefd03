# Screen Documentation

## Cortex 7 Metadata
- **Document Type**: Screen Implementation Guide
- **Component**: Screen Components and Navigation
- **Technology**: React Native, React Navigation, TypeScript
- **Tags**: `#screens` `#navigation` `#ui-screens` `#implementation`
- **Last Updated**: 2025-01-19
- **Status**: Active ✅
- **Source**: Consolidated from memory-bank/screens

## Overview
Comprehensive documentation for all screen components in the MiningOperationsApp, including implementation details, navigation patterns, and screen-specific features.

## Screen Architecture

### Screen Categories

#### 1. Production Screens ✅
- **ProductionOverviewScreen**: Main dashboard with charts and metrics
- **ProductionDetailsScreen**: Detailed production data and analytics
- **ProductionReportsScreen**: Historical reports and data export

#### 2. Equipment Screens 🔄
- **EquipmentManagementScreen**: Equipment tracking and status
- **EquipmentDetailsScreen**: Individual equipment information
- **MaintenanceScheduleScreen**: Maintenance planning and tracking

#### 3. Safety Screens 🔄
- **SafetyReportingScreen**: Incident reporting and tracking
- **SafetyDashboardScreen**: Safety metrics and analytics
- **SafetyTrainingScreen**: Training records and compliance

#### 4. Settings Screens 🔄
- **SettingsScreen**: App configuration and preferences
- **ProfileScreen**: User profile and account settings
- **NotificationScreen**: Notification preferences

## ProductionOverviewScreen Implementation

### Core Features ✅
- **Horizontal Scrollable Charts**: Dynamic chart system
- **Real-time Data**: Live production metrics from database
- **Period Selection**: Daily, Weekly, Monthly, Yearly views
- **Chart Types**: Trends, Impact, Fuel consumption

### Component Structure
```typescript
const ProductionOverviewScreen: React.FC = () => {
  // State management
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('Daily');
  const [selectedChart, setSelectedChart] = useState<ChartType>('trends');
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(true);

  // Data loading
  useEffect(() => {
    loadProductionData();
  }, [selectedPeriod]);

  // Chart data processing
  const processChartData = useCallback((data: ProductionMetric[]) => {
    return {
      trends: generateTrendsChart(data),
      impact: generateImpactChart(data),
      fuel: generateFuelChart(data)
    };
  }, []);

  return (
    <Container>
      <Header title="Production Overview" />
      <PeriodSelector 
        selected={selectedPeriod}
        onSelect={setSelectedPeriod}
      />
      <ChartTabs
        selected={selectedChart}
        onSelect={setSelectedChart}
      />
      <ScrollableChart
        data={chartData?.[selectedChart]}
        chartType={selectedChart}
      />
      <MetricsSummary data={chartData} />
    </Container>
  );
};
```

### Data Flow
```
Database → loadProductionData() → processChartData() → ScrollableChart → UI
    ↓              ↓                    ↓                ↓           ↓
JWT Handling → Error Recovery → Chart Processing → Rendering → User Interaction
```

### Performance Optimizations
- **Data Limitation**: 8-point limit for chart performance
- **Memoization**: React.memo for expensive components
- **Lazy Loading**: Dynamic imports for chart components
- **Caching**: In-memory caching for frequently accessed data

## Navigation Patterns

### Tab Navigation Structure
```typescript
const TabNavigator = () => (
  <Tab.Navigator
    screenOptions={{
      tabBarStyle: styles.tabBar,
      tabBarActiveTintColor: Colors.primary,
      tabBarInactiveTintColor: Colors.textSecondary,
    }}
  >
    <Tab.Screen 
      name="Production" 
      component={ProductionStack}
      options={{
        tabBarIcon: ({ color }) => (
          <Ionicons name="analytics" size={24} color={color} />
        ),
      }}
    />
    <Tab.Screen 
      name="Equipment" 
      component={EquipmentStack}
      options={{
        tabBarIcon: ({ color }) => (
          <Ionicons name="construct" size={24} color={color} />
        ),
      }}
    />
    <Tab.Screen 
      name="Safety" 
      component={SafetyStack}
      options={{
        tabBarIcon: ({ color }) => (
          <Ionicons name="shield-checkmark" size={24} color={color} />
        ),
      }}
    />
    <Tab.Screen 
      name="Settings" 
      component={SettingsStack}
      options={{
        tabBarIcon: ({ color }) => (
          <Ionicons name="settings" size={24} color={color} />
        ),
      }}
    />
  </Tab.Navigator>
);
```

### Stack Navigation Structure
```typescript
const ProductionStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: Colors.primary,
      },
      headerTintColor: Colors.textInverse,
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    }}
  >
    <Stack.Screen 
      name="ProductionOverview" 
      component={ProductionOverviewScreen}
      options={{ title: 'Production Overview' }}
    />
    <Stack.Screen 
      name="ProductionDetails" 
      component={ProductionDetailsScreen}
      options={{ title: 'Production Details' }}
    />
    <Stack.Screen 
      name="ProductionReports" 
      component={ProductionReportsScreen}
      options={{ title: 'Production Reports' }}
    />
  </Stack.Navigator>
);
```

## Dashboard Screen Updates (2025-01-19)

### Header Layout Enhancement (NEW)
- **Layout**: 3-column header design (Profile Photo | Text | Notification)
- **Profile Photo**: Integrated with Supabase database and storage
- **Implementation**: ProfilePhotoManager component with fallback to user initials
- **Features**:
  - Circular profile photo (48x48px)
  - Database integration with avatar_url field
  - Automatic fallback to user initials
  - Error handling and loading states
  - Future-ready for photo upload/editing
- **Current Photo**: https://ohqbaimnhwvdfrmxvhxv.supabase.co/storage/v1/object/public/profile-photos/anakku.jpeg

### Quick Actions Layout Change
- **Before**: 2x2 grid layout with `flexWrap: 'wrap'`
- **After**: Single horizontal row similar to footer navigation
- **Implementation**: `justifyContent: 'space-between'` with equal flex distribution
- **Responsive**: Adapts icon and text sizes based on screen size
- **Touch Targets**: Enhanced with proper minimum sizes and activeOpacity

### Activity Documentation Section (NEW)
- **Position**: Between Quick Actions and Recent Activity
- **Component**: `ActivityDocumentationCarousel`
- **Features**:
  - Horizontal scrollable carousel with maximum width utilization
  - Auto-scroll every 3 seconds
  - Manual navigation with pause/resume
  - Visual pagination indicators
  - Image loading states and error handling
  - Responsive design across screen sizes
  - Cards: 320px height, maximum width minus minimal margins
  - Images: 180px height with justified text content

### Dashboard Layout Structure (Updated)
```typescript
<ScrollView>
  {/* Header */}
  <View style={styles.header}>...</View>

  {/* Statistics Cards */}
  <View style={styles.statsContainer}>...</View>

  {/* Quick Actions - HORIZONTAL LAYOUT */}
  <View style={styles.quickActions}>
    <View style={styles.actionsGrid}>
      {/* 4 action cards in single row */}
    </View>
  </View>

  {/* Activity Documentation - NEW SECTION */}
  <View style={styles.activityDocumentationSection}>
    <ActivityDocumentationCarousel />
  </View>

  {/* Recent Activity */}
  <View style={styles.recentActivity}>...</View>
</ScrollView>
```

## Screen Lifecycle Patterns

### Data Loading Pattern
```typescript
const useScreenData = (screenName: string) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const result = await DatabaseService.getScreenData(screenName);
        setData(result);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [screenName]);

  return { data, loading, error, refetch: loadData };
};
```

### Focus/Blur Handling Pattern
```typescript
const useScreenFocus = (callback: () => void) => {
  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', callback);
    return unsubscribe;
  }, [navigation, callback]);
};

// Usage in screen
const ProductionOverviewScreen = () => {
  useScreenFocus(() => {
    // Refresh data when screen comes into focus
    loadProductionData();
  });
};
```

### Error Handling Pattern
```typescript
const useErrorHandler = () => {
  const showError = useCallback((error: Error) => {
    Alert.alert(
      'Error',
      error.message || 'An unexpected error occurred',
      [{ text: 'OK' }]
    );
  }, []);

  const handleAsyncError = useCallback(async (asyncFn: () => Promise<any>) => {
    try {
      return await asyncFn();
    } catch (error) {
      showError(error as Error);
      throw error;
    }
  }, [showError]);

  return { showError, handleAsyncError };
};
```

## Screen State Management

### Local State Pattern
```typescript
const ProductionOverviewScreen = () => {
  // Local state for UI-specific data
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('Daily');
  const [selectedChart, setSelectedChart] = useState<ChartType>('trends');
  
  // Derived state
  const chartConfig = useMemo(() => ({
    period: selectedPeriod,
    type: selectedChart,
  }), [selectedPeriod, selectedChart]);

  // State persistence
  useEffect(() => {
    AsyncStorage.setItem('selectedPeriod', selectedPeriod);
  }, [selectedPeriod]);
};
```

### Global State Integration
```typescript
// Context-based state management
const ProductionContext = createContext();

const ProductionProvider = ({ children }) => {
  const [productionData, setProductionData] = useState(null);
  const [loading, setLoading] = useState(false);

  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await DatabaseService.getProductionMetrics();
      setProductionData(data);
    } finally {
      setLoading(false);
    }
  }, []);

  return (
    <ProductionContext.Provider value={{
      data: productionData,
      loading,
      loadData,
    }}>
      {children}
    </ProductionContext.Provider>
  );
};
```

## Screen Testing Patterns

### Screen Rendering Tests
```typescript
describe('ProductionOverviewScreen', () => {
  it('should render without crashing', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <ProductionOverviewScreen />
      </NavigationContainer>
    );
    
    expect(getByTestId('production-overview-screen')).toBeTruthy();
  });

  it('should display loading state initially', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <ProductionOverviewScreen />
      </NavigationContainer>
    );
    
    expect(getByTestId('loading-spinner')).toBeTruthy();
  });
});
```

### Navigation Tests
```typescript
describe('Navigation', () => {
  it('should navigate to production details', async () => {
    const { getByText } = render(
      <NavigationContainer>
        <ProductionStack />
      </NavigationContainer>
    );
    
    fireEvent.press(getByText('View Details'));
    
    await waitFor(() => {
      expect(getByText('Production Details')).toBeTruthy();
    });
  });
});
```

### Data Loading Tests
```typescript
describe('Data Loading', () => {
  it('should load production data on mount', async () => {
    const mockData = { metrics: [] };
    jest.spyOn(DatabaseService, 'getProductionMetrics')
        .mockResolvedValue(mockData);

    const { getByTestId } = render(<ProductionOverviewScreen />);
    
    await waitFor(() => {
      expect(getByTestId('chart-container')).toBeTruthy();
    });
    
    expect(DatabaseService.getProductionMetrics).toHaveBeenCalled();
  });
});
```

## Performance Optimization

### Screen-Level Optimizations
```typescript
// Memoized screen component
const ProductionOverviewScreen = React.memo(() => {
  // Screen implementation
});

// Lazy-loaded screens
const LazyProductionDetails = React.lazy(() => 
  import('./ProductionDetailsScreen')
);

// Optimized re-renders
const useOptimizedState = (initialState) => {
  const [state, setState] = useState(initialState);
  
  const optimizedSetState = useCallback((newState) => {
    setState(prevState => {
      if (JSON.stringify(prevState) === JSON.stringify(newState)) {
        return prevState; // Prevent unnecessary re-renders
      }
      return newState;
    });
  }, []);
  
  return [state, optimizedSetState];
};
```

### Memory Management
```typescript
const ProductionOverviewScreen = () => {
  useEffect(() => {
    // Cleanup on unmount
    return () => {
      // Clear timers, subscriptions, etc.
    };
  }, []);

  // Efficient data processing
  const processedData = useMemo(() => {
    return expensiveDataProcessing(rawData);
  }, [rawData]);
};
```

---
*Screen documentation following Cortex 7 standards for comprehensive screen implementation reference.*
