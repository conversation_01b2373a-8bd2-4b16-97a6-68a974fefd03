-- Migration: Create daily_production_metrics table for detailed production tracking
-- This table stores daily production data with plan vs actual metrics

-- Create the daily_production_metrics table
CREATE TABLE IF NOT EXISTS daily_production_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    date DATE NOT NULL,
    monthly VARCHAR(20) NOT NULL, -- e.g., "December 2023", "January 2024"
    week INTEGER NOT NULL, -- Week number (1-53)
    
    -- Overburden metrics
    actual_ob DECIMAL(12,2) DEFAULT 0, -- Actual overburden volume
    plan_ob DECIMAL(12,2) DEFAULT 0,   -- Planned overburden volume
    
    -- Ore metrics
    actual_ore DECIMAL(12,2) DEFAULT 0, -- Actual ore production
    plan_ore DECIMAL(12,2) DEFAULT 0,   -- Planned ore production
    
    -- Rain metrics
    actual_rain DECIMAL(8,2) DEFAULT 0, -- Actual rain hours/impact
    plan_rain DECIMAL(8,2) DEFAULT 0,   -- Planned rain hours/impact
    
    -- Slippery conditions metrics
    actual_slippery DECIMAL(8,2) DEFAULT 0, -- Actual slippery hours/impact
    plan_slippery DECIMAL(8,2) DEFAULT 0,   -- Planned slippery hours/impact
    
    -- Fuel metrics
    actual_fuel DECIMAL(10,2) DEFAULT 0, -- Actual fuel consumption
    plan_fuel DECIMAL(10,2) DEFAULT 0,   -- Planned fuel consumption
    
    -- Foreign keys and metadata
    location_id UUID REFERENCES locations(id) NOT NULL,
    created_by UUID REFERENCES users(id),
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_date_location UNIQUE(date, location_id),
    CONSTRAINT positive_metrics CHECK (
        actual_ob >= 0 AND plan_ob >= 0 AND
        actual_ore >= 0 AND plan_ore >= 0 AND
        actual_rain >= 0 AND plan_rain >= 0 AND
        actual_slippery >= 0 AND plan_slippery >= 0 AND
        actual_fuel >= 0 AND plan_fuel >= 0
    )
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_daily_production_metrics_date ON daily_production_metrics(date);
CREATE INDEX IF NOT EXISTS idx_daily_production_metrics_monthly ON daily_production_metrics(monthly);
CREATE INDEX IF NOT EXISTS idx_daily_production_metrics_week ON daily_production_metrics(week);
CREATE INDEX IF NOT EXISTS idx_daily_production_metrics_location ON daily_production_metrics(location_id);
CREATE INDEX IF NOT EXISTS idx_daily_production_metrics_date_location ON daily_production_metrics(date, location_id);

-- Create composite index for time-based queries
CREATE INDEX IF NOT EXISTS idx_daily_production_metrics_time_range ON daily_production_metrics(date, monthly, week);

-- Enable Row Level Security
ALTER TABLE daily_production_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Users can view all production metrics
CREATE POLICY "Users can view daily production metrics" ON daily_production_metrics
    FOR SELECT USING (true);

-- Only authenticated users can insert production metrics
CREATE POLICY "Authenticated users can insert daily production metrics" ON daily_production_metrics
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Users can update metrics they created or if they have supervisor/manager role
CREATE POLICY "Users can update daily production metrics" ON daily_production_metrics
    FOR UPDATE USING (
        created_by = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('supervisor', 'manager', 'admin')
        )
    );

-- Only supervisors/managers can delete metrics
CREATE POLICY "Supervisors can delete daily production metrics" ON daily_production_metrics
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('supervisor', 'manager', 'admin')
        )
    );

-- Create trigger for updated_at timestamp
CREATE OR REPLACE FUNCTION update_daily_production_metrics_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_daily_production_metrics_updated_at
    BEFORE UPDATE ON daily_production_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_daily_production_metrics_updated_at();

-- Add helpful comments
COMMENT ON TABLE daily_production_metrics IS 'Daily production metrics with plan vs actual tracking for mining operations';
COMMENT ON COLUMN daily_production_metrics.monthly IS 'Month and year for grouping (e.g., "December 2023")';
COMMENT ON COLUMN daily_production_metrics.week IS 'Week number of the year (1-53)';
COMMENT ON COLUMN daily_production_metrics.actual_ob IS 'Actual overburden volume in cubic meters';
COMMENT ON COLUMN daily_production_metrics.plan_ob IS 'Planned overburden volume in cubic meters';
COMMENT ON COLUMN daily_production_metrics.actual_ore IS 'Actual ore production in tons';
COMMENT ON COLUMN daily_production_metrics.plan_ore IS 'Planned ore production in tons';
COMMENT ON COLUMN daily_production_metrics.actual_rain IS 'Actual rain impact hours';
COMMENT ON COLUMN daily_production_metrics.plan_rain IS 'Planned rain impact hours';
COMMENT ON COLUMN daily_production_metrics.actual_slippery IS 'Actual slippery conditions impact hours';
COMMENT ON COLUMN daily_production_metrics.plan_slippery IS 'Planned slippery conditions impact hours';
COMMENT ON COLUMN daily_production_metrics.actual_fuel IS 'Actual fuel consumption in liters';
COMMENT ON COLUMN daily_production_metrics.plan_fuel IS 'Planned fuel consumption in liters';
