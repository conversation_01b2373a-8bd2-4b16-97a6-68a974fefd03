import { Dimensions } from 'react-native';
import { ShadowPresets } from '../utils/shadowHelper';

const { width, height } = Dimensions.get('window');

export const Layout = {
  window: {
    width,
    height,
  },
  isSmallDevice: width < 375,
  
  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  
  // Border radius
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
  
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Icon sizes
  iconSize: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 40,
  },
  
  // Header height
  headerHeight: 60,
  
  // Tab bar height
  tabBarHeight: 80,
  
  // Card dimensions
  cardMinHeight: 120,
  cardPadding: 16,
  
  // Button dimensions
  buttonHeight: 48,
  buttonMinWidth: 120,
  
  // Input dimensions
  inputHeight: 48,
  
  // Shadow (Cross-platform)
  shadow: ShadowPresets.medium,

  // Light shadow (Cross-platform)
  shadowLight: ShadowPresets.small,
};
