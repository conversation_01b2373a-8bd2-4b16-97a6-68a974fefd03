# 🏭 Mining Operations Database

Comprehensive database schema for mining operations management with daily production reporting, calendar integration, and advanced analytics.

## 📋 **Overview**

This database is specifically designed for mining operations with focus on:
- **Daily Production Reporting** with OB (Overburden) and ORE tracking
- **Production Calendar** with weekly, monthly, yearly analysis
- **Advanced Ratios**: Stripping Ratio (SR) and Fuel Ratio (FR)
- **Weather Impact** tracking and operational efficiency
- **User Management** with role-based access control
- **Comprehensive Analytics** with automated calculations
- **Mining Certifications** (Greencard, IUT, OTT) management
- **Certificate Renewal** tracking and compliance
- **Plan vs Actual Tracking** for safety activities by role level

## 🎯 **Key Features**

### **📊 Production Management**
- **Daily Mining Reports** with shift-based data entry
- **Auto-calculated Ratios**: SR (Overburden:Ore), FR (Fuel:Material)
- **Achievement Tracking** against targets
- **Weather Impact** monitoring
- **Operational Efficiency** calculations

### **📅 Calendar Integration**
- **Production Calendar** with business days and holidays
- **Multi-period Targets**: Daily, Weekly, Monthly, Yearly
- **Automated Aggregations** for all time periods
- **Target vs Actual** variance analysis

### **👥 User Management**
- **Role-based Access Control** (6 user roles)
- **Location-based Permissions**
- **Audit Trail** for compliance
- **Session Management** for security

### **📈 Analytics & Reporting**
- **Real-time Dashboard** views
- **Weekly/Monthly/Yearly** summaries
- **Performance KPIs** and metrics
- **Trend Analysis** capabilities

## 🗂️ **Database Structure**

### **Core Tables**
```
daily_mining_report          # Main production data table
production_calendar          # Business calendar with holidays
production_targets_calendar  # Targets by time periods
user_profiles               # User management
user_permissions           # Access control
equipment                  # Equipment registry
equipment_metrics         # Equipment performance data
maintenance_schedules     # Preventive maintenance
work_orders              # Maintenance work orders
safety_incidents         # Safety incident tracking
safety_inspections       # Safety inspections
safety_training          # Training programs
safety_training_attendees # Training attendance
safety_metrics           # Safety KPIs
mining_certifications    # Greencard, IUT, OTT certificates
certificate_renewals     # Certificate renewal tracking
safety_activity_plans    # Monthly safety activity plans by role
safety_activity_actuals  # Daily safety activity actuals tracking
audit_logs              # Compliance tracking
app_settings            # System configuration
```

### **Key Views**
```
v_weekly_production_summary   # Weekly aggregated data
v_monthly_production_summary  # Monthly aggregated data
v_yearly_production_summary   # Yearly aggregated data
v_production_dashboard       # Real-time dashboard
mv_equipment_utilization     # Equipment performance metrics
mv_safety_summary           # Safety metrics summary
mv_production_performance   # Production performance metrics
v_monthly_safety_plan_actual # Monthly plan vs actual tracking
v_daily_safety_dashboard    # Daily safety activities dashboard
v_rls_monitoring           # RLS policy monitoring
```

## 🚀 **Quick Start**

### **1. Database Setup**

#### **Option A: Complete Deployment (Recommended)**
```bash
# Connect to your PostgreSQL database
psql "your-database-connection-string"

# Run complete deployment
\i deploy.sql
```

#### **Option B: Step-by-Step Deployment**
```bash
# Execute files in order
\i 01-core-setup.sql
\i 02-production-calendar.sql
\i 03-daily-mining-report.sql
\i 04-analytical-views.sql
\i 06-user-management.sql
\i 07-equipment-management.sql
\i 08-safety-management.sql
\i 09-rls-policies.sql
\i 10-indexes-performance.sql
\i 05-sample-data.sql  # Optional: for testing
```

### **2. Verification**
```sql
-- Check deployment status
SELECT * FROM schema_migrations ORDER BY version;

-- View sample data
SELECT location, report_date, total_actual_material, 
       total_achievement_percent, actual_sr, actual_fr
FROM daily_mining_report 
ORDER BY report_date DESC LIMIT 10;

-- Check users
SELECT full_name, role, primary_location, is_active 
FROM user_profiles;
```

## 📊 **Data Model**

### **Daily Mining Report Structure**
```
Location | Date | Week | Month | Year
├── Shift 1 OB | Shift 2 OB | Total Actual OB | Plan OB
├── Shift 1 ORE | Shift 2 ORE | Total Actual ORE | Plan ORE  
├── Fuel Actual | Fuel Plan
├── Actual FR | Plan FR (Auto-calculated)
├── Actual SR | Plan SR (Auto-calculated)
├── Rain Actual | Rain Plan
├── Slippery Actual | Slippery Plan
└── Achievement % | Operational Efficiency %
```

### **Auto-calculated Fields**
- **Total Actual OB/ORE**: Sum of both shifts
- **Achievement %**: (Actual / Plan) × 100
- **Stripping Ratio (SR)**: Overburden ÷ Ore
- **Fuel Ratio (FR)**: Fuel ÷ Total Material
- **Operational Efficiency**: (Working Hours - Downtime) ÷ Working Hours × 100

## 🔧 **Usage Examples**

### **1. Insert Daily Report**
```sql
INSERT INTO daily_mining_report (
    location, report_date, 
    shift_1_ob, shift_2_ob, plan_ob,
    shift_1_ore, shift_2_ore, plan_ore,
    fuel_actual, fuel_plan, plan_fr, plan_sr,
    reported_by, status
) VALUES (
    'Pit A', '2024-01-20',
    850.5, 920.3, 1800.0,
    450.2, 380.7, 800.0,
    1250.5, 1360.0, 0.85, 2.50,
    'John Supervisor', 'Submitted'
);
```

### **2. Weekly Summary Query**
```sql
SELECT 
    location,
    week_start_date,
    week_actual_material,
    week_plan_material,
    week_total_achievement_percent,
    week_actual_sr,
    week_actual_fr
FROM v_weekly_production_summary 
WHERE year_number = 2024 AND week_number = 3;
```

### **3. Monthly Performance Dashboard**
```sql
SELECT 
    location,
    month_name,
    month_actual_ob,
    month_actual_ore,
    month_total_achievement_percent,
    month_actual_sr,
    month_fuel_cost_per_ton
FROM v_monthly_production_summary 
WHERE year_number = 2024 AND month_number = 1;
```

### **4. Production Dashboard**
```sql
SELECT 
    location,
    report_date,
    total_achievement_percent as daily_achievement,
    month_total_achievement_percent as mtd_achievement,
    year_total_achievement_percent as ytd_achievement,
    actual_sr,
    actual_fr,
    weather_impact_level,
    status
FROM v_production_dashboard 
WHERE report_date >= CURRENT_DATE - 7
ORDER BY location, report_date DESC;

### **5. Equipment Performance Query**
```sql
SELECT
    e.equipment_number,
    e.name,
    em.recorded_date,
    em.utilization_rate,
    em.availability_rate,
    em.health_score,
    em.fuel_efficiency
FROM equipment e
JOIN equipment_metrics em ON e.id = em.equipment_id
WHERE em.recorded_date >= CURRENT_DATE - 7
AND em.health_score < 90
ORDER BY em.health_score ASC;
```

### **6. Safety Incident Analysis**
```sql
SELECT
    incident_number,
    title,
    incident_type,
    severity,
    location,
    occurred_at,
    status,
    assigned_investigator
FROM safety_incidents
WHERE occurred_at >= CURRENT_DATE - 30
AND severity IN ('High', 'Critical')
ORDER BY occurred_at DESC;
```

### **7. Maintenance Work Orders**
```sql
SELECT
    wo.work_order_number,
    wo.title,
    e.equipment_number,
    wo.priority,
    wo.status,
    wo.scheduled_start_date,
    up.full_name as assigned_to_name
FROM work_orders wo
JOIN equipment e ON wo.equipment_id = e.id
LEFT JOIN user_profiles up ON wo.assigned_to = up.id
WHERE wo.status IN ('Created', 'Assigned', 'In Progress')
ORDER BY wo.priority DESC, wo.scheduled_start_date ASC;
```

### **8. Mining Certifications (Greencard, IUT, OTT)**
```sql
-- View all certificates with expiry status
SELECT
    holder_name,
    certificate_type,
    certificate_number,
    valid_until,
    days_remaining,
    calculated_status,
    greencard_level,
    iut_license_type,
    ott_operation_type
FROM v_certificate_dashboard
WHERE days_remaining <= 90
ORDER BY days_remaining ASC;
```

### **9. Greencard Management**
```sql
-- Greencard holders by level and location
SELECT
    holder_name,
    employee_id,
    primary_location,
    greencard_level,
    greencard_categories,
    valid_until,
    status,
    completed_training_hours,
    required_training_hours
FROM v_greencard_summary
WHERE status IN ('Valid', 'Expiring Soon')
ORDER BY primary_location, greencard_level;
```

### **10. Certificate Renewal Tracking**
```sql
-- Certificates requiring renewal
SELECT
    mc.certificate_type,
    up.full_name,
    mc.certificate_number,
    mc.valid_until,
    cr.renewal_due_date,
    cr.status as renewal_status,
    cr.training_required,
    cr.medical_exam_required
FROM mining_certifications mc
JOIN user_profiles up ON mc.holder_id = up.id
LEFT JOIN certificate_renewals cr ON mc.id = cr.certificate_id
WHERE mc.valid_until <= CURRENT_DATE + INTERVAL '60 days'
ORDER BY mc.valid_until ASC;
```

### **11. Safety Activity Plan vs Actual Tracking**
```sql
-- Monthly plan vs actual summary
SELECT
    location,
    plan_year,
    plan_month,
    -- Greencard Plan vs Actual
    greencard_total_plan,
    greencard_total_actual,
    greencard_total_achievement,
    -- IUT Plan vs Actual
    iut_total_plan,
    iut_total_actual,
    iut_total_achievement,
    -- OTT Plan vs Actual
    ott_total_plan,
    ott_total_actual,
    ott_total_achievement
FROM v_monthly_safety_plan_actual
WHERE plan_year = 2024 AND plan_month = 1
ORDER BY location;
```

### **12. Daily Safety Activities Dashboard**
```sql
-- Daily safety activities tracking
SELECT
    actual_date,
    location,
    -- Daily Actuals by Activity Type
    greencard_total_actual,
    iut_total_actual,
    ott_total_actual,
    -- Monthly Targets for Reference
    monthly_greencard_target,
    monthly_iut_target,
    monthly_ott_target,
    notes,
    recorded_by_name
FROM v_daily_safety_dashboard
WHERE actual_date >= CURRENT_DATE - 7
ORDER BY actual_date DESC, location;
```

### **13. Create Safety Activity Plans**
```sql
-- Create default plans for a location and year
SELECT create_default_safety_plans('Pit A', 2024);

-- Record daily safety activities
SELECT record_daily_safety_activities(
    'Pit A',                    -- location
    CURRENT_DATE,               -- date
    2, 1, 1,                   -- Greencard: GL, SV, DH
    2, 1, 0,                   -- IUT: GL, SV, DH
    2, 1, 1,                   -- OTT: GL, SV, DH
    'Good safety performance',  -- notes
    NULL                       -- recorded_by (auto-detect)
);
```
```

## 👥 **User Roles & Permissions**

### **User Roles**
1. **Super Admin**: Full system access
2. **Site Manager**: Multi-location management
3. **Shift Supervisor**: Location-specific operations
4. **Equipment Operator**: Data entry and viewing
5. **Safety Officer**: Safety-related access
6. **Observer**: Read-only access

### **Sample Users** (from sample data)
```
<EMAIL>      - Super Admin
<EMAIL>    - Site Manager  
<EMAIL> - Shift Supervisor (Pit A)
<EMAIL> - Shift Supervisor (Pit B)
<EMAIL> - Shift Supervisor (Pit C)
<EMAIL>   - Equipment Operator
<EMAIL>     - Safety Officer
```

## 📱 **Mobile App Integration**

### **React Native with Supabase**
```javascript
// Insert daily report
const insertDailyReport = async (reportData) => {
  const { data, error } = await supabase
    .from('daily_mining_report')
    .insert([{
      location: reportData.location,
      report_date: reportData.date,
      shift_1_ob: reportData.shift1OB,
      shift_2_ob: reportData.shift2OB,
      plan_ob: reportData.planOB,
      shift_1_ore: reportData.shift1ORE,
      shift_2_ore: reportData.shift2ORE,
      plan_ore: reportData.planORE,
      fuel_actual: reportData.fuelActual,
      fuel_plan: reportData.fuelPlan,
      plan_fr: reportData.planFR,
      plan_sr: reportData.planSR,
      reported_by: reportData.reportedBy,
      status: 'Draft'
    }]);
  
  return { data, error };
};

// Get weekly summary
const getWeeklySummary = async (location, year, week) => {
  const { data, error } = await supabase
    .from('v_weekly_production_summary')
    .select('*')
    .eq('location', location)
    .eq('year_number', year)
    .eq('week_number', week);
  
  return { data, error };
};
```

## 🔒 **Security Features**

- **Row Level Security (RLS)** enabled on sensitive tables
- **Role-based Access Control** with granular permissions
- **Audit Logging** for all data changes
- **Session Management** with device tracking
- **Data Validation** with comprehensive constraints

## 📈 **Performance Optimizations**

- **Strategic Indexing** for common query patterns
- **Materialized Views** for complex aggregations
- **Generated Columns** for calculated fields
- **Proper Constraints** for data integrity
- **Query Optimization** with table statistics

## 🛠️ **Maintenance**

### **Regular Tasks**
```sql
-- Update table statistics (weekly)
SELECT populate_production_calendar(2024); -- For new year

-- Analyze tables for performance
ANALYZE daily_mining_report;
ANALYZE production_calendar;

-- Clean old audit logs (monthly)
DELETE FROM audit_logs 
WHERE event_timestamp < NOW() - INTERVAL '1 year';
```

### **Backup Strategy**
```bash
# Daily backup
pg_dump mining_operations > backup_$(date +%Y%m%d).sql

# Restore
psql mining_operations < backup_20240120.sql
```

## 📞 **Support**

### **Database Schema Information**
- **Version**: 1.0
- **PostgreSQL**: 14+ required
- **Extensions**: uuid-ossp, pg_stat_statements
- **Compatibility**: Supabase ready

### **Key Metrics**
- **Tables**: 21 core tables (including plan/actual tracking)
- **Views**: 14 analytical views (including plan vs actual views)
- **Functions**: 25+ utility functions (including plan management)
- **Indexes**: 70+ performance indexes
- **Sample Data**: 20+ daily reports, 4 equipment units, safety activities, certificates
- **RLS Policies**: 35+ security policies
- **User Roles**: 6 different access levels
- **Certifications**: Greencard, IUT, OTT management integrated
- **Plan Tracking**: Monthly plans with daily actual tracking by role level

---

**🏭 Built for Mining Industry Excellence**

This database schema provides a solid foundation for mining operations management with focus on daily production tracking, comprehensive analytics, and operational efficiency.
