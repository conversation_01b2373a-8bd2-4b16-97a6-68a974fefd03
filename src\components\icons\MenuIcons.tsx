import React from 'react';
import Svg, { G, Path, Defs, LinearGradient, Stop, Circle, Rect } from 'react-native-svg';

interface IconProps {
  width?: number;
  height?: number;
  opacity?: number;
}

// Board Icon (Dashboard/Management)
export const BoardIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="board1" x1="4.667" x2="8.327" y1="7.182" y2="15.777" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#b0f098" />
        <Stop offset="1" stopColor="#52d17c" />
      </LinearGradient>
      <LinearGradient id="board2" x1="8.4" x2="13.077" y1="2.889" y2="9.103" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#52d17c" />
        <Stop offset="1" stopColor="#309c61" />
      </LinearGradient>
      <LinearGradient id="board3" x1="2.857" x2="8.01" y1="6.421" y2="11.174" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#42b870" />
        <Stop offset="1" stopColor="#1a7f7c" />
      </LinearGradient>
      <LinearGradient id="board4" x1="2.857" x2="4.433" y1="2.8" y2="6.881" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#b0f098" />
        <Stop offset="1" stopColor="#64de89" />
      </LinearGradient>
    </Defs>
    <G fill="none">
      <Path fill="url(#board1)" d="m14 10l-3-1l-3 1l-1 2l1 2h3.5a2.5 2.5 0 0 0 2.5-2.5z" />
      <Path fill="url(#board2)" d="M8 2L7 6l1 4h6V4.5A2.5 2.5 0 0 0 11.5 2z" />
      <Path fill="url(#board3)" d="M8 14V6L5 5L2 6v5.5A2.5 2.5 0 0 0 4.5 14z" />
      <Path fill="url(#board4)" d="M8 2v4H2V4.5A2.5 2.5 0 0 1 4.5 2z" />
    </G>
  </Svg>
);

// Calendar Icon (Attendance)
export const CalendarIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="cal1" x1="2" x2="14" y1="3" y2="13" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#4FC3F7" />
        <Stop offset="1" stopColor="#29B6F6" />
      </LinearGradient>
    </Defs>
    <Rect x="2" y="3" width="12" height="10" rx="2" fill="url(#cal1)" />
    <Rect x="2" y="3" width="12" height="3" rx="2" fill="#1976D2" />
    <Circle cx="5" cy="1.5" r="0.5" fill="#666" />
    <Circle cx="11" cy="1.5" r="0.5" fill="#666" />
    <Rect x="4.5" y="0.5" width="1" height="3" fill="#666" />
    <Rect x="10.5" y="0.5" width="1" height="3" fill="#666" />
    <Circle cx="5" cy="8" r="0.5" fill="white" />
    <Circle cx="8" cy="8" r="0.5" fill="white" />
    <Circle cx="11" cy="8" r="0.5" fill="white" />
    <Circle cx="5" cy="10.5" r="0.5" fill="white" />
    <Circle cx="8" cy="10.5" r="0.5" fill="white" />
  </Svg>
);

// Clock Icon (Time/ATR)
export const ClockIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="clock1" x1="0" x2="16" y1="0" y2="16" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#FF9800" />
        <Stop offset="1" stopColor="#F57C00" />
      </LinearGradient>
    </Defs>
    <Circle cx="8" cy="8" r="7" fill="url(#clock1)" />
    <Circle cx="8" cy="8" r="6" fill="none" stroke="white" strokeWidth="0.5" />
    <Path d="M8 4v4l3 2" stroke="white" strokeWidth="1.5" strokeLinecap="round" fill="none" />
    <Circle cx="8" cy="8" r="1" fill="white" />
  </Svg>
);

// Location Icon (Position)
export const LocationIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="loc1" x1="8" x2="8" y1="1" y2="15" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#E91E63" />
        <Stop offset="1" stopColor="#C2185B" />
      </LinearGradient>
    </Defs>
    <Path d="M8 1C5.24 1 3 3.24 3 6c0 3.5 5 8 5 8s5-4.5 5-8c0-2.76-2.24-5-5-5z" fill="url(#loc1)" />
    <Circle cx="8" cy="6" r="2" fill="white" />
  </Svg>
);

// Phone Icon (Bakomsel)
export const PhoneIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="phone1" x1="4" x2="12" y1="1" y2="15" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#9C27B0" />
        <Stop offset="1" stopColor="#7B1FA2" />
      </LinearGradient>
    </Defs>
    <Rect x="4" y="1" width="8" height="14" rx="2" fill="url(#phone1)" />
    <Rect x="5" y="2.5" width="6" height="9" rx="0.5" fill="#E1BEE7" />
    <Circle cx="8" cy="13" r="1" fill="white" />
  </Svg>
);

// Info Icon (Information)
export const InfoIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="info1" x1="0" x2="16" y1="0" y2="16" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#2196F3" />
        <Stop offset="1" stopColor="#1976D2" />
      </LinearGradient>
    </Defs>
    <Circle cx="8" cy="8" r="7" fill="url(#info1)" />
    <Circle cx="8" cy="5" r="1" fill="white" />
    <Rect x="7" y="7" width="2" height="5" rx="1" fill="white" />
  </Svg>
);

// People Icon (Karyawan)
export const PeopleIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="people1" x1="0" x2="16" y1="0" y2="16" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#4CAF50" />
        <Stop offset="1" stopColor="#388E3C" />
      </LinearGradient>
    </Defs>
    <Circle cx="5" cy="4" r="2.5" fill="url(#people1)" />
    <Circle cx="11" cy="4" r="2.5" fill="url(#people1)" />
    <Path d="M1 14v-2c0-1.5 1.5-3 4-3s4 1.5 4 3v2" fill="url(#people1)" />
    <Path d="M7 14v-2c0-1.5 1.5-3 4-3s4 1.5 4 3v2" fill="url(#people1)" />
  </Svg>
);

// Document Icon (Reports/Raport)
export const DocumentIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="doc1" x1="3" x2="13" y1="1" y2="15" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#FFC107" />
        <Stop offset="1" stopColor="#FF8F00" />
      </LinearGradient>
    </Defs>
    <Path d="M3 1v14h8l2-2V1H3z" fill="url(#doc1)" />
    <Path d="M11 13v2l2-2h-2z" fill="#FF8F00" />
    <Rect x="5" y="4" width="6" height="1" rx="0.5" fill="white" />
    <Rect x="5" y="6" width="6" height="1" rx="0.5" fill="white" />
    <Rect x="5" y="8" width="4" height="1" rx="0.5" fill="white" />
  </Svg>
);

// Video Icon (iPeak)
export const VideoIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="video1" x1="1" x2="15" y1="4" y2="12" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#F44336" />
        <Stop offset="1" stopColor="#D32F2F" />
      </LinearGradient>
    </Defs>
    <Rect x="1" y="4" width="10" height="8" rx="1" fill="url(#video1)" />
    <Path d="M11 6l4-2v8l-4-2V6z" fill="url(#video1)" />
    <Path d="M6 7l3 1.5L6 10V7z" fill="white" />
  </Svg>
);

// Grid Icon (Lihat Semua)
export const GridIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="grid1" x1="0" x2="16" y1="0" y2="16" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#607D8B" />
        <Stop offset="1" stopColor="#455A64" />
      </LinearGradient>
    </Defs>
    <Rect x="1" y="1" width="6" height="6" rx="1" fill="url(#grid1)" />
    <Rect x="9" y="1" width="6" height="6" rx="1" fill="url(#grid1)" />
    <Rect x="1" y="9" width="6" height="6" rx="1" fill="url(#grid1)" />
    <Rect x="9" y="9" width="6" height="6" rx="1" fill="url(#grid1)" />
  </Svg>
);

// Export default
export default BoardIcon;
