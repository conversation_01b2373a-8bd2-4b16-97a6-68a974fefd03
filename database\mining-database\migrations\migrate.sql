-- =====================================================
-- Mining Operations Database - Migration Runner
-- =====================================================
-- File: migrate.sql
-- Description: Automated migration runner for all database migrations
-- Version: 1.0
-- Date: 2024-01-20
-- =====================================================

-- =====================================================
-- MIGRATION RUNNER SETUP
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MINING OPERATIONS DATABASE - MIGRATION RUNNER';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Version: 1.0';
    RAISE NOTICE 'Date: %', NOW();
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'User: %', current_user;
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Starting automated migration process...';
    RAISE NOTICE '';
END $$;

-- =====================================================
-- PRE-MIGRATION CHECKS
-- =====================================================

-- Check PostgreSQL version
DO $$
DECLARE
    pg_version_num INTEGER;
BEGIN
    SELECT current_setting('server_version_num')::INTEGER INTO pg_version_num;
    
    IF pg_version_num < 140000 THEN
        RAISE WARNING 'PostgreSQL version % detected. Version 14+ recommended for optimal performance.', 
            current_setting('server_version');
    ELSE
        RAISE NOTICE 'PostgreSQL version % - Compatible', current_setting('server_version');
    END IF;
END $$;

-- Check required extensions
DO $$
BEGIN
    -- Check if uuid-ossp extension is available
    IF NOT EXISTS (SELECT 1 FROM pg_available_extensions WHERE name = 'uuid-ossp') THEN
        RAISE WARNING 'uuid-ossp extension not available. Some features may not work properly.';
    END IF;
    
    -- Check if pg_stat_statements extension is available
    IF NOT EXISTS (SELECT 1 FROM pg_available_extensions WHERE name = 'pg_stat_statements') THEN
        RAISE NOTICE 'pg_stat_statements extension not available. Query monitoring will be limited.';
    END IF;
END $$;

-- Create migration tracking table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
    version VARCHAR(50) PRIMARY KEY,
    description TEXT,
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    applied_by VARCHAR(100) DEFAULT current_user,
    checksum VARCHAR(64),
    execution_time_ms INTEGER
);

-- =====================================================
-- MIGRATION EXECUTION FUNCTIONS
-- =====================================================

-- Function to check if migration was already applied
CREATE OR REPLACE FUNCTION migration_applied(migration_version VARCHAR(50))
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (SELECT 1 FROM schema_migrations WHERE version = migration_version);
END;
$$ LANGUAGE plpgsql;

-- Function to record migration start
CREATE OR REPLACE FUNCTION start_migration(migration_version VARCHAR(50), migration_description TEXT)
RETURNS TIMESTAMPTZ AS $$
DECLARE
    start_time TIMESTAMPTZ := NOW();
BEGIN
    IF migration_applied(migration_version) THEN
        RAISE NOTICE 'Migration % already applied, skipping...', migration_version;
        RETURN NULL;
    END IF;
    
    INSERT INTO schema_migrations (version, description, applied_at) 
    VALUES (migration_version, migration_description, start_time)
    ON CONFLICT (version) DO NOTHING;
    
    RAISE NOTICE 'Starting Migration %: %', migration_version, migration_description;
    RETURN start_time;
END;
$$ LANGUAGE plpgsql;

-- Function to complete migration
CREATE OR REPLACE FUNCTION complete_migration(migration_version VARCHAR(50), start_time TIMESTAMPTZ)
RETURNS void AS $$
DECLARE
    end_time TIMESTAMPTZ := NOW();
    execution_time INTEGER;
BEGIN
    IF start_time IS NULL THEN
        RETURN; -- Migration was skipped
    END IF;
    
    execution_time := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
    
    UPDATE schema_migrations 
    SET 
        applied_at = end_time,
        checksum = md5(random()::text),
        execution_time_ms = execution_time
    WHERE version = migration_version;
    
    RAISE NOTICE 'Migration % completed in % ms', migration_version, execution_time;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- EXECUTE MIGRATIONS IN ORDER
-- =====================================================

-- Migration 001: Initial Schema
DO $$
DECLARE
    start_time TIMESTAMPTZ;
BEGIN
    start_time := start_migration('001', 'Initial schema setup for Mining Operations App');
    
    IF start_time IS NOT NULL THEN
        -- Execute migration 001
        \i 001_initial_schema.sql
        
        complete_migration('001', start_time);
    END IF;
END $$;

-- Migration 002: Equipment and Safety
DO $$
DECLARE
    start_time TIMESTAMPTZ;
BEGIN
    -- Check dependency
    IF NOT migration_applied('001') THEN
        RAISE EXCEPTION 'Migration 001 must be applied before running migration 002';
    END IF;
    
    start_time := start_migration('002', 'Equipment management and safety modules with IUT/OTT');
    
    IF start_time IS NOT NULL THEN
        -- Execute migration 002
        \i 002_equipment_safety.sql
        
        complete_migration('002', start_time);
    END IF;
END $$;

-- Migration 003: Security and Performance
DO $$
DECLARE
    start_time TIMESTAMPTZ;
BEGIN
    -- Check dependency
    IF NOT migration_applied('002') THEN
        RAISE EXCEPTION 'Migration 002 must be applied before running migration 003';
    END IF;
    
    start_time := start_migration('003', 'Row Level Security policies and performance optimization');
    
    IF start_time IS NOT NULL THEN
        -- Execute migration 003
        \i 003_security_performance.sql
        
        complete_migration('003', start_time);
    END IF;
END $$;

-- Migration 004: Sample Data (Optional)
DO $$
DECLARE
    start_time TIMESTAMPTZ;
    load_sample_data BOOLEAN := true; -- Set to false to skip sample data
BEGIN
    -- Check dependency
    IF NOT migration_applied('003') THEN
        RAISE EXCEPTION 'Migration 003 must be applied before running migration 004';
    END IF;
    
    IF load_sample_data THEN
        start_time := start_migration('004', 'Sample data for testing and demonstration');
        
        IF start_time IS NOT NULL THEN
            -- Execute migration 004
            \i 004_sample_data.sql
            
            complete_migration('004', start_time);
        END IF;
    ELSE
        RAISE NOTICE 'Skipping Migration 004: Sample data loading disabled';
    END IF;
END $$;

-- =====================================================
-- POST-MIGRATION VERIFICATION
-- =====================================================

DO $$
DECLARE
    migration_record RECORD;
    total_execution_time INTEGER := 0;
    table_count INTEGER;
    view_count INTEGER;
    index_count INTEGER;
    function_count INTEGER;
    policy_count INTEGER;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MIGRATION SUMMARY';
    RAISE NOTICE '================================================';
    
    -- Show all applied migrations
    FOR migration_record IN 
        SELECT version, description, applied_at, execution_time_ms 
        FROM schema_migrations 
        ORDER BY version
    LOOP
        RAISE NOTICE 'Migration %: % (% ms)', 
            migration_record.version, 
            migration_record.description, 
            migration_record.execution_time_ms;
        total_execution_time := total_execution_time + COALESCE(migration_record.execution_time_ms, 0);
    END LOOP;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Total execution time: % ms', total_execution_time;
    RAISE NOTICE '';
    
    -- Count database objects
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    
    SELECT COUNT(*) INTO view_count 
    FROM information_schema.views 
    WHERE table_schema = 'public';
    
    SELECT COUNT(*) INTO index_count 
    FROM pg_indexes 
    WHERE schemaname = 'public';
    
    SELECT COUNT(*) INTO function_count 
    FROM information_schema.routines 
    WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';
    
    SELECT COUNT(*) INTO policy_count 
    FROM pg_policies 
    WHERE schemaname = 'public';
    
    RAISE NOTICE 'Database Objects Created:';
    RAISE NOTICE '- Tables: %', table_count;
    RAISE NOTICE '- Views: %', view_count;
    RAISE NOTICE '- Indexes: %', index_count;
    RAISE NOTICE '- Functions: %', function_count;
    RAISE NOTICE '- RLS Policies: %', policy_count;
    RAISE NOTICE '';
END $$;

-- =====================================================
-- FEATURE VERIFICATION
-- =====================================================

DO $$
DECLARE
    daily_reports INTEGER;
    equipment_count INTEGER;
    safety_incidents INTEGER;
    certifications INTEGER;
    iut_inspections INTEGER;
    ott_observations INTEGER;
    users INTEGER;
BEGIN
    -- Count sample data (if loaded)
    SELECT COUNT(*) INTO daily_reports FROM daily_mining_report;
    SELECT COUNT(*) INTO equipment_count FROM equipment;
    SELECT COUNT(*) INTO safety_incidents FROM safety_incidents;
    SELECT COUNT(*) INTO certifications FROM mining_certifications;
    
    -- Count IUT/OTT records (if tables exist)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'iut_inspections') THEN
        SELECT COUNT(*) INTO iut_inspections FROM iut_inspections;
    ELSE
        iut_inspections := 0;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ott_observations') THEN
        SELECT COUNT(*) INTO ott_observations FROM ott_observations;
    ELSE
        ott_observations := 0;
    END IF;
    
    SELECT COUNT(*) INTO users FROM user_profiles;
    
    RAISE NOTICE 'Sample Data Verification:';
    RAISE NOTICE '- Daily mining reports: %', daily_reports;
    RAISE NOTICE '- Equipment records: %', equipment_count;
    RAISE NOTICE '- Safety incidents: %', safety_incidents;
    RAISE NOTICE '- Mining certifications: %', certifications;
    RAISE NOTICE '- IUT inspections: %', iut_inspections;
    RAISE NOTICE '- OTT observations: %', ott_observations;
    RAISE NOTICE '- User profiles: %', users;
    RAISE NOTICE '';
END $$;

-- =====================================================
-- FINAL STATUS AND RECOMMENDATIONS
-- =====================================================

DO $$
DECLARE
    rls_enabled_count INTEGER;
    critical_tables TEXT[] := ARRAY[
        'daily_mining_report', 'user_profiles', 'equipment', 
        'safety_incidents', 'mining_certifications'
    ];
    table_name TEXT;
    missing_rls TEXT[] := '{}';
BEGIN
    -- Check RLS status on critical tables
    FOREACH table_name IN ARRAY critical_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_tables t
            JOIN pg_class c ON c.relname = t.tablename
            WHERE t.schemaname = 'public' 
            AND t.tablename = table_name
            AND c.relrowsecurity = true
        ) THEN
            missing_rls := array_append(missing_rls, table_name);
        END IF;
    END LOOP;
    
    SELECT COUNT(*) INTO rls_enabled_count
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public' 
    AND c.relrowsecurity = true;
    
    RAISE NOTICE '================================================';
    RAISE NOTICE 'SECURITY STATUS';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Tables with RLS enabled: %', rls_enabled_count;
    
    IF array_length(missing_rls, 1) > 0 THEN
        RAISE WARNING 'Critical tables without RLS: %', array_to_string(missing_rls, ', ');
    ELSE
        RAISE NOTICE 'All critical tables have RLS enabled ✓';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'MIGRATION COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '================================================';
    RAISE NOTICE '';
    RAISE NOTICE 'Your Mining Operations Database is ready with:';
    RAISE NOTICE '✓ Production tracking and reporting';
    RAISE NOTICE '✓ Equipment management and maintenance';
    RAISE NOTICE '✓ Safety incident management';
    RAISE NOTICE '✓ Greencard, IUT, and OTT certifications';
    RAISE NOTICE '✓ IUT (Inspeksi Umum Terencana) inspections';
    RAISE NOTICE '✓ OTT (Observasi Tugas Terencana) observations';
    RAISE NOTICE '✓ Row Level Security for data protection';
    RAISE NOTICE '✓ Performance optimization';
    RAISE NOTICE '';
    RAISE NOTICE 'Next Steps:';
    RAISE NOTICE '1. Configure app settings for your environment';
    RAISE NOTICE '2. Set up user accounts and permissions';
    RAISE NOTICE '3. Populate production targets and calendar';
    RAISE NOTICE '4. Connect your mobile app';
    RAISE NOTICE '5. Start entering production data';
    RAISE NOTICE '';
    RAISE NOTICE 'For support and documentation, see README.md files';
    RAISE NOTICE '================================================';
END $$;

-- =====================================================
-- CLEANUP MIGRATION FUNCTIONS
-- =====================================================

-- Drop temporary migration functions
DROP FUNCTION IF EXISTS migration_applied(VARCHAR);
DROP FUNCTION IF EXISTS start_migration(VARCHAR, TEXT);
DROP FUNCTION IF EXISTS complete_migration(VARCHAR, TIMESTAMPTZ);

-- =====================================================
-- FINAL VERIFICATION QUERIES
-- =====================================================

-- Show final migration status
SELECT 
    version,
    description,
    applied_at,
    execution_time_ms
FROM schema_migrations 
ORDER BY version;
