# SVG Icons System Documentation

## Overview

The SVG Icons System provides custom, colorful SVG icons for menu items in the Mining Operations App. This system uses React Native SVG to render scalable, high-quality icons with gradient colors and mining industry themes.

## Features

- **Custom SVG Icons**: Hand-crafted icons with gradient colors
- **Scalable**: Vector-based icons that scale perfectly at any size
- **Performance Optimized**: Lightweight SVG components
- **Mining Industry Themed**: Icons designed for mining operations context
- **Consistent Design**: Unified color palette and design language

## Implementation

### Core Components

#### MenuIcons.tsx (`src/components/icons/MenuIcons.tsx`)

Contains all custom SVG icon components with the following icons:

1. **CalendarIcon** - For attendance and scheduling
2. **ClockIcon** - For time tracking and ATR
3. **LocationIcon** - For position and location services
4. **PhoneIcon** - For communication (Bakomsel)
5. **InfoIcon** - For information and help
6. **PeopleIcon** - For employee management
7. **DocumentIcon** - For reports and documents
8. **VideoIcon** - For video services (iPeak)
9. **GridIcon** - For "view all" functionality

#### Icon Properties

```typescript
interface IconProps {
  width?: number;      // Default: 24
  height?: number;     // Default: 24
  opacity?: number;    // Default: 1
}
```

### Usage in DashboardScreen

#### Menu Item Configuration

```typescript
interface MenuItem {
  id: string;
  title: string;
  icon: string;                    // Fallback Ionicon name
  color: string;                   // Background color
  customIconType?: 'calendar' | 'clock' | 'location' | 'phone' | 'info' | 'people' | 'document' | 'video' | 'grid';
  onPress: () => void;
}

// Example menu item with custom SVG icon
{
  id: 'attendance',
  title: 'Attendance Recording',
  icon: 'calendar',              // Fallback
  color: Colors.primary,
  customIconType: 'calendar',    // Uses CalendarIcon SVG
  onPress: () => navigation.navigate('ProductionOverview' as never),
}
```

#### Icon Rendering Logic

```typescript
const renderCustomIcon = (iconType?: string) => {
  const iconProps = { width: 24, height: 24, opacity: 1 };
  
  switch (iconType) {
    case 'calendar':
      return <CalendarIcon {...iconProps} />;
    case 'clock':
      return <ClockIcon {...iconProps} />;
    case 'location':
      return <LocationIcon {...iconProps} />;
    // ... other cases
    default:
      return null;
  }
};

// In menu item render
<View style={[styles.menuIcon, { backgroundColor: item.color }]}>
  {item.customIconType ? (
    renderCustomIcon(item.customIconType)
  ) : (
    <Ionicons name={item.icon as any} size={24} color={Colors.white} />
  )}
</View>
```

## Icon Design Specifications

### Color Schemes

Each icon uses carefully selected gradient colors:

- **Calendar**: Blue gradients (#4FC3F7 → #29B6F6)
- **Clock**: Orange gradients (#FF9800 → #F57C00)
- **Location**: Pink gradients (#E91E63 → #C2185B)
- **Phone**: Purple gradients (#9C27B0 → #7B1FA2)
- **Info**: Blue gradients (#2196F3 → #1976D2)
- **People**: Green gradients (#4CAF50 → #388E3C)
- **Document**: Yellow gradients (#FFC107 → #FF8F00)
- **Video**: Red gradients (#F44336 → #D32F2F)
- **Grid**: Gray gradients (#607D8B → #455A64)

### Design Principles

1. **Consistent Size**: All icons designed for 16x16 viewBox
2. **Gradient Usage**: Linear gradients for depth and visual appeal
3. **Mining Context**: Colors and shapes relevant to mining operations
4. **Accessibility**: High contrast and clear shapes
5. **Scalability**: Vector-based for crisp rendering at any size

## Technical Implementation

### SVG Structure

```typescript
export const CalendarIcon: React.FC<IconProps> = ({ width = 24, height = 24, opacity = 1 }) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" style={{ opacity }}>
    <Defs>
      <LinearGradient id="cal1" x1="2" x2="14" y1="3" y2="13" gradientUnits="userSpaceOnUse">
        <Stop stopColor="#4FC3F7" />
        <Stop offset="1" stopColor="#29B6F6" />
      </LinearGradient>
    </Defs>
    <Rect x="2" y="3" width="12" height="10" rx="2" fill="url(#cal1)" />
    {/* Additional elements */}
  </Svg>
);
```

### Performance Considerations

1. **Memoization**: Icons are functional components that can be memoized
2. **Reusability**: Single icon components used across the app
3. **Bundle Size**: Optimized SVG paths for minimal bundle impact
4. **Rendering**: Hardware-accelerated SVG rendering

## Integration with Background

### Background vs Menu Icons

- **Background**: Uses original ImageBackground with mining landscape
- **Menu Icons**: Uses custom SVG icons for visual appeal and branding
- **Separation**: Clear distinction between background imagery and functional icons

### Visual Hierarchy

```
🏔️ Background Image (Mining landscape)
  ├── 🌫️ Dark overlay (30% opacity)
  ├── 👤 Profile + Status indicator
  ├── 🔍 Search bar (white with shadow)
  └── 📅 Date display (white text)

📱 Content Area (White, rounded top)
  ├── 🏷️ Tab navigation
  ├── 🎯 Menu grid with SVG icons ⭐ NEW
  └── 📺 Video section header
```

## Benefits

### User Experience

1. **Visual Appeal**: Colorful, modern icons enhance UI aesthetics
2. **Brand Consistency**: Custom icons align with mining industry theme
3. **Recognition**: Unique icons improve user recognition and navigation
4. **Professional Look**: High-quality SVG icons provide polished appearance

### Technical Benefits

1. **Scalability**: Vector icons scale perfectly on all screen sizes
2. **Performance**: Lightweight SVG components with minimal overhead
3. **Customization**: Easy to modify colors, sizes, and styles
4. **Maintainability**: Centralized icon system for easy updates

## Future Enhancements

### Planned Features

1. **Icon Animations**: Add subtle animations for interactive feedback
2. **Theme Support**: Dynamic color schemes based on app theme
3. **Additional Icons**: Expand icon library for new features
4. **Icon Variants**: Multiple styles (outline, filled, etc.)

### Technical Improvements

1. **Icon Font Generation**: Convert SVGs to icon font for better performance
2. **Dynamic Loading**: Lazy load icons based on usage
3. **Accessibility**: Enhanced screen reader support
4. **Internationalization**: Icon variants for different locales

## Related Documentation

- [Dashboard Screen](dashboard-screen.md) - Main implementation
- [Design System](../design/design-system.md) - Color and styling guidelines
- [Shadow System](../development/shadow-system.md) - Cross-platform styling
- [Architecture Overview](../architecture/overview.md) - System architecture

## File References

### Implementation Files
- `src/components/icons/MenuIcons.tsx` - SVG icon components
- `src/screens/DashboardScreen.tsx` - Icon usage implementation
- `assets/fluent-color--board-16.svg` - Original SVG reference

### Related Files
- `src/components/icons/BoardIcon.tsx` - Individual board icon component
- `src/components/backgrounds/` - Background components (not used for menu)
- `src/constants/colors.ts` - Color definitions for icon backgrounds
