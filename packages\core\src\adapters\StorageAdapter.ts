/**
 * Abstract Storage Adapter Interface
 * Platform-specific implementations should extend this class
 */
export abstract class StorageAdapter {
  /**
   * Save data to storage
   * @param key - Storage key
   * @param data - Data to store
   */
  abstract save(key: string, data: any): Promise<void>;

  /**
   * Load data from storage
   * @param key - Storage key
   * @returns Stored data or null if not found
   */
  abstract load(key: string): Promise<any>;

  /**
   * Delete data from storage
   * @param key - Storage key
   */
  abstract delete(key: string): Promise<void>;

  /**
   * Clear all data from storage
   */
  abstract clear(): Promise<void>;

  /**
   * Get all storage keys
   * @returns Array of all keys
   */
  abstract getAllKeys(): Promise<string[]>;

  /**
   * Check if key exists in storage
   * @param key - Storage key
   * @returns True if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const data = await this.load(key);
      return data !== null && data !== undefined;
    } catch {
      return false;
    }
  }

  /**
   * Get storage size (if supported by platform)
   * @returns Storage size in bytes or -1 if not supported
   */
  async getSize(): Promise<number> {
    // Default implementation returns -1 (not supported)
    // Platform-specific implementations can override this
    return -1;
  }

  /**
   * Batch save multiple key-value pairs
   * @param items - Array of key-value pairs
   */
  async batchSave(items: Array<{ key: string; data: any }>): Promise<void> {
    // Default implementation saves items sequentially
    // Platform-specific implementations can optimize this
    for (const item of items) {
      await this.save(item.key, item.data);
    }
  }

  /**
   * Batch load multiple keys
   * @param keys - Array of keys to load
   * @returns Array of loaded data (null for missing keys)
   */
  async batchLoad(keys: string[]): Promise<any[]> {
    // Default implementation loads items sequentially
    // Platform-specific implementations can optimize this
    const results: any[] = [];
    for (const key of keys) {
      try {
        const data = await this.load(key);
        results.push(data);
      } catch {
        results.push(null);
      }
    }
    return results;
  }

  /**
   * Batch delete multiple keys
   * @param keys - Array of keys to delete
   */
  async batchDelete(keys: string[]): Promise<void> {
    // Default implementation deletes items sequentially
    // Platform-specific implementations can optimize this
    for (const key of keys) {
      await this.delete(key);
    }
  }

  /**
   * Get keys matching a pattern
   * @param pattern - Pattern to match (supports wildcards)
   * @returns Array of matching keys
   */
  async getKeysMatching(pattern: string): Promise<string[]> {
    const allKeys = await this.getAllKeys();
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return allKeys.filter(key => regex.test(key));
  }

  /**
   * Serialize data for storage
   * @param data - Data to serialize
   * @returns Serialized data
   */
  protected serialize(data: any): string {
    try {
      return JSON.stringify(data);
    } catch (error) {
      throw new Error(`Failed to serialize data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Deserialize data from storage
   * @param serializedData - Serialized data
   * @returns Deserialized data
   */
  protected deserialize(serializedData: string): any {
    try {
      return JSON.parse(serializedData);
    } catch (error) {
      throw new Error(`Failed to deserialize data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate storage key
   * @param key - Key to validate
   */
  protected validateKey(key: string): void {
    if (!key || typeof key !== 'string') {
      throw new Error('Storage key must be a non-empty string');
    }

    if (key.length > 250) {
      throw new Error('Storage key is too long (max 250 characters)');
    }

    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    if (invalidChars.test(key)) {
      throw new Error('Storage key contains invalid characters');
    }
  }

  /**
   * Validate data size (if platform has limits)
   * @param data - Data to validate
   * @param maxSize - Maximum size in bytes
   */
  protected validateDataSize(data: any, maxSize?: number): void {
    if (!maxSize) return;

    const serialized = this.serialize(data);
    const size = new Blob([serialized]).size;

    if (size > maxSize) {
      throw new Error(`Data size (${size} bytes) exceeds maximum allowed size (${maxSize} bytes)`);
    }
  }

  /**
   * Create a namespaced key
   * @param namespace - Namespace
   * @param key - Original key
   * @returns Namespaced key
   */
  protected createNamespacedKey(namespace: string, key: string): string {
    return `${namespace}:${key}`;
  }

  /**
   * Extract key from namespaced key
   * @param namespacedKey - Namespaced key
   * @param namespace - Namespace
   * @returns Original key
   */
  protected extractKeyFromNamespace(namespacedKey: string, namespace: string): string {
    const prefix = `${namespace}:`;
    if (namespacedKey.startsWith(prefix)) {
      return namespacedKey.substring(prefix.length);
    }
    return namespacedKey;
  }

  /**
   * Compress data before storage (if supported)
   * @param data - Data to compress
   * @returns Compressed data
   */
  protected async compress(data: string): Promise<string> {
    // Default implementation returns data as-is
    // Platform-specific implementations can add compression
    return data;
  }

  /**
   * Decompress data after loading (if supported)
   * @param compressedData - Compressed data
   * @returns Decompressed data
   */
  protected async decompress(compressedData: string): Promise<string> {
    // Default implementation returns data as-is
    // Platform-specific implementations can add decompression
    return compressedData;
  }

  /**
   * Encrypt data before storage (if supported)
   * @param data - Data to encrypt
   * @returns Encrypted data
   */
  protected async encrypt(data: string): Promise<string> {
    // Default implementation returns data as-is
    // Platform-specific implementations can add encryption
    return data;
  }

  /**
   * Decrypt data after loading (if supported)
   * @param encryptedData - Encrypted data
   * @returns Decrypted data
   */
  protected async decrypt(encryptedData: string): Promise<string> {
    // Default implementation returns data as-is
    // Platform-specific implementations can add decryption
    return encryptedData;
  }
}
