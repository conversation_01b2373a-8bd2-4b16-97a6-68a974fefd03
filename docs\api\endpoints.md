# 🌐 API Endpoints Documentation

## 📋 Table of Contents
- [API Overview](#api-overview)
- [Authentication](#authentication)
- [Production Metrics](#production-metrics)
- [User Management](#user-management)
- [Equipment Management](#equipment-management)
- [Reports & Analytics](#reports--analytics)
- [Error Handling](#error-handling)

## 🎯 API Overview

### Base Configuration
```typescript
const API_CONFIG = {
  baseURL: 'https://your-project.supabase.co',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'apikey': process.env.SUPABASE_ANON_KEY,
  }
};
```

### Response Format
```typescript
interface APIResponse<T> {
  data: T | null;
  error: {
    message: string;
    details?: string;
    hint?: string;
  } | null;
  count?: number;
  status: number;
  statusText: string;
}
```

## 🔐 Authentication

### Login
```http
POST /auth/v1/token?grant_type=password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "refresh_token": "refresh_token_here",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "user_metadata": {
      "first_name": "John",
      "last_name": "Doe",
      "role": "operator"
    }
  }
}
```

### Refresh Token
```http
POST /auth/v1/token?grant_type=refresh_token
Content-Type: application/json

{
  "refresh_token": "refresh_token_here"
}
```

### Logout
```http
POST /auth/v1/logout
Authorization: Bearer {access_token}
```

## 📊 Production Metrics

### Get Daily Production Metrics
```http
GET /rest/v1/daily_production_metrics
Authorization: Bearer {access_token}
```

**Query Parameters:**
- `date`: Filter by specific date (YYYY-MM-DD)
- `location_id`: Filter by location UUID
- `order`: Sort order (date.asc, date.desc)
- `limit`: Number of records to return
- `offset`: Pagination offset

**Example:**
```http
GET /rest/v1/daily_production_metrics?date=gte.2025-07-01&date=lte.2025-07-31&location_id=eq.550e8400-e29b-41d4-a716-************&order=date.desc
```

**Response:**
```json
[
  {
    "id": "uuid",
    "date": "2025-07-23",
    "location_id": "550e8400-e29b-41d4-a716-************",
    "created_by": "user_uuid",
    "actual_ob": 6100,
    "plan_ob": 8000,
    "actual_ore": 3550,
    "plan_ore": 5000,
    "actual_fuel": 590,
    "plan_fuel": 600,
    "actual_rain": 0,
    "plan_rain": 2,
    "actual_slippery": 0,
    "plan_slippery": 1,
    "monthly": "July 2025",
    "week": 30,
    "notes": "Current day production - on track",
    "weather_conditions": "Clear",
    "created_at": "2025-07-23T10:00:00Z",
    "updated_at": "2025-07-23T10:00:00Z"
  }
]
```

### Create Daily Production Metric
```http
POST /rest/v1/daily_production_metrics
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "date": "2025-07-24",
  "location_id": "550e8400-e29b-41d4-a716-************",
  "actual_ob": 6200,
  "plan_ob": 8000,
  "actual_ore": 3600,
  "plan_ore": 5000,
  "actual_fuel": 580,
  "plan_fuel": 600,
  "notes": "Good production day"
}
```

### Update Daily Production Metric
```http
PATCH /rest/v1/daily_production_metrics?id=eq.{metric_id}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "actual_ob": 6300,
  "actual_ore": 3650,
  "notes": "Updated production figures"
}
```

### Bulk Create Production Metrics
```http
POST /rest/v1/daily_production_metrics
Authorization: Bearer {access_token}
Content-Type: application/json

[
  {
    "date": "2025-07-24",
    "location_id": "550e8400-e29b-41d4-a716-************",
    "actual_ob": 6200,
    "actual_ore": 3600
  },
  {
    "date": "2025-07-25",
    "location_id": "550e8400-e29b-41d4-a716-************",
    "actual_ob": 6100,
    "actual_ore": 3500
  }
]
```

## 👥 User Management

### Get Current User Profile
```http
GET /auth/v1/user
Authorization: Bearer {access_token}
```

### Get Users List
```http
GET /rest/v1/users
Authorization: Bearer {access_token}
```

**Query Parameters:**
- `role`: Filter by user role (admin, supervisor, operator, viewer)
- `is_active`: Filter by active status (true/false)
- `select`: Specify fields to return

**Example:**
```http
GET /rest/v1/users?role=eq.operator&is_active=eq.true&select=id,email,first_name,last_name,role
```

### Update User Profile
```http
PATCH /rest/v1/users?id=eq.{user_id}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Smith",
  "phone": "+1234567890",
  "department": "Mining Operations"
}
```

## 🏗️ Equipment Management

### Get Equipment List
```http
GET /rest/v1/equipment
Authorization: Bearer {access_token}
```

**Query Parameters:**
- `type`: Filter by equipment type
- `status`: Filter by status (operational, maintenance, repair, retired)
- `location_id`: Filter by location

**Example:**
```http
GET /rest/v1/equipment?status=eq.operational&type=eq.excavator
```

**Response:**
```json
[
  {
    "id": "uuid",
    "name": "Excavator CAT 390F",
    "equipment_number": "EX-001",
    "type": "excavator",
    "manufacturer": "Caterpillar",
    "model": "390F",
    "year_manufactured": 2020,
    "capacity": 4.5,
    "capacity_unit": "m³",
    "status": "operational",
    "location_id": "location_uuid",
    "assigned_operator_id": "user_uuid",
    "last_maintenance_date": "2025-07-01",
    "next_maintenance_date": "2025-08-01"
  }
]
```

### Update Equipment Status
```http
PATCH /rest/v1/equipment?id=eq.{equipment_id}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "status": "maintenance",
  "assigned_operator_id": null,
  "notes": "Scheduled maintenance"
}
```

## 📈 Reports & Analytics

### Get Production Summary
```http
GET /rest/v1/rpc/get_production_summary
Authorization: Bearer {access_token}
```

**Parameters:**
```json
{
  "start_date": "2025-07-01",
  "end_date": "2025-07-31",
  "location_id": "550e8400-e29b-41d4-a716-************",
  "group_by": "daily"
}
```

**Response:**
```json
{
  "summary": {
    "total_ob": 189000,
    "total_ore": 110000,
    "average_strip_ratio": 1.72,
    "total_fuel": 18500,
    "achievement_percentage": 76.3
  },
  "daily_breakdown": [
    {
      "date": "2025-07-01",
      "actual_ob": 6100,
      "actual_ore": 3550,
      "strip_ratio": 1.72,
      "fuel_consumed": 590
    }
  ]
}
```

### Get Analytics Data
```http
GET /rest/v1/rpc/get_analytics_data
Authorization: Bearer {access_token}
```

**Parameters:**
```json
{
  "metric_type": "strip_ratio",
  "period": "monthly",
  "start_date": "2025-01-01",
  "end_date": "2025-12-31",
  "location_id": "550e8400-e29b-41d4-a716-************"
}
```

### Export Production Report
```http
POST /rest/v1/rpc/export_production_report
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "start_date": "2025-07-01",
  "end_date": "2025-07-31",
  "location_id": "550e8400-e29b-41d4-a716-************",
  "format": "csv",
  "include_charts": true
}
```

**Response:**
```json
{
  "export_id": "uuid",
  "download_url": "https://storage.supabase.co/v1/object/public/reports/export_uuid.csv",
  "expires_at": "2025-07-24T10:00:00Z"
}
```

## 🏢 Locations

### Get Locations
```http
GET /rest/v1/locations
Authorization: Bearer {access_token}
```

**Response:**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Main Pit",
    "code": "PIT-001",
    "type": "pit",
    "description": "Primary mining pit",
    "latitude": -6.2088,
    "longitude": 106.8456,
    "elevation": 150.5,
    "timezone": "Asia/Jakarta",
    "is_active": true,
    "parent_location_id": null
  }
]
```

## 🔄 Real-time Subscriptions

### Subscribe to Production Metrics Changes
```typescript
const subscription = supabase
  .channel('production_metrics')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'daily_production_metrics',
      filter: 'location_id=eq.550e8400-e29b-41d4-a716-************'
    },
    (payload) => {
      console.log('Production metrics changed:', payload);
      // Handle real-time updates
    }
  )
  .subscribe();
```

### Subscribe to Equipment Status Changes
```typescript
const equipmentSubscription = supabase
  .channel('equipment_status')
  .on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'equipment',
      filter: 'status=neq.operational'
    },
    (payload) => {
      console.log('Equipment status changed:', payload);
      // Handle equipment alerts
    }
  )
  .subscribe();
```

## ❌ Error Handling

### Error Response Format
```json
{
  "error": {
    "message": "Invalid input",
    "details": "Missing required field: location_id",
    "hint": "Ensure all required fields are provided",
    "code": "PGRST116"
  }
}
```

### Common Error Codes
- `PGRST116`: Missing required field
- `PGRST301`: Row not found
- `PGRST302`: Duplicate key violation
- `PGRST204`: No content (successful delete)
- `42501`: Insufficient privileges
- `23505`: Unique constraint violation

### Rate Limiting
```http
HTTP/1.1 429 Too Many Requests
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1627846261

{
  "error": {
    "message": "Rate limit exceeded",
    "details": "Maximum 100 requests per minute allowed"
  }
}
```

### Client-side Error Handling
```typescript
try {
  const { data, error } = await supabase
    .from('daily_production_metrics')
    .select('*')
    .eq('location_id', locationId);

  if (error) {
    throw new Error(`Database error: ${error.message}`);
  }

  return data;
} catch (error) {
  console.error('API Error:', error);
  
  if (error.code === 'PGRST301') {
    // Handle not found
  } else if (error.code === '42501') {
    // Handle permission denied
  } else {
    // Handle generic error
  }
  
  throw error;
}
```

---

**Related Documentation**:
- [Authentication](authentication.md)
- [Data Models](data-models.md)
- [Error Handling](error-handling.md)
