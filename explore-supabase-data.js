const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ohqbaimnhwvdfrmxvhxv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ocWJhaW1uaHd2ZGZybXh2aHh2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4ODA3NzEsImV4cCI6MjA2ODQ1Njc3MX0.Qq-2pKIvW2SSJlgQqTW6I_gXdxt81oWv2wViadb9b-Q';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function exploreProductionMetrics() {
  console.log('📊 Exploring Production Metrics Data...\n');
  
  try {
    // Get total count and date range
    const { count } = await supabase
      .from('daily_production_metrics')
      .select('*', { count: 'exact', head: true });

    console.log(`📈 Total Production Records: ${count}`);

    // Get date range
    const { data: firstRecord } = await supabase
      .from('daily_production_metrics')
      .select('date')
      .order('date', { ascending: true })
      .limit(1);

    const { data: lastRecord } = await supabase
      .from('daily_production_metrics')
      .select('date')
      .order('date', { ascending: false })
      .limit(1);

    if (firstRecord && lastRecord) {
      console.log(`📅 Date Range: ${firstRecord[0].date} to ${lastRecord[0].date}`);
    }

    // Get sample records with all fields
    const { data: sampleData } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .order('date', { ascending: false })
      .limit(3);

    if (sampleData && sampleData.length > 0) {
      console.log('\n📋 Sample Production Records:');
      sampleData.forEach((record, index) => {
        console.log(`\n${index + 1}. Date: ${record.date}`);
        console.log(`   OB (Overburden): ${record.actual_ob} Bcm`);
        console.log(`   Ore: ${record.actual_ore} tons`);
        console.log(`   Fuel: ${record.actual_fuel} L`);
        console.log(`   Strip Ratio: ${record.actual_ore > 0 ? (record.actual_ob / record.actual_ore).toFixed(2) : '0.00'}`);
        console.log(`   Fuel Ratio: ${(record.actual_ob + record.actual_ore) > 0 ? (record.actual_fuel / (record.actual_ob + record.actual_ore)).toFixed(2) : '0.00'} L/ton`);
        
        if (record.target_ob || record.target_ore) {
          console.log(`   Target OB: ${record.target_ob || 'N/A'} Bcm`);
          console.log(`   Target Ore: ${record.target_ore || 'N/A'} tons`);
          
          if (record.target_ob && record.actual_ob) {
            const obAchievement = ((record.actual_ob / record.target_ob) * 100).toFixed(1);
            console.log(`   OB Achievement: ${obAchievement}%`);
          }
          
          if (record.target_ore && record.actual_ore) {
            const oreAchievement = ((record.actual_ore / record.target_ore) * 100).toFixed(1);
            console.log(`   Ore Achievement: ${oreAchievement}%`);
          }
        }
      });
    }

  } catch (error) {
    console.error('❌ Error exploring production metrics:', error.message);
  }
}

async function getMonthlyStatistics() {
  console.log('\n📊 Monthly Production Statistics...\n');
  
  try {
    // Get current month data (September 2025)
    const { data: septemberData } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .gte('date', '2025-09-01')
      .lte('date', '2025-09-30')
      .order('date', { ascending: true });

    if (septemberData && septemberData.length > 0) {
      const totalOB = septemberData.reduce((sum, record) => sum + (record.actual_ob || 0), 0);
      const totalOre = septemberData.reduce((sum, record) => sum + (record.actual_ore || 0), 0);
      const totalFuel = septemberData.reduce((sum, record) => sum + (record.actual_fuel || 0), 0);
      const avgStripRatio = totalOre > 0 ? (totalOB / totalOre).toFixed(2) : '0.00';
      const avgFuelRatio = (totalOB + totalOre) > 0 ? (totalFuel / (totalOB + totalOre)).toFixed(2) : '0.00';

      console.log(`📅 September 2025 Summary (${septemberData.length} days):`);
      console.log(`   Total Overburden: ${totalOB.toLocaleString()} Bcm`);
      console.log(`   Total Ore: ${totalOre.toLocaleString()} tons`);
      console.log(`   Total Material: ${(totalOB + totalOre).toLocaleString()} tons`);
      console.log(`   Total Fuel: ${totalFuel.toLocaleString()} L`);
      console.log(`   Average Strip Ratio: ${avgStripRatio}`);
      console.log(`   Average Fuel Ratio: ${avgFuelRatio} L/ton`);
      
      // Daily averages
      console.log(`\n📊 Daily Averages:`);
      console.log(`   OB: ${(totalOB / septemberData.length).toFixed(0)} Bcm/day`);
      console.log(`   Ore: ${(totalOre / septemberData.length).toFixed(0)} tons/day`);
      console.log(`   Fuel: ${(totalFuel / septemberData.length).toFixed(0)} L/day`);
    }

    // Get previous month for comparison (August 2025)
    const { data: augustData } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .gte('date', '2025-08-01')
      .lte('date', '2025-08-31')
      .order('date', { ascending: true });

    if (augustData && augustData.length > 0) {
      const totalOB = augustData.reduce((sum, record) => sum + (record.actual_ob || 0), 0);
      const totalOre = augustData.reduce((sum, record) => sum + (record.actual_ore || 0), 0);
      const totalFuel = augustData.reduce((sum, record) => sum + (record.actual_fuel || 0), 0);

      console.log(`\n📅 August 2025 Summary (${augustData.length} days):`);
      console.log(`   Total Overburden: ${totalOB.toLocaleString()} Bcm`);
      console.log(`   Total Ore: ${totalOre.toLocaleString()} tons`);
      console.log(`   Total Fuel: ${totalFuel.toLocaleString()} L`);
    }

  } catch (error) {
    console.error('❌ Error getting monthly statistics:', error.message);
  }
}

async function checkDatabaseSchema() {
  console.log('\n🏗️ Checking Database Schema...\n');
  
  try {
    // Check what columns exist in daily_production_metrics
    const { data: sampleRecord } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .limit(1);

    if (sampleRecord && sampleRecord.length > 0) {
      console.log('📋 daily_production_metrics table columns:');
      const columns = Object.keys(sampleRecord[0]);
      columns.forEach(col => {
        const value = sampleRecord[0][col];
        const type = typeof value;
        console.log(`   - ${col}: ${type} (example: ${value})`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  }
}

async function testQueries() {
  console.log('\n🧪 Testing Advanced Queries...\n');
  
  try {
    // Test aggregation query
    console.log('📊 Testing aggregation queries...');
    
    // Get weekly totals for current month
    const { data: weeklyData } = await supabase
      .from('daily_production_metrics')
      .select('date, actual_ob, actual_ore, actual_fuel')
      .gte('date', '2025-09-01')
      .lte('date', '2025-09-07')
      .order('date', { ascending: true });

    if (weeklyData && weeklyData.length > 0) {
      const weekTotal = weeklyData.reduce((acc, day) => ({
        ob: acc.ob + (day.actual_ob || 0),
        ore: acc.ore + (day.actual_ore || 0),
        fuel: acc.fuel + (day.actual_fuel || 0)
      }), { ob: 0, ore: 0, fuel: 0 });

      console.log(`📅 Week 1 September 2025 (${weeklyData.length} days):`);
      console.log(`   Total OB: ${weekTotal.ob.toLocaleString()} Bcm`);
      console.log(`   Total Ore: ${weekTotal.ore.toLocaleString()} tons`);
      console.log(`   Total Fuel: ${weekTotal.fuel.toLocaleString()} L`);
      console.log(`   Strip Ratio: ${weekTotal.ore > 0 ? (weekTotal.ob / weekTotal.ore).toFixed(2) : '0.00'}`);
    }

  } catch (error) {
    console.error('❌ Error testing queries:', error.message);
  }
}

async function main() {
  console.log('🚀 Exploring Supabase Database Data...\n');
  
  try {
    await exploreProductionMetrics();
    await getMonthlyStatistics();
    await checkDatabaseSchema();
    await testQueries();

    console.log('\n✅ Data exploration completed successfully!');
    console.log('\n🌐 Access your Supabase dashboard at:');
    console.log(`   https://supabase.com/dashboard/project/ohqbaimnhwvdfrmxvhxv`);
    console.log('\n📊 Useful Supabase dashboard sections:');
    console.log('   - Table Editor: View and edit data');
    console.log('   - SQL Editor: Run custom queries');
    console.log('   - API Docs: Auto-generated API documentation');
    console.log('   - Database: Schema and relationships');

  } catch (error) {
    console.error('\n❌ Data exploration failed:', error.message);
  }
}

main().catch(console.error);
