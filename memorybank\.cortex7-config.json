{"memorybank": {"version": "1.0.0", "standard": "Cortex 7", "project": "MiningOperationsApp", "technology": ["React Native", "TypeScript", "Supabase", "Expo"], "lastUpdated": "2025-01-19", "autoIndex": true, "searchable": true}, "structure": {"implementation": {"description": "Technical implementation details and guides", "tags": ["implementation", "technical", "guides"]}, "charts": {"description": "Chart system documentation and improvements", "tags": ["charts", "scrollable", "optimization", "mobile"]}, "database": {"description": "Database integration and production metrics", "tags": ["database", "supabase", "jwt", "production-metrics"]}, "typescript": {"description": "TypeScript error resolution and type safety", "tags": ["typescript", "errors", "type-safety", "compilation"]}, "testing": {"description": "Testing utilities and verification frameworks", "tags": ["testing", "verification", "quality-assurance"]}, "troubleshooting": {"description": "Common issues and resolution guides", "tags": ["troubleshooting", "debugging", "issues", "solutions"]}, "changelog": {"description": "Version history and feature evolution", "tags": ["changelog", "versions", "history", "features"]}, "project-overview": {"description": "High-level project information and roadmap", "tags": ["project", "overview", "roadmap", "technology-stack"]}, "architecture": {"description": "System architecture and design patterns", "tags": ["architecture", "design-patterns", "component-structure"]}, "components": {"description": "Component library and UI patterns", "tags": ["components", "ui-library", "reusable", "patterns"]}, "screens": {"description": "Screen components and navigation patterns", "tags": ["screens", "navigation", "ui-screens", "implementation"]}, "navigation": {"description": "Navigation system and routing documentation", "tags": ["navigation", "routing", "react-navigation", "mobile-navigation"]}, "styling": {"description": "Design system and styling guidelines", "tags": ["styling", "design-system", "colors", "layout", "typography"]}, "consolidated": {"description": "Legacy documentation and consolidated summaries", "tags": ["legacy", "consolidated", "archive"]}}, "searchTags": ["react-native", "expo", "typescript", "supabase", "charts", "scrollable", "database", "production-metrics", "ui-ux", "mobile-app", "mining-operations", "horizontal-scrolling", "dynamic-width", "jwt-handling", "error-resolution", "testing", "performance", "optimization"], "quickAccess": {"mainSummary": "CONSOLIDATED_SUMMARY.md", "projectOverview": "project-overview/README.md", "systemArchitecture": "architecture/README.md", "chartSystem": "charts/README.md", "databaseIntegration": "database/README.md", "typescriptFixes": "typescript/README.md", "testingFramework": "testing/README.md", "componentLibrary": "components/README.md", "screenDocumentation": "screens/README.md", "navigationSystem": "navigation/README.md", "designSystem": "styling/README.md", "troubleshooting": "troubleshooting/README.md"}, "indexing": {"enabled": true, "autoUpdate": true, "searchDepth": 3, "includeCodeBlocks": true, "includeMetadata": true}}