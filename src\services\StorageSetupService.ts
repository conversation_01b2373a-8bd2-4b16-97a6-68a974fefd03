import { supabase } from '../config/supabase';

export interface StorageSetupResult {
  success: boolean;
  message: string;
  details?: any;
}

class StorageSetupService {
  private static instance: StorageSetupService;

  static getInstance(): StorageSetupService {
    if (!StorageSetupService.instance) {
      StorageSetupService.instance = new StorageSetupService();
    }
    return StorageSetupService.instance;
  }

  /**
   * Check if storage is properly configured
   */
  async checkStorageConfiguration(): Promise<StorageSetupResult> {
    try {
      console.log('🔍 Checking storage configuration...');

      // Check if we can access storage
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();

      if (listError) {
        console.error('❌ Cannot access storage:', listError);
        return {
          success: false,
          message: 'Cannot access Supabase storage. Check your configuration.',
          details: listError
        };
      }

      console.log('📋 Available buckets:', buckets?.map(b => ({ id: b.id, name: b.name, public: b.public })));

      // Check if profile-photos bucket exists
      const profilePhotosBucket = buckets?.find(bucket => bucket.id === 'profile-photos');

      if (!profilePhotosBucket) {
        console.log('⚠️ Profile-photos bucket not found. Available buckets:', buckets?.map(b => b.id));
        return {
          success: false,
          message: 'Profile-photos bucket not found',
          details: { availableBuckets: buckets?.map(b => b.id) || [] }
        };
      }

      console.log('✅ Profile-photos bucket found:', {
        id: profilePhotosBucket.id,
        name: profilePhotosBucket.name,
        public: profilePhotosBucket.public,
        fileSizeLimit: profilePhotosBucket.file_size_limit,
        allowedMimeTypes: profilePhotosBucket.allowed_mime_types
      });

      // Check if bucket is public
      if (!profilePhotosBucket.public) {
        console.log('⚠️ Profile-photos bucket is not public');
        return {
          success: false,
          message: 'Profile-photos bucket is not configured as public',
          details: profilePhotosBucket
        };
      }

      console.log('✅ Storage configuration is correct');
      return {
        success: true,
        message: 'Storage is properly configured',
        details: profilePhotosBucket
      };

    } catch (error) {
      console.error('❌ Error checking storage configuration:', error);
      return {
        success: false,
        message: 'Failed to check storage configuration',
        details: error
      };
    }
  }

  /**
   * Attempt to create profile-photos bucket
   */
  async createProfilePhotosBucket(): Promise<StorageSetupResult> {
    try {
      console.log('🔧 Creating profile-photos bucket...');

      const { data, error } = await supabase.storage.createBucket('profile-photos', {
        public: true,
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      });

      if (error) {
        // Check if error is because bucket already exists
        if (error.message?.includes('already exists') || error.message?.includes('Duplicate')) {
          console.log('ℹ️ Bucket already exists, checking configuration...');

          // Check if existing bucket is properly configured
          const checkResult = await this.checkStorageConfiguration();
          if (checkResult.success) {
            return {
              success: true,
              message: 'Profile-photos bucket already exists and is properly configured',
              details: checkResult.details
            };
          } else {
            return {
              success: false,
              message: 'Bucket exists but is not properly configured',
              details: checkResult.details
            };
          }
        }

        console.error('❌ Failed to create bucket:', error);
        return {
          success: false,
          message: 'Failed to create profile-photos bucket',
          details: error
        };
      }

      console.log('✅ Profile-photos bucket created successfully');
      return {
        success: true,
        message: 'Profile-photos bucket created successfully',
        details: data
      };

    } catch (error) {
      console.error('❌ Error creating bucket:', error);
      return {
        success: false,
        message: 'Error creating profile-photos bucket',
        details: error
      };
    }
  }

  /**
   * Setup storage with automatic bucket creation
   */
  async setupStorage(): Promise<StorageSetupResult> {
    try {
      console.log('🚀 Starting storage setup...');

      // First check current configuration
      const checkResult = await this.checkStorageConfiguration();

      if (checkResult.success) {
        console.log('✅ Storage already configured properly');
        return checkResult; // Already configured
      }

      // Log the specific issue
      console.log('⚠️ Storage configuration issue:', checkResult.message);

      // If bucket doesn't exist, try to create it
      if (checkResult.message.includes('not found')) {
        console.log('🔧 Attempting to create profile-photos bucket...');
        const createResult = await this.createProfilePhotosBucket();

        if (!createResult.success) {
          console.error('❌ Failed to create bucket:', createResult.details);
          return {
            success: false,
            message: 'Could not create storage bucket. Bucket may already exist or insufficient permissions.',
            details: createResult.details
          };
        }

        // Verify creation was successful
        const verifyResult = await this.checkStorageConfiguration();
        return verifyResult;
      }

      // For other issues (like bucket not public), return the original check result
      return checkResult;

    } catch (error) {
      console.error('❌ Error in storage setup:', error);
      return {
        success: false,
        message: 'Storage setup failed',
        details: error
      };
    }
  }

  /**
   * Get storage status for debugging
   */
  async getStorageStatus(): Promise<any> {
    try {
      const { data: buckets, error } = await supabase.storage.listBuckets();
      
      return {
        canAccessStorage: !error,
        error: error?.message,
        buckets: buckets?.map(b => ({
          id: b.id,
          name: b.name,
          public: b.public,
          createdAt: b.created_at
        })) || [],
        profilePhotosBucketExists: buckets?.some(b => b.id === 'profile-photos'),
        profilePhotosBucketPublic: buckets?.find(b => b.id === 'profile-photos')?.public
      };
    } catch (error) {
      return {
        canAccessStorage: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        buckets: []
      };
    }
  }





  /**
   * Complete storage diagnostic
   */
  async runDiagnostic(): Promise<any> {
    console.log('🔍 Running complete storage diagnostic...');

    const status = await this.getStorageStatus();
    const setupResult = await this.setupStorage();
    
    // Test functionality removed - using ProfileImageService for actual uploads

    const diagnostic = {
      timestamp: new Date().toISOString(),
      storageStatus: status,
      setupResult,
      recommendations: []
    };

    // Generate recommendations
    if (!status.canAccessStorage) {
      diagnostic.recommendations.push('Check Supabase configuration and API keys');
    }
    
    if (!status.profilePhotosBucketExists) {
      diagnostic.recommendations.push('Create profile-photos bucket in Supabase Dashboard');
    }

    if (status.profilePhotosBucketExists && !status.profilePhotosBucketPublic) {
      diagnostic.recommendations.push('Make profile-photos bucket public in Supabase Dashboard');
    }

    if (!setupResult.success) {
      diagnostic.recommendations.push('Manual setup required in Supabase Dashboard');
    }

    console.log('📊 Storage diagnostic complete:', diagnostic);
    return diagnostic;
  }
}

export default StorageSetupService;
