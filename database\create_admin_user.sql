-- =====================================================
-- Create Administrator User for Dashboard Header Management
-- =====================================================

-- First, create the admin user in auth.users (this should be done via Supabase Auth UI or API)
-- But we'll prepare the profile data for when the admin user signs up

-- Insert admin user profile (replace with actual admin user ID after signup)
-- You'll need to replace 'ADMIN_USER_ID_HERE' with the actual UUID from auth.users
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    employee_id,
    nik,
    departemen,
    jabatan,
    is_active,
    created_at,
    updated_at
) VALUES (
    'ADMIN_USER_ID_HERE', -- Replace with actual admin user ID from auth.users
    '<EMAIL>',
    'System Administrator',
    '+62-812-3456-7890',
    'ADMIN001',
    '3201234567890001',
    'Administration',
    'System Administrator',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    updated_at = NOW();

-- Alternative: Create multiple admin users
INSERT INTO users (
    id,
    email,
    full_name,
    phone,
    employee_id,
    nik,
    departemen,
    jabatan,
    is_active,
    created_at,
    updated_at
) VALUES 
(
    'ADMIN_USER_ID_1', -- Replace with actual UUID
    '<EMAIL>',
    'Admin User 1',
    '+62-812-1111-1111',
    'ADMIN001',
    '3201111111111111',
    'Administration',
    'Administrator',
    true,
    NOW(),
    NOW()
),
(
    'ADMIN_USER_ID_2', -- Replace with actual UUID
    '<EMAIL>',
    'Mining Manager',
    '+62-812-2222-2222',
    'MGR001',
    '3202222222222222',
    'Management',
    'Operations Manager',
    true,
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    departemen = EXCLUDED.departemen,
    jabatan = EXCLUDED.jabatan,
    updated_at = NOW();

-- Create a function to check if user is admin (enhanced version)
CREATE OR REPLACE FUNCTION is_user_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_id 
        AND is_active = true
        AND (
            departemen IN ('Administration', 'Management') 
            OR LOWER(jabatan) LIKE '%admin%'
            OR LOWER(jabatan) LIKE '%manager%'
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION is_user_admin(UUID) TO authenticated;

-- Create a function to get admin users list
CREATE OR REPLACE FUNCTION get_admin_users()
RETURNS TABLE (
    id UUID,
    email VARCHAR(255),
    full_name VARCHAR(255),
    departemen VARCHAR(100),
    jabatan VARCHAR(100)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.email,
        u.full_name,
        u.departemen,
        u.jabatan
    FROM users u
    WHERE u.is_active = true
    AND (
        u.departemen IN ('Administration', 'Management') 
        OR LOWER(u.jabatan) LIKE '%admin%'
        OR LOWER(u.jabatan) LIKE '%manager%'
    )
    ORDER BY u.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_admin_users() TO authenticated;
