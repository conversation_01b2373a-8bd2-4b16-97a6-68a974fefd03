#!/usr/bin/env ts-node

import { DatabaseService } from '../src/services/supabase';
import { supabase } from '../src/services/supabase';

// Function to check Supabase connection and database structure
async function checkSupabaseConnection() {
  try {
    console.log('🔍 Checking Supabase connection...');

    // Test basic connection
    const { data, error } = await supabase
      .from('daily_production_metrics')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }

    console.log('✅ Supabase connection successful');
    console.log(`📊 Total records in daily_production_metrics: ${data?.length || 0}`);
    return true;

  } catch (error) {
    console.error('❌ Connection error:', error);
    return false;
  }
}

// Function to check database tables and structure
async function checkDatabaseStructure() {
  try {
    console.log('\n🏗️  Checking database structure...');

    // Check if required tables exist
    const tables = [
      'daily_production_metrics',
      'users',
      'locations',
      'equipment',
      'production_reports',
      'safety_incidents'
    ];

    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });

        if (error) {
          console.log(`❌ Table '${table}': ${error.message}`);
        } else {
          console.log(`✅ Table '${table}': exists`);
        }
      } catch (err) {
        console.log(`❌ Table '${table}': not accessible`);
      }
    }

  } catch (error) {
    console.error('❌ Error checking database structure:', error);
  }
}

// Function to get database statistics
async function getDatabaseStats() {
  try {
    console.log('\n📊 Getting database statistics...');

    // Get production metrics count
    const { count: metricsCount } = await supabase
      .from('daily_production_metrics')
      .select('*', { count: 'exact', head: true });

    console.log(`📈 Production Metrics: ${metricsCount || 0} records`);

    // Get date range of data
    const { data: dateRange } = await supabase
      .from('daily_production_metrics')
      .select('date')
      .order('date', { ascending: true })
      .limit(1);

    const { data: latestDate } = await supabase
      .from('daily_production_metrics')
      .select('date')
      .order('date', { ascending: false })
      .limit(1);

    if (dateRange && dateRange.length > 0 && latestDate && latestDate.length > 0) {
      console.log(`📅 Date Range: ${dateRange[0].date} to ${latestDate[0].date}`);
    }

    // Get recent records
    const { data: recentData } = await supabase
      .from('daily_production_metrics')
      .select('date, actual_ob, actual_ore, actual_fuel')
      .order('date', { ascending: false })
      .limit(5);

    if (recentData && recentData.length > 0) {
      console.log('\n📋 Recent 5 records:');
      recentData.forEach((record: any) => {
        const stripRatio = record.actual_ore > 0 ? (record.actual_ob / record.actual_ore).toFixed(2) : '0.00';
        console.log(`  ${record.date}: OB=${record.actual_ob}, Ore=${record.actual_ore}, Strip Ratio=${stripRatio}, Fuel=${record.actual_fuel}L`);
      });
    }

  } catch (error) {
    console.error('❌ Error getting database stats:', error);
  }
}

// Function to test data queries
async function testDataQueries() {
  try {
    console.log('\n🧪 Testing data queries...');

    // Test date range query
    const endDate = '2025-07-23';
    const startDate = '2025-07-01';

    const { data, error } = await supabase
      .from('daily_production_metrics')
      .select('*')
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: true });

    if (error) {
      console.error('❌ Query failed:', error.message);
      return;
    }

    console.log(`✅ Query successful: ${data?.length || 0} records found for July 2025`);

    if (data && data.length > 0) {
      // Calculate some basic statistics
      const totalOb = data.reduce((sum: number, item: any) => sum + (item.actual_ob || 0), 0);
      const totalOre = data.reduce((sum: number, item: any) => sum + (item.actual_ore || 0), 0);
      const avgStripRatio = totalOre > 0 ? (totalOb / totalOre).toFixed(2) : '0.00';

      console.log(`📊 July 2025 Statistics:`);
      console.log(`  Total Overburden: ${totalOb.toLocaleString()} Bcm`);
      console.log(`  Total Ore: ${totalOre.toLocaleString()} tons`);
      console.log(`  Average Strip Ratio: ${avgStripRatio}`);
    }

  } catch (error) {
    console.error('❌ Error testing queries:', error);
  }
}

// Main function
async function main() {
  console.log('🚀 Starting Supabase Database Check...\n');

  try {
    // Check connection
    const isConnected = await checkSupabaseConnection();
    if (!isConnected) {
      console.log('❌ Cannot proceed without database connection');
      process.exit(1);
    }

    // Check structure
    await checkDatabaseStructure();

    // Get statistics
    await getDatabaseStats();

    // Test queries
    await testDataQueries();

    console.log('\n✅ Database check completed successfully!');

  } catch (error) {
    console.error('\n❌ Database check failed:', error);
    process.exit(1);
  }
}

// Export functions
export { checkSupabaseConnection, checkDatabaseStructure, getDatabaseStats, testDataQueries };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}