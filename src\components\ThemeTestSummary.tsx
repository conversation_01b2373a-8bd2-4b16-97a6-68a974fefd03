import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useTheme, useThemeColors } from '../contexts/ThemeContext';
import { Layout } from '../constants/layout';
import ThemedCard from './ThemedCard';
import ThemedText from './ThemedText';
import ThemedButton from './ThemedButton';

const ThemeTestSummary: React.FC = () => {
  const { themeMode, isDarkMode, toggleTheme } = useTheme();
  const colors = useThemeColors();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
      padding: Layout.spacing.lg,
    },
    section: {
      marginBottom: Layout.spacing.xl,
    },
    title: {
      fontSize: Layout.fontSize.xl,
      fontWeight: 'bold',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: Layout.fontSize.lg,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: Layout.spacing.md,
    },
    info: {
      fontSize: Layout.fontSize.md,
      color: colors.textSecondary,
      marginBottom: Layout.spacing.sm,
    },
    colorBox: {
      width: 50,
      height: 50,
      borderRadius: Layout.borderRadius.md,
      marginRight: Layout.spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    colorRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Layout.spacing.sm,
    },
    colorLabel: {
      fontSize: Layout.fontSize.sm,
      color: colors.textSecondary,
    },
    implementationStatus: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Layout.spacing.xs,
    },
    statusIcon: {
      marginRight: Layout.spacing.xs,
    },
    statusText: {
      fontSize: Layout.fontSize.sm,
      color: colors.textPrimary,
    },
  });

  const implementedScreens = [
    '✅ ProfileScreenWithAuth - Fully implemented',
    '✅ DashboardScreen - Quick Actions transparent, Production/Activity dark',
    '✅ LoginScreen - Theme hooks added',
    '✅ ProductionOverviewScreen - Theme hooks added',
    '✅ EquipmentScreen - Theme hooks added',
    '✅ SafetyScreen - Theme hooks added',
    '✅ ReportsScreen - Theme hooks added',
    '✅ EditProfileScreen - Theme hooks added',
    '✅ ThemeSettingsScreen - Fully functional',
  ];

  const themeComponents = [
    '✅ ThemeContext - Professional dark colors',
    '✅ ThemedCard - Elevation and shadows',
    '✅ ThemedText - Typography variants',
    '✅ ThemedButton - Button variants',
    '✅ ThemedScreen - Screen wrapper',
  ];

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🎨 Theme Implementation Summary</Text>

      {/* Current Theme Status */}
      <View style={styles.section}>
        <ThemedCard variant="elevated" padding="large">
          <ThemedText variant="primary" size="lg" weight="bold" style={{ marginBottom: Layout.spacing.md }}>
            Current Theme Status
          </ThemedText>
          <ThemedText variant="secondary" style={{ marginBottom: Layout.spacing.sm }}>
            Mode: {themeMode}
          </ThemedText>
          <ThemedText variant="secondary" style={{ marginBottom: Layout.spacing.sm }}>
            Dark Mode Active: {isDarkMode ? 'Yes' : 'No'}
          </ThemedText>
          <ThemedButton
            title={`Switch to ${isDarkMode ? 'Light' : 'Dark'} Mode`}
            onPress={toggleTheme}
            variant="primary"
            size="medium"
            icon="color-palette-outline"
          />
        </ThemedCard>
      </View>

      {/* Color Palette */}
      <View style={styles.section}>
        <ThemedCard variant="outlined" padding="large">
          <ThemedText variant="primary" size="lg" weight="bold" style={{ marginBottom: Layout.spacing.md }}>
            Color Palette
          </ThemedText>
          
          <View style={styles.colorRow}>
            <View style={[styles.colorBox, { backgroundColor: colors.background }]} />
            <ThemedText variant="secondary" size="sm">Background</ThemedText>
          </View>
          
          <View style={styles.colorRow}>
            <View style={[styles.colorBox, { backgroundColor: colors.surface }]} />
            <ThemedText variant="secondary" size="sm">Surface</ThemedText>
          </View>
          
          <View style={styles.colorRow}>
            <View style={[styles.colorBox, { backgroundColor: colors.primary }]} />
            <ThemedText variant="secondary" size="sm">Primary</ThemedText>
          </View>
          
          <View style={styles.colorRow}>
            <View style={[styles.colorBox, { backgroundColor: colors.cardBackground }]} />
            <ThemedText variant="secondary" size="sm">Card Background</ThemedText>
          </View>
        </ThemedCard>
      </View>

      {/* Implementation Status */}
      <View style={styles.section}>
        <ThemedCard variant="elevated" padding="large">
          <ThemedText variant="primary" size="lg" weight="bold" style={{ marginBottom: Layout.spacing.md }}>
            Implemented Screens
          </ThemedText>
          {implementedScreens.map((screen, index) => (
            <ThemedText key={index} variant="secondary" size="sm" style={{ marginBottom: Layout.spacing.xs }}>
              {screen}
            </ThemedText>
          ))}
        </ThemedCard>
      </View>

      {/* Theme Components */}
      <View style={styles.section}>
        <ThemedCard variant="outlined" padding="large">
          <ThemedText variant="primary" size="lg" weight="bold" style={{ marginBottom: Layout.spacing.md }}>
            Theme Components
          </ThemedText>
          {themeComponents.map((component, index) => (
            <ThemedText key={index} variant="secondary" size="sm" style={{ marginBottom: Layout.spacing.xs }}>
              {component}
            </ThemedText>
          ))}
        </ThemedCard>
      </View>

      {/* Dashboard Specific Updates */}
      <View style={styles.section}>
        <ThemedCard variant="elevated" padding="large">
          <ThemedText variant="primary" size="lg" weight="bold" style={{ marginBottom: Layout.spacing.md }}>
            Dashboard Specific Updates
          </ThemedText>
          <ThemedText variant="success" size="sm" style={{ marginBottom: Layout.spacing.xs }}>
            ✅ Quick Actions: Transparent background, only icons and text visible
          </ThemedText>
          <ThemedText variant="success" size="sm" style={{ marginBottom: Layout.spacing.xs }}>
            ✅ Production Overview: Dark cards with white text
          </ThemedText>
          <ThemedText variant="success" size="sm" style={{ marginBottom: Layout.spacing.xs }}>
            ✅ Recent Activity: Dark cards with white text
          </ThemedText>
          <ThemedText variant="success" size="sm" style={{ marginBottom: Layout.spacing.xs }}>
            ✅ Professional shadows and elevation
          </ThemedText>
        </ThemedCard>
      </View>
    </ScrollView>
  );
};

export default ThemeTestSummary;
