import React, { memo, useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { Colors } from '../../constants/colors';
import { Layout } from '../../constants/layout';
import { ShadowPresets } from '../../utils/shadowHelper';

const { width: screenWidth } = Dimensions.get('window');

interface ChartSkeletonProps {
  width?: number;
  height?: number;
  showTabs?: boolean;
  showLegend?: boolean;
}

const ChartSkeleton: React.FC<ChartSkeletonProps> = memo(({
  width = screenWidth - 32,
  height = 220,
  showTabs = true,
  showLegend = true,
}) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const ShimmerView = ({ style }: { style: any }) => (
    <Animated.View
      style={[
        style,
        styles.shimmer,
        { opacity: shimmerOpacity }
      ]}
    />
  );

  return (
    <View style={styles.container}>
      {/* Chart Tabs Skeleton */}
      {showTabs && (
        <View style={styles.tabsContainer}>
          {Array.from({ length: 4 }).map((_, index) => (
            <ShimmerView
              key={index}
              style={[
                styles.tabSkeleton,
                index === 0 && styles.activeTabSkeleton
              ]}
            />
          ))}
        </View>
      )}

      {/* Chart Area Skeleton */}
      <View style={[styles.chartContainer, { width, height }]}>
        {/* Y-Axis Labels */}
        <View style={styles.yAxisContainer}>
          {Array.from({ length: 5 }).map((_, index) => (
            <ShimmerView
              key={index}
              style={styles.yAxisLabelSkeleton}
            />
          ))}
        </View>

        {/* Chart Content */}
        <View style={styles.chartContent}>
          {/* Chart Lines/Bars */}
          <View style={styles.chartDataContainer}>
            {Array.from({ length: 8 }).map((_, index) => (
              <View key={index} style={styles.dataPointContainer}>
                <ShimmerView
                  style={[
                    styles.dataPointSkeleton,
                    { height: Math.random() * 120 + 20 }
                  ]}
                />
              </View>
            ))}
          </View>

          {/* X-Axis Labels */}
          <View style={styles.xAxisContainer}>
            {Array.from({ length: 8 }).map((_, index) => (
              <ShimmerView
                key={index}
                style={styles.xAxisLabelSkeleton}
              />
            ))}
          </View>
        </View>
      </View>

      {/* Legend Skeleton */}
      {showLegend && (
        <View style={styles.legendContainer}>
          {Array.from({ length: 2 }).map((_, index) => (
            <View key={index} style={styles.legendItem}>
              <ShimmerView style={styles.legendDotSkeleton} />
              <ShimmerView style={styles.legendTextSkeleton} />
            </View>
          ))}
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: Layout.borderRadius.md,
    padding: Layout.spacing.md,
    marginBottom: Layout.spacing.lg,
    ...ShadowPresets.card,
  },
  shimmer: {
    backgroundColor: Colors.border,
  },
  tabsContainer: {
    flexDirection: 'row',
    marginBottom: Layout.spacing.md,
    gap: Layout.spacing.sm,
  },
  tabSkeleton: {
    width: 80,
    height: 32,
    borderRadius: Layout.borderRadius.sm,
  },
  activeTabSkeleton: {
    backgroundColor: `${Colors.primary}30`,
  },
  chartContainer: {
    flexDirection: 'row',
    marginBottom: Layout.spacing.md,
  },
  yAxisContainer: {
    width: 40,
    height: '100%',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: Layout.spacing.xs,
    paddingTop: Layout.spacing.sm,
    paddingBottom: Layout.spacing.lg,
  },
  yAxisLabelSkeleton: {
    width: 30,
    height: 12,
    borderRadius: 6,
  },
  chartContent: {
    flex: 1,
    height: '100%',
  },
  chartDataContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    paddingHorizontal: Layout.spacing.sm,
    paddingBottom: Layout.spacing.lg,
  },
  dataPointContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  dataPointSkeleton: {
    width: 8,
    borderRadius: 4,
    minHeight: 20,
  },
  xAxisContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: Layout.spacing.sm,
    height: 20,
  },
  xAxisLabelSkeleton: {
    width: 20,
    height: 12,
    borderRadius: 6,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Layout.spacing.lg,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Layout.spacing.xs,
  },
  legendDotSkeleton: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendTextSkeleton: {
    width: 60,
    height: 12,
    borderRadius: 6,
  },
});

export default ChartSkeleton;
