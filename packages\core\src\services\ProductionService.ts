import { ProductionMetric, ValidationResult, FilterCriteria, SortCriteria, PaginationOptions } from '../types';
import { BaseService } from './BaseService';
import { ProductionCalculator } from '../utils/ProductionCalculator';
import { ProductionValidator } from '../utils/ProductionValidator';

export class ProductionService extends BaseService {
  private static instance: ProductionService;

  public static getInstance(): ProductionService {
    if (!ProductionService.instance) {
      ProductionService.instance = new ProductionService();
    }
    return ProductionService.instance;
  }

  /**
   * Get production metrics with filtering, sorting, and pagination
   */
  async getProductionMetrics(options: {
    filters?: FilterCriteria[];
    sorting?: SortCriteria[];
    pagination?: PaginationOptions;
  } = {}): Promise<ProductionMetric[]> {
    try {
      const repository = this.getRepository('production');
      return await repository.findMany(options);
    } catch (error) {
      this.logger.error('Failed to get production metrics', error);
      throw error;
    }
  }

  /**
   * Get production metrics by date range
   */
  async getProductionMetricsByDateRange(
    startDate: string,
    endDate: string,
    locationId?: string
  ): Promise<ProductionMetric[]> {
    const filters: FilterCriteria[] = [
      { field: 'date', operator: 'gte', value: startDate },
      { field: 'date', operator: 'lte', value: endDate }
    ];

    if (locationId) {
      filters.push({ field: 'location_id', operator: 'eq', value: locationId });
    }

    return this.getProductionMetrics({
      filters,
      sorting: [{ field: 'date', direction: 'asc' }]
    });
  }

  /**
   * Create new production metric
   */
  async createProductionMetric(data: Omit<ProductionMetric, 'id' | 'created_at' | 'updated_at'>): Promise<ProductionMetric> {
    try {
      // Validate data
      const validation = this.validateProductionMetric(data);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      // Calculate derived fields
      const enrichedData = this.enrichProductionData(data);

      // Save to repository
      const repository = this.getRepository('production');
      const result = await repository.create(enrichedData);

      this.logger.info('Production metric created', { id: result.id });
      return result;
    } catch (error) {
      this.logger.error('Failed to create production metric', error);
      throw error;
    }
  }

  /**
   * Update production metric
   */
  async updateProductionMetric(
    id: string,
    data: Partial<Omit<ProductionMetric, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<ProductionMetric> {
    try {
      // Get existing record
      const repository = this.getRepository('production');
      const existing = await repository.findById(id);
      if (!existing) {
        throw new Error(`Production metric not found: ${id}`);
      }

      // Merge data
      const mergedData = { ...existing, ...data };

      // Validate merged data
      const validation = this.validateProductionMetric(mergedData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      // Calculate derived fields
      const enrichedData = this.enrichProductionData(mergedData);

      // Update repository
      const result = await repository.update(id, enrichedData);

      this.logger.info('Production metric updated', { id });
      return result;
    } catch (error) {
      this.logger.error('Failed to update production metric', error);
      throw error;
    }
  }

  /**
   * Delete production metric
   */
  async deleteProductionMetric(id: string): Promise<void> {
    try {
      const repository = this.getRepository('production');
      await repository.delete(id);
      this.logger.info('Production metric deleted', { id });
    } catch (error) {
      this.logger.error('Failed to delete production metric', error);
      throw error;
    }
  }

  /**
   * Bulk create production metrics
   */
  async bulkCreateProductionMetrics(data: Omit<ProductionMetric, 'id' | 'created_at' | 'updated_at'>[]): Promise<ProductionMetric[]> {
    try {
      // Validate all records
      const validationResults = data.map(item => this.validateProductionMetric(item));
      const invalidRecords = validationResults
        .map((result, index) => ({ result, index }))
        .filter(({ result }) => !result.isValid);

      if (invalidRecords.length > 0) {
        const errors = invalidRecords.map(({ result, index }) => 
          `Record ${index + 1}: ${result.errors.map(e => e.message).join(', ')}`
        );
        throw new Error(`Validation failed for ${invalidRecords.length} records:\n${errors.join('\n')}`);
      }

      // Enrich all data
      const enrichedData = data.map(item => this.enrichProductionData(item));

      // Bulk insert
      const repository = this.getRepository('production');
      const results = await repository.bulkCreate(enrichedData);

      this.logger.info('Bulk production metrics created', { count: results.length });
      return results;
    } catch (error) {
      this.logger.error('Failed to bulk create production metrics', error);
      throw error;
    }
  }

  /**
   * Get production summary for a period
   */
  async getProductionSummary(
    startDate: string,
    endDate: string,
    locationId?: string
  ): Promise<{
    totalOb: number;
    totalOre: number;
    averageStripRatio: number;
    totalFuel: number;
    achievementPercentage: number;
    recordCount: number;
  }> {
    try {
      const metrics = await this.getProductionMetricsByDateRange(startDate, endDate, locationId);

      const summary = ProductionCalculator.calculateSummary(metrics);
      
      this.logger.info('Production summary calculated', { 
        startDate, 
        endDate, 
        locationId, 
        recordCount: metrics.length 
      });

      return summary;
    } catch (error) {
      this.logger.error('Failed to get production summary', error);
      throw error;
    }
  }

  /**
   * Get production trends
   */
  async getProductionTrends(
    startDate: string,
    endDate: string,
    groupBy: 'daily' | 'weekly' | 'monthly',
    locationId?: string
  ): Promise<{
    labels: string[];
    obData: number[];
    oreData: number[];
    stripRatioData: number[];
    fuelData: number[];
  }> {
    try {
      const metrics = await this.getProductionMetricsByDateRange(startDate, endDate, locationId);
      const trends = ProductionCalculator.calculateTrends(metrics, groupBy);
      
      this.logger.info('Production trends calculated', { 
        startDate, 
        endDate, 
        groupBy, 
        locationId 
      });

      return trends;
    } catch (error) {
      this.logger.error('Failed to get production trends', error);
      throw error;
    }
  }

  /**
   * Validate production metric data
   */
  private validateProductionMetric(data: any): ValidationResult {
    return ProductionValidator.validate(data);
  }

  /**
   * Enrich production data with calculated fields
   */
  private enrichProductionData(data: any): any {
    const enriched = { ...data };

    // Calculate strip ratio
    if (enriched.actual_ob && enriched.actual_ore) {
      enriched.strip_ratio = ProductionCalculator.calculateStripRatio(
        enriched.actual_ob,
        enriched.actual_ore
      );
    }

    // Calculate achievement percentages
    if (enriched.actual_ob && enriched.plan_ob) {
      enriched.ob_achievement = ProductionCalculator.calculateAchievement(
        enriched.actual_ob,
        enriched.plan_ob
      );
    }

    if (enriched.actual_ore && enriched.plan_ore) {
      enriched.ore_achievement = ProductionCalculator.calculateAchievement(
        enriched.actual_ore,
        enriched.plan_ore
      );
    }

    if (enriched.actual_fuel && enriched.plan_fuel) {
      enriched.fuel_efficiency = ProductionCalculator.calculateEfficiency(
        enriched.actual_fuel,
        enriched.plan_fuel
      );
    }

    // Set timestamps
    const now = new Date().toISOString();
    if (!enriched.created_at) {
      enriched.created_at = now;
    }
    enriched.updated_at = now;

    return enriched;
  }
}
