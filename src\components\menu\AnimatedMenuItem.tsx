import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Platform,
  Easing,
  ImageBackground,
} from 'react-native';
// LinearGradient and SVG imports removed - using background image instead
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';
import { Layout } from '../../constants/layout';
import { ShadowPresets } from '../../utils/shadowHelper';

interface AnimatedMenuItemProps {
  id: string;
  title: string;
  icon: string;
  color: string;
  onPress: () => void;
  index: number;
  hasNotification?: boolean;
  notificationCount?: number;
}

// Helper function removed - no longer needed for background image approach

const AnimatedMenuItem: React.FC<AnimatedMenuItemProps> = ({
  title,
  icon,
  onPress,
  index,
  hasNotification = false,
  notificationCount = 0,
}) => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const pressScaleAnim = useRef(new Animated.Value(1)).current;
  const shadowAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const shimmerAnim = useRef(new Animated.Value(0)).current;
  const transparencyAnim = useRef(new Animated.Value(1)).current;

  // Enhanced entrance animation with stagger effect
  useEffect(() => {
    const delay = index * 120; // Stagger by 120ms per item for better visual flow

    // Sequential animation for more dramatic effect
    Animated.sequence([
      Animated.delay(delay),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 80,
          friction: 12,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Run shadow animation separately to avoid native driver conflicts
    Animated.timing(shadowAnim, {
      toValue: 1,
      duration: 800,
      delay: delay,
      easing: Easing.out(Easing.quad),
      useNativeDriver: false,
    }).start();

    // Enhanced shimmer animation with better timing
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.delay(delay + 1000), // Wait for entrance animation to complete
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
        Animated.delay(3000), // Pause between shimmer cycles
      ])
    );

    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, [index]);

  // Enhanced press animation handlers with transparency feedback
  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(pressScaleAnim, {
        toValue: 0.92,
        tension: 400,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 150,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(transparencyAnim, {
        toValue: 0.7,
        duration: 150,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(pressScaleAnim, {
        toValue: 1,
        tension: 400,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 0,
        duration: 200,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(transparencyAnim, {
        toValue: 1,
        duration: 200,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePress = () => {
    // Haptic feedback for mobile
    if (Platform.OS !== 'web') {
      // Add haptic feedback here if needed
    }
    onPress();
  };

  // Animated styles
  const animatedContainerStyle = {
    opacity: fadeAnim,
    transform: [
      { scale: Animated.multiply(scaleAnim, pressScaleAnim) },
      {
        rotate: rotateAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '2deg'],
        }),
      },
    ],
  } as any;

  const animatedShadowStyle = {
    ...ShadowPresets.button,
    shadowOpacity: shadowAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 0.15],
    }),
    elevation: shadowAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 8],
    }),
  };

  const animatedTransparencyStyle = {
    opacity: transparencyAnim,
  };

  return (
    <Animated.View style={[styles.container, animatedShadowStyle]}>
      <Animated.View style={animatedContainerStyle}>
        <TouchableOpacity
          style={styles.touchable}
          onPress={handlePress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          accessible={true}
          accessibilityLabel={`${title} menu item`}
          accessibilityRole="button"
        >
          <Animated.View style={[styles.menuIconContainer, animatedTransparencyStyle]}>
            {/* Background image using menubg.png */}
            <ImageBackground
              source={require('../../../assets/menubg.png')}
              style={styles.menuBackground}
              resizeMode="contain"
            >
              {/* Icon positioned absolutely in center */}
              <View style={styles.iconContainer}>
                <Ionicons name={icon as any} size={28} color={Colors.primary} />
              </View>

              {/* Enhanced animated shimmer effect overlay */}
              <Animated.View
                style={[
                  styles.shimmerOverlay,
                  {
                    opacity: shimmerAnim.interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [0, 0.4, 0],
                    }),
                    transform: [
                      {
                        translateX: shimmerAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [-100, 100],
                        }),
                      },
                      {
                        scaleX: shimmerAnim.interpolate({
                          inputRange: [0, 0.5, 1],
                          outputRange: [0.3, 1.5, 0.3],
                        }),
                      },
                    ],
                  },
                ]}
              />
            </ImageBackground>

            {/* Notification badge positioned outside background image */}
            {hasNotification && (
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationText}>
                  {notificationCount > 9 ? '9+' : notificationCount.toString()}
                </Text>
              </View>
            )}
          </Animated.View>

          <Text style={styles.menuTitle} numberOfLines={2}>
            {title}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '22%',
    alignItems: 'center',
    marginBottom: Layout.spacing.lg,
    height: 100, // Fixed height to accommodate teardrop shape
    overflow: 'visible', // Ensure notification badges are not clipped
  },
  touchable: {
    alignItems: 'center',
    width: '100%',
  },
  menuIconContainer: {
    width: 70,
    height: 90,
    marginBottom: Layout.spacing.sm,
    overflow: 'visible', // Changed to visible to prevent notification badge clipping
    // Enhanced shadow for depth
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  menuBackground: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  iconContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  notificationBadge: {
    position: 'absolute',
    top: -8, // Adjusted to prevent clipping while staying visible
    right: -8, // Positioned outside the container to prevent clipping
    backgroundColor: Colors.secondary,
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.surface,
    zIndex: 25, // Highest z-index to ensure visibility above all elements
    // Enhanced shadow for better visibility
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 8,
  },
  notificationText: {
    color: Colors.white,
    fontSize: 11, // Slightly larger for better readability
    fontWeight: 'bold',
    textAlign: 'center',
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: -120,
    width: 60,
    height: '100%',
    // Enhanced shimmer for background image
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    transform: [{ skewX: '-15deg' }],
    // Rounded shape for shimmer
    borderRadius: 20,
    zIndex: 5,
  },
  menuTitle: {
    fontSize: Layout.fontSize.xs,
    color: Colors.textPrimary,
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 16,
  },
});

export default AnimatedMenuItem;
