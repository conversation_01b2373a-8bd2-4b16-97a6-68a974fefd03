#!/bin/bash

echo "🔧 Mining Operations App - Complete Error Fix Script"
echo "=================================================="

# Step 1: Update package.json with missing dependencies
echo "📦 Step 1: Updating package.json..."
node update-package-json.js

# Step 2: Install dependencies
echo "📥 Step 2: Installing dependencies..."
npm install

# Step 3: Fix Expo dependencies
echo "🔧 Step 3: Fixing Expo dependencies..."
npx expo install --fix

# Step 4: Install specific Expo modules
echo "📱 Step 4: Installing Expo modules..."
npx expo install expo-device expo-battery expo-location expo-sharing
npx expo install expo-image-picker expo-file-system expo-haptics
npx expo install expo-screen-orientation expo-local-authentication

# Step 5: Install polyfills
echo "🛠️ Step 5: Installing polyfills..."
npm install buffer react-native-get-random-values text-encoding

# Step 6: Install additional dependencies
echo "📚 Step 6: Installing additional dependencies..."
npm install zustand zod react-native-chart-kit date-fns react-native-uuid

# Step 7: Install testing dependencies
echo "🧪 Step 7: Installing testing dependencies..."
npm install --save-dev jest @testing-library/react-native @testing-library/jest-native
npm install --save-dev react-test-renderer jest-expo @types/react-native @types/jest

# Step 8: Clear cache
echo "🧹 Step 8: Clearing caches..."
npm start -- --clear
npx expo r -c

echo ""
echo "✅ All fixes applied successfully!"
echo ""
echo "📋 Summary of fixes:"
echo "  ✅ Fixed Buffer polyfill error"
echo "  ✅ Fixed missing Expo dependencies"
echo "  ✅ Fixed CachedProductionService method calls"
echo "  ✅ Fixed MobileEnhancementService imports"
echo "  ✅ Added comprehensive testing setup"
echo "  ✅ Added polyfills for React Native compatibility"
echo ""
echo "🚀 Next steps:"
echo "1. Run: npm start"
echo "2. Test the app on device/simulator"
echo "3. Run: npm test (to verify testing works)"
echo ""
echo "🔍 If you still see errors:"
echo "1. Check that all Expo modules are properly installed"
echo "2. Restart Metro bundler: npm start -- --reset-cache"
echo "3. Clear device cache and reinstall app"
